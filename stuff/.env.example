# AI-Powered Mock Test System - Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
PORT=3000
NODE_ENV=development

# =============================================================================
# FIREBASE CONFIGURATION (Optional for development)
# =============================================================================
# Get these values from Firebase Console → Project Settings → Service Accounts
# Download the service account JSON file and extract these values:

FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# =============================================================================
# AI/LLM CONFIGURATION (Optional for AI features)
# =============================================================================
# Get your Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key-here

# =============================================================================
# REDIS CONFIGURATION (Optional for production caching)
# =============================================================================
# Redis is disabled by default in development
ENABLE_REDIS=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================
# 
# QUICK START (No setup required):
# 1. Copy this file to .env
# 2. Run: npm install && npm run dev
# 3. App will use mock database automatically
#
# FOR PRODUCTION:
# 1. Set up Firebase (see README.md)
# 2. Get Gemini API key
# 3. Fill in the values above
# 4. Set NODE_ENV=production
#
# =============================================================================

[debug] [2025-06-13T05:01:53.987Z] ----------------------------------------------------------------------
[debug] [2025-06-13T05:01:54.122Z] Command:       /home/<USER>/.nvm/versions/node/v22.14.0/bin/node /home/<USER>/.nvm/versions/node/v22.14.0/bin/firebase init
[debug] [2025-06-13T05:01:54.123Z] CLI Version:   14.7.0
[debug] [2025-06-13T05:01:54.124Z] Platform:      linux
[debug] [2025-06-13T05:01:54.124Z] Node Version:  v22.14.0
[debug] [2025-06-13T05:01:54.125Z] Time:          Fri Jun 13 2025 10:31:54 GMT+0530 (India Standard Time)
[debug] [2025-06-13T05:01:54.126Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-13T05:01:54.141Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-13T05:01:54.143Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /home/<USER>/web_project

[debug] [2025-06-13T05:03:36.776Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (/home/<USER>/.nvm/versions/node/v22.14.0/lib/node_modules/firebase-tools/node_modules/@inquirer/core/dist/commonjs/lib/create-prompt.js:101:37)
    at Interface.emit (node:events:518:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1124:18)
    at ReadStream.onkeypress (node:internal/readline/interface:263:20)
    at ReadStream.emit (node:events:530:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.

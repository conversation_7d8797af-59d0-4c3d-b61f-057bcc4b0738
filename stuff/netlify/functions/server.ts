import { Handler } from '@netlify/functions';
import serverless from 'serverless-http';
import app from '../../server/app';

// Wrap the Express app with serverless-http
const handler: Handler = async (event, context) => {
  // Make context available to routes
  app.locals.context = context;
  
  // Handle the request with serverless-http
  const handler = serverless(app);
  const result = await handler(event, context);
  return result;
};

export { handler };

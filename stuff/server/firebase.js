import admin from 'firebase-admin';
import { MockFirestore } from './services/mockFirebaseService.js';

const mockFirebase = {
  getDb: () => new MockFirestore()
};

let db = null;

async function initializeFirebase() {
  if (!db) {
    try {
      // Use mock DB for development and test environments
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        console.log(`Using mock Firestore for ${process.env.NODE_ENV}`);
        db = mockFirebase.getDb();
        return;
      }

      // Only try to load service account in production
      try {
        const serviceAccount = await import('../firebase-service-account.json', { assert: { type: 'json' } });
        const serviceAccountData = serviceAccount.default;
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccountData)
        });
        db = admin.firestore();
        console.log('Firebase initialized successfully');
      } catch (error) {
        console.warn('Failed to initialize Firebase with service account, falling back to mock DB');
        db = mockFirebase.getDb();
      }
    } catch (error) {
      console.error('Error initializing Firebase:', error);
      throw error;
    }
  }
  return db;
}

async function getDb() {
  if (!db) {
    await initializeFirebase();
  }
  return db;
}

export { getDb };

// Schema for past year questions
export const pastYearQuestionSchema = {
    type: 'object',
    required: ['question_text', 'options', 'correct_answer', 'year', 'exam_name'],
    properties: {
        question_id: { type: 'string' },
        question_text: { type: 'string' },
        options: {
            type: 'array',
            items: { type: 'string' },
            minItems: 2
        },
        correct_answer: { type: 'string' },
        explanation: { type: 'string' },
        year: { 
            type: 'integer',
            minimum: 1900,
            maximum: 2100
        },
        exam_name: { type: 'string' },
        source_url: { type: 'string' },
        topics: {
            type: 'array',
            items: { type: 'string' }
        },
        difficulty: {
            type: 'string',
            enum: ['easy', 'medium', 'hard', 'expert']
        },
        verified: { 
            type: 'boolean',
            default: false
        },
        usage_count: {
            type: 'integer',
            default: 0
        },
        last_used: { 
            type: 'string',
            format: 'date-time'
        },
        created_at: { 
            type: 'string',
            format: 'date-time'
        },
        updated_at: { 
            type: 'string',
            format: 'date-time'
        }
    }
};

import jwt from 'jsonwebtoken';
import loggingService from '../services/loggingService.js';
import redisService from '../services/redisService.js';

// JWT authentication middleware
export const authenticateJWT = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'No token provided' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        return res.status(403).json({ error: 'Invalid token' });
    }
};

// Role-based access control middleware
export const checkRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ error: 'Insufficient permissions' });
        }

        next();
    };
};

// API request validation middleware
export const validateRequest = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        next();
    };
};

// Rate limiting per user middleware
export const userRateLimit = async (req, res, next) => {
    if (!req.user) {
        return next();
    }

    const key = `rate_limit:${req.user.id}`;
    const limit = 100; // requests per minute
    const ttl = 60; // seconds

    try {
        const count = await redisService.increment(key, ttl);
        if (count > limit) {
            return res.status(429).json({ error: 'Too many requests' });
        }
        next();
    } catch (error) {
        loggingService.logError(error, { operation: 'userRateLimit' });
        next(); // Continue on Redis error
    }
};

// Audit logging middleware
export const auditLog = (action) => {
    return async (req, res, next) => {
        if (!req.user) {
            return next();
        }

        try {
            await loggingService.logAudit({
                userId: req.user.id,
                action,
                resource: req.originalUrl,
                method: req.method,
                timestamp: new Date()
            });
        } catch (error) {
            loggingService.logError(error, { operation: 'auditLog' });
        }

        next();
    };
};

// Admin authentication middleware
export const authenticateAdmin = [authenticateJWT, checkRole(['admin'])];

import express from 'express';
import { getDb } from '../firebase.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

// Helper to get data with defaults if doc doesn't exist or is partial
const getDataOrDefault = (doc, defaults) => {
  if (doc.exists) {
    return { ...defaults, ...doc.data() };
  }
  return defaults;
};

// Fetch user statistics
const getUserStats = async (userId) => {
  const db = await getDb();
  const userRef = db.collection('users').doc(userId);
  const doc = await userRef.get();

  const defaultStats = {
    xp: 0,
    level: 1,
    streak: 0,
    rank: 0, // Assuming rank is calculated elsewhere or stored
    rankChange: 0, // Assuming rank change is calculated elsewhere
    todayProgress: 0, // General daily progress/XP, not specific to a mission here
    badges: []
  };
  return getDataOrDefault(doc, defaultStats);
};

// Fetch active daily mission (general mission, not user-specific progress yet)
const getDailyMission = async (userId) => {
  const db = await getDb();
  // Assuming 'missions' collection stores general missions
  // And 'isActive' marks the current daily mission
  const missionsRef = db.collection('missions').where('isActive', '==', true).limit(1);
  const snapshot = await missionsRef.get();

  const defaultMission = {
    title: "No active mission today.",
    progress: 0, // User-specific progress for this mission would ideally be fetched elsewhere
    total: 1,
    reward: 0
  };

  if (snapshot.empty) {
    return defaultMission;
  }
  
  // Assuming the mission doc has fields: title, totalSteps, reward
  const missionData = snapshot.docs[0].data();
  return {
    title: missionData.title || "Unnamed Mission",
    // TODO: Fetch user-specific progress for this mission (e.g., from a 'userMissions' collection)
    progress: 0, // Placeholder: User's progress on this specific mission
    total: missionData.totalSteps || 1,
    reward: missionData.reward || 0
  };
};

// Fetch hot topics
const getHotTopics = async () => {
  const db = await getDb();
  // Assuming 'topics' collection has 'studentsEngaged' and 'trend' fields
  const topicsRef = db.collection('topics').orderBy('studentsEngaged', 'desc').limit(3);
  const snapshot = await topicsRef.get();

  if (snapshot.empty) {
    return [];
  }
  return snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      name: data.name || "Unknown Topic",
      students: data.studentsEngaged || 0,
      trend: data.trend || "stable" // 'up', 'down', 'stable'
    };
  });
};

// Fetch recent achievements for a user
const getRecentAchievements = async (userId) => {
  const db = await getDb();
  // Assuming 'userAchievements' collection links users to achievements
  // and has 'achievedDate', 'achievementName', 'description', 'progress'
  const achievementsRef = db.collection('userAchievements')
                            .where('userId', '==', userId)
                            .orderBy('achievedDate', 'desc')
                            .limit(3);
  const snapshot = await achievementsRef.get();

  if (snapshot.empty) {
    return [];
  }
  return snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      name: data.achievementName || "Unnamed Achievement",
      description: data.description || "",
      progress: data.progress || 0 // Progress towards this achievement if it's incremental
    };
  });
};

// Route: GET /api/users/:userId/dashboard
router.get('/:userId/dashboard', async (req, res) => {
  const { userId } = req.params;

  console.log(`Fetching dashboard data for user: ${userId} from Firestore`);

  if (!userId) {
    return res.status(400).json({ error: 'User ID is required.' });
  }

  try {
    // Fetch all dashboard components concurrently
    const [stats, dailyMission, hotTopics, recentAchievements] = await Promise.all([
      getUserStats(userId),
      getDailyMission(userId), // userId passed for potential future use in mission fetching
      getHotTopics(),
      getRecentAchievements(userId)
    ]);

    res.json({
      stats,
      dailyMission,
      hotTopics,
      recentAchievements
    });
  } catch (error) {
    console.error('Error fetching dashboard data from Firestore:', error);
    loggingService.logError(error, { 
        operation: 'fetchDashboardData', 
        userId: userId,
        message: 'Failed to fetch dashboard data from Firestore',
        route: req.originalUrl
    });
    res.status(500).json({ error: 'Failed to fetch dashboard data', message: error.message });
  }
});

export default router;
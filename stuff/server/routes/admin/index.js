import express from 'express';
import authRouter from './auth.js';
import examConfigRouter from './examConfig.js';
import syllabusUploadRouter from './syllabusUpload.js';
import preComputationRouter from './questionPreComputation.js';
import learningObjectivesRouter from './learningObjectives.js';
import questionDistributionRouter from './questionDistribution.js';
import monitoringRouter from './monitoring.js';
import feedbackRouter from './feedback.js';
import pastYearQuestionsRouter from './pastYearQuestions.js';
import analyticsRouter from './analytics.js';
import questionTypesRouter from './questionTypes.js';
import challengeAdminRouter from './challengeAdmin.js';
import { authenticateAdmin } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Public admin routes
router.use('/auth', authRouter);

// Protected admin routes
router.use(authenticateAdmin);
router.use('/exam', examConfigRouter);
router.use('/syllabus', syllabusUploadRouter);
router.use('/precomputation', preComputationRouter);
router.use('/learning-objectives', learningObjectivesRouter);
router.use('/question-distribution', questionDistributionRouter);
router.use('/monitoring', monitoringRouter);
router.use('/feedback', feedbackRouter);
router.use('/past-year-questions', pastYearQuestionsRouter);
router.use('/analytics', analyticsRouter);
router.use('/question-types', questionTypesRouter);
router.use('/challenge', challengeAdminRouter);

// Error handling
router.use((err, req, res, next) => {
    loggingService.logError(err, {
        path: req.path,
        method: req.method,
        user: req.user?.id
    });
    
    res.status(err.status || 500).json({
        error: err.message || 'Internal server error'
    });
});

export default router;

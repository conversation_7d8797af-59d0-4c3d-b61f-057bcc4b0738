import express from 'express';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import challengeService from '../../services/challengeService.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Get all pending challenges
 * GET /api/admin/challenge/pending
 */
router.get('/pending', async (req, res) => {
    try {
        const snapshot = await challengeService.db.collection('challenges')
            .where('status', '==', 'pending')
            .orderBy('created_at', 'desc')
            .get();

        const challenges = snapshot.docs.map(doc => doc.data());
        res.json(challenges);
    } catch (error) {
        loggingService.logError('Failed to get pending challenges', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Update challenge status
 * PUT /api/admin/challenge/:challengeId/status
 */
router.put('/:challengeId/status', async (req, res) => {
    try {
        const { status, adminFeedback } = req.body;
        await challengeService.updateChallengeStatus(req.params.challengeId, status, adminFeedback);
        res.json({ success: true });
    } catch (error) {
        loggingService.logError('Failed to update challenge status', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get all challenges for review
 * GET /api/admin/challenge/review
 */
router.get('/review', async (req, res) => {
    try {
        const snapshot = await challengeService.db.collection('challenges')
            .where('evaluation.needs_review', '==', true)
            .orderBy('created_at', 'desc')
            .get();

        const challenges = snapshot.docs.map(doc => doc.data());
        res.json(challenges);
    } catch (error) {
        loggingService.logError('Failed to get challenges for review', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get challenge statistics
 * GET /api/admin/challenge/stats
 */
router.get('/stats', async (req, res) => {
    try {
        const snapshot = await challengeService.db.collection('challenges').get();
        const challenges = snapshot.docs.map(doc => doc.data());

        const stats = {
            total: challenges.length,
            pending: challenges.filter(c => c.status === 'pending').length,
            evaluated: challenges.filter(c => c.status === 'evaluated').length,
            needsReview: challenges.filter(c => c.evaluation?.needs_review).length,
            byQuestionType: {},
            byDifficulty: {},
            bySubject: {}
        };

        // Get question details for each challenge
        for (const challenge of challenges) {
            const questionDoc = await challengeService.db.collection('questions')
                .doc(challenge.question_id)
                .get();
            
            if (questionDoc.exists) {
                const question = questionDoc.data();
                
                // Count by question type
                stats.byQuestionType[question.question_type] = 
                    (stats.byQuestionType[question.question_type] || 0) + 1;
                
                // Count by difficulty
                stats.byDifficulty[question.difficulty] = 
                    (stats.byDifficulty[question.difficulty] || 0) + 1;
                
                // Count by subject
                if (question.subject) {
                    stats.bySubject[question.subject] = 
                        (stats.bySubject[question.subject] || 0) + 1;
                }
            }
        }

        res.json(stats);
    } catch (error) {
        loggingService.logError('Failed to get challenge statistics', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

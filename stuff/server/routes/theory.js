import express from 'express';
import theoryService from '../services/theoryService.js';
import loggingService from '../services/loggingService.js';
import { authenticateJWT } from '../middleware/auth.js';

const router = express.Router();

router.post('/theory-explanation', authenticateJWT, async (req, res) => {
    try {
        const explanation = await theoryService.getTheoryExplanation(req.body);
        res.json({ success: true, explanation });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'getTheoryExplanation',
            route: req.originalUrl,
            userId: req.user?.id,
            body: req.body
        });
        res.status(500).json({
            success: false,
            error: 'Error getting theory explanation',
            message: error.message
        });
    }
});

router.post('/on-demand-explanation', authenticateJWT, async (req, res) => {
    try {
        const { questionId, topic, subtopic, difficulty } = req.body;
        const explanation = await theoryService.getOnDemandExplanation({
            questionId,
            topic,
            subtopic,
            difficulty
        });
        res.json({ success: true, explanation });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'getOnDemandExplanation',
            route: req.originalUrl,
            userId: req.user?.id,
            body: req.body
        });
        res.status(500).json({
            success: false,
            error: 'Error getting on-demand explanation',
            message: error.message
        });
    }
});

router.post('/related-concepts', authenticateJWT, async (req, res) => {
    try {
        const { topic, subtopic, difficulty } = req.body;
        const concepts = await theoryService.getRelatedConcepts({
            topic,
            subtopic,
            difficulty
        });
        res.json({ success: true, concepts });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'getRelatedConcepts',
            route: req.originalUrl,
            userId: req.user?.id,
            body: req.body
        });
        res.status(500).json({
            success: false,
            error: 'Error getting related concepts',
            message: error.message
        });
    }
});

router.post('/challenge-answer', authenticateJWT, async (req, res) => {
    try {
        const evaluation = await theoryService.evaluateChallenge(req.body);
        res.json({ success: true, evaluation });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'evaluateChallenge',
            route: req.originalUrl,
            userId: req.user?.id,
            body: req.body
        });
        res.status(500).json({
            success: false,
            error: 'Error evaluating challenge',
            message: error.message
        });
    }
});

export default router;

import express from 'express';
import loggingService from '../services/loggingService.js';
import { authenticateJWT } from '../middleware/auth.js';
import llmService from '../services/llmService.js'; 

const router = express.Router();

/**
 * Generate theory explanation for a question
 * POST /api/learning/theory
 */
router.post('/theory', authenticateJWT, async (req, res) => { 
  try {
    const { questionId, questionText, syllabusName } = req.body; 
    const userId = req.user?.id; 

    if (!questionId || !questionText) {
      return res.status(400).json({ 
        error: 'Missing required parameters: questionId and questionText are required.' 
      });
    }
    
    
    if (!llmService || typeof llmService.generateTheoryExplanation !== 'function') {
        loggingService.logError(new Error('llmService.generateTheoryExplanation is not available'), {
            operation: 'generateTheoryExplanation',
            route: req.originalUrl,
            userId: userId,
            body: req.body,
            message: 'LLM service for theory generation is not configured or unavailable.'
        });
        return res.status(500).json({
            error: 'Service Unavailable',
            message: 'Theory explanation service is currently unavailable.'
        });
    }

    
    const explanation = await llmService.generateTheoryExplanation(questionText, syllabusName); 

    
    
    
    
    
    
    
    
    

    res.json({ explanation });
    
  } catch (error) {
    loggingService.logError(error, {
        operation: 'generateTheoryExplanation',
        route: req.originalUrl,
        userId: req.user?.id, 
        body: req.body
    });
    res.status(500).json({ 
      error: 'Failed to generate theory explanation',
      message: error.message 
    });
  }
});

export default router;
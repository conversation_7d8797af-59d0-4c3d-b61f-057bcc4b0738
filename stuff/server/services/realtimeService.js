const { Server } = require('socket.io');
const { getDb } = require('../firebase');
const loggingService = require('./loggingService');

class RealtimeService {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST']
      }
    });
    this.db = getDb();
    this.activeUsers = new Map();
    this.studyGroups = new Map();
    this.setupSocketHandlers();
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      // User joins study session
      socket.on('join_study_session', async ({ userId, sessionId }) => {
        try {
          socket.join(sessionId);
          this.activeUsers.set(socket.id, { userId, sessionId });
          
          // Get session details
          const session = await this.db.collection('study_sessions')
            .doc(sessionId)
            .get();
          
          // Notify others in session
          socket.to(sessionId).emit('user_joined', {
            userId,
            timestamp: new Date()
          });

          // Send current session state
          socket.emit('session_state', session.data());
        } catch (error) {
          console.error('Error joining study session:', error);
          socket.emit('error', { message: 'Failed to join study session' });
        }
      });

      // Real-time progress update
      socket.on('progress_update', async ({ userId, progress }) => {
        try {
          const user = this.activeUsers.get(socket.id);
          if (!user) return;

          // Update progress in database
          await this.db.collection('user_progress')
            .doc(userId)
            .set(progress, { merge: true });

          // Broadcast progress to session members
          socket.to(user.sessionId).emit('progress_updated', {
            userId,
            progress,
            timestamp: new Date()
          });
        } catch (error) {
          console.error('Error updating progress:', error);
          socket.emit('error', { message: 'Failed to update progress' });
        }
      });

      // Collaborative chat message
      socket.on('chat_message', ({ userId, message }) => {
        const user = this.activeUsers.get(socket.id);
        if (!user) return;

        // Broadcast message to session members
        this.io.to(user.sessionId).emit('new_message', {
          userId,
          message,
          timestamp: new Date()
        });
      });

      // Study group creation
      socket.on('create_study_group', async ({ userId, groupName, subject }) => {
        try {
          const groupId = Math.random().toString(36).substring(7);
          const group = {
            id: groupId,
            name: groupName,
            subject,
            creator: userId,
            members: [userId],
            createdAt: new Date()
          };

          await this.db.collection('study_groups')
            .doc(groupId)
            .set(group);

          this.studyGroups.set(groupId, group);
          socket.join(groupId);
          socket.emit('group_created', group);
        } catch (error) {
          console.error('Error creating study group:', error);
          socket.emit('error', { message: 'Failed to create study group' });
        }
      });

      // Disconnect handling
      socket.on('disconnect', () => {
        const user = this.activeUsers.get(socket.id);
        if (user) {
          socket.to(user.sessionId).emit('user_left', {
            userId: user.userId,
            timestamp: new Date()
          });
          this.activeUsers.delete(socket.id);
        }
      });
    });
  }

  // Utility method to broadcast progress updates
  broadcastProgress(sessionId, progress) {
    this.io.to(sessionId).emit('progress_broadcast', {
      progress,
      timestamp: new Date()
    });
  }
}

module.exports = RealtimeService;

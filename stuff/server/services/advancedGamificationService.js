import { db } from '../firebase.js';
import { collection, doc, getDoc, setDoc, updateDoc, query, where, orderBy, limit, getDocs, addDoc } from 'firebase/firestore';
import loggingService from './loggingService.js';

class AdvancedGamificationService {
  constructor() {
    this.badges = this.initializeBadges();
    this.achievements = this.initializeAchievements();
    this.levelThresholds = [0, 100, 250, 500, 1000, 2000, 4000, 8000, 15000, 30000, 50000];
  }

  // Initialize comprehensive badge system
  initializeBadges() {
    return {
      // Consistency Badges
      streak_3: { name: "Getting Started", icon: "🔥", description: "3-day study streak", xp: 50, rarity: "common" },
      streak_7: { name: "Week Warrior", icon: "🔥", description: "7-day study streak", xp: 100, rarity: "common" },
      streak_15: { name: "Dedication Master", icon: "🔥", description: "15-day study streak", xp: 250, rarity: "rare" },
      streak_30: { name: "Unstoppable Force", icon: "🔥", description: "30-day study streak", xp: 500, rarity: "epic" },
      streak_100: { name: "Legend", icon: "🔥", description: "100-day study streak", xp: 2000, rarity: "legendary" },

      // Performance Badges
      accuracy_80: { name: "Sharp Shooter", icon: "🎯", description: "80% accuracy in a test", xp: 75, rarity: "common" },
      accuracy_90: { name: "Precision Master", icon: "🎯", description: "90% accuracy in a test", xp: 150, rarity: "rare" },
      accuracy_95: { name: "Near Perfect", icon: "🎯", description: "95% accuracy in a test", xp: 300, rarity: "epic" },
      perfect_score: { name: "Perfectionist", icon: "💯", description: "100% score in a test", xp: 500, rarity: "epic" },

      // Speed Badges
      speed_demon: { name: "Speed Demon", icon: "⚡", description: "Complete test in under 30 seconds per question", xp: 100, rarity: "rare" },
      lightning_fast: { name: "Lightning Fast", icon: "⚡", description: "Complete test in under 20 seconds per question", xp: 200, rarity: "epic" },

      // Volume Badges
      questions_100: { name: "Century Club", icon: "💪", description: "Answer 100 questions", xp: 100, rarity: "common" },
      questions_500: { name: "Knowledge Seeker", icon: "💪", description: "Answer 500 questions", xp: 250, rarity: "rare" },
      questions_1000: { name: "Question Master", icon: "💪", description: "Answer 1000 questions", xp: 500, rarity: "epic" },
      questions_5000: { name: "Quiz Legend", icon: "💪", description: "Answer 5000 questions", xp: 1000, rarity: "legendary" },

      // Subject Mastery Badges
      physics_master: { name: "Physics Master", icon: "⚛️", description: "90% average in Physics", xp: 300, rarity: "epic" },
      chemistry_master: { name: "Chemistry Master", icon: "🧪", description: "90% average in Chemistry", xp: 300, rarity: "epic" },
      math_master: { name: "Math Master", icon: "📐", description: "90% average in Mathematics", xp: 300, rarity: "epic" },
      biology_master: { name: "Biology Master", icon: "🧬", description: "90% average in Biology", xp: 300, rarity: "epic" },

      // Social Badges
      challenger: { name: "Challenger", icon: "⚔️", description: "Send 10 challenges to friends", xp: 100, rarity: "common" },
      social_butterfly: { name: "Social Butterfly", icon: "🦋", description: "Add 5 friends", xp: 75, rarity: "common" },
      mentor: { name: "Mentor", icon: "👨‍🏫", description: "Help 10 students improve", xp: 200, rarity: "rare" },

      // Special Event Badges
      early_bird: { name: "Early Bird", icon: "🌅", description: "Study before 7 AM", xp: 50, rarity: "common" },
      night_owl: { name: "Night Owl", icon: "🦉", description: "Study after 11 PM", xp: 50, rarity: "common" },
      weekend_warrior: { name: "Weekend Warrior", icon: "🏋️", description: "Study on weekends", xp: 75, rarity: "common" },

      // Improvement Badges
      comeback_kid: { name: "Comeback Kid", icon: "📈", description: "Improve score by 20% in retake", xp: 150, rarity: "rare" },
      steady_climber: { name: "Steady Climber", icon: "📊", description: "Consistent improvement over 7 days", xp: 200, rarity: "rare" },

      // Exploration Badges
      explorer: { name: "Explorer", icon: "🗺️", description: "Try all subjects", xp: 100, rarity: "common" },
      versatile: { name: "Versatile", icon: "🎭", description: "Score 80%+ in 3 different subjects", xp: 250, rarity: "rare" },

      // Engagement Badges
      explanation_reader: { name: "Knowledge Seeker", icon: "📚", description: "Read 100 explanations", xp: 100, rarity: "common" },
      hint_master: { name: "Strategic Thinker", icon: "💡", description: "Use hints wisely (high success rate)", xp: 75, rarity: "common" },
      
      // Milestone Badges
      first_test: { name: "First Steps", icon: "👶", description: "Complete your first test", xp: 25, rarity: "common" },
      level_10: { name: "Rising Star", icon: "⭐", description: "Reach level 10", xp: 200, rarity: "rare" },
      level_25: { name: "Expert", icon: "🏆", description: "Reach level 25", xp: 500, rarity: "epic" },
      level_50: { name: "Grandmaster", icon: "👑", description: "Reach level 50", xp: 1000, rarity: "legendary" }
    };
  }

  // Initialize achievement system
  initializeAchievements() {
    return {
      daily_missions: {
        name: "Daily Mission Completion",
        description: "Complete daily study missions",
        tiers: [
          { requirement: 7, reward: { xp: 100, hints: 2 }, name: "Week Completer" },
          { requirement: 30, reward: { xp: 500, hints: 5 }, name: "Month Master" },
          { requirement: 100, reward: { xp: 2000, hints: 10 }, name: "Mission Legend" }
        ]
      },
      leaderboard_ranks: {
        name: "Leaderboard Rankings",
        description: "Achieve high rankings on leaderboards",
        tiers: [
          { requirement: 100, reward: { xp: 200, badge: "top_100" }, name: "Top 100" },
          { requirement: 50, reward: { xp: 500, badge: "top_50" }, name: "Top 50" },
          { requirement: 10, reward: { xp: 1000, badge: "top_10" }, name: "Top 10" },
          { requirement: 1, reward: { xp: 2000, badge: "champion" }, name: "Champion" }
        ]
      }
    };
  }

  // Award XP with psychological bonuses
  async awardXP(userId, baseXP, context = {}) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const currentXP = userData.gamification?.xp || 0;
      const totalXP = userData.gamification?.totalXp || 0;

      // Calculate bonus multipliers
      let bonusMultiplier = 1;
      let bonusReasons = [];

      // Streak bonus
      const streak = userData.gamification?.streak?.current || 0;
      if (streak >= 7) {
        bonusMultiplier += 0.2;
        bonusReasons.push("7-day streak bonus (+20%)");
      }
      if (streak >= 30) {
        bonusMultiplier += 0.3;
        bonusReasons.push("30-day streak bonus (+30%)");
      }

      // Performance bonus
      if (context.accuracy >= 0.9) {
        bonusMultiplier += 0.25;
        bonusReasons.push("High accuracy bonus (+25%)");
      }

      // Speed bonus
      if (context.avgTimePerQuestion && context.avgTimePerQuestion < 30) {
        bonusMultiplier += 0.15;
        bonusReasons.push("Speed bonus (+15%)");
      }

      // Random lucky bonus (5% chance)
      if (Math.random() < 0.05) {
        bonusMultiplier += 0.5;
        bonusReasons.push("🍀 Lucky bonus (+50%)!");
      }

      // Weekend bonus
      const isWeekend = [0, 6].includes(new Date().getDay());
      if (isWeekend) {
        bonusMultiplier += 0.1;
        bonusReasons.push("Weekend warrior bonus (+10%)");
      }

      const finalXP = Math.round(baseXP * bonusMultiplier);
      const newXP = currentXP + finalXP;
      const newTotalXP = totalXP + finalXP;

      // Check for level up
      const oldLevel = this.calculateLevel(currentXP);
      const newLevel = this.calculateLevel(newXP);
      const leveledUp = newLevel > oldLevel;

      // Update user data
      await updateDoc(userRef, {
        'gamification.xp': newXP,
        'gamification.totalXp': newTotalXP,
        'gamification.level': newLevel,
        'gamification.lastXpGain': {
          amount: finalXP,
          baseAmount: baseXP,
          bonusMultiplier: bonusMultiplier,
          bonusReasons: bonusReasons,
          timestamp: new Date()
        }
      });

      // Check for new badges
      const newBadges = await this.checkAndAwardBadges(userId, userData, context);

      // Log XP award
      await loggingService.logInfo('XP awarded', {
        userId,
        baseXP,
        finalXP,
        bonusMultiplier,
        bonusReasons,
        leveledUp,
        newLevel,
        newBadges: newBadges.length
      });

      return {
        xpAwarded: finalXP,
        bonusMultiplier,
        bonusReasons,
        leveledUp,
        newLevel,
        newBadges,
        totalXP: newXP
      };

    } catch (error) {
      await loggingService.logError(error, { operation: 'awardXP', userId, baseXP });
      throw error;
    }
  }

  // Calculate user level based on XP
  calculateLevel(xp) {
    for (let i = this.levelThresholds.length - 1; i >= 0; i--) {
      if (xp >= this.levelThresholds[i]) {
        return i;
      }
    }
    return 0;
  }

  // Get XP needed for next level
  getXPForNextLevel(currentXP) {
    const currentLevel = this.calculateLevel(currentXP);
    if (currentLevel >= this.levelThresholds.length - 1) {
      return null; // Max level reached
    }
    return this.levelThresholds[currentLevel + 1] - currentXP;
  }

  // Check and award badges based on user activity
  async checkAndAwardBadges(userId, userData, context = {}) {
    try {
      const currentBadges = userData.gamification?.badges || [];
      const newBadges = [];

      // Check streak badges
      const streak = userData.gamification?.streak?.current || 0;
      const streakBadges = ['streak_3', 'streak_7', 'streak_15', 'streak_30', 'streak_100'];
      for (const badgeId of streakBadges) {
        const requiredStreak = parseInt(badgeId.split('_')[1]);
        if (streak >= requiredStreak && !currentBadges.includes(badgeId)) {
          newBadges.push(badgeId);
        }
      }

      // Check accuracy badges
      if (context.accuracy) {
        if (context.accuracy >= 0.8 && !currentBadges.includes('accuracy_80')) {
          newBadges.push('accuracy_80');
        }
        if (context.accuracy >= 0.9 && !currentBadges.includes('accuracy_90')) {
          newBadges.push('accuracy_90');
        }
        if (context.accuracy >= 0.95 && !currentBadges.includes('accuracy_95')) {
          newBadges.push('accuracy_95');
        }
        if (context.accuracy === 1.0 && !currentBadges.includes('perfect_score')) {
          newBadges.push('perfect_score');
        }
      }

      // Check question volume badges
      const totalQuestions = userData.analytics?.totalQuestionsAnswered || 0;
      const volumeBadges = [
        { id: 'questions_100', requirement: 100 },
        { id: 'questions_500', requirement: 500 },
        { id: 'questions_1000', requirement: 1000 },
        { id: 'questions_5000', requirement: 5000 }
      ];
      
      for (const badge of volumeBadges) {
        if (totalQuestions >= badge.requirement && !currentBadges.includes(badge.id)) {
          newBadges.push(badge.id);
        }
      }

      // Check level badges
      const currentLevel = userData.gamification?.level || 0;
      const levelBadges = [
        { id: 'level_10', requirement: 10 },
        { id: 'level_25', requirement: 25 },
        { id: 'level_50', requirement: 50 }
      ];

      for (const badge of levelBadges) {
        if (currentLevel >= badge.requirement && !currentBadges.includes(badge.id)) {
          newBadges.push(badge.id);
        }
      }

      // Check time-based badges
      const currentHour = new Date().getHours();
      if (currentHour < 7 && !currentBadges.includes('early_bird')) {
        newBadges.push('early_bird');
      }
      if (currentHour >= 23 && !currentBadges.includes('night_owl')) {
        newBadges.push('night_owl');
      }

      // Award new badges
      if (newBadges.length > 0) {
        const userRef = doc(db, 'users', userId);
        const updatedBadges = [...currentBadges, ...newBadges];
        
        await updateDoc(userRef, {
          'gamification.badges': updatedBadges,
          'gamification.lastBadgeEarned': {
            badges: newBadges,
            timestamp: new Date()
          }
        });

        // Award XP for badges
        let badgeXP = 0;
        for (const badgeId of newBadges) {
          if (this.badges[badgeId]) {
            badgeXP += this.badges[badgeId].xp;
          }
        }

        if (badgeXP > 0) {
          await updateDoc(userRef, {
            'gamification.xp': (userData.gamification?.xp || 0) + badgeXP,
            'gamification.totalXp': (userData.gamification?.totalXp || 0) + badgeXP
          });
        }
      }

      return newBadges;

    } catch (error) {
      await loggingService.logError(error, { operation: 'checkAndAwardBadges', userId });
      return [];
    }
  }

  // Update user streak
  async updateStreak(userId) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const streak = userData.gamification?.streak || { current: 0, longest: 0, lastStudyDate: null };
      
      const today = new Date().toDateString();
      const lastStudyDate = streak.lastStudyDate ? new Date(streak.lastStudyDate.toDate()).toDateString() : null;
      
      let newStreak = { ...streak };

      if (lastStudyDate === today) {
        // Already studied today, no change
        return newStreak;
      }

      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayString = yesterday.toDateString();

      if (lastStudyDate === yesterdayString) {
        // Continuing streak
        newStreak.current += 1;
      } else if (lastStudyDate === null || lastStudyDate < yesterdayString) {
        // Starting new streak or broken streak
        newStreak.current = 1;
      }

      // Update longest streak
      if (newStreak.current > newStreak.longest) {
        newStreak.longest = newStreak.current;
      }

      newStreak.lastStudyDate = new Date();

      await updateDoc(userRef, {
        'gamification.streak': newStreak
      });

      return newStreak;

    } catch (error) {
      await loggingService.logError(error, { operation: 'updateStreak', userId });
      throw error;
    }
  }

  // Generate daily mission for user
  async generateDailyMission(userId) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const analytics = userData.analytics || {};
      
      // Analyze user's weak areas
      const weakSubjects = [];
      if (analytics.subjectPerformance) {
        for (const [subject, performance] of Object.entries(analytics.subjectPerformance)) {
          if (performance.accuracy < 0.7) {
            weakSubjects.push(subject);
          }
        }
      }

      // Generate personalized mission
      const missions = [
        {
          type: 'accuracy',
          title: 'Accuracy Challenge',
          description: 'Score 80% or higher in any subject',
          target: { accuracy: 0.8 },
          reward: { xp: 100, hints: 2 }
        },
        {
          type: 'volume',
          title: 'Question Marathon',
          description: 'Answer 20 questions correctly',
          target: { correctAnswers: 20 },
          reward: { xp: 150, hints: 3 }
        },
        {
          type: 'speed',
          title: 'Speed Run',
          description: 'Complete a test in under 10 minutes',
          target: { maxTime: 600 },
          reward: { xp: 120, hints: 2 }
        },
        {
          type: 'improvement',
          title: 'Weakness Warrior',
          description: `Improve your ${weakSubjects[0] || 'weakest'} subject performance`,
          target: { subject: weakSubjects[0] || 'Physics', improvement: 0.1 },
          reward: { xp: 200, hints: 4 }
        },
        {
          type: 'consistency',
          title: 'Streak Keeper',
          description: 'Maintain your study streak',
          target: { maintainStreak: true },
          reward: { xp: 80, hints: 1 }
        }
      ];

      // Select mission based on user's current performance
      let selectedMission;
      if (weakSubjects.length > 0) {
        selectedMission = missions.find(m => m.type === 'improvement');
      } else {
        selectedMission = missions[Math.floor(Math.random() * missions.length)];
      }

      const today = new Date().toDateString();
      const dailyMission = {
        ...selectedMission,
        date: today,
        progress: 0,
        completed: false,
        createdAt: new Date()
      };

      await updateDoc(userRef, {
        'gamification.dailyMission': dailyMission
      });

      return dailyMission;

    } catch (error) {
      await loggingService.logError(error, { operation: 'generateDailyMission', userId });
      throw error;
    }
  }

  // Update daily mission progress
  async updateDailyMissionProgress(userId, sessionData) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        return null;
      }

      const userData = userDoc.data();
      const mission = userData.gamification?.dailyMission;
      
      if (!mission || mission.completed) {
        return null;
      }

      const today = new Date().toDateString();
      if (mission.date !== today) {
        // Generate new mission for today
        return await this.generateDailyMission(userId);
      }

      let progress = mission.progress;
      let completed = false;

      // Update progress based on mission type
      switch (mission.type) {
        case 'accuracy':
          if (sessionData.accuracy >= mission.target.accuracy) {
            progress = 100;
            completed = true;
          }
          break;
        
        case 'volume':
          const correctAnswers = sessionData.correctAnswers || 0;
          progress = Math.min(100, (correctAnswers / mission.target.correctAnswers) * 100);
          completed = progress >= 100;
          break;
        
        case 'speed':
          if (sessionData.totalTime <= mission.target.maxTime) {
            progress = 100;
            completed = true;
          }
          break;
        
        case 'consistency':
          progress = 100;
          completed = true;
          break;
      }

      const updatedMission = {
        ...mission,
        progress,
        completed,
        completedAt: completed ? new Date() : null
      };

      await updateDoc(userRef, {
        'gamification.dailyMission': updatedMission
      });

      // Award mission completion rewards
      if (completed && !mission.completed) {
        await this.awardXP(userId, mission.reward.xp, { missionCompleted: true });
        
        // Award hints if specified
        if (mission.reward.hints) {
          const currentHints = userData.gamification?.hints || 0;
          await updateDoc(userRef, {
            'gamification.hints': currentHints + mission.reward.hints
          });
        }
      }

      return updatedMission;

    } catch (error) {
      await loggingService.logError(error, { operation: 'updateDailyMissionProgress', userId });
      return null;
    }
  }

  // Get comprehensive gamification profile
  async getGamificationProfile(userId) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const gamification = userData.gamification || {};
      
      const profile = {
        level: gamification.level || 0,
        xp: gamification.xp || 0,
        totalXp: gamification.totalXp || 0,
        xpForNextLevel: this.getXPForNextLevel(gamification.xp || 0),
        streak: gamification.streak || { current: 0, longest: 0 },
        badges: gamification.badges || [],
        badgeDetails: (gamification.badges || []).map(badgeId => ({
          id: badgeId,
          ...this.badges[badgeId]
        })),
        dailyMission: gamification.dailyMission || null,
        hints: gamification.hints || 0,
        lastXpGain: gamification.lastXpGain || null,
        lastBadgeEarned: gamification.lastBadgeEarned || null
      };

      return profile;

    } catch (error) {
      await loggingService.logError(error, { operation: 'getGamificationProfile', userId });
      throw error;
    }
  }

  // Get leaderboard data
  async getLeaderboard(type = 'daily', category = 'xp', limit = 100) {
    try {
      const today = new Date();
      let dateFilter;

      switch (type) {
        case 'daily':
          dateFilter = today.toDateString();
          break;
        case 'weekly':
          const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
          dateFilter = weekStart.toDateString();
          break;
        case 'monthly':
          dateFilter = `${today.getFullYear()}-${today.getMonth() + 1}`;
          break;
        case 'all-time':
          dateFilter = 'all-time';
          break;
      }

      // Query users based on category
      let orderField;
      switch (category) {
        case 'xp':
          orderField = type === 'all-time' ? 'gamification.totalXp' : 'gamification.xp';
          break;
        case 'streak':
          orderField = 'gamification.streak.current';
          break;
        case 'level':
          orderField = 'gamification.level';
          break;
        default:
          orderField = 'gamification.xp';
      }

      const usersQuery = query(
        collection(db, 'users'),
        orderBy(orderField, 'desc'),
        limit(limit)
      );

      const snapshot = await getDocs(usersQuery);
      const leaderboard = [];

      snapshot.forEach((doc, index) => {
        const userData = doc.data();
        leaderboard.push({
          rank: index + 1,
          userId: doc.id,
          username: userData.profile?.username || 'Anonymous',
          avatar: userData.profile?.avatar || null,
          value: this.getLeaderboardValue(userData, category, type),
          level: userData.gamification?.level || 0,
          badges: userData.gamification?.badges?.length || 0
        });
      });

      return {
        type,
        category,
        dateFilter,
        entries: leaderboard,
        lastUpdated: new Date()
      };

    } catch (error) {
      await loggingService.logError(error, { operation: 'getLeaderboard', type, category });
      throw error;
    }
  }

  // Helper method to get leaderboard value
  getLeaderboardValue(userData, category, type) {
    switch (category) {
      case 'xp':
        return type === 'all-time' 
          ? userData.gamification?.totalXp || 0 
          : userData.gamification?.xp || 0;
      case 'streak':
        return userData.gamification?.streak?.current || 0;
      case 'level':
        return userData.gamification?.level || 0;
      default:
        return userData.gamification?.xp || 0;
    }
  }

  // Award daily login reward
  async awardDailyReward(userId) {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data();
      const lastReward = userData.gamification?.lastDailyReward;
      const today = new Date().toDateString();

      if (lastReward && new Date(lastReward.toDate()).toDateString() === today) {
        return { alreadyClaimed: true };
      }

      // Generate random reward
      const rewards = [
        { type: 'xp', amount: 50, description: '50 XP Bonus' },
        { type: 'xp', amount: 100, description: '100 XP Bonus' },
        { type: 'hints', amount: 2, description: '2 Bonus Hints' },
        { type: 'hints', amount: 3, description: '3 Bonus Hints' },
        { type: 'xp', amount: 200, description: '200 XP Jackpot!', rare: true },
        { type: 'hints', amount: 5, description: '5 Hint Bonanza!', rare: true }
      ];

      // Higher chance for rare rewards if user has long streak
      const streak = userData.gamification?.streak?.current || 0;
      const rareChance = streak >= 7 ? 0.3 : 0.1;
      
      const availableRewards = Math.random() < rareChance 
        ? rewards 
        : rewards.filter(r => !r.rare);
      
      const selectedReward = availableRewards[Math.floor(Math.random() * availableRewards.length)];

      // Apply reward
      const updates = {
        'gamification.lastDailyReward': new Date()
      };

      if (selectedReward.type === 'xp') {
        const currentXP = userData.gamification?.xp || 0;
        const totalXP = userData.gamification?.totalXp || 0;
        updates['gamification.xp'] = currentXP + selectedReward.amount;
        updates['gamification.totalXp'] = totalXP + selectedReward.amount;
      } else if (selectedReward.type === 'hints') {
        const currentHints = userData.gamification?.hints || 0;
        updates['gamification.hints'] = currentHints + selectedReward.amount;
      }

      await updateDoc(userRef, updates);

      return {
        reward: selectedReward,
        claimed: true
      };

    } catch (error) {
      await loggingService.logError(error, { operation: 'awardDailyReward', userId });
      throw error;
    }
  }
}

export default new AdvancedGamificationService();
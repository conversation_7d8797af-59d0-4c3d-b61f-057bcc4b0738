import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';

class AnalyticsService {
    constructor() {
        this.db = null;
    }

    async getDbInstance() {
        if (!this.db) {
            this.db = await getDb();
        }
        return this.db;
    }

    /**
     * Get user's performance trends over time
     */
    async getPerformanceTrends(userId, timeRange = '30d') {
        try {
            const db = await this.getDbInstance();
            const cutoffDate = this.getDateFromRange(timeRange);
            const sessionsRef = db.collection('test_sessions')
                .where('user_id', '==', userId)
                .where('end_time', '>=', cutoffDate)
                .orderBy('end_time', 'asc');

            const snapshot = await sessionsRef.get();
            const sessions = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            return this.aggregatePerformanceData(sessions);
        } catch (error) {
            loggingService.logError('Failed to get performance trends', { error });
            throw error;
        }
    }

    /**
     * Get user's weak areas based on performance
     */
    async getWeakAreas(userId) {
        try {
            const db = await this.getDbInstance();
            const sessionsRef = db.collection('test_sessions')
                .where('user_id', '==', userId)
                .orderBy('end_time', 'desc')
                .limit(50); // Consider last 50 sessions

            const snapshot = await sessionsRef.get();
            const sessions = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            return this.analyzeWeakAreas(sessions);
        } catch (error) {
            loggingService.logError('Failed to get weak areas', { error });
            throw error;
        }
    }

    /**
     * Generate personalized study plan
     */
    async generateStudyPlan(userId) {
        try {
            const db = await this.getDbInstance();
            // Get weak areas
            const weakAreas = await this.getWeakAreas(userId);

            // Get user's skill levels
            const userSkillsDoc = await db.collection('user_skills').doc(userId).get();
            const skillLevels = userSkillsDoc.exists ? userSkillsDoc.data() : {};

            // Get syllabus data for recommendations
            const syllabiSnapshot = await db.collection('syllabi').get();
            const syllabi = syllabiSnapshot.docs.map(doc => doc.data());

            return this.createStudyPlan(weakAreas, skillLevels, syllabi);
        } catch (error) {
            loggingService.logError('Failed to generate study plan', { error });
            throw error;
        }
    }

    /**
     * Get detailed topic performance
     */
    async getTopicPerformance(userId, topic) {
        try {
            const db = await this.getDbInstance();
            const sessionsRef = db.collection('test_sessions')
                .where('user_id', '==', userId)
                .where('topic', '==', topic)
                .orderBy('end_time', 'desc')
                .limit(20);

            const snapshot = await sessionsRef.get();
            const sessions = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));

            return this.analyzeTopicPerformance(sessions);
        } catch (error) {
            loggingService.logError('Failed to get topic performance', { error });
            throw error;
        }
    }

    /**
     * Helper: Get date from range string
     */
    getDateFromRange(range) {
        const now = new Date();
        const days = parseInt(range);
        return new Date(now.setDate(now.getDate() - days));
    }

    /**
     * Helper: Aggregate performance data
     */
    aggregatePerformanceData(sessions) {
        const dailyScores = {};
        const topicScores = {};
        let totalTime = 0;
        let totalQuestions = 0;

        sessions.forEach(session => {
            const date = new Date(session.end_time).toISOString().split('T')[0];
            
            // Daily scores
            if (!dailyScores[date]) {
                dailyScores[date] = {
                    scores: [],
                    avgTime: 0,
                    totalQuestions: 0
                };
            }
            dailyScores[date].scores.push(session.score);
            dailyScores[date].totalQuestions += session.total_questions;
            
            // Topic scores
            if (!topicScores[session.topic]) {
                topicScores[session.topic] = {
                    scores: [],
                    attempts: 0,
                    correct: 0
                };
            }
            topicScores[session.topic].scores.push(session.score);
            topicScores[session.topic].attempts += session.total_questions;
            topicScores[session.topic].correct += session.correct_answers_count;

            // Overall stats
            totalTime += (new Date(session.end_time) - new Date(session.start_time)) / 1000;
            totalQuestions += session.total_questions;
        });

        // Calculate averages
        const trends = {
            daily: Object.entries(dailyScores).map(([date, data]) => ({
                date,
                avgScore: data.scores.reduce((a, b) => a + b, 0) / data.scores.length,
                totalQuestions: data.totalQuestions
            })),
            topics: Object.entries(topicScores).map(([topic, data]) => ({
                topic,
                avgScore: data.scores.reduce((a, b) => a + b, 0) / data.scores.length,
                accuracy: (data.correct / data.attempts) * 100
            })),
            overall: {
                avgTimePerQuestion: totalQuestions ? totalTime / totalQuestions : 0,
                totalSessions: sessions.length,
                totalQuestions
            }
        };

        return trends;
    }

    /**
     * Helper: Analyze weak areas
     */
    analyzeWeakAreas(sessions) {
        const topicStats = {};

        sessions.forEach(session => {
            if (!topicStats[session.topic]) {
                topicStats[session.topic] = {
                    attempts: 0,
                    correct: 0,
                    scores: [],
                    subtopics: {}
                };
            }

            topicStats[session.topic].attempts += session.total_questions;
            topicStats[session.topic].correct += session.correct_answers_count;
            topicStats[session.topic].scores.push(session.score);

            // Analyze subtopics
            session.questions_answered.forEach(qa => {
                const subtopic = qa.subtopic || 'general';
                if (!topicStats[session.topic].subtopics[subtopic]) {
                    topicStats[session.topic].subtopics[subtopic] = {
                        attempts: 0,
                        correct: 0
                    };
                }
                topicStats[session.topic].subtopics[subtopic].attempts++;
                if (qa.is_correct) {
                    topicStats[session.topic].subtopics[subtopic].correct++;
                }
            });
        });

        // Calculate accuracies and identify weak areas
        const weakAreas = [];
        Object.entries(topicStats).forEach(([topic, stats]) => {
            const topicAccuracy = (stats.correct / stats.attempts) * 100;
            const avgScore = stats.scores.reduce((a, b) => a + b, 0) / stats.scores.length;

            if (topicAccuracy < 70 || avgScore < 60) {
                const weakSubtopics = Object.entries(stats.subtopics)
                    .filter(([_, subStats]) => (subStats.correct / subStats.attempts) * 100 < 70)
                    .map(([subtopic]) => subtopic);

                weakAreas.push({
                    topic,
                    accuracy: topicAccuracy,
                    avgScore,
                    weakSubtopics,
                    priority: this.calculatePriority(topicAccuracy, avgScore, stats.attempts)
                });
            }
        });

        return weakAreas.sort((a, b) => b.priority - a.priority);
    }

    /**
     * Helper: Calculate priority score for weak areas
     */
    calculatePriority(accuracy, avgScore, attempts) {
        // Lower accuracy and score = higher priority
        // More attempts = higher priority (frequently tested topics)
        return ((100 - accuracy) * 0.4 + (100 - avgScore) * 0.4 + Math.min(attempts, 50) * 0.2);
    }

    /**
     * Helper: Create personalized study plan
     */
    createStudyPlan(weakAreas, skillLevels, syllabi) {
        const plan = {
            immediate_focus: [],
            short_term: [],
            long_term: [],
            recommended_resources: {}
        };

        weakAreas.forEach(area => {
            const topicSkill = skillLevels[area.topic] || { rating: 1200 };
            const syllabusContent = syllabi.find(s => 
                s.topics.some(t => t.name === area.topic)
            );

            const focusArea = {
                topic: area.topic,
                current_skill_level: topicSkill.rating,
                target_improvement: Math.round((70 - area.accuracy) * 1.5), // Aim for at least 70% accuracy
                weak_subtopics: area.weakSubtopics,
                recommended_questions: Math.round((100 - area.accuracy) / 10) * 5 // 5 questions per 10% below perfect
            };

            // Add syllabus references if available
            if (syllabusContent) {
                const topicContent = syllabusContent.topics.find(t => t.name === area.topic);
                if (topicContent) {
                    focusArea.syllabus_references = topicContent.resources || [];
                    plan.recommended_resources[area.topic] = topicContent.resources || [];
                }
            }

            // Categorize based on priority
            if (area.priority > 70) {
                plan.immediate_focus.push(focusArea);
            } else if (area.priority > 50) {
                plan.short_term.push(focusArea);
            } else {
                plan.long_term.push(focusArea);
            }
        });

        return plan;
    }

    /**
     * Helper: Analyze topic performance
     */
    analyzeTopicPerformance(sessions) {
        const subtopicStats = {};
        const timeStats = {
            morning: { total: 0, correct: 0 },
            afternoon: { total: 0, correct: 0 },
            evening: { total: 0, correct: 0 }
        };
        const difficultyStats = {
            easy: { total: 0, correct: 0 },
            medium: { total: 0, correct: 0 },
            hard: { total: 0, correct: 0 }
        };

        sessions.forEach(session => {
            const hour = new Date(session.start_time).getHours();
            const timeOfDay = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';

            session.questions_answered.forEach(qa => {
                // Subtopic stats
                const subtopic = qa.subtopic || 'general';
                if (!subtopicStats[subtopic]) {
                    subtopicStats[subtopic] = { total: 0, correct: 0, times: [] };
                }
                subtopicStats[subtopic].total++;
                if (qa.is_correct) subtopicStats[subtopic].correct++;
                subtopicStats[subtopic].times.push(qa.time_taken_seconds);

                // Time of day stats
                timeStats[timeOfDay].total++;
                if (qa.is_correct) timeStats[timeOfDay].correct++;

                // Difficulty stats
                const difficulty = qa.difficulty || 'medium';
                difficultyStats[difficulty].total++;
                if (qa.is_correct) difficultyStats[difficulty].correct++;
            });
        });

        return {
            subtopics: Object.entries(subtopicStats).map(([subtopic, stats]) => ({
                subtopic,
                accuracy: (stats.correct / stats.total) * 100,
                avgTime: stats.times.reduce((a, b) => a + b, 0) / stats.times.length,
                attempts: stats.total
            })),
            timeOfDay: Object.entries(timeStats).map(([time, stats]) => ({
                time,
                accuracy: stats.total ? (stats.correct / stats.total) * 100 : 0,
                attempts: stats.total
            })),
            byDifficulty: Object.entries(difficultyStats).map(([difficulty, stats]) => ({
                difficulty,
                accuracy: stats.total ? (stats.correct / stats.total) * 100 : 0,
                attempts: stats.total
            }))
        };
    }
}

const analyticsService = new AnalyticsService();
export default analyticsService;

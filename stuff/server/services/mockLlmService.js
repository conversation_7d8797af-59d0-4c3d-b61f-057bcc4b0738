import loggingService from './loggingService.js';

/**
 * Generate questions using mock LLM responses for development
 */
async function generateQuestionsWithLLM(params) {
  const {
    subject,
    level,
    numQuestions = 1,
    topic = '',
    test_type = 'practice'
  } = params;

  const prompt = `Generate ${numQuestions} ${level} level questions for ${subject} topic: ${topic}`;

  try {
    // Generate mock questions
    const questions = Array(parseInt(numQuestions)).fill(null).map((_, i) => ({
      id: `q${Date.now()}_${i}`,
      question: `Sample ${level} ${subject} question ${i + 1} about ${topic}`,
      options: ['A', 'B', 'C', 'D'].map(opt => `Option ${opt}`),
      correctAnswer: 'A',
      explanation: `This is a sample explanation for question ${i + 1}`,
      difficulty: level,
      topic: topic,
      test_type: test_type,
      multimedia: null
    }));

    await loggingService.logLlmCall(
      'question_generation',
      prompt,
      JSON.stringify(questions),
      0
    );

    return questions;
  } catch (error) {
    await loggingService.logLlmCall(
      'question_generation',
      prompt,
      null,
      0,
      error
    );
    throw error;
  }
}

/**
 * Generate theory explanation using mock LLM responses
 */
async function generateTheoryExplanation(topic, details = {}) {
  const prompt = `Generate theory explanation for ${topic}`;

  try {
    const explanation = {
      mainContent: `This is a sample theory explanation for ${topic}.`,
      keyPoints: [
        'Key point 1',
        'Key point 2',
        'Key point 3'
      ],
      examples: [
        'Example 1',
        'Example 2'
      ],
      commonErrors: [
        'Common error 1',
        'Common error 2'
      ],
      references: [
        'Reference 1',
        'Reference 2'
      ]
    };

    await loggingService.logLlmCall(
      'theory_explanation',
      prompt,
      JSON.stringify(explanation),
      0
    );

    return explanation;
  } catch (error) {
    await loggingService.logLlmCall(
      'theory_explanation',
      prompt,
      null,
      0,
      error
    );
    throw error;
  }
}

/**
 * Evaluate challenge using mock LLM responses
 */
async function evaluateChallengeWithLLM(question, userChallenge) {
  const prompt = `Evaluate challenge for question: ${question.question}\nChallenge: ${userChallenge}`;

  try {
    const evaluation = {
      verdict: 'valid',
      reasoning: 'This is a mock evaluation of the challenge.',
      suggestedCorrection: null
    };

    await loggingService.logLlmCall(
      'challenge_evaluation',
      prompt,
      JSON.stringify(evaluation),
      0
    );

    return evaluation;
  } catch (error) {
    await loggingService.logLlmCall(
      'challenge_evaluation',
      prompt,
      null,
      0,
      error
    );
    throw error;
  }
}

export {
  generateQuestionsWithLLM,
  generateTheoryExplanation,
  evaluateChallengeWithLLM
};

export default {
  generateQuestionsWithLLM,
  generateTheoryExplanation,
  evaluateChallengeWithLLM
};

import { v2 as cloudinary } from 'cloudinary';
import sharp from 'sharp';
import loggingService from './loggingService.js';
import redisService from './redisService.js';

class CDNService {
    constructor() {
        // Configure Cloudinary
        cloudinary.config({
            cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
            api_key: process.env.CLOUDINARY_API_KEY,
            api_secret: process.env.CLOUDINARY_API_SECRET
        });

        // Cache configuration
        this.cacheConfig = {
            imageCache: 'cdn:images:',
            defaultTTL: 3600 * 24 // 24 hours
        };
    }

    async uploadImage(file, options = {}) {
        try {
            // Optimize image before upload
            const optimizedBuffer = await this.optimizeImage(file.buffer, options);

            // Upload to Cloudinary
            const result = await cloudinary.uploader.upload_stream({
                folder: 'mock-test-system',
                resource_type: 'image',
                ...options
            }).end(optimizedBuffer);

            // Cache the result
            const cacheKey = `${this.cacheConfig.imageCache}${result.public_id}`;
            await redisService.set(cacheKey, result, this.cacheConfig.defaultTTL);

            return {
                url: result.secure_url,
                publicId: result.public_id,
                width: result.width,
                height: result.height,
                format: result.format,
                resourceType: result.resource_type
            };
        } catch (error) {
            loggingService.logError(error, { operation: 'uploadImage' });
            throw error;
        }
    }

    async optimizeImage(buffer, options = {}) {
        try {
            const {
                maxWidth = 1200,
                maxHeight = 1200,
                quality = 80,
                format = 'webp'
            } = options;

            let imageProcessor = sharp(buffer);

            // Resize if needed
            imageProcessor = imageProcessor.resize(maxWidth, maxHeight, {
                fit: 'inside',
                withoutEnlargement: true
            });

            // Convert to desired format
            switch (format.toLowerCase()) {
                case 'webp':
                    imageProcessor = imageProcessor.webp({ quality });
                    break;
                case 'jpeg':
                case 'jpg':
                    imageProcessor = imageProcessor.jpeg({ quality });
                    break;
                case 'png':
                    imageProcessor = imageProcessor.png({ quality });
                    break;
                default:
                    imageProcessor = imageProcessor.webp({ quality });
            }

            return imageProcessor.toBuffer();
        } catch (error) {
            loggingService.logError(error, { operation: 'optimizeImage' });
            throw error;
        }
    }

    async getImage(publicId) {
        try {
            // Check cache first
            const cacheKey = `${this.cacheConfig.imageCache}${publicId}`;
            const cached = await redisService.get(cacheKey);
            
            if (cached) {
                return cached;
            }

            // If not in cache, fetch from Cloudinary
            const result = await cloudinary.api.resource(publicId);
            
            // Cache the result
            await redisService.set(cacheKey, result, this.cacheConfig.defaultTTL);

            return result;
        } catch (error) {
            loggingService.logError(error, { operation: 'getImage', publicId });
            throw error;
        }
    }

    async deleteImage(publicId) {
        try {
            // Delete from Cloudinary
            await cloudinary.uploader.destroy(publicId);
            
            // Remove from cache
            const cacheKey = `${this.cacheConfig.imageCache}${publicId}`;
            await redisService.del(cacheKey);

            return true;
        } catch (error) {
            loggingService.logError(error, { operation: 'deleteImage', publicId });
            throw error;
        }
    }

    generateImageUrl(publicId, transformations = {}) {
        try {
            const {
                width,
                height,
                crop = 'fill',
                quality = 'auto',
                format = 'auto'
            } = transformations;

            return cloudinary.url(publicId, {
                width,
                height,
                crop,
                quality,
                format,
                secure: true
            });
        } catch (error) {
            loggingService.logError(error, { operation: 'generateImageUrl', publicId });
            throw error;
        }
    }

    async optimizeAndCacheImages(images) {
        try {
            const optimizedImages = await Promise.all(
                images.map(async (image) => {
                    const optimized = await this.optimizeImage(image.buffer, {
                        maxWidth: 800,
                        maxHeight: 800,
                        quality: 75,
                        format: 'webp'
                    });

                    return {
                        ...image,
                        buffer: optimized,
                        optimized: true
                    };
                })
            );

            return optimizedImages;
        } catch (error) {
            loggingService.logError(error, { operation: 'optimizeAndCacheImages' });
            throw error;
        }
    }

    // Helper method to clear image cache
    async clearImageCache() {
        try {
            const pattern = `${this.cacheConfig.imageCache}*`;
            const keys = await redisService.client.keys(pattern);
            
            if (keys.length > 0) {
                await redisService.client.del(...keys);
            }

            return true;
        } catch (error) {
            loggingService.logError(error, { operation: 'clearImageCache' });
            throw error;
        }
    }
}

export default new CDNService();

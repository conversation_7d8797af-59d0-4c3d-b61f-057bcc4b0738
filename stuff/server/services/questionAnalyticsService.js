import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';
import questionMixingService from './questionMixingService.js';

class QuestionAnalyticsService {
    /**
     * Track question performance
     * @param {Object} data Performance data
     */
    async trackPerformance(data) {
        const {
            questionId,
            userId,
            testId,
            isCorrect,
            responseTime,
            source, // 'past_year' or 'generated'
            topics = []
        } = data;

        try {
            const db = await getDb();
            // Add performance record
            await db.collection('question_performance').add({
                question_id: questionId,
                user_id: userId,
                test_id: testId,
                is_correct: isCorrect,
                response_time: responseTime,
                source,
                topics,
                timestamp: new Date().toISOString()
            });

            // Update question stats
            await this.updateQuestionStats(questionId, isCorrect, responseTime);

            // Update topic stats
            for (const topic of topics) {
                await this.updateTopicStats(topic, isCorrect, source);
            }

            // Update user stats
            await this.updateUserStats(userId, topics, isCorrect, source);
        } catch (error) {
            loggingService.logError('Failed to track performance', { error });
        }
    }

    /**
     * Update question statistics
     * @param {string} questionId Question ID
     * @param {boolean} isCorrect Whether answer was correct
     * @param {number} responseTime Response time in seconds
     */
    async updateQuestionStats(questionId, isCorrect, responseTime) {
        try {
            const db = await getDb();
            const statsRef = db.collection('question_stats').doc(questionId);
            const statsDoc = await statsRef.get();

            if (!statsDoc.exists) {
                await statsRef.set({
                    total_attempts: 1,
                    correct_attempts: isCorrect ? 1 : 0,
                    total_time: responseTime,
                    average_time: responseTime,
                    difficulty_score: isCorrect ? 0 : 1,
                    last_updated: new Date().toISOString()
                });
            } else {
                const stats = statsDoc.data();
                const newTotalAttempts = stats.total_attempts + 1;
                const newTotalTime = stats.total_time + responseTime;

                await statsRef.update({
                    total_attempts: newTotalAttempts,
                    correct_attempts: stats.correct_attempts + (isCorrect ? 1 : 0),
                    total_time: newTotalTime,
                    average_time: newTotalTime / newTotalAttempts,
                    difficulty_score: (stats.difficulty_score * stats.total_attempts + (isCorrect ? 0 : 1)) / newTotalAttempts,
                    last_updated: new Date().toISOString()
                });
            }
        } catch (error) {
            loggingService.logError('Failed to update question stats', { error });
        }
    }

    /**
     * Update topic statistics
     * @param {string} topicId Topic ID
     * @param {boolean} isCorrect Whether answer was correct
     * @param {string} source Question source
     */
    async updateTopicStats(topicId, isCorrect, source) {
        try {
            const db = await getDb();
            const statsRef = db.collection('topic_stats').doc(topicId);
            const statsDoc = await statsRef.get();

            if (!statsDoc.exists) {
                await statsRef.set({
                    total_attempts: 1,
                    correct_attempts: isCorrect ? 1 : 0,
                    past_year_attempts: source === 'past_year' ? 1 : 0,
                    past_year_correct: source === 'past_year' && isCorrect ? 1 : 0,
                    generated_attempts: source === 'generated' ? 1 : 0,
                    generated_correct: source === 'generated' && isCorrect ? 1 : 0,
                    last_updated: new Date().toISOString()
                });
            } else {
                const stats = statsDoc.data();
                const update = {
                    total_attempts: stats.total_attempts + 1,
                    correct_attempts: stats.correct_attempts + (isCorrect ? 1 : 0),
                    last_updated: new Date().toISOString()
                };

                if (source === 'past_year') {
                    update.past_year_attempts = stats.past_year_attempts + 1;
                    if (isCorrect) update.past_year_correct = stats.past_year_correct + 1;
                } else {
                    update.generated_attempts = stats.generated_attempts + 1;
                    if (isCorrect) update.generated_correct = stats.generated_correct + 1;
                }

                await statsRef.update(update);
            }
        } catch (error) {
            loggingService.logError('Failed to update topic stats', { error });
        }
    }

    /**
     * Update user statistics
     * @param {string} userId User ID
     * @param {Array} topics Topics covered
     * @param {boolean} isCorrect Whether answer was correct
     * @param {string} source Question source
     */
    async updateUserStats(userId, topics, isCorrect, source) {
        try {
            const db = await getDb();
            const statsRef = db.collection('user_stats').doc(userId);
            const statsDoc = await statsRef.get();

            const now = new Date();
            const dateKey = now.toISOString().split('T')[0];

            if (!statsDoc.exists) {
                await statsRef.set({
                    total_attempts: 1,
                    correct_attempts: isCorrect ? 1 : 0,
                    topic_attempts: topics.reduce((acc, topic) => ({
                        ...acc,
                        [topic]: { attempts: 1, correct: isCorrect ? 1 : 0 }
                    }), {}),
                    daily_stats: {
                        [dateKey]: {
                            attempts: 1,
                            correct: isCorrect ? 1 : 0,
                            past_year_attempts: source === 'past_year' ? 1 : 0,
                            past_year_correct: source === 'past_year' && isCorrect ? 1 : 0,
                            generated_attempts: source === 'generated' ? 1 : 0,
                            generated_correct: source === 'generated' && isCorrect ? 1 : 0
                        }
                    },
                    last_updated: now.toISOString()
                });
            } else {
                const stats = statsDoc.data();
                const update = {
                    total_attempts: stats.total_attempts + 1,
                    correct_attempts: stats.correct_attempts + (isCorrect ? 1 : 0),
                    last_updated: now.toISOString()
                };

                // Update topic attempts
                const topicAttempts = stats.topic_attempts || {};
                topics.forEach(topic => {
                    if (!topicAttempts[topic]) {
                        topicAttempts[topic] = { attempts: 0, correct: 0 };
                    }
                    topicAttempts[topic].attempts++;
                    if (isCorrect) topicAttempts[topic].correct++;
                });
                update.topic_attempts = topicAttempts;

                // Update daily stats
                const dailyStats = stats.daily_stats || {};
                if (!dailyStats[dateKey]) {
                    dailyStats[dateKey] = {
                        attempts: 0,
                        correct: 0,
                        past_year_attempts: 0,
                        past_year_correct: 0,
                        generated_attempts: 0,
                        generated_correct: 0
                    };
                }
                dailyStats[dateKey].attempts++;
                if (isCorrect) dailyStats[dateKey].correct++;
                if (source === 'past_year') {
                    dailyStats[dateKey].past_year_attempts++;
                    if (isCorrect) dailyStats[dateKey].past_year_correct++;
                } else {
                    dailyStats[dateKey].generated_attempts++;
                    if (isCorrect) dailyStats[dateKey].generated_correct++;
                }
                update.daily_stats = dailyStats;

                await statsRef.update(update);
            }

            // Update mixing rules based on performance
            const dailyStats = statsDoc.exists ? 
                statsDoc.data().daily_stats?.[dateKey] : null;

            if (dailyStats && 
                (dailyStats.past_year_attempts + dailyStats.generated_attempts) >= 50) {
                await questionMixingService.updateMixingRules({
                    test_type: 'practice',
                    past_year_correct_rate: dailyStats.past_year_correct / dailyStats.past_year_attempts,
                    generated_correct_rate: dailyStats.generated_correct / dailyStats.generated_attempts,
                    total_questions: dailyStats.past_year_attempts + dailyStats.generated_attempts
                });
            }
        } catch (error) {
            loggingService.logError('Failed to update user stats', { error });
        }
    }

    /**
     * Get user performance analytics
     * @param {string} userId User ID
     * @returns {Promise<Object>} Performance analytics
     */
    async getUserAnalytics(userId) {
        try {
            const db = await getDb();
            const statsDoc = await db.collection('user_stats').doc(userId).get();
            if (!statsDoc.exists) {
                return null;
            }

            const stats = statsDoc.data();
            const now = new Date();
            const last30Days = Array.from({ length: 30 }, (_, i) => {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                return date.toISOString().split('T')[0];
            }).reverse();

            // Calculate daily performance
            const dailyPerformance = last30Days.map(date => {
                const dayStats = stats.daily_stats?.[date] || {
                    attempts: 0,
                    correct: 0,
                    past_year_attempts: 0,
                    past_year_correct: 0,
                    generated_attempts: 0,
                    generated_correct: 0
                };
                return {
                    date,
                    ...dayStats,
                    accuracy: dayStats.attempts > 0 ? 
                        (dayStats.correct / dayStats.attempts) * 100 : 0
                };
            });

            // Calculate topic performance
            const topicPerformance = Object.entries(stats.topic_attempts || {})
                .map(([topic, data]) => ({
                    topic,
                    attempts: data.attempts,
                    correct: data.correct,
                    accuracy: (data.correct / data.attempts) * 100
                }))
                .sort((a, b) => b.attempts - a.attempts);

            // Calculate overall stats
            const overallAccuracy = stats.total_attempts > 0 ?
                (stats.correct_attempts / stats.total_attempts) * 100 : 0;

            return {
                overall: {
                    total_attempts: stats.total_attempts,
                    correct_attempts: stats.correct_attempts,
                    accuracy: overallAccuracy
                },
                daily_performance: dailyPerformance,
                topic_performance: topicPerformance,
                last_updated: stats.last_updated
            };
        } catch (error) {
            loggingService.logError('Failed to get user analytics', { error });
            throw error;
        }
    }

    /**
     * Get topic performance analytics
     * @param {string} topicId Topic ID
     * @returns {Promise<Object>} Topic analytics
     */
    async getTopicAnalytics(topicId) {
        try {
            const db = await getDb();
            const statsDoc = await db.collection('topic_stats').doc(topicId).get();
            if (!statsDoc.exists) {
                return null;
            }

            const stats = statsDoc.data();
            return {
                total_attempts: stats.total_attempts,
                correct_attempts: stats.correct_attempts,
                accuracy: (stats.correct_attempts / stats.total_attempts) * 100,
                past_year_accuracy: stats.past_year_attempts > 0 ?
                    (stats.past_year_correct / stats.past_year_attempts) * 100 : 0,
                generated_accuracy: stats.generated_attempts > 0 ?
                    (stats.generated_correct / stats.generated_attempts) * 100 : 0,
                question_source_distribution: {
                    past_year: stats.past_year_attempts,
                    generated: stats.generated_attempts
                },
                last_updated: stats.last_updated
            };
        } catch (error) {
            loggingService.logError('Failed to get topic analytics', { error });
            throw error;
        }
    }
}

const questionAnalyticsService = new QuestionAnalyticsService();
export default questionAnalyticsService;

import loggingService from './loggingService.js';

class QuestionDistributionService {
    constructor() {
        this.loggingService = loggingService;
    }

    /**
     * Calculate question distribution based on syllabus weights and difficulty levels
     * @param {Object} syllabus - The syllabus with units, topics, and their weights
     * @param {number} totalQuestions - Total number of questions to distribute
     * @returns {Object} Question distribution by unit, topic, and difficulty
     */
    calculateQuestionDistribution(syllabus, totalQuestions) {
        if (!syllabus || !syllabus.units) {
            throw new Error('Invalid syllabus');
        }
        if (totalQuestions <= 0) {
            throw new Error('Total questions must be positive');
        }

        try {
            // First ensure minimum questions are met
            let remainingQuestions = totalQuestions;
            const distribution = {
                totalQuestions,
                units: syllabus.units.map(unit => {
                    const unitDist = {
                        id: unit.id,
                        name: unit.name,
                        weight: unit.weight,
                        min_questions: unit.min_questions || 0,
                        questionCount: 0,
                        topics: unit.topics.map(topic => ({
                            id: topic.id,
                            name: topic.name,
                            weight: topic.weight,
                            min_questions: topic.min_questions || 0,
                            questionCount: 0,
                            distribution: {
                                easy: 0,
                                medium: 0,
                                hard: 0,
                                expert: 0
                            }
                        }))
                    };

                    // Allocate minimum questions to unit
                    unitDist.questionCount = Math.max(unit.min_questions || 0, 0);
                    remainingQuestions -= unitDist.questionCount;

                    // Allocate minimum questions to topics
                    unit.topics.forEach((topic, i) => {
                        unitDist.topics[i].questionCount = Math.max(topic.min_questions || 0, 0);
                        remainingQuestions -= unitDist.topics[i].questionCount;
                    });

                    return unitDist;
                })
            };

            if (remainingQuestions < 0) {
                throw new Error('Total questions less than minimum required questions');
            }

            // Distribute remaining questions by weight
            this.distributeRemainingQuestions(distribution.units, remainingQuestions);

            // Calculate difficulty distribution for each topic
            distribution.units.forEach(unit => {
                unit.topics.forEach(topic => {
                    const originalTopic = syllabus.units
                        .find(u => u.id === unit.id)
                        ?.topics.find(t => t.id === topic.id);

                    if (originalTopic?.difficulty_distribution) {
                        topic.distribution = this.calculateDifficultyDistribution(
                            topic.questionCount,
                            originalTopic.difficulty_distribution
                        );
                    }
                });
            });

            return distribution;
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'calculateQuestionDistribution',
                syllabusId: syllabus.id,
                totalQuestions
            });
            throw error;
        }
    }

    /**
     * Distribute remaining questions based on weights
     * @param {Array} units - Array of unit distributions
     * @param {number} remainingQuestions - Number of questions to distribute
     */
    distributeRemainingQuestions(units, remainingQuestions) {
        if (remainingQuestions <= 0) return;

        // Calculate total weight
        const totalWeight = units.reduce((sum, unit) => sum + (unit.weight || 0), 0);

        // If total weight is 0, distribute evenly
        if (totalWeight === 0) {
            const perUnit = Math.floor(remainingQuestions / units.length);
            units.forEach(unit => {
                unit.questionCount += perUnit;
                this.distributeQuestionsToTopics(unit, perUnit);
            });
            return;
        }

        // Distribute by weight
        units.forEach(unit => {
            const unitQuestions = Math.round((unit.weight / totalWeight) * remainingQuestions);
            unit.questionCount += unitQuestions;
            this.distributeQuestionsToTopics(unit, unitQuestions);
        });
    }

    /**
     * Distribute questions to topics within a unit
     * @param {Object} unit - Unit distribution object
     * @param {number} questions - Number of questions to distribute
     */
    distributeQuestionsToTopics(unit, questions) {
        const totalWeight = unit.topics.reduce((sum, topic) => sum + (topic.weight || 0), 0);

        // If total weight is 0, distribute evenly
        if (totalWeight === 0) {
            const perTopic = Math.floor(questions / unit.topics.length);
            unit.topics.forEach(topic => {
                topic.questionCount += perTopic;
            });
            return;
        }

        // Distribute by weight
        unit.topics.forEach(topic => {
            const topicQuestions = Math.round((topic.weight / totalWeight) * questions);
            topic.questionCount += topicQuestions;
        });
    }

    /**
     * Calculate question distribution by difficulty level
     * @param {number} questionCount - Number of questions
     * @param {Object} distribution - Difficulty distribution percentages
     * @returns {Object} Number of questions per difficulty level
     */
    calculateDifficultyDistribution(questionCount, distribution) {
        const result = {
            easy: Math.round((distribution.easy / 100) * questionCount),
            medium: Math.round((distribution.medium / 100) * questionCount),
            hard: Math.round((distribution.hard / 100) * questionCount),
            expert: Math.round((distribution.expert / 100) * questionCount)
        };

        // Adjust for rounding errors
        const total = Object.values(result).reduce((sum, val) => sum + val, 0);
        const diff = questionCount - total;

        if (diff !== 0) {
            // Add/subtract from the largest category
            const largest = Object.entries(result)
                .reduce((max, [key, val]) => val > max[1] ? [key, val] : max, ['', 0]);
            result[largest[0]] += diff;
        }

        return result;
    }

    /**
     * Validate distribution settings in a syllabus
     * @param {Object} syllabus - The syllabus to validate
     * @returns {Object} Validation results
     */
    validateDistribution(syllabus) {
        const errors = [];

        try {
            // Validate unit weights
            const totalUnitWeight = syllabus.units.reduce((sum, unit) => sum + (unit.weight || 0), 0);
            if (Math.abs(totalUnitWeight - 100) > 0.1) {
                errors.push('Unit weights must sum to 100%');
            }

            // Validate each unit
            syllabus.units.forEach(unit => {
                // Validate topic weights
                const totalTopicWeight = unit.topics.reduce((sum, topic) => sum + (topic.weight || 0), 0);
                if (Math.abs(totalTopicWeight - 100) > 0.1) {
                    errors.push(`Topic weights in ${unit.name} must sum to 100%`);
                }

                // Validate unit difficulty distribution
                if (unit.difficulty_distribution) {
                    const totalDiff = Object.values(unit.difficulty_distribution)
                        .reduce((sum, val) => sum + (val || 0), 0);
                    if (Math.abs(totalDiff - 100) > 0.1) {
                        errors.push(`Difficulty distribution in ${unit.name} must sum to 100%`);
                    }
                }

                // Validate topic difficulty distributions
                unit.topics.forEach(topic => {
                    if (topic.difficulty_distribution) {
                        const totalDiff = Object.values(topic.difficulty_distribution)
                            .reduce((sum, val) => sum + (val || 0), 0);
                        if (Math.abs(totalDiff - 100) > 0.1) {
                            errors.push(`Difficulty distribution in ${unit.name} - ${topic.name} must sum to 100%`);
                        }
                    }
                });
            });

            return {
                valid: errors.length === 0,
                errors
            };
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'validateDistribution',
                syllabusId: syllabus.id
            });
            throw error;
        }
    }
}

const questionDistributionService = new QuestionDistributionService();
export default questionDistributionService;

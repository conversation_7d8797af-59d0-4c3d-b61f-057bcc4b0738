import NodeCache from 'node-cache';
import loggingService from './loggingService.js';

class CacheService {
  constructor() {
    this.loggingService = loggingService;
    // Cache with 1 hour TTL by default
    this.cache = new NodeCache({
      stdTTL: 3600,
      checkperiod: 120,
      useClones: false
    });

    // Cache metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      keys: 0
    };

    // Start metrics collection
    this.startMetricsCollection();
  }

  /**
   * Generate a cache key from parameters
   */
  generateKey(type, params) {
    return [
      type,
      params.subject,
      params.topic || '',
      params.level || '',
      params.syllabusId || '',
      params.unitId || '',
      params.topicId || ''
    ].join(':');
  }

  /**
   * Get item from cache
   */
  get(type, params) {
    const key = this.generateKey(type, params);
    const value = this.cache.get(key);
    
    if (value) {
      this.metrics.hits++;
      this.loggingService.logInfo('Cache hit', { key });
      return value;
    }

    this.metrics.misses++;
    this.loggingService.logInfo('Cache miss', { key });
    return null;
  }

  /**
   * Set item in cache
   */
  set(type, params, value, ttl = 3600) {
    const key = this.generateKey(type, params);
    this.cache.set(key, value, ttl);
    this.metrics.keys = this.cache.keys().length;
    this.loggingService.logInfo('Cache set', { key, ttl });
  }

  /**
   * Delete item from cache
   */
  delete(type, params) {
    const key = this.generateKey(type, params);
    this.cache.del(key);
    this.metrics.keys = this.cache.keys().length;
    this.loggingService.logInfo('Cache delete', { key });
  }

  /**
   * Clear entire cache
   */
  clear() {
    this.cache.flushAll();
    this.metrics.keys = 0;
    this.loggingService.logInfo('Cache cleared');
  }

  /**
   * Start collecting cache metrics
   */
  startMetricsCollection() {
    setInterval(() => {
      const metrics = {
        timestamp: new Date(),
        ...this.metrics,
        hitRate: this.getHitRate(),
        memoryUsage: process.memoryUsage().heapUsed
      };

      loggingService.logInfo('Cache metrics', { metrics });
    }, 300000); // Every 5 minutes
  }

  getHitRate() {
    const total = this.metrics.hits + this.metrics.misses;
    return total > 0 ? Math.round((this.metrics.hits / total) * 100) / 100 : 0;
  }

  /**
   * Get cache stats
   */
  getStats() {
    const total = this.metrics.hits + this.metrics.misses;
    return {
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      hitRate: total > 0 ? this.metrics.hits / total : 0,
      memoryUsage: process.memoryUsage().heapUsed,
      keys: this.cache.keys().length,
      stats: this.cache.getStats()
    };
  }
}

export default new CacheService();

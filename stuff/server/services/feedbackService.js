import { getDb } from '../firebase.js';
import { feedbackSchema } from '../models/feedbackSchema.js';
import Ajv from 'ajv';
import loggingService from './loggingService.js';
import questionGenerationService from './questionGenerationService.js';

class FeedbackService {
    constructor() {
        this.ajv = new Ajv();
        this.validateFeedback = this.ajv.compile(feedbackSchema);
    }

    /**
     * Submit feedback for a question
     * @param {Object} feedbackData The feedback data
     * @returns {Promise<string>} The feedback ID
     */
    async submitFeedback(feedbackData) {
        try {
            // Add timestamps
            feedbackData.created_at = new Date().toISOString();
            feedbackData.updated_at = feedbackData.created_at;
            feedbackData.status = 'pending';

            // Validate feedback data
            const valid = this.validateFeedback(feedbackData);
            if (!valid) {
                throw new Error(`Invalid feedback data: ${JSON.stringify(this.validateFeedback.errors)}`);
            }

            const db = await getDb();
            // Save to Firestore
            const docRef = await db.collection('question_feedback').add(feedbackData);

            // If this is a challenge or correction, trigger LLM review
            if (feedbackData.feedback_type === 'challenge_answer' || 
                feedbackData.feedback_type === 'content_correction') {
                this.reviewFeedback(docRef.id, feedbackData);
            }

            return docRef.id;
        } catch (error) {
            loggingService.logError('Failed to submit feedback', { error });
            throw error;
        }
    }

    /**
     * Review feedback using LLM
     * @param {string} feedbackId The feedback ID
     * @param {Object} feedbackData The feedback data
     */
    async reviewFeedback(feedbackId, feedbackData) {
        try {
            const db = await getDb();
            // Get the original question
            const questionDoc = await db.collection('questions')
                .doc(feedbackData.question_id)
                .get();

            if (!questionDoc.exists) {
                throw new Error('Question not found');
            }

            const question = questionDoc.data();

            // Prepare prompt for LLM review
            let prompt = '';
            if (feedbackData.feedback_type === 'challenge_answer') {
                prompt = `Review this challenge to a question's answer:
                Question: ${question.question_text}
                Options: ${JSON.stringify(question.options)}
                Original Answer: ${question.correct_answer}
                Original Explanation: ${question.explanation}
                User's Challenge: ${feedbackData.notes}
                
                Please evaluate if the challenge is valid and provide a detailed response.
                If the challenge is valid, explain why and suggest corrections.
                If the challenge is invalid, explain why the original answer is correct.
                Format your response as JSON:
                {
                    "is_valid": boolean,
                    "explanation": "detailed explanation",
                    "suggested_changes": {
                        "correct_answer": "only if different",
                        "explanation": "only if needs updating"
                    }
                }`;
            } else {
                prompt = `Review this suggested correction to a question:
                Original Question: ${question.question_text}
                Original Options: ${JSON.stringify(question.options)}
                Original Answer: ${question.correct_answer}
                Original Explanation: ${question.explanation}
                
                Suggested Changes: ${JSON.stringify(feedbackData.suggested_correction)}
                
                Please evaluate if the suggested changes improve the question.
                Consider clarity, accuracy, and educational value.
                Format your response as JSON:
                {
                    "should_update": boolean,
                    "explanation": "detailed explanation",
                    "final_version": {
                        "question_text": "...",
                        "options": [...],
                        "correct_answer": "...",
                        "explanation": "..."
                    }
                }`;
            }

            // Get LLM review
            const review = await questionGenerationService.getLLMReview(prompt);

            // Update feedback with review
            await db.collection('question_feedback').doc(feedbackId).update({
                llm_review: review,
                status: 'reviewed',
                updated_at: new Date().toISOString()
            });

            // If the review suggests changes are valid, mark for human review
            if ((feedbackData.feedback_type === 'challenge_answer' && review.is_valid) ||
                (feedbackData.feedback_type === 'content_correction' && review.should_update)) {
                await this.notifyReviewers(feedbackId, feedbackData, review);
            }
        } catch (error) {
            loggingService.logError('Failed to review feedback', { error });
            // Don't throw - we want to keep the feedback even if review fails
        }
    }

    /**
     * Notify reviewers about feedback that needs attention
     * @param {string} feedbackId The feedback ID
     * @param {Object} feedbackData The feedback data
     * @param {Object} review The LLM review
     */
    async notifyReviewers(feedbackId, feedbackData, review) {
        try {
            const db = await getDb();
            // Add to review queue
            await db.collection('review_queue').add({
                feedback_id: feedbackId,
                question_id: feedbackData.question_id,
                feedback_type: feedbackData.feedback_type,
                created_at: new Date().toISOString(),
                priority: feedbackData.feedback_type === 'challenge_answer' ? 'high' : 'normal'
            });

            // TODO: Implement actual notification system (email, dashboard, etc.)
        } catch (error) {
            loggingService.logError('Failed to notify reviewers', { error });
        }
    }

    /**
     * Get feedback for a question
     * @param {string} questionId The question ID
     * @returns {Promise<Array>} List of feedback items
     */
    async getQuestionFeedback(questionId) {
        try {
            const db = await getDb();
            const snapshot = await db.collection('question_feedback')
                .where('question_id', '==', questionId)
                .orderBy('created_at', 'desc')
                .get();

            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            loggingService.logError('Failed to get question feedback', { error });
            throw error;
        }
    }

    /**
     * Get feedback queue for reviewers
     * @param {Object} filters Optional filters (status, type, etc.)
     * @returns {Promise<Array>} List of feedback items needing review
     */
    async getReviewQueue(filters = {}) {
        try {
            const db = await getDb();
            let query = db.collection('review_queue')
                .orderBy('created_at', 'desc');

            if (filters.priority) {
                query = query.where('priority', '==', filters.priority);
            }

            const snapshot = await query.get();
            const queue = [];

            for (const doc of snapshot.docs) {
                const queueItem = doc.data();
                const feedbackDoc = await db.collection('question_feedback')
                    .doc(queueItem.feedback_id)
                    .get();

                if (feedbackDoc.exists) {
                    queue.push({
                        ...queueItem,
                        feedback: feedbackDoc.data()
                    });
                }
            }

            return queue;
        } catch (error) {
            loggingService.logError('Failed to get review queue', { error });
            throw error;
        }
    }

    /**
     * Process reviewer decision
     * @param {string} feedbackId The feedback ID
     * @param {Object} decision The reviewer's decision
     */
    async processReviewerDecision(feedbackId, decision) {
        try {
            const db = await getDb();
            const feedbackDoc = await db.collection('question_feedback')
                .doc(feedbackId)
                .get();

            if (!feedbackDoc.exists) {
                throw new Error('Feedback not found');
            }

            const feedback = feedbackDoc.data();

            // Update feedback status
            await feedbackDoc.ref.update({
                status: decision.approved ? 'approved' : 'rejected',
                reviewer_id: decision.reviewer_id,
                reviewer_notes: decision.notes,
                updated_at: new Date().toISOString()
            });

            // If approved, update the question
            if (decision.approved) {
                const questionDoc = await db.collection('questions')
                    .doc(feedback.question_id)
                    .get();

                if (questionDoc.exists) {
                    const updates = feedback.feedback_type === 'content_correction' ?
                        feedback.suggested_correction :
                        feedback.llm_review.suggested_changes;

                    await questionDoc.ref.update({
                        ...updates,
                        updated_at: new Date().toISOString(),
                        last_feedback_id: feedbackId
                    });
                }
            }

            // Remove from review queue
            const queueSnapshot = await db.collection('review_queue')
                .where('feedback_id', '==', feedbackId)
                .get();

            for (const doc of queueSnapshot.docs) {
                await doc.ref.delete();
            }
        } catch (error) {
            loggingService.logError('Failed to process reviewer decision', { error });
            throw error;
        }
    }
}

const feedbackService = new FeedbackService();
export default feedbackService;

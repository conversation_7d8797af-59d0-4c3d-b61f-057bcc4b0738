import Redis from 'ioredis';
import loggingService from './loggingService.js';

class RedisService {
    constructor() {
        this.isConnected = false;
        this.client = null;
        this.subscriber = null;
        this.publisher = null;

        // Only try to connect to Redis in production or if explicitly enabled
        if (process.env.NODE_ENV === 'production' || process.env.ENABLE_REDIS === 'true') {
            this.initializeRedis();
        } else {
            console.log('Redis disabled for development. Set ENABLE_REDIS=true to enable.');
        }
    }

    initializeRedis() {
        try {
            this.client = new Redis({
                host: process.env.REDIS_HOST || 'localhost',
                port: process.env.REDIS_PORT || 6379,
                password: process.env.REDIS_PASSWORD,
                retryStrategy: (times) => {
                    // Limit retries to prevent infinite retry loops
                    if (times > 3) {
                        console.log('Redis connection failed after 3 retries. Disabling Redis.');
                        return null; // Stop retrying
                    }
                    const delay = Math.min(times * 50, 2000);
                    return delay;
                },
                maxRetriesPerRequest: 3,
                lazyConnect: true
            });

            this.subscriber = this.client.duplicate();
            this.publisher = this.client.duplicate();

            this.client.on('connect', () => {
                this.isConnected = true;
                console.log('Redis connected successfully');
            });

            this.client.on('error', (error) => {
                this.isConnected = false;
                if (process.env.NODE_ENV === 'development') {
                    console.log('Redis connection error (development mode):', error.message);
                } else {
                    loggingService.logError(error, { service: 'redis' });
                }
            });

            this.client.on('close', () => {
                this.isConnected = false;
                console.log('Redis connection closed');
            });

            // Try to connect
            this.client.connect().catch((error) => {
                console.log('Failed to connect to Redis:', error.message);
                this.isConnected = false;
            });

            // Initialize message queue handlers only if connected
            if (this.isConnected) {
                this.initializeMessageQueues();
            }
        } catch (error) {
            console.log('Redis initialization failed:', error.message);
            this.isConnected = false;
        }
    }

    // Caching methods
    async get(key) {
        if (!this.isConnected || !this.client) {
            return null;
        }
        try {
            const value = await this.client.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'redis.get', key });
            }
            return null;
        }
    }

    async set(key, value, expirySeconds = 3600) {
        if (!this.isConnected || !this.client) {
            return false;
        }
        try {
            await this.client.setex(key, expirySeconds, JSON.stringify(value));
            return true;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'redis.set', key });
            }
            return false;
        }
    }

    async del(key) {
        if (!this.isConnected || !this.client) {
            return false;
        }
        try {
            await this.client.del(key);
            return true;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'redis.del', key });
            }
            return false;
        }
    }

    // Message queue methods
    initializeMessageQueues() {
        if (!this.isConnected || !this.subscriber) {
            return;
        }

        try {
            // Define message queue channels
            this.queues = {
                questionGeneration: 'queue:question-generation',
                grading: 'queue:grading',
                analytics: 'queue:analytics',
                notifications: 'queue:notifications'
            };

            // Subscribe to channels
            this.subscriber.subscribe(...Object.values(this.queues));

            // Set up message handlers
            this.subscriber.on('message', async (channel, message) => {
                try {
                    const data = JSON.parse(message);
                    await this.processMessage(channel, data);
                } catch (error) {
                    if (process.env.NODE_ENV !== 'development') {
                        loggingService.logError(error, { operation: 'processMessage', channel });
                    }
                }
            });
        } catch (error) {
            console.log('Failed to initialize message queues:', error.message);
        }
    }

    async enqueue(queueName, data) {
        if (!this.isConnected || !this.publisher) {
            console.log(`Queue operation skipped (Redis not connected): ${queueName}`);
            return null;
        }

        try {
            const queue = this.queues[queueName];
            if (!queue) {
                throw new Error(`Invalid queue name: ${queueName}`);
            }

            const message = {
                id: `${queueName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                timestamp: Date.now(),
                data
            };

            await this.publisher.publish(queue, JSON.stringify(message));
            return message.id;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'enqueue', queue: queueName });
            }
            return null;
        }
    }

    async processMessage(channel, message) {
        switch (channel) {
            case this.queues.questionGeneration:
                await this.processQuestionGeneration(message);
                break;
            case this.queues.grading:
                await this.processGrading(message);
                break;
            case this.queues.analytics:
                await this.processAnalytics(message);
                break;
            case this.queues.notifications:
                await this.processNotification(message);
                break;
            default:
                loggingService.logError(new Error(`Unknown channel: ${channel}`));
        }
    }

    // Message processors
    async processQuestionGeneration(message) {
        try {
            const { data } = message;
            // Handle question generation request
            // This would integrate with the LLM service
        } catch (error) {
            loggingService.logError(error, { operation: 'processQuestionGeneration' });
        }
    }

    async processGrading(message) {
        try {
            const { data } = message;
            // Handle grading request
            // This would integrate with the grading service
        } catch (error) {
            loggingService.logError(error, { operation: 'processGrading' });
        }
    }

    async processAnalytics(message) {
        try {
            const { data } = message;
            // Handle analytics processing
            // This would integrate with the analytics service
        } catch (error) {
            loggingService.logError(error, { operation: 'processAnalytics' });
        }
    }

    async processNotification(message) {
        try {
            const { data } = message;
            // Handle notification sending
            // This would integrate with the notification service
        } catch (error) {
            loggingService.logError(error, { operation: 'processNotification' });
        }
    }

    // Rate limiting
    async checkRateLimit(key, limit, windowSeconds) {
        if (!this.isConnected || !this.client) {
            // Allow request if Redis is not available (fail open)
            return true;
        }

        const now = Date.now();
        const windowMs = windowSeconds * 1000;

        try {
            // Add the current timestamp to the sorted set
            await this.client.zadd(key, now, `${now}-${Math.random()}`);

            // Remove old entries outside the window
            await this.client.zremrangebyscore(key, '-inf', now - windowMs);

            // Count remaining entries in the window
            const count = await this.client.zcard(key);

            // Set expiry on the key
            await this.client.expire(key, windowSeconds);

            return count <= limit;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'checkRateLimit', key });
            }
            // Allow request if Redis operation fails (fail open)
            return true;
        }
    }

    // Load balancing helper
    async getServerLoad() {
        if (!this.isConnected || !this.client) {
            return 0;
        }

        try {
            const info = await this.client.info('cpu');
            const cpuUsage = parseFloat(info.match(/used_cpu_sys=(\d+\.\d+)/)[1]);
            return cpuUsage;
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'getServerLoad' });
            }
            return 0;
        }
    }

    // Cleanup
    async cleanup() {
        if (!this.client) {
            return;
        }

        try {
            if (this.subscriber) {
                await this.subscriber.quit();
            }
            if (this.publisher) {
                await this.publisher.quit();
            }
            if (this.client) {
                await this.client.quit();
            }
        } catch (error) {
            if (process.env.NODE_ENV !== 'development') {
                loggingService.logError(error, { operation: 'cleanup' });
            }
        }
    }
}

const redisService = new RedisService();
export default redisService;

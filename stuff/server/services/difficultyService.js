import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';

/**
 * Service to handle adaptive difficulty adjustments and user performance tracking
 * Uses a modified ELO rating system to track user skill level and question difficulty
 */
class DifficultyService {
    constructor() {
        this.db = null;
        this.K_FACTOR = 32; // Standard K-factor for ELO calculations
        this.DIFFICULTY_LEVELS = {
            EASY: { min: 0, max: 1200, label: 'easy' },
            MEDIUM: { min: 1201, max: 1800, label: 'medium' },
            HARD: { min: 1801, max: 3000, label: 'hard' }
        };
        this.DEFAULT_USER_RATING = 1200; // Starting at medium difficulty
        this.DEFAULT_QUESTION_RATING = 1500; // Default question difficulty
    }

    async getDbInstance() {
        if (!this.db) {
            this.db = await getDb();
        }
        return this.db;
    }

    /**
     * Calculate new ELO ratings after a question attempt
     * @param {number} userRating - Current user rating
     * @param {number} questionRating - Question difficulty rating
     * @param {boolean} isCorrect - Whether the user answered correctly
     * @returns {Object} New ratings for user and question
     */
    calculateNewRatings(userRating, questionRating, isCorrect) {
        const expectedScore = this.calculateExpectedScore(userRating, questionRating);
        const actualScore = isCorrect ? 1 : 0;
        
        const userRatingChange = Math.round(this.K_FACTOR * (actualScore - expectedScore));
        const questionRatingChange = Math.round(this.K_FACTOR * (expectedScore - actualScore) * 0.5); // Questions adjust slower
        
        return {
            newUserRating: userRating + userRatingChange,
            newQuestionRating: questionRating + questionRatingChange
        };
    }

    /**
     * Calculate expected score based on ELO formula
     */
    calculateExpectedScore(userRating, questionRating) {
        return 1 / (1 + Math.pow(10, (questionRating - userRating) / 400));
    }

    /**
     * Get difficulty label based on rating
     */
    getDifficultyLabel(rating) {
        if (rating <= this.DIFFICULTY_LEVELS.EASY.max) return 'easy';
        if (rating <= this.DIFFICULTY_LEVELS.MEDIUM.max) return 'medium';
        return 'hard';
    }

    /**
     * Update user's topic-specific ratings after a test session
     */
    async updateUserRatings(userId, testResults) {
        const db = await this.getDbInstance();
        const batch = db.batch();
        const userSkillsRef = db.collection('user_skills').doc(userId);
        const userSkillsDoc = await userSkillsRef.get();
        
        let skills = userSkillsDoc.exists ? userSkillsDoc.data() : {};
        
        for (const result of testResults) {
            const { topic, subtopic, isCorrect, questionId } = result;
            const topicKey = `${topic}${subtopic ? '_' + subtopic : ''}`;
            
            // Get or initialize user's rating for this topic
            const currentRating = skills[topicKey]?.rating || this.DEFAULT_USER_RATING;
            
            // Get question's current difficulty rating
            const questionDoc = await db.collection('questions').doc(questionId).get();
            const questionRating = questionDoc.data()?.difficulty_rating || this.DEFAULT_QUESTION_RATING;

            // Calculate new ratings
            const { newUserRating, newQuestionRating } = this.calculateNewRatings(
                currentRating,
                questionRating,
                isCorrect
            );

            // Update user skills
            skills[topicKey] = {
                rating: newUserRating,
                difficulty_level: this.getDifficultyLabel(newUserRating),
                last_updated: new Date().toISOString(),
                total_questions: (skills[topicKey]?.total_questions || 0) + 1,
                correct_answers: (skills[topicKey]?.correct_answers || 0) + (isCorrect ? 1 : 0)
            };

            // Update question difficulty
            batch.update(db.collection('questions').doc(questionId), {
                difficulty_rating: newQuestionRating,
                difficulty: this.getDifficultyLabel(newQuestionRating),
                total_attempts: questionDoc.data().total_attempts + 1 || 1,
                correct_attempts: questionDoc.data().correct_attempts + (isCorrect ? 1 : 0) || (isCorrect ? 1 : 0)
            });
        }
        
        // Update user skills document
        batch.set(userSkillsRef, skills);
        
        try {
            await batch.commit();
            return skills;
        } catch (error) {
            loggingService.logError('Failed to update user ratings', { error });
            throw error;
        }
    }

    /**
     * Get appropriate questions for user's skill level
     */
    async getAdaptiveQuestions(params) {
        const { userId, topic, subtopic, count = 10 } = params;
        
        try {
            const db = await this.getDbInstance();
            // Get user's current skill level for the topic
            const userSkillsDoc = await db.collection('user_skills').doc(userId).get();
            const skills = userSkillsDoc.exists ? userSkillsDoc.data() : {};
            const topicKey = `${topic}${subtopic ? '_' + subtopic : ''}`;
            const userRating = skills[topicKey]?.rating || this.DEFAULT_USER_RATING;

            // Calculate rating range for question selection
            const ratingRange = 300; // Questions within ±300 of user's rating
            const minRating = userRating - ratingRange;
            const maxRating = userRating + ratingRange;

            // Query questions within the rating range
            const questionsSnapshot = await db.collection('questions')
                .where('topic', '==', topic)
                .where('subtopic', '==', subtopic || null)
                .where('difficulty_rating', '>=', minRating)
                .where('difficulty_rating', '<=', maxRating)
                .orderBy('difficulty_rating')
                .limit(count * 2) // Get more than needed for randomization
                .get();
            
            let questions = questionsSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
            
            // Randomly select required number of questions
            questions = this.shuffleArray(questions).slice(0, count);
            
            return questions;
        } catch (error) {
            loggingService.logError('Failed to get adaptive questions', { error });
            throw error;
        }
    }

    /**
     * Get user's current skill levels across topics
     */
    async getUserSkillLevels(userId) {
        try {
            const db = await this.getDbInstance();
            const userSkillsDoc = await db.collection('user_skills').doc(userId).get();
            return userSkillsDoc.exists ? userSkillsDoc.data() : {};
        } catch (error) {
            loggingService.logError('Failed to get user skill levels', { error });
            throw error;
        }
    }

    /**
     * Shuffle array using Fisher-Yates algorithm
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }
}

const difficultyService = new DifficultyService();
export default difficultyService;

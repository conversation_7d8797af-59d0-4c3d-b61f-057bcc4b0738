import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';

class QuestionAnalysisService {
  constructor() {
    this.db = getDb();
    this.difficultyLevels = ['easy', 'medium', 'hard', 'expert'];
    this.userPerformanceCache = new Map();
  }

  // Calculate adaptive difficulty based on user performance
  async calculateAdaptiveDifficulty(userId, subject, topic) {
    if (process.env.NODE_ENV === 'development') {
      // In development, return fixed difficulty for testing
      return 'medium';
    }

    try {
      const userStats = await this.getUserPerformanceStats(userId, subject, topic);
      const { correctRate, avgResponseTime, questionsAttempted } = userStats;

      // Weight factors for difficulty calculation
      const correctRateWeight = 0.5;
      const responseTimeWeight = 0.3;
      const attemptsWeight = 0.2;

      // Calculate difficulty score (0-1)
      let difficultyScore = 
        (correctRate * correctRateWeight) +
        (Math.min(avgResponseTime / 120, 1) * responseTimeWeight) +
        (Math.min(questionsAttempted / 50, 1) * attemptsWeight);

      // Map score to difficulty level
      if (difficultyScore < 0.3) return 'easy';
      if (difficultyScore < 0.6) return 'medium';
      if (difficultyScore < 0.85) return 'hard';
      return 'expert';
    } catch (error) {
      console.error('Error calculating adaptive difficulty:', error);
      return 'medium'; // Default fallback
    }
  }

  // Track question diversity to avoid repetition
  async trackQuestionDiversity(userId, questionId, metadata) {
    if (process.env.NODE_ENV === 'development') {
      // In development, just log the tracking
      console.log('Development: Tracking question diversity', { userId, questionId, metadata });
      return;
    }

    try {
      const ref = this.db.collection('questionDiversity')
        .doc(`${userId}_${questionId}`);
      
      await ref.set({
        userId,
        questionId,
        lastSeen: new Date(),
        timesShown: 1,
        metadata
      }, { merge: true });
    } catch (error) {
      console.error('Error tracking question diversity:', error);
    }
  }

  // Check if a question should be filtered based on diversity rules
  async shouldFilterQuestion(userId, questionId, metadata) {
    if (process.env.NODE_ENV === 'development') {
      // In development, never filter questions
      return false;
    }

    try {
      const ref = this.db.collection('questionDiversity')
        .doc(`${userId}_${questionId}`);
      const doc = await ref.get();

      if (!doc.exists) return false;

      const data = doc.data();
      const hoursSinceLastSeen = (new Date() - data.lastSeen.toDate()) / (1000 * 60 * 60);
      
      // Filter if shown in last 24 hours or shown more than 3 times
      return hoursSinceLastSeen < 24 || data.timesShown >= 3;
    } catch (error) {
      console.error('Error checking question filter:', error);
      return false;
    }
  }

  // Validate domain-specific knowledge in questions
  async validateDomainKnowledge(question, subject) {
    if (process.env.NODE_ENV === 'development') {
      // In development, always validate
      return { isValid: true };
    }

    try {
      // Simple validation in production
      const minLength = 20; // Minimum question length
      const maxLength = 500; // Maximum question length
      const questionLength = question.length;

      return {
        isValid: questionLength >= minLength && questionLength <= maxLength,
        feedback: {
          tooShort: questionLength < minLength,
          tooLong: questionLength > maxLength
        }
      };
    } catch (error) {
      console.error('Error validating domain knowledge:', error);
      return { isValid: true }; // Fail open to not block question generation
    }
  }

  // Process multimedia content for questions
  async processMultimediaContent(content) {
    if (!content) return null;

    try {
      const supportedTypes = ['image', 'diagram', 'code'];
      const processed = {};

      for (const [type, data] of Object.entries(content)) {
        if (!supportedTypes.includes(type)) continue;

        // In development, just pass through the content
        processed[type] = data;
      }

      return processed;
    } catch (error) {
      console.error('Error processing multimedia content:', error);
      return null;
    }
  }

  // Calculate question quality score based on user feedback
  async updateQuestionQualityScore(questionId, feedback) {
    if (process.env.NODE_ENV === 'development') {
      // In development, return mock score
      return 0.85;
    }

    try {
      const ref = this.db.collection('questionQuality').doc(questionId);
      const doc = await ref.get();

      const currentStats = doc.exists ? doc.data() : {
        totalFeedback: 0,
        helpfulCount: 0,
        clarityScore: 0,
        difficultyAccuracy: 0
      };

      // Update stats with new feedback
      const newStats = {
        totalFeedback: currentStats.totalFeedback + 1,
        helpfulCount: currentStats.helpfulCount + (feedback.helpful ? 1 : 0),
        clarityScore: (currentStats.clarityScore * currentStats.totalFeedback + feedback.clarity) / (currentStats.totalFeedback + 1),
        difficultyAccuracy: (currentStats.difficultyAccuracy * currentStats.totalFeedback + feedback.difficultyMatch) / (currentStats.totalFeedback + 1)
      };

      // Calculate overall quality score (0-1)
      const qualityScore = 
        (newStats.helpfulCount / newStats.totalFeedback * 0.4) +
        (newStats.clarityScore * 0.3) +
        (newStats.difficultyAccuracy * 0.3);

      await ref.set({
        ...newStats,
        qualityScore,
        lastUpdated: new Date()
      });

      return qualityScore;
    } catch (error) {
      console.error('Error updating question quality score:', error);
      return null;
    }
  }

  // Validate question alignment with syllabus
  validateSyllabusAlignment(question, syllabusContext) {
    if (process.env.NODE_ENV === 'development') {
      // In development, return mock validation results
      return {
        isValid: true,
        scores: {
          keywordMatch: 0.8,
          objectiveAlignment: 0.7,
          distributionFit: 0.9
        },
        overallScore: 0.8,
        feedback: []
      };
    }

    try {
      const {
        keywords: syllabusKeywords,
        learningObjectives,
        questionDistribution
      } = syllabusContext;

      // Validation scores (0-1)
      const scores = {
        // Check if question keywords match syllabus keywords
        keywordMatch: this.calculateKeywordMatchScore(
          question.keywords,
          syllabusKeywords
        ),

        // Check if question aligns with learning objectives
        objectiveAlignment: this.calculateObjectiveAlignmentScore(
          question.learning_objective,
          learningObjectives
        ),

        // Check if question type matches distribution
        distributionFit: this.calculateDistributionFitScore(
          question.type,
          question.cognitive_level,
          questionDistribution
        )
      };

      // Weight factors for overall validation
      const weights = {
        keywordMatch: 0.4,
        objectiveAlignment: 0.4,
        distributionFit: 0.2
      };

      // Calculate weighted average score
      const overallScore =
        (scores.keywordMatch * weights.keywordMatch) +
        (scores.objectiveAlignment * weights.objectiveAlignment) +
        (scores.distributionFit * weights.distributionFit);

      // Question is valid if score is above threshold
      const validationThreshold = 0.7;
      const isValid = overallScore >= validationThreshold;

      return {
        isValid,
        scores,
        overallScore,
        feedback: this.generateValidationFeedback(scores)
      };
    } catch (error) {
      console.error('Error validating syllabus alignment:', error);
      return { isValid: true }; // Fail open to not block question generation
    }
  }

  // Calculate keyword match score
  calculateKeywordMatchScore(questionKeywords, syllabusKeywords) {
    if (!questionKeywords || !syllabusKeywords) return 0;

    const normalizedQuestionKeywords = questionKeywords.map(k =>
      k.toLowerCase().trim()
    );
    const normalizedSyllabusKeywords = syllabusKeywords.map(k =>
      k.toLowerCase().trim()
    );

    const matchingKeywords = normalizedQuestionKeywords.filter(k =>
      normalizedSyllabusKeywords.includes(k)
    );

    return matchingKeywords.length / Math.max(questionKeywords.length, 1);
  }

  // Calculate learning objective alignment score
  calculateObjectiveAlignmentScore(questionObjective, learningObjectives) {
    if (!questionObjective || !learningObjectives) return 0;

    // Convert to lowercase for comparison
    const normalizedObjective = questionObjective.toLowerCase();
    const normalizedLearningObjectives = Object.values(learningObjectives)
      .map(obj => obj.toLowerCase());

    // Check if question objective matches or is similar to any learning objective
    const similarityScores = normalizedLearningObjectives.map(obj =>
      this.calculateStringSimilarity(normalizedObjective, obj)
    );

    return Math.max(...similarityScores, 0);
  }

  // Calculate distribution fit score
  calculateDistributionFitScore(questionType, cognitiveLevel, distribution) {
    if (!distribution || !questionType || !cognitiveLevel) return 0;

    const typeDistribution = distribution[questionType] || {};
    const levelDistribution = typeDistribution[cognitiveLevel] || 0;

    // If this type and level combination is expected in distribution
    if (levelDistribution > 0) return 1;

    // If at least the question type is expected
    if (Object.keys(typeDistribution).length > 0) return 0.5;

    return 0;
  }

  // Calculate string similarity (simple implementation)
  calculateStringSimilarity(str1, str2) {
    const words1 = str1.split(/\W+/);
    const words2 = str2.split(/\W+/);

    const commonWords = words1.filter(word =>
      words2.includes(word)
    );

    return commonWords.length / Math.max(words1.length, words2.length);
  }

  // Generate validation feedback
  generateValidationFeedback(scores) {
    const feedback = [];

    if (scores.keywordMatch < 0.5) {
      feedback.push('Question keywords do not align well with syllabus keywords');
    }

    if (scores.objectiveAlignment < 0.5) {
      feedback.push('Question does not clearly address learning objectives');
    }

    if (scores.distributionFit < 0.5) {
      feedback.push('Question type/level does not match expected distribution');
    }

    return feedback;
  }

  // Helper methods
  async getUserPerformanceStats(userId, subject, topic) {
    if (process.env.NODE_ENV === 'development') {
      // In development, return mock stats
      return {
        correctRate: 0.7,
        avgResponseTime: 45,
        questionsAttempted: 25
      };
    }

    const cacheKey = `${userId}_${subject}_${topic}`;
    
    if (this.userPerformanceCache.has(cacheKey)) {
      return this.userPerformanceCache.get(cacheKey);
    }

    try {
      const snapshot = await this.db.collection('userPerformance')
        .where('userId', '==', userId)
        .where('subject', '==', subject)
        .where('topic', '==', topic)
        .orderBy('timestamp', 'desc')
        .limit(50)
        .get();

      const attempts = snapshot.docs.map(doc => doc.data());
      
      const stats = {
        correctRate: attempts.filter(a => a.isCorrect).length / attempts.length || 0,
        avgResponseTime: attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length || 0,
        questionsAttempted: attempts.length
      };

      this.userPerformanceCache.set(cacheKey, stats);
      setTimeout(() => this.userPerformanceCache.delete(cacheKey), 5 * 60 * 1000); // Cache for 5 minutes

      return stats;
    } catch (error) {
      console.error('Error getting user performance stats:', error);
      return { correctRate: 0.5, avgResponseTime: 60, questionsAttempted: 0 };
    }
  }
}

export default new QuestionAnalysisService();

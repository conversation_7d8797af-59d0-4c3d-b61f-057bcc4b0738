import request from 'supertest';
import { app } from '../../../server/index.js';
import { getDb } from '../../../server/firebase.js';
import jwt from 'jsonwebtoken';

describe('Analytics Routes', () => {
    let authToken;
    let testUserId;

    beforeAll(() => {
        testUserId = 'test-user-123';
        authToken = jwt.sign({ userId: testUserId }, process.env.JWT_SECRET);
    });

    beforeEach(async () => {
        // Clear test data
        const db = getDb();
        const testSessions = await db.collection('test_sessions')
            .where('user_id', '==', testUserId)
            .get();
        
        for (const doc of testSessions.docs) {
            await doc.ref.delete();
        }

        // Add test session data
        await db.collection('test_sessions').add({
            user_id: testUserId,
            start_time: new Date(Date.now() - 86400000), // 1 day ago
            end_time: new Date(Date.now() - 85800000), // 10 minutes after start
            score: 85,
            total_questions: 20,
            correct_answers: 17,
            topics: {
                'algebra': { score: 90, questions: 10 },
                'geometry': { score: 80, questions: 10 }
            }
        });
    });

    describe('GET /api/analytics/performance', () => {
        it('should return 401 without auth token', async () => {
            const response = await request(app)
                .get('/api/analytics/performance');
            
            expect(response.status).toBe(401);
        });

        it('should return performance data with auth token', async () => {
            const response = await request(app)
                .get('/api/analytics/performance')
                .set('Authorization', `Bearer ${authToken}`);
            
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('averageScore');
            expect(response.body).toHaveProperty('totalTests');
            expect(response.body).toHaveProperty('topicPerformance');
        });

        it('should filter by date range', async () => {
            const response = await request(app)
                .get('/api/analytics/performance')
                .query({ timeRange: '7d' })
                .set('Authorization', `Bearer ${authToken}`);
            
            expect(response.status).toBe(200);
            expect(response.body.totalTests).toBe(1);
        });
    });

    describe('GET /api/analytics/weak-areas', () => {
        it('should identify weak topics', async () => {
            const response = await request(app)
                .get('/api/analytics/weak-areas')
                .set('Authorization', `Bearer ${authToken}`);
            
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('weakTopics');
            expect(Array.isArray(response.body.weakTopics)).toBe(true);
            expect(response.body.weakTopics[0]).toHaveProperty('topic');
            expect(response.body.weakTopics[0]).toHaveProperty('score');
        });
    });

    describe('GET /api/analytics/study-plan', () => {
        it('should generate personalized study plan', async () => {
            const response = await request(app)
                .get('/api/analytics/study-plan')
                .set('Authorization', `Bearer ${authToken}`);
            
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('recommendations');
            expect(response.body).toHaveProperty('dailyGoals');
            expect(Array.isArray(response.body.recommendations)).toBe(true);
            expect(Array.isArray(response.body.dailyGoals)).toBe(true);
        });
    });
});

import dotenv from 'dotenv';
import { initializeFirebase } from '../server/firebase.js';
import redisService from '../server/services/redisService.js';

// Load environment variables
dotenv.config({ path: '.env.test' });

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
    credential: {
        cert: jest.fn()
    },
    initializeApp: jest.fn(),
    firestore: jest.fn(() => ({
        collection: jest.fn(),
        doc: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn()
    }))
}));

// Mock Redis
jest.mock('ioredis', () => {
    return jest.fn().mockImplementation(() => ({
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        expire: jest.fn()
    }));
});

// Global test setup
beforeAll(async () => {
    // Initialize services with test configuration
    initializeFirebase();
    await redisService.connect();
});

// Global test teardown
afterAll(async () => {
    await redisService.disconnect();
});

// Reset mocks between tests
beforeEach(() => {
    jest.clearAllMocks();
});

import QuestionAnalysisService from '../../../server/services/questionAnalysisService.js';
import { getDb } from '../../../server/firebase.js';
import loggingService from '../../../server/services/loggingService.js';

jest.mock('../../../server/firebase.js');
jest.mock('../../../server/services/loggingService.js');

describe('QuestionAnalysisService', () => {
    let service;
    let mockDb;

    beforeEach(() => {
        mockDb = {
            collection: jest.fn().mockReturnThis(),
            doc: jest.fn().mockReturnThis(),
            get: jest.fn(),
            set: jest.fn(),
            where: jest.fn().mockReturnThis(),
            orderBy: jest.fn().mockReturnThis()
        };
        getDb.mockReturnValue(mockDb);
        service = new QuestionAnalysisService();
    });

    describe('calculateAdaptiveDifficulty', () => {
        it('should return medium difficulty for new users', async () => {
            mockDb.get.mockResolvedValue({
                exists: false
            });

            const difficulty = await service.calculateAdaptiveDifficulty('user1', 'math', 'algebra');
            expect(difficulty).toBe('medium');
        });

        it('should calculate correct difficulty based on performance', async () => {
            mockDb.get.mockResolvedValue({
                exists: true,
                data: () => ({
                    correctRate: 0.9,
                    avgResponseTime: 45,
                    questionsAttempted: 100
                })
            });

            const difficulty = await service.calculateAdaptiveDifficulty('user1', 'math', 'algebra');
            expect(difficulty).toBe('expert');
        });
    });

    describe('trackQuestionDiversity', () => {
        it('should track question attempts correctly', async () => {
            const userId = 'user1';
            const questionId = 'question1';
            const metadata = {
                subject: 'math',
                topic: 'algebra',
                difficulty: 'medium'
            };

            await service.trackQuestionDiversity(userId, questionId, metadata);

            expect(mockDb.doc).toHaveBeenCalledWith(expect.any(String));
            expect(mockDb.set).toHaveBeenCalledWith(expect.objectContaining({
                userId,
                questionId,
                timestamp: expect.any(Date)
            }));
        });
    });

    describe('validateDomainKnowledge', () => {
        it('should validate question content against domain rules', async () => {
            const question = {
                subject: 'math',
                topic: 'algebra',
                content: 'Solve for x: 2x + 5 = 13',
                keywords: ['equation', 'linear', 'solve']
            };

            const result = await service.validateDomainKnowledge(question);
            expect(result.isValid).toBe(true);
            expect(result.score).toBeGreaterThan(0);
        });
    });

    describe('processMultimediaContent', () => {
        it('should process and optimize image content', async () => {
            const content = {
                type: 'image',
                data: Buffer.from('test-image'),
                metadata: {
                    width: 800,
                    height: 600,
                    format: 'jpg'
                }
            };

            const result = await service.processMultimediaContent(content);
            expect(result.optimized).toBe(true);
            expect(result.url).toMatch(/^https:/);
        });
    });

    describe('calculateQuestionQuality', () => {
        it('should calculate quality score based on user feedback', async () => {
            const questionId = 'question1';
            mockDb.get.mockResolvedValue({
                exists: true,
                data: () => ({
                    feedback: [
                        { rating: 4, type: 'helpfulness' },
                        { rating: 5, type: 'clarity' },
                        { rating: 3, type: 'difficulty' }
                    ]
                })
            });

            const quality = await service.calculateQuestionQuality(questionId);
            expect(quality.score).toBeGreaterThanOrEqual(0);
            expect(quality.score).toBeLessThanOrEqual(5);
            expect(quality.metrics).toEqual({
                helpfulness: 4,
                clarity: 5,
                difficulty: 3
            });
        });

        it('should handle questions with no feedback', async () => {
            const questionId = 'question2';
            mockDb.get.mockResolvedValue({
                exists: true,
                data: () => ({ feedback: [] })
            });

            const quality = await service.calculateQuestionQuality(questionId);
            expect(quality.score).toBe(0);
            expect(quality.metrics).toEqual({
                helpfulness: 0,
                clarity: 0,
                difficulty: 0
            });
        });

        it('should log quality calculation', async () => {
            const questionId = 'question1';
            mockDb.get.mockResolvedValue({
                exists: true,
                data: () => ({
                    feedback: [{ rating: 4, type: 'helpfulness' }]
                })
            });

            await service.calculateQuestionQuality(questionId);
            expect(loggingService.logInfo).toHaveBeenCalledWith(
                'Calculated question quality',
                expect.objectContaining({
                    questionId,
                    score: expect.any(Number)
                })
            );
        });
    });

    describe('getDifficultyStats', () => {
        it('should return difficulty statistics for a topic', async () => {
            const topic = 'algebra';
            mockDb.get.mockResolvedValue({
                docs: [
                    { data: () => ({ difficulty: 'easy', attempts: 100, correctRate: 0.8 }) },
                    { data: () => ({ difficulty: 'medium', attempts: 150, correctRate: 0.6 }) },
                    { data: () => ({ difficulty: 'hard', attempts: 50, correctRate: 0.4 }) }
                ]
            });

            const stats = await service.getDifficultyStats(topic);
            expect(stats).toEqual({
                easy: { attempts: 100, correctRate: 0.8 },
                medium: { attempts: 150, correctRate: 0.6 },
                hard: { attempts: 50, correctRate: 0.4 }
            });
        });
    });

    describe('getQuestionRecommendations', () => {
        it('should recommend questions based on user performance', async () => {
            const userId = 'user1';
            const topic = 'algebra';
            
            // Mock user performance
            mockDb.get.mockResolvedValueOnce({
                exists: true,
                data: () => ({
                    correctRate: 0.7,
                    avgResponseTime: 60,
                    questionsAttempted: 50
                })
            });

            // Mock available questions
            mockDb.get.mockResolvedValueOnce({
                docs: [
                    { id: 'q1', data: () => ({ difficulty: 'medium', topic }) },
                    { id: 'q2', data: () => ({ difficulty: 'hard', topic }) }
                ]
            });

            const recommendations = await service.getQuestionRecommendations(userId, topic);
            expect(recommendations).toHaveLength(2);
            expect(recommendations[0]).toHaveProperty('id');
            expect(recommendations[0]).toHaveProperty('difficulty');
        });
    });
});

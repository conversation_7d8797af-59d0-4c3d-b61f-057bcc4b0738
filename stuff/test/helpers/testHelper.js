import sinon from 'sinon';
import { MockFirestore } from '../../server/services/mockFirebaseService.js';

// Mock environment variables
process.env.NODE_ENV = 'test';

// Create mock database
const mockDb = new MockFirestore();

// Create test data
const testData = {
  questions: [
    {
      id: 'test1',
      question: 'Test question 1?',
      type: 'multiple_choice',
      options: ['A', 'B', 'C', 'D'],
      correct_answer: 'A',
      subject: 'Math',
      topic: 'Algebra',
      level: 'medium'
    },
    {
      id: 'test2',
      question: 'Test question 2?',
      type: 'true_false',
      correct_answer: true,
      subject: 'Science',
      topic: 'Physics',
      level: 'hard'
    }
  ],
  syllabus: [
    {
      id: 'syllabus1',
      name: 'Test Syllabus',
      subject: 'Math',
      units: [
        {
          id: 'unit1',
          name: 'Algebra',
          topics: [
            {
              id: 'topic1',
              name: 'Linear Equations',
              description: 'Basic linear equations and inequalities',
              keywords: ['equation', 'linear', 'variable'],
              learning_objectives: ['Solve linear equations', 'Graph linear inequalities']
            }
          ]
        }
      ]
    }
  ]
};

// Initialize test data in mock database
for (const question of testData.questions) {
  mockDb.collection('questions').doc(question.id).set(question);
}

for (const syllabus of testData.syllabus) {
  mockDb.collection('syllabus').doc(syllabus.id).set(syllabus);
}

// Mock Firebase functions
const mockFirebase = {
  getDb: () => mockDb
};

// Mock logging service
const mockLoggingService = {
  logInfo: sinon.spy(),
  logError: sinon.spy(),
  logWarning: sinon.spy(),
  logDebug: sinon.spy()
};

// Reset all mocks between tests
function resetMocks() {
  mockLoggingService.logInfo.resetHistory();
  mockLoggingService.logError.resetHistory();
  mockLoggingService.logWarning.resetHistory();
  mockLoggingService.logDebug.resetHistory();
}

export {
  mockDb,
  mockFirebase,
  mockLoggingService,
  testData,
  resetMocks
};

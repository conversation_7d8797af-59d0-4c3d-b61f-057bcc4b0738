import { expect } from 'chai';
import sinon from 'sinon';
import { mockDb, mockLoggingService } from '../helpers/testHelper.js';
import questionDistributionService from '../../server/services/questionDistributionService.js';

describe('QuestionDistributionService', () => {
    let sandbox;
    const testSyllabus = {
        id: 'test-syllabus',
        name: 'Test Syllabus',
        units: [
            {
                id: 'unit-1',
                name: 'Unit 1',
                weight: 60,
                difficulty_distribution: {
                    easy: 30,
                    medium: 40,
                    hard: 20,
                    expert: 10
                },
                min_questions: 10,
                topics: [
                    {
                        id: 'topic-1',
                        name: 'Topic 1',
                        weight: 40,
                        difficulty_distribution: {
                            easy: 25,
                            medium: 45,
                            hard: 20,
                            expert: 10
                        },
                        min_questions: 5
                    },
                    {
                        id: 'topic-2',
                        name: 'Topic 2',
                        weight: 60,
                        difficulty_distribution: {
                            easy: 35,
                            medium: 35,
                            hard: 20,
                            expert: 10
                        },
                        min_questions: 5
                    }
                ]
            },
            {
                id: 'unit-2',
                name: 'Unit 2',
                weight: 40,
                difficulty_distribution: {
                    easy: 20,
                    medium: 40,
                    hard: 30,
                    expert: 10
                },
                min_questions: 10,
                topics: [
                    {
                        id: 'topic-3',
                        name: 'Topic 3',
                        weight: 100,
                        difficulty_distribution: {
                            easy: 20,
                            medium: 40,
                            hard: 30,
                            expert: 10
                        },
                        min_questions: 10
                    }
                ]
            }
        ]
    };

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        sandbox.stub(mockLoggingService, 'logInfo');
        sandbox.stub(mockLoggingService, 'logError');
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('calculateQuestionDistribution', () => {
        it('should calculate correct distribution for total questions', () => {
            const totalQuestions = 100;
            const distribution = questionDistributionService.calculateQuestionDistribution(testSyllabus, totalQuestions);

            expect(distribution.totalQuestions).to.equal(totalQuestions);
            expect(distribution.units).to.have.lengthOf(2);

            // Verify Unit 1 (60% weight)
            const unit1 = distribution.units.find(u => u.id === 'unit-1');
            expect(unit1.questionCount).to.equal(60);
            expect(unit1.topics).to.have.lengthOf(2);

            // Verify Topic 1 in Unit 1 (40% of 60 = 24 questions)
            const topic1 = unit1.topics.find(t => t.id === 'topic-1');
            expect(topic1.questionCount).to.equal(24);
            expect(topic1.distribution.easy).to.equal(6); // 25% of 24
            expect(topic1.distribution.medium).to.equal(11); // 45% of 24
            expect(topic1.distribution.hard).to.equal(5); // 20% of 24
            expect(topic1.distribution.expert).to.equal(2); // 10% of 24

            // Verify Unit 2 (40% weight)
            const unit2 = distribution.units.find(u => u.id === 'unit-2');
            expect(unit2.questionCount).to.equal(40);
        });

        it('should respect minimum question requirements', () => {
            const totalQuestions = 20; // Less than sum of min_questions
            const distribution = questionDistributionService.calculateQuestionDistribution(testSyllabus, totalQuestions);

            // Should allocate at least minimum questions
            distribution.units.forEach(unit => {
                expect(unit.questionCount).to.be.at.least(unit.min_questions);
                unit.topics.forEach(topic => {
                    expect(topic.questionCount).to.be.at.least(topic.min_questions);
                });
            });
        });

        it('should handle zero weights gracefully', () => {
            const modifiedSyllabus = {
                ...testSyllabus,
                units: [
                    {
                        ...testSyllabus.units[0],
                        weight: 0
                    },
                    {
                        ...testSyllabus.units[1],
                        weight: 0
                    }
                ]
            };

            const distribution = questionDistributionService.calculateQuestionDistribution(modifiedSyllabus, 100);
            
            // Should distribute evenly when weights are zero
            expect(distribution.units[0].questionCount).to.equal(50);
            expect(distribution.units[1].questionCount).to.equal(50);
        });

        it('should validate input parameters', () => {
            expect(() => questionDistributionService.calculateQuestionDistribution(null, 100))
                .to.throw('Invalid syllabus');
            expect(() => questionDistributionService.calculateQuestionDistribution(testSyllabus, 0))
                .to.throw('Total questions must be positive');
            expect(() => questionDistributionService.calculateQuestionDistribution(testSyllabus, -1))
                .to.throw('Total questions must be positive');
        });
    });

    describe('validateDistribution', () => {
        it('should validate unit weights sum to 100%', () => {
            const invalidSyllabus = {
                ...testSyllabus,
                units: [
                    { ...testSyllabus.units[0], weight: 70 },
                    { ...testSyllabus.units[1], weight: 40 }
                ]
            };

            const result = questionDistributionService.validateDistribution(invalidSyllabus);
            expect(result.valid).to.be.false;
            expect(result.errors).to.include('Unit weights must sum to 100%');
        });

        it('should validate topic weights within units sum to 100%', () => {
            const invalidSyllabus = {
                ...testSyllabus,
                units: [
                    {
                        ...testSyllabus.units[0],
                        topics: [
                            { ...testSyllabus.units[0].topics[0], weight: 30 },
                            { ...testSyllabus.units[0].topics[1], weight: 50 }
                        ]
                    }
                ]
            };

            const result = questionDistributionService.validateDistribution(invalidSyllabus);
            expect(result.valid).to.be.false;
            expect(result.errors).to.include('Topic weights in Unit 1 must sum to 100%');
        });

        it('should validate difficulty distributions sum to 100%', () => {
            const invalidSyllabus = {
                ...testSyllabus,
                units: [
                    {
                        ...testSyllabus.units[0],
                        difficulty_distribution: {
                            easy: 30,
                            medium: 40,
                            hard: 20,
                            expert: 20 // Total > 100
                        }
                    }
                ]
            };

            const result = questionDistributionService.validateDistribution(invalidSyllabus);
            expect(result.valid).to.be.false;
            expect(result.errors).to.include('Difficulty distribution in Unit 1 must sum to 100%');
        });
    });
});

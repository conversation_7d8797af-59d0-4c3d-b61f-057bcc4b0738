<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syllabus Manager - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/adminPanel/common.css" rel="stylesheet">
    <link href="/css/adminPanel/syllabusManager.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/examConfig.html">Exam Config</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/syllabusManager.html">Syllabus Manager</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/monitoring.html">Monitoring</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <button class="btn btn-outline-light" onclick="logout()">Logout</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Syllabus List -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Syllabi</h5>
                        <div class="btn-group">
                            <button class="btn btn-primary btn-sm" onclick="showCreateSyllabusModal()">
                                <i class="bi bi-plus"></i> New
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="showBulkUploadModal()">
                                <i class="bi bi-upload"></i> Bulk Upload
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="list-group" id="syllabusList">
                            <!-- Syllabi will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Syllabus Editor -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Syllabus Editor</h5>
                    </div>
                    <div class="card-body">
                        <form id="syllabusForm">
                            <ul class="nav nav-tabs mb-3" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#basicInfo" type="button">Basic Info</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#distribution" type="button">Distribution</button>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane fade show active" id="basicInfo">
                                    <!-- Basic Info -->
                                    <div class="mb-3">
                                        <label class="form-label">Name</label>
                                        <input type="text" class="form-control" id="syllabusName" required>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Subject</label>
                                                <input type="text" class="form-control" id="syllabusSubject" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Level</label>
                                                <select class="form-select" id="syllabusLevel" required>
                                                    <option value="high_school">High School</option>
                                                    <option value="undergraduate">Undergraduate</option>
                                                    <option value="graduate">Graduate</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Description</label>
                                        <textarea class="form-control" id="syllabusDescription" rows="3"></textarea>
                                    </div>

                                    <!-- Units -->
                                    <div class="mb-3">
                                        <label class="form-label d-flex justify-content-between">
                                            <span>Units</span>
                                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addUnit()">
                                                <i class="bi bi-plus"></i> Add Unit
                                            </button>
                                        </label>
                                        <div id="unitsList">
                                            <!-- Units will be added here -->
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="distribution">
                                    <div class="mb-3">
                                        <label class="form-label">Question Distribution Settings</label>
                                        <div class="table-responsive">
                                            <table class="table table-sm" id="distributionTable">
                                                <thead>
                                                    <tr>
                                                        <th>Unit/Topic</th>
                                                        <th>Weight (%)</th>
                                                        <th>Difficulty Distribution</th>
                                                        <th>Min Questions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Populated dynamically -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="alert alert-info">
                                        <h6>Distribution Guidelines</h6>
                                        <ul class="mb-0">
                                            <li>Unit weights should sum to 100%</li>
                                            <li>Topic weights within a unit should sum to 100%</li>
                                            <li>Difficulty levels: Easy, Medium, Hard, Expert</li>
                                            <li>Minimum questions ensure topic coverage</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="button" class="btn btn-secondary me-2" onclick="cancel()">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" id="bulkUploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Upload Syllabi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkUploadForm">
                        <div class="bulk-upload-zone mb-3" id="dropZone">
                            <div class="bulk-upload-icon">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <h5>Drag & Drop Files Here</h5>
                            <p class="text-muted">or</p>
                            <input type="file" class="form-control" id="syllabusFile" accept=".json,.xlsx" required>
                            <div class="form-text mt-3">
                                Upload a JSON or Excel file containing one or more syllabi.<br>
                                <a href="#" id="downloadTemplate" class="text-primary">
                                    <i class="bi bi-download"></i> Download Excel Template
                                </a>
                            </div>
                        </div>
                        <div class="mb-3 mt-4">
                            <div class="form-text">
                                <strong>JSON Format Reference:</strong>
                                <pre class="mt-2"><code>{
  "name": "Course Name",
  "subject": "Subject Name",
  "level": "undergraduate",
  "description": "Course description",
  "units": [
    {
      "name": "Unit Name",
      "weight": 30,
      "topics": [
        {
          "name": "Topic Name",
          "weight": 40,
          "learning_objectives": [
            "Objective 1",
            "Objective 2"
          ],
          "keywords": [
            "keyword1",
            "keyword2"
          ]
        }
      ]
    }
  ]
}</code></pre>
                            </div>
                        </div>
                        <div id="bulkUploadValidation" class="alert alert-info d-none">
                            <h6>Validation Results:</h6>
                            <div id="validationResults"></div>
                        </div>
                        <div id="bulkUploadProgress" class="d-none">
                            <div class="progress mb-2">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="text-muted small"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary" id="bulkUploadSubmitBtn">Upload</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pre-computation Modal -->
    <div class="modal fade" id="preComputationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Generate Questions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        This will generate approximately 100 questions per topic across different difficulty levels and question types.
                        The process may take some time depending on the syllabus size.
                    </div>

                    <div id="preComputationStatus" class="d-none">
                        <h6 class="mb-3">Generation Progress</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="d-flex justify-content-between text-muted small">
                            <div id="preComputationProgress">0%</div>
                            <div id="preComputationStats"></div>
                        </div>
                        <div id="preComputationDetails" class="mt-3 small"></div>
                    </div>

                    <div id="preComputationError" class="alert alert-danger d-none">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="preComputationErrorMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="startPreComputationBtn">Start Generation</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Syllabus Modal -->
    <div class="modal fade" id="createSyllabusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Syllabus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createSyllabusForm">
                        <div class="mb-3">
                            <label class="form-label">Name</label>
                            <input type="text" class="form-control" id="newSyllabusName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subject</label>
                            <input type="text" class="form-control" id="newSyllabusSubject" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Level</label>
                            <select class="form-select" id="newSyllabusLevel" required>
                                <option value="high_school">High School</option>
                                <option value="undergraduate">Undergraduate</option>
                                <option value="graduate">Graduate</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createSyllabus()">Create</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" id="bulkUploadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bulk Upload Syllabus</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Upload JSON or Excel File</label>
                        <input type="file" class="form-control" id="bulkUploadFile" accept=".json,.xlsx,.xls">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Template Format</label>
                        <pre class="bg-light p-3 rounded">
{
  "name": "Course Name",
  "subject": "Subject Name",
  "level": "undergraduate",
  "units": [
    {
      "name": "Unit Name",
      "topics": [
        {
          "name": "Topic Name",
          "learning_objectives": ["Objective 1", "Objective 2"],
          "keywords": ["keyword1", "keyword2"]
        }
      ]
    }
  ]
}</pre>
                    </div>
                    <div id="validationPreview" class="d-none">
                        <h6>Validation Results</h6>
                        <div class="alert" role="alert" id="validationAlert"></div>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Item</th>
                                        <th>Message</th>
                                    </tr>
                                </thead>
                                <tbody id="validationResults"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="validateBtn" onclick="validateBulkUpload()">Validate</button>
                    <button type="button" class="btn btn-success d-none" id="uploadBtn" onclick="processBulkUpload()">Upload</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"></script>
    <script src="/js/adminPanel/auth.js"></script>
    <script src="/js/adminPanel/syllabusManager.js"></script>
</body>
</html>

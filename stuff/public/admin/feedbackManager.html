<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Feedback Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/adminPanel/common.css" rel="stylesheet">
    <link href="/css/adminPanel/feedbackManager.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/syllabusManager.html">Syllabus</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/questionDistribution.html">Question Distribution</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/feedbackManager.html">Feedback</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/monitoring.html">Monitoring</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <!-- Review Queue -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Review Queue</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshQueue()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-secondary active" onclick="filterQueue('all')">All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="filterQueue('high')">High Priority</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="filterQueue('normal')">Normal</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="reviewQueue" class="list-group">
                            <!-- Review items will be dynamically added here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Feedback Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="stats-item">
                            <label>Pending Reviews</label>
                            <h3 id="pendingCount">0</h3>
                        </div>
                        <div class="stats-item">
                            <label>High Priority</label>
                            <h3 id="highPriorityCount">0</h3>
                        </div>
                        <div class="stats-item">
                            <label>Processed Today</label>
                            <h3 id="processedToday">0</h3>
                        </div>
                        <hr>
                        <div class="stats-chart">
                            <canvas id="feedbackChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Review History -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Review History</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Question</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Reviewer</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="reviewHistory">
                                    <!-- History items will be dynamically added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Modal -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Review Feedback</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="review-content">
                        <!-- Original Question -->
                        <div class="card mb-3">
                            <div class="card-header">Original Question</div>
                            <div class="card-body">
                                <p id="originalQuestion"></p>
                                <div id="originalOptions" class="options-list"></div>
                                <hr>
                                <strong>Correct Answer:</strong>
                                <p id="originalAnswer"></p>
                                <strong>Explanation:</strong>
                                <p id="originalExplanation"></p>
                            </div>
                        </div>

                        <!-- Feedback -->
                        <div class="card mb-3">
                            <div class="card-header">User Feedback</div>
                            <div class="card-body">
                                <strong>Type:</strong>
                                <p id="feedbackType"></p>
                                <strong>Notes:</strong>
                                <p id="feedbackNotes"></p>
                                <div id="suggestedChanges" class="suggested-changes">
                                    <!-- Suggested changes will be shown here -->
                                </div>
                            </div>
                        </div>

                        <!-- LLM Review -->
                        <div class="card mb-3">
                            <div class="card-header">AI Review</div>
                            <div class="card-body">
                                <div id="llmReview"></div>
                            </div>
                        </div>

                        <!-- Reviewer Decision -->
                        <div class="card">
                            <div class="card-header">Your Decision</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea id="reviewerNotes" class="form-control" rows="3"></textarea>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button class="btn btn-danger me-2" onclick="rejectFeedback()">Reject</button>
                                    <button class="btn btn-success" onclick="approveFeedback()">Approve</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/adminPanel/feedbackManager.js"></script>
</body>
</html>

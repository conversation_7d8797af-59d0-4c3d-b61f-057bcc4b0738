// Main Application Module
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Firebase
    initializeFirebase();
    
    // Initialize authentication system
    initializeAuth();
    
    // Initialize tab navigation
    initializeTabs();
    
    // Load appropriate module content based on current tab
    loadInitialContent();
    
    // Setup global error handler
    setupErrorHandling();
    
    console.log('AI-Powered Mock Test System initialized');
});

function initializeTabs() {
    const tabs = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const target = tab.getAttribute('data-target');
            
            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Show selected tab content
            tabContents.forEach(content => {
                content.classList.toggle('hidden', content.id !== target);
            });
            
            // Update URL hash
            window.location.hash = target;
            
            // Load content if needed
            loadTabContent(target);
        });
    });
    
    // Handle initial tab from URL hash
    const initialTab = window.location.hash ? window.location.hash.substring(1) : 'exam-explorer';
    const tabToActivate = document.querySelector(`.tab-item[data-target="${initialTab}"]`) || document.querySelector('.tab-item');
    if (tabToActivate) {
        tabToActivate.click();
    }
}

function loadInitialContent() {
    // Load exam data
    fetchExamData()
        .then(data => {
            console.log('Exam data loaded');
        })
        .catch(error => {
            console.error('Failed to load exam data:', error);
            showError('Failed to load exam data. Please refresh the page.');
        });
}

function loadTabContent(tabId) {
    // Add extra loading logic for specific tabs if needed
    switch(tabId) {
        case 'exam-explorer':
            // examExplorer.js already handles its initialization
            break;
        case 'mock-test':
            // mockTest.js already handles its initialization
            break;
        case 'analytics':
            // Analytics might need a refresh when tab is selected
            if (isUserSignedIn()) {
                // Trigger analytics refresh if needed
            } else {
                showLoginPrompt('analytics-container', 'Please sign in to view your analytics');
            }
            break;
        case 'syllabus':
            // syllabus.js already handles its initialization
            break;
    }
}

async function fetchExamData() {
    try {
        return await apiGet('exams');
    } catch (error) {
        console.error('Error fetching exam data:', error);
        throw error;
    }
}

function setupErrorHandling() {
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
        // You might want to log these errors to a monitoring service
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
        // You might want to log these errors to a monitoring service
    });
}

function showLoginPrompt(containerId, message) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="text-center py-10">
            <p class="text-gray-600 mb-4">${message}</p>
            <button id="${containerId}-login-btn" class="bg-indigo-600 text-white py-2 px-6 rounded-md hover:bg-indigo-700">
                Sign In
            </button>
        </div>
    `;
    
    document.getElementById(`${containerId}-login-btn`).addEventListener('click', () => {
        signInAnonymously()
            .then(() => {
                // Reload the tab content after sign in
                const activeTab = document.querySelector('.tab-item.active');
                if (activeTab) {
                    loadTabContent(activeTab.getAttribute('data-target'));
                }
            })
            .catch(error => {
                console.error('Sign in failed:', error);
                showError('Failed to sign in. Please try again.');
            });
    });
}

function showError(message, duration = 5000) {
    const errorContainer = document.createElement('div');
    errorContainer.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
    errorContainer.textContent = message;
    
    document.body.appendChild(errorContainer);
    
    setTimeout(() => {
        errorContainer.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => errorContainer.remove(), 500);
    }, duration);
}

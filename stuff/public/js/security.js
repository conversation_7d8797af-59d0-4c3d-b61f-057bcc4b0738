// Security measures for website protection
document.addEventListener('DOMContentLoaded', () => {
    // Disable right click
    document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        showToast('Right-click is disabled for content protection', 'warning');
    });

    // Disable keyboard shortcuts for screenshots and dev tools
    document.addEventListener('keydown', (e) => {
        // Prevent PrintScreen
        if (e.key === 'PrintScreen') {
            e.preventDefault();
            showToast('Screenshots are not allowed', 'warning');
        }

        // Prevent Ctrl+Shift+I/J/C (Dev Tools)
        if (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J' || e.key === 'C')) {
            e.preventDefault();
            showToast('Developer tools access is restricted', 'warning');
        }

        // Prevent Ctrl+S (Save)
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            showToast('Saving page content is restricted', 'warning');
        }

        // Prevent Ctrl+P (Print)
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            showToast('Printing is disabled', 'warning');
        }
    });

    // Disable text selection
    document.addEventListener('selectstart', (e) => {
        if (!e.target.matches('input, textarea')) {
            e.preventDefault();
        }
    });

    // Anti-iframe protection
    if (window.self !== window.top) {
        window.top.location.href = window.self.location.href;
    }

    // Add CSS to prevent selection and dragging
    const style = document.createElement('style');
    style.textContent = `
        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
        }
        img, video {
            pointer-events: none;
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
        }
    `;
    document.head.appendChild(style);

    // Bot detection
    const botPatterns = [
        /bot/i, /spider/i, /crawl/i, /APIs-Google/i,
        /AdsBot/i, /Googlebot/i, /mediapartners/i, /Python/i,
        /curl/i, /wget/i, /scraping/i, /phantomjs/i,
        /selenium/i, /nightmare/i, /jsdom/i, /headless/i
    ];

    const userAgent = navigator.userAgent.toLowerCase();
    if (botPatterns.some(pattern => pattern.test(userAgent))) {
        document.body.innerHTML = 'Access denied';
        return;
    }

    // Simple browser fingerprinting
    const fingerprint = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        screenResolution: `${window.screen.width}x${window.screen.height}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        cookiesEnabled: navigator.cookieEnabled
    };

    // Store fingerprint in sessionStorage
    sessionStorage.setItem('browserFingerprint', JSON.stringify(fingerprint));

    // Validate fingerprint on each page load
    const storedFingerprint = sessionStorage.getItem('browserFingerprint');
    if (storedFingerprint) {
        const currentFingerprint = JSON.stringify(fingerprint);
        if (storedFingerprint !== currentFingerprint) {
            location.reload(); // Force reload if fingerprint changes
        }
    }
});

// Firebase Configuration
const firebaseConfig = {
  apiKey: "YOUR_API_KEY", // Replace with your actual API key
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com", // Replace with your actual auth domain
  projectId: "YOUR_PROJECT_ID", // Replace with your actual project ID
  storageBucket: "YOUR_PROJECT_ID.appspot.com", // Replace with your actual storage bucket
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID", // Replace with your actual messaging sender ID
  appId: "YOUR_APP_ID" // Replace with your actual app ID
};

// Initialize Firebase
// It's better to import specific services from the Firebase SDK v9+ (modular)
// Assuming you are using Firebase v9+ as per modern practices.
// If you are using an older version (like in your current config.js), the below needs adjustment.
// For Firebase v9+ (recommended):
// import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-app.js";
// import { getFirestore } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-firestore.js";
// import { getAuth } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-auth.js";

// const app = initializeApp(firebaseConfig);
// const db = getFirestore(app);
// const auth = getAuth(app);

// If sticking to the older SDK version as implied by firebase.initializeApp:
const app = firebase.initializeApp(firebaseConfig); // Ensure 'firebase' object is available globally or imported
const db = firebase.firestore(); // Make sure you've included the firestore script
const auth = firebase.auth(); // Make sure you've included the auth script

// Global app constants
const __app_id = 'ai_mock_test_system';
const API_BASE_URL = '/api';

// Global app state
let userId = null;
let currentUser = null;

// Helper functions
function showLoader(element) {
  element.innerHTML = '<div class="flex justify-center p-4"><div class="loader"></div></div>';
}

function showError(element, message) {
  element.innerHTML = `<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">${message}</div>`;
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// API helper functions
async function apiGet(endpoint, params = {}) {
  const url = new URL(`${API_BASE_URL}/${endpoint}`);
  Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));
  
  const headers = {
    'Content-Type': 'application/json'
  };
  
  if (userId) {
    headers['user-id'] = userId;
  }
  
  const response = await fetch(url, {
    method: 'GET',
    headers
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'API request failed');
  }
  
  return response.json();
}

async function apiPost(endpoint, data = {}) {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  if (userId) {
    headers['user-id'] = userId;
  }
  
  const response = await fetch(`${API_BASE_URL}/${endpoint}`, {
    method: 'POST',
    headers,
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'API request failed');
  }
  
  return response.json();
}

async function apiPut(endpoint, data = {}) {
  try {
    const url = `${API_BASE_URL}/${endpoint}`;
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (userId) {
      headers['user-id'] = userId;
    }
    
    return fetch(url, {
      method: 'PUT',
      headers: headers,
      body: JSON.stringify(data)
    }).then(response => {
      if (!response.ok) {
        throw new Error(`API PUT Error: ${response.statusText}`);
      }
      return response.json();
    });
  } catch (error) {
    console.error(`Error in PUT ${endpoint}:`, error);
    throw error;
  }
}

async function apiDelete(endpoint) {
  try {
    const url = `${API_BASE_URL}/${endpoint}`;
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (userId) {
      headers['user-id'] = userId;
    }
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: headers
    });
    
    if (!response.ok) {
      throw new Error(`API DELETE Error: ${response.statusText}`);
    }
    
    return response.json();
  } catch (error) {
    console.error(`Error in DELETE ${endpoint}:`, error);
    throw error;
  }
}

// Attach helper functions to window for module accessibility
window.apiGet = apiGet;
window.apiPost = apiPost;
window.apiPut = apiPut;
window.apiDelete = apiDelete;
window.formatTime = formatTime;

class TheoryFeatures {
    constructor() {
        this.initializeTheoryButtons();
        this.initializeChallengeButtons();
        this.initializeModals();
    }

    initializeTheoryButtons() {
        document.querySelectorAll('.understand-theory-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const questionId = e.target.dataset.questionId;
                const question = document.querySelector(`[data-question-id="${questionId}"]`);
                await this.showTheoryExplanation(question);
            });
        });
    }

    initializeChallengeButtons() {
        document.querySelectorAll('.challenge-answer-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const questionId = e.target.dataset.questionId;
                const question = document.querySelector(`[data-question-id="${questionId}"]`);
                await this.showChallengeForm(question);
            });
        });
    }

    initializeModals() {
        // Theory Modal
        const theoryModal = `
            <div id="theory-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Theory Explanation</h2>
                    <div id="theory-content" class="theory-content">
                        <div class="loading">Loading explanation...</div>
                    </div>
                </div>
            </div>
        `;

        // Challenge Modal
        const challengeModal = `
            <div id="challenge-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Challenge Answer</h2>
                    <div class="challenge-form">
                        <p class="question-text"></p>
                        <p class="correct-answer"></p>
                        <textarea id="challenge-text" 
                                placeholder="Explain why you think this answer or explanation is incorrect or incomplete..."
                                rows="4"></textarea>
                        <button class="submit-challenge">Submit Challenge</button>
                    </div>
                    <div class="challenge-response" style="display: none;">
                        <h3>Evaluation</h3>
                        <div class="evaluation-content"></div>
                    </div>
                </div>
            </div>
        `;

        // Add modals to body
        document.body.insertAdjacentHTML('beforeend', theoryModal);
        document.body.insertAdjacentHTML('beforeend', challengeModal);

        // Close button handlers
        document.querySelectorAll('.modal .close').forEach(closeBtn => {
            closeBtn.onclick = () => {
                closeBtn.closest('.modal').style.display = 'none';
            };
        });

        // Close on outside click
        window.onclick = (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        };
    }

    async showTheoryExplanation(question) {
        const modal = document.getElementById('theory-modal');
        const content = document.getElementById('theory-content');
        modal.style.display = 'block';
        content.innerHTML = '<div class="loading">Loading explanation...</div>';

        try {
            const response = await fetch('/api/theory-explanation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    question: question.querySelector('.question-text').textContent,
                    topic: question.dataset.topic,
                    syllabus: question.dataset.syllabus
                })
            });

            const data = await response.json();
            
            content.innerHTML = `
                <div class="theory-section">
                    <h3>Core Principles</h3>
                    ${data.explanation.principles}
                </div>
                ${data.explanation.formulas ? `
                    <div class="theory-section">
                        <h3>Relevant Formulas</h3>
                        ${data.explanation.formulas}
                    </div>
                ` : ''}
                <div class="theory-section">
                    <h3>Practical Implications</h3>
                    ${data.explanation.implications}
                </div>
                <div class="theory-section">
                    <h3>Examples</h3>
                    ${data.explanation.examples}
                </div>
                <div class="theory-section">
                    <h3>Common Misconceptions</h3>
                    ${data.explanation.misconceptions}
                </div>
                <div class="theory-section">
                    <h3>Related Topics</h3>
                    ${data.explanation.relatedTopics}
                </div>
            `;
        } catch (error) {
            content.innerHTML = `
                <div class="error">
                    Error loading explanation. Please try again later.
                </div>
            `;
            console.error('Error fetching theory explanation:', error);
        }
    }

    async showChallengeForm(question) {
        const modal = document.getElementById('challenge-modal');
        const challengeForm = modal.querySelector('.challenge-form');
        const challengeResponse = modal.querySelector('.challenge-response');
        
        // Reset form
        challengeForm.style.display = 'block';
        challengeResponse.style.display = 'none';
        modal.querySelector('#challenge-text').value = '';
        
        // Set question details
        modal.querySelector('.question-text').textContent = question.querySelector('.question-text').textContent;
        modal.querySelector('.correct-answer').textContent = `Correct Answer: ${question.dataset.correctAnswer}`;
        
        modal.style.display = 'block';

        // Handle challenge submission
        modal.querySelector('.submit-challenge').onclick = async () => {
            const challengeText = modal.querySelector('#challenge-text').value;
            if (!challengeText.trim()) {
                alert('Please explain your challenge before submitting.');
                return;
            }

            try {
                const response = await fetch('/api/challenge-answer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        questionId: question.dataset.questionId,
                        question: question.querySelector('.question-text').textContent,
                        correctAnswer: question.dataset.correctAnswer,
                        originalExplanation: question.dataset.explanation,
                        userChallenge: challengeText
                    })
                });

                const data = await response.json();
                
                // Show evaluation
                challengeForm.style.display = 'none';
                challengeResponse.style.display = 'block';
                challengeResponse.querySelector('.evaluation-content').innerHTML = `
                    <div class="evaluation-section">
                        <h4>Validity Assessment</h4>
                        <p>${data.evaluation.validityAssessment}</p>
                    </div>
                    ${data.evaluation.identifiedIssues ? `
                        <div class="evaluation-section">
                            <h4>Identified Issues</h4>
                            <p>${data.evaluation.identifiedIssues}</p>
                        </div>
                    ` : ''}
                    <div class="evaluation-section">
                        <h4>Additional Clarification</h4>
                        <p>${data.evaluation.clarification}</p>
                    </div>
                    <div class="evaluation-section">
                        <h4>Recommendation</h4>
                        <p>${data.evaluation.recommendation}</p>
                    </div>
                `;
            } catch (error) {
                challengeResponse.querySelector('.evaluation-content').innerHTML = `
                    <div class="error">
                        Error submitting challenge. Please try again later.
                    </div>
                `;
                console.error('Error submitting challenge:', error);
            }
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.theoryFeatures = new TheoryFeatures();
});

class QuestionTypeRenderer {
    constructor() {
        this.questionTypes = {
            multiple_choice: this.renderMultipleChoice.bind(this),
            fill_in_blanks: this.renderFillInBlanks.bind(this),
            true_false: this.renderTrueFalse.bind(this),
            short_answer: this.renderShortAnswer.bind(this)
        };
    }

    renderQuestion(question, container) {
        const renderer = this.questionTypes[question.type];
        if (!renderer) {
            throw new Error(`Unsupported question type: ${question.type}`);
        }
        return renderer(question, container);
    }

    renderMultipleChoice(question, container) {
        const template = `
            <div class="question multiple-choice" data-question-id="${question.id}">
                <p class="question-text">${question.text}</p>
                <div class="options">
                    ${question.options.map((option, index) => `
                        <div class="option">
                            <input type="radio" 
                                   name="q${question.id}" 
                                   id="q${question.id}_${index}"
                                   value="${option}">
                            <label for="q${question.id}_${index}">${option}</label>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        container.innerHTML = template;
        return container.querySelector('.question');
    }

    renderFillInBlanks(question, container) {
        const template = `
            <div class="question fill-in-blanks" data-question-id="${question.id}">
                <p class="question-text">
                    ${question.text.replace(/\[___\]/g, (match, index) => `
                        <input type="text" 
                               class="blank-input"
                               data-blank-index="${index}"
                               placeholder="Type your answer">
                    `)}
                </p>
            </div>
        `;
        container.innerHTML = template;
        return container.querySelector('.question');
    }

    renderTrueFalse(question, container) {
        const template = `
            <div class="question true-false" data-question-id="${question.id}">
                <p class="question-text">${question.text}</p>
                <div class="options">
                    <div class="option">
                        <input type="radio" 
                               name="q${question.id}" 
                               id="q${question.id}_true"
                               value="true">
                        <label for="q${question.id}_true">True</label>
                    </div>
                    <div class="option">
                        <input type="radio" 
                               name="q${question.id}" 
                               id="q${question.id}_false"
                               value="false">
                        <label for="q${question.id}_false">False</label>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML = template;
        return container.querySelector('.question');
    }

    renderShortAnswer(question, container) {
        const template = `
            <div class="question short-answer" data-question-id="${question.id}">
                <p class="question-text">${question.text}</p>
                <div class="answer-area">
                    <textarea class="short-answer-input"
                              placeholder="Type your answer here..."
                              rows="4"></textarea>
                    <div class="word-count">Words: 0</div>
                </div>
                ${question.keyPoints ? `
                    <div class="key-points-hint">
                        <p>Your answer should address:</p>
                        <ul>
                            ${question.keyPoints.map(point => 
                                `<li>${point}</li>`
                            ).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
        container.innerHTML = template;
        
        // Add word counter
        const textarea = container.querySelector('.short-answer-input');
        const wordCount = container.querySelector('.word-count');
        textarea.addEventListener('input', () => {
            const words = textarea.value.trim().split(/\s+/).length;
            wordCount.textContent = `Words: ${words}`;
        });

        return container.querySelector('.question');
    }

    validateAnswer(question, answer) {
        switch (question.type) {
            case 'multiple_choice':
                return this.validateMultipleChoice(question, answer);
            case 'fill_in_blanks':
                return this.validateFillInBlanks(question, answer);
            case 'true_false':
                return this.validateTrueFalse(question, answer);
            case 'short_answer':
                return this.validateShortAnswer(question, answer);
            default:
                throw new Error(`Unsupported question type: ${question.type}`);
        }
    }

    validateMultipleChoice(question, answer) {
        return {
            correct: answer === question.correctAnswer,
            score: answer === question.correctAnswer ? 1 : 0
        };
    }

    validateFillInBlanks(question, answer) {
        const normalizedAnswer = answer.toLowerCase().trim();
        const isCorrect = question.acceptableAnswers.some(
            acceptable => acceptable.toLowerCase().trim() === normalizedAnswer
        );
        return {
            correct: isCorrect,
            score: isCorrect ? 1 : 0
        };
    }

    validateTrueFalse(question, answer) {
        const normalizedAnswer = answer.toLowerCase() === 'true';
        return {
            correct: normalizedAnswer === question.correctAnswer,
            score: normalizedAnswer === question.correctAnswer ? 1 : 0
        };
    }

    async validateShortAnswer(question, answer) {
        // Use LLM to evaluate short answer based on key points
        const evaluation = await fetch('/api/evaluate-short-answer', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                answer,
                modelAnswer: question.modelAnswer,
                keyPoints: question.keyPoints,
                rubric: question.rubric
            })
        }).then(res => res.json());

        return {
            correct: evaluation.score >= question.rubric.minimumPoints,
            score: evaluation.score,
            feedback: evaluation.feedback
        };
    }
}

// Export for use in other modules
window.QuestionTypeRenderer = QuestionTypeRenderer;

// Global state
let currentSyllabusId = null;
let currentObjectives = [];
let currentTopics = [];
let objectiveModal = null;
let topicMappingModal = null;
let selectedTopicId = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Initialize Bootstrap modals
    objectiveModal = new bootstrap.Modal(document.getElementById('objectiveModal'));
    topicMappingModal = new bootstrap.Modal(document.getElementById('topicMappingModal'));

    // Load syllabi
    loadSyllabi();

    // Initialize form handlers
    document.getElementById('objectiveForm').addEventListener('submit', (e) => {
        e.preventDefault();
        saveObjective();
    });
});

// Load syllabi list
async function loadSyllabi() {
    try {
        const response = await fetch('/api/admin/syllabus');
        if (!response.ok) throw new Error('Failed to load syllabi');
        
        const syllabi = await response.json();
        renderSyllabusList(syllabi);
    } catch (error) {
        showToast('Failed to load syllabi: ' + error.message, 'error');
    }
}

// Render syllabi list
function renderSyllabusList(syllabi) {
    const listContainer = document.getElementById('syllabusList');
    listContainer.innerHTML = syllabi.map(syllabus => `
        <button class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                onclick="selectSyllabus('${syllabus.id}')">
            <div>
                <div class="fw-bold">${syllabus.name}</div>
                <small class="text-muted">${syllabus.subject}</small>
            </div>
            <span class="badge bg-primary rounded-pill">
                ${syllabus.objectives?.length || 0} objectives
            </span>
        </button>
    `).join('');
}

// Select a syllabus
async function selectSyllabus(syllabusId) {
    currentSyllabusId = syllabusId;
    
    // Update active state in list
    document.querySelectorAll('#syllabusList button').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('onclick').includes(syllabusId)) {
            btn.classList.add('active');
        }
    });

    // Load objectives and topics
    await Promise.all([
        loadObjectives(syllabusId),
        loadTopics(syllabusId)
    ]);
}

// Load objectives for a syllabus
async function loadObjectives(syllabusId) {
    try {
        const response = await fetch(`/api/admin/learning-objectives/syllabus/${syllabusId}`);
        if (!response.ok) throw new Error('Failed to load objectives');
        
        currentObjectives = await response.json();
        renderObjectives();
    } catch (error) {
        showToast('Failed to load objectives: ' + error.message, 'error');
    }
}

// Render objectives list
function renderObjectives() {
    const container = document.getElementById('objectivesList');
    container.innerHTML = currentObjectives.map(objective => `
        <div class="objective-item">
            <div class="objective-header">
                <h6 class="objective-title">${objective.name}</h6>
                <div class="objective-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editObjective('${objective.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteObjective('${objective.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="objective-description">${objective.description || ''}</div>
            <div class="objective-meta">
                <span><i class="bi bi-bar-chart"></i> ${objective.level}</span>
                <span><i class="bi bi-percent"></i> ${objective.weight}%</span>
            </div>
        </div>
    `).join('');
}

// Load topics for a syllabus
async function loadTopics(syllabusId) {
    try {
        const response = await fetch(`/api/admin/syllabus/${syllabusId}`);
        if (!response.ok) throw new Error('Failed to load topics');
        
        const syllabus = await response.json();
        currentTopics = extractTopics(syllabus);
        renderTopicMapping();
    } catch (error) {
        showToast('Failed to load topics: ' + error.message, 'error');
    }
}

// Extract topics from syllabus
function extractTopics(syllabus) {
    const topics = [];
    syllabus.units?.forEach(unit => {
        unit.topics?.forEach(topic => {
            topics.push({
                id: topic.id,
                name: topic.name,
                unitName: unit.name,
                objectives: topic.learningObjectiveIds || []
            });
        });
    });
    return topics;
}

// Render topic mapping
function renderTopicMapping() {
    const container = document.getElementById('topicMappingList');
    container.innerHTML = currentTopics.map(topic => `
        <div class="topic-item">
            <div class="topic-header">
                <div>
                    <strong>${topic.name}</strong>
                    <small class="text-muted">${topic.unitName}</small>
                </div>
                <button class="btn btn-sm btn-outline-primary" onclick="showTopicMappingModal('${topic.id}')">
                    Map Objectives
                </button>
            </div>
            <div class="topic-objectives">
                ${renderTopicObjectives(topic.objectives)}
            </div>
        </div>
    `).join('');
}

// Render objectives for a topic
function renderTopicObjectives(objectiveIds) {
    return objectiveIds.map(id => {
        const objective = currentObjectives.find(o => o.id === id);
        return objective ? `
            <span class="objective-badge">
                ${objective.name}
            </span>
        ` : '';
    }).join('');
}

// Show create/edit objective modal
function showObjectiveModal(objectiveId = null) {
    const form = document.getElementById('objectiveForm');
    const title = document.getElementById('objectiveModalTitle');

    if (objectiveId) {
        const objective = currentObjectives.find(o => o.id === objectiveId);
        if (!objective) return;

        title.textContent = 'Edit Learning Objective';
        document.getElementById('objectiveId').value = objective.id;
        document.getElementById('objectiveName').value = objective.name;
        document.getElementById('objectiveDescription').value = objective.description || '';
        document.getElementById('objectiveLevel').value = objective.level;
        document.getElementById('objectiveWeight').value = objective.weight || 0;
    } else {
        title.textContent = 'Add Learning Objective';
        form.reset();
        document.getElementById('objectiveId').value = '';
    }

    objectiveModal.show();
}

// Save objective
async function saveObjective() {
    const objectiveId = document.getElementById('objectiveId').value;
    const objective = {
        name: document.getElementById('objectiveName').value,
        description: document.getElementById('objectiveDescription').value,
        level: document.getElementById('objectiveLevel').value,
        weight: parseInt(document.getElementById('objectiveWeight').value),
        syllabusId: currentSyllabusId
    };

    try {
        const url = objectiveId ? 
            `/api/admin/learning-objectives/${objectiveId}` :
            '/api/admin/learning-objectives';
        
        const response = await fetch(url, {
            method: objectiveId ? 'PUT' : 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(objective)
        });

        if (!response.ok) throw new Error('Failed to save objective');

        objectiveModal.hide();
        await loadObjectives(currentSyllabusId);
        showToast('Learning objective saved successfully', 'success');
    } catch (error) {
        showToast('Failed to save objective: ' + error.message, 'error');
    }
}

// Delete objective
async function deleteObjective(objectiveId) {
    if (!confirm('Are you sure you want to delete this learning objective?')) return;

    try {
        const response = await fetch(`/api/admin/learning-objectives/${objectiveId}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Failed to delete objective');

        await loadObjectives(currentSyllabusId);
        showToast('Learning objective deleted successfully', 'success');
    } catch (error) {
        showToast('Failed to delete objective: ' + error.message, 'error');
    }
}

// Show topic mapping modal
function showTopicMappingModal(topicId) {
    selectedTopicId = topicId;
    const topic = currentTopics.find(t => t.id === topicId);
    if (!topic) return;

    document.getElementById('selectedTopicName').textContent = `${topic.name} (${topic.unitName})`;
    
    const checkboxesContainer = document.getElementById('objectiveCheckboxes');
    checkboxesContainer.innerHTML = currentObjectives.map(objective => `
        <label class="list-group-item">
            <input class="form-check-input" type="checkbox" value="${objective.id}"
                   ${topic.objectives.includes(objective.id) ? 'checked' : ''}>
            <span>${objective.name}</span>
        </label>
    `).join('');

    topicMappingModal.show();
}

// Save topic mapping
async function saveTopicMapping() {
    if (!selectedTopicId) return;

    const checkboxes = document.querySelectorAll('#objectiveCheckboxes input[type="checkbox"]');
    const objectiveIds = Array.from(checkboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

    try {
        const response = await fetch(`/api/admin/learning-objectives/map/${selectedTopicId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ objectiveIds })
        });

        if (!response.ok) throw new Error('Failed to save mapping');

        topicMappingModal.hide();
        await loadTopics(currentSyllabusId);
        showToast('Topic mapping saved successfully', 'success');
    } catch (error) {
        showToast('Failed to save mapping: ' + error.message, 'error');
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    // You can implement this using Bootstrap's toast component
    // or any other notification library
    alert(message);
}

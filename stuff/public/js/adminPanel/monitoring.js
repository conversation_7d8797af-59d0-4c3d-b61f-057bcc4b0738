// Global state
let performanceChart = null;
let qualityChart = null;
let coverageChart = null;
let currentTimeRange = '7d';
let updateInterval = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    initializeCharts();
    loadInitialData();
    startAutoUpdate();
});

// Initialize charts
function initializeCharts() {
    // Performance Chart
    const perfCtx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(perfCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Response Time (ms)',
                    borderColor: '#007bff',
                    data: []
                },
                {
                    label: 'Error Rate (%)',
                    borderColor: '#dc3545',
                    data: []
                },
                {
                    label: 'Success Rate (%)',
                    borderColor: '#28a745',
                    data: []
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Quality Chart
    const qualityCtx = document.getElementById('qualityChart').getContext('2d');
    qualityChart = new Chart(qualityCtx, {
        type: 'radar',
        data: {
            labels: ['Clarity', 'Difficulty Match', 'Domain Accuracy', 'Grammar', 'Uniqueness'],
            datasets: [{
                label: 'Current Quality',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.2)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });

    // Coverage Chart
    const coverageCtx = document.getElementById('coverageChart').getContext('2d');
    coverageChart = new Chart(coverageCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Coverage (%)',
                backgroundColor: '#28a745',
                data: []
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Load initial data
async function loadInitialData() {
    try {
        // Load system metrics
        const metricsResponse = await fetch('/api/admin/monitoring/metrics');
        if (metricsResponse.ok) {
            const metrics = await metricsResponse.json();
            updateMetrics(metrics);
        }

        // Load performance data
        await updatePerformanceData();

        // Load quality metrics
        const qualityResponse = await fetch('/api/admin/monitoring/quality');
        if (qualityResponse.ok) {
            const quality = await qualityResponse.json();
            updateQualityChart(quality);
        }

        // Load coverage data
        const coverageResponse = await fetch('/api/admin/monitoring/coverage');
        if (coverageResponse.ok) {
            const coverage = await coverageResponse.json();
            updateCoverageChart(coverage);
        }

        // Load recent alerts
        await loadRecentAlerts();

        // Load recent activity
        await loadRecentActivity();

    } catch (error) {
        console.error('Failed to load monitoring data:', error);
        showToast('Failed to load monitoring data', 'error');
    }
}

// Update system metrics
function updateMetrics(metrics) {
    // Update counters
    document.getElementById('totalQuestions').textContent = metrics.totalQuestions.toLocaleString();
    document.getElementById('activeUsers').textContent = metrics.activeUsers.toLocaleString();
    document.getElementById('testsTaken').textContent = metrics.testsTaken.toLocaleString();

    // Update trends
    updateTrend('questionsTrend', metrics.questionsTrend);
    updateTrend('usersTrend', metrics.usersTrend);
    updateTrend('testsTrend', metrics.testsTrend);

    // Update health indicators
    updateHealthIndicator('cpuUsage', metrics.cpu);
    updateHealthIndicator('memoryUsage', metrics.memory);
    updateHealthIndicator('storageUsage', metrics.storage);
    updateHealthIndicator('apiLatency', metrics.apiLatency, true);
}

// Update trend indicator
function updateTrend(elementId, value) {
    const element = document.getElementById(elementId);
    const parent = element.parentElement;
    const icon = parent.querySelector('i');

    if (value > 0) {
        parent.className = 'metric-trend positive';
        icon.className = 'bi bi-arrow-up';
        element.textContent = `+${value}%`;
    } else {
        parent.className = 'metric-trend negative';
        icon.className = 'bi bi-arrow-down';
        element.textContent = `${value}%`;
    }
}

// Update health indicator
function updateHealthIndicator(elementId, value, isLatency = false) {
    const element = document.getElementById(elementId);
    const bar = document.getElementById(elementId.replace('Usage', 'Bar'));

    if (isLatency) {
        element.textContent = `${value}ms`;
        const percentage = Math.min((value / 1000) * 100, 100);
        bar.style.width = `${percentage}%`;
        bar.className = `progress-bar ${percentage > 80 ? 'danger' : percentage > 50 ? 'warning' : ''}`;
    } else {
        element.textContent = `${value}%`;
        bar.style.width = `${value}%`;
        bar.className = `progress-bar ${value > 80 ? 'danger' : value > 50 ? 'warning' : ''}`;
    }
}

// Update performance data
async function updatePerformanceData() {
    try {
        const response = await fetch(`/api/admin/monitoring/performance?timeRange=${currentTimeRange}`);
        if (!response.ok) throw new Error('Failed to load performance data');

        const data = await response.json();
        
        performanceChart.data.labels = data.timestamps;
        performanceChart.data.datasets[0].data = data.responseTime;
        performanceChart.data.datasets[1].data = data.errorRate;
        performanceChart.data.datasets[2].data = data.successRate;
        performanceChart.update();
    } catch (error) {
        console.error('Failed to update performance data:', error);
    }
}

// Update quality chart
function updateQualityChart(quality) {
    qualityChart.data.datasets[0].data = [
        quality.clarity,
        quality.difficultyMatch,
        quality.domainAccuracy,
        quality.grammar,
        quality.uniqueness
    ];
    qualityChart.update();
}

// Update coverage chart
function updateCoverageChart(coverage) {
    coverageChart.data.labels = coverage.map(item => item.name);
    coverageChart.data.datasets[0].data = coverage.map(item => item.coverage);
    coverageChart.update();
}

// Load recent alerts
async function loadRecentAlerts() {
    try {
        const response = await fetch('/api/admin/monitoring/alerts');
        if (!response.ok) throw new Error('Failed to load alerts');

        const alerts = await response.json();
        const container = document.getElementById('alertsList');

        container.innerHTML = alerts.map(alert => `
            <div class="list-group-item">
                <div class="alert-item">
                    <i class="bi ${getAlertIcon(alert.level)} alert-icon ${alert.level}"></i>
                    <div class="alert-content">
                        <div>${alert.message}</div>
                        <small class="alert-time">${formatTime(alert.timestamp)}</small>
                    </div>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Failed to load alerts:', error);
    }
}

// Load recent activity
async function loadRecentActivity() {
    try {
        const response = await fetch('/api/admin/monitoring/activity');
        if (!response.ok) throw new Error('Failed to load activity');

        const activities = await response.json();
        const container = document.getElementById('activityTable');

        container.innerHTML = activities.map(activity => `
            <tr>
                <td>${formatTime(activity.timestamp)}</td>
                <td>${activity.event}</td>
                <td>${activity.user}</td>
                <td>${activity.details}</td>
                <td>
                    <span class="status-badge ${activity.status.toLowerCase()}">
                        ${activity.status}
                    </span>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Failed to load activity:', error);
    }
}

// Helper functions
function getAlertIcon(level) {
    switch (level) {
        case 'critical':
            return 'bi-exclamation-triangle-fill';
        case 'warning':
            return 'bi-exclamation-circle-fill';
        default:
            return 'bi-info-circle-fill';
    }
}

function formatTime(timestamp) {
    return new Date(timestamp).toLocaleString();
}

// Update time range
function updateTimeRange(range) {
    currentTimeRange = range;
    
    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.textContent.toLowerCase().includes(range)) {
            btn.classList.add('active');
        }
    });

    updatePerformanceData();
}

// Refresh activity
function refreshActivity() {
    const button = document.querySelector('button[onclick="refreshActivity()"]');
    button.classList.add('refreshing');
    
    Promise.all([
        loadRecentAlerts(),
        loadRecentActivity()
    ]).finally(() => {
        button.classList.remove('refreshing');
    });
}

// Auto-update
function startAutoUpdate() {
    // Update every 30 seconds
    updateInterval = setInterval(async () => {
        const metricsResponse = await fetch('/api/admin/monitoring/metrics');
        if (metricsResponse.ok) {
            const metrics = await metricsResponse.json();
            updateMetrics(metrics);
        }

        await updatePerformanceData();
    }, 30000);
}

// Cleanup on page unload
window.addEventListener('unload', () => {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});

// Show toast notification
function showToast(message, type = 'info') {
    // You can implement this using Bootstrap's toast component
    // or any other notification library
    alert(message);
}

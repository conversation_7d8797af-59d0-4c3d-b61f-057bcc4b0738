class ExamConfigManager {
    constructor() {
        this.initializeEventListeners();
        this.loadExamConfigs();
    }

    initializeEventListeners() {
        // Create exam button
        document.getElementById('create-exam-btn').addEventListener('click', () => {
            this.showExamConfigModal();
        });

        // Save exam config
        document.getElementById('save-exam-config').addEventListener('click', () => {
            this.saveExamConfig();
        });

        // Edit exam config
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-exam-btn')) {
                const examId = e.target.dataset.examId;
                this.editExamConfig(examId);
            }
        });

        // Delete exam config
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-exam-btn')) {
                const examId = e.target.dataset.examId;
                if (confirm('Are you sure you want to delete this exam configuration?')) {
                    this.deleteExamConfig(examId);
                }
            }
        });
    }

    async loadExamConfigs() {
        try {
            const response = await fetch('/api/admin/exam-configs');
            const configs = await response.json();
            this.renderExamConfigs(configs);
        } catch (error) {
            console.error('Error loading exam configs:', error);
            this.showError('Failed to load exam configurations');
        }
    }

    renderExamConfigs(configs) {
        const container = document.getElementById('exam-configs-list');
        container.innerHTML = '';

        configs.forEach(config => {
            const examCard = document.createElement('div');
            examCard.className = 'exam-config-card';
            examCard.innerHTML = `
                <div class="card-header">
                    <h3>${config.name}</h3>
                    <div class="card-actions">
                        <button class="edit-exam-btn" data-exam-id="${config.id}">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="delete-exam-btn" data-exam-id="${config.id}">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <p><strong>Type:</strong> ${config.type}</p>
                    <p><strong>Duration:</strong> ${config.duration} minutes</p>
                    <p><strong>Total Marks:</strong> ${config.totalMarks}</p>
                    <p><strong>Subjects:</strong> ${config.subjects.join(', ')}</p>
                    <div class="config-details">
                        <div class="detail-item">
                            <h4>Question Types</h4>
                            <ul>
                                ${config.questions.types.map(type => `<li>${type}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="detail-item">
                            <h4>Features</h4>
                            <ul>
                                ${Object.entries(config.ui.features)
                                    .filter(([_, enabled]) => enabled)
                                    .map(([feature]) => `<li>${this.formatFeatureName(feature)}</li>`)
                                    .join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(examCard);
        });
    }

    formatFeatureName(feature) {
        return feature
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase());
    }

    showExamConfigModal(config = null) {
        const modal = document.getElementById('exam-config-modal');
        const form = document.getElementById('exam-config-form');

        // Reset form
        form.reset();

        if (config) {
            // Fill form with existing config
            Object.entries(config).forEach(([key, value]) => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = value;
                    } else if (Array.isArray(value)) {
                        input.value = value.join(',');
                    } else {
                        input.value = value;
                    }
                }
            });
            form.dataset.examId = config.id;
        } else {
            delete form.dataset.examId;
        }

        modal.style.display = 'block';
    }

    async saveExamConfig() {
        const form = document.getElementById('exam-config-form');
        const formData = new FormData(form);
        const config = {};

        formData.forEach((value, key) => {
            if (key.includes('.')) {
                // Handle nested properties (e.g., questions.types)
                const [parent, child] = key.split('.');
                config[parent] = config[parent] || {};
                config[parent][child] = this.parseFormValue(value);
            } else {
                config[key] = this.parseFormValue(value);
            }
        });

        try {
            const url = form.dataset.examId ? 
                `/api/admin/exam-configs/${form.dataset.examId}` :
                '/api/admin/exam-configs';
            
            const response = await fetch(url, {
                method: form.dataset.examId ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            if (!response.ok) {
                throw new Error('Failed to save exam configuration');
            }

            // Close modal and reload configs
            document.getElementById('exam-config-modal').style.display = 'none';
            this.loadExamConfigs();
            this.showSuccess('Exam configuration saved successfully');
        } catch (error) {
            console.error('Error saving exam config:', error);
            this.showError('Failed to save exam configuration');
        }
    }

    parseFormValue(value) {
        if (value === 'true') return true;
        if (value === 'false') return false;
        if (value.includes(',')) return value.split(',').map(v => v.trim());
        if (!isNaN(value) && value !== '') return Number(value);
        return value;
    }

    async editExamConfig(examId) {
        try {
            const response = await fetch(`/api/admin/exam-configs/${examId}`);
            const config = await response.json();
            this.showExamConfigModal(config);
        } catch (error) {
            console.error('Error loading exam config:', error);
            this.showError('Failed to load exam configuration');
        }
    }

    async deleteExamConfig(examId) {
        try {
            const response = await fetch(`/api/admin/exam-configs/${examId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error('Failed to delete exam configuration');
            }

            this.loadExamConfigs();
            this.showSuccess('Exam configuration deleted successfully');
        } catch (error) {
            console.error('Error deleting exam config:', error);
            this.showError('Failed to delete exam configuration');
        }
    }

    showSuccess(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success';
        alert.textContent = message;
        this.showAlert(alert);
    }

    showError(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger';
        alert.textContent = message;
        this.showAlert(alert);
    }

    showAlert(alert) {
        const container = document.getElementById('alert-container');
        container.appendChild(alert);
        setTimeout(() => alert.remove(), 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.examConfigManager = new ExamConfigManager();
});

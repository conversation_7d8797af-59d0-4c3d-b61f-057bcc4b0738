// Global state
let dailyPerformanceChart = null;
let sourceDistributionChart = null;
let userProgress<PERSON>hart = null;
let userTopics<PERSON>hart = null;
let topicProgressChart = null;
let topicSourceChart = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    initializeCharts();
    setupNavigation();
    loadOverviewData();
});

// Initialize charts
function initializeCharts() {
    // Daily performance chart
    const dailyCtx = document.getElementById('dailyPerformanceChart').getContext('2d');
    dailyPerformanceChart = new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Past Year Questions',
                    data: [],
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true
                },
                {
                    label: 'Generated Questions',
                    data: [],
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => `${value}%`
                    }
                }
            }
        }
    });

    // Source distribution chart
    const sourceCtx = document.getElementById('sourceDistributionChart').getContext('2d');
    sourceDistributionChart = new Chart(sourceCtx, {
        type: 'doughnut',
        data: {
            labels: ['Past Year', 'Generated'],
            datasets: [{
                data: [0, 0],
                backgroundColor: ['#0d6efd', '#198754']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Navigation handling
function setupNavigation() {
    document.querySelectorAll('[data-view]').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            showView(e.target.dataset.view);
        });
    });
}

function showView(viewName) {
    // Hide all views
    document.querySelectorAll('.view-section').forEach(section => {
        section.classList.add('d-none');
    });

    // Show selected view
    document.getElementById(viewName).classList.remove('d-none');

    // Load view data
    switch (viewName) {
        case 'overview':
            loadOverviewData();
            break;
        case 'users':
            loadUserData();
            break;
        case 'topics':
            loadTopicData();
            break;
    }

    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.dataset.view === viewName) {
            link.classList.add('active');
        }
    });
}

// Data loading functions
async function loadOverviewData() {
    try {
        const response = await fetch('/api/admin/analytics/overview');
        if (!response.ok) throw new Error('Failed to load overview data');

        const data = await response.json();

        // Update stat cards
        document.getElementById('totalQuestions').textContent = data.totalQuestions;
        document.getElementById('averageAccuracy').textContent = `${data.averageAccuracy.toFixed(1)}%`;
        document.getElementById('activeUsers').textContent = data.activeUsers;
        document.getElementById('topicsCovered').textContent = data.topicsCovered;

        // Update progress bars
        updateProgressBar('totalQuestions', data.totalQuestions, data.maxQuestions);
        updateProgressBar('averageAccuracy', data.averageAccuracy, 100);
        updateProgressBar('activeUsers', data.activeUsers, data.totalUsers);
        updateProgressBar('topicsCovered', data.topicsCovered, data.totalTopics);

        // Update charts
        updateDailyPerformanceChart(data.dailyPerformance);
        updateSourceDistributionChart(data.sourceDistribution);
    } catch (error) {
        console.error('Failed to load overview data:', error);
        showToast('Failed to load overview data', 'error');
    }
}

async function loadUserData(page = 1, search = '') {
    try {
        const response = await fetch(`/api/admin/analytics/users?page=${page}&search=${search}`);
        if (!response.ok) throw new Error('Failed to load user data');

        const data = await response.json();
        updateUserTable(data.users);
        updatePagination('userPagination', data.totalPages, page, loadUserData);
    } catch (error) {
        console.error('Failed to load user data:', error);
        showToast('Failed to load user data', 'error');
    }
}

async function loadTopicData(page = 1, search = '') {
    try {
        const response = await fetch(`/api/admin/analytics/topics?page=${page}&search=${search}`);
        if (!response.ok) throw new Error('Failed to load topic data');

        const data = await response.json();
        updateTopicTable(data.topics);
        updatePagination('topicPagination', data.totalPages, page, loadTopicData);
    } catch (error) {
        console.error('Failed to load topic data:', error);
        showToast('Failed to load topic data', 'error');
    }
}

// Chart update functions
function updateDailyPerformanceChart(data) {
    dailyPerformanceChart.data.labels = data.dates;
    dailyPerformanceChart.data.datasets[0].data = data.pastYearAccuracy;
    dailyPerformanceChart.data.datasets[1].data = data.generatedAccuracy;
    dailyPerformanceChart.update();
}

function updateSourceDistributionChart(data) {
    sourceDistributionChart.data.datasets[0].data = [
        data.pastYear,
        data.generated
    ];
    sourceDistributionChart.update();
}

// Table update functions
function updateUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.totalAttempts}</td>
            <td>${user.accuracy.toFixed(1)}%</td>
            <td>${user.topicsCovered}</td>
            <td>${formatDate(user.lastActive)}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="showUserDetails('${user.id}')">
                    Details
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateTopicTable(topics) {
    const tbody = document.getElementById('topicTableBody');
    tbody.innerHTML = '';

    topics.forEach(topic => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${topic.name}</td>
            <td>${topic.totalQuestions}</td>
            <td>${topic.averageAccuracy.toFixed(1)}%</td>
            <td>${topic.pastYearAccuracy.toFixed(1)}%</td>
            <td>${topic.generatedAccuracy.toFixed(1)}%</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="showTopicDetails('${topic.id}')">
                    Details
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Modal functions
async function showUserDetails(userId) {
    try {
        const response = await fetch(`/api/admin/analytics/user/${userId}`);
        if (!response.ok) throw new Error('Failed to load user details');

        const data = await response.json();
        updateUserDetailsModal(data);
        new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
    } catch (error) {
        console.error('Failed to load user details:', error);
        showToast('Failed to load user details', 'error');
    }
}

async function showTopicDetails(topicId) {
    try {
        const response = await fetch(`/api/admin/analytics/topic/${topicId}`);
        if (!response.ok) throw new Error('Failed to load topic details');

        const data = await response.json();
        updateTopicDetailsModal(data);
        new bootstrap.Modal(document.getElementById('topicDetailsModal')).show();
    } catch (error) {
        console.error('Failed to load topic details:', error);
        showToast('Failed to load topic details', 'error');
    }
}

function updateUserDetailsModal(data) {
    // Update progress chart
    if (userProgressChart) userProgressChart.destroy();
    const progressCtx = document.getElementById('userProgressChart').getContext('2d');
    userProgressChart = new Chart(progressCtx, {
        type: 'line',
        data: {
            labels: data.daily_performance.map(d => d.date),
            datasets: [{
                label: 'Accuracy',
                data: data.daily_performance.map(d => d.accuracy),
                borderColor: '#0d6efd',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Update topics chart
    if (userTopicsChart) userTopicsChart.destroy();
    const topicsCtx = document.getElementById('userTopicsChart').getContext('2d');
    userTopicsChart = new Chart(topicsCtx, {
        type: 'radar',
        data: {
            labels: data.topic_performance.map(t => t.topic),
            datasets: [{
                label: 'Topic Accuracy',
                data: data.topic_performance.map(t => t.accuracy),
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                borderColor: '#0d6efd'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Update history table
    const tbody = document.getElementById('userHistoryBody');
    tbody.innerHTML = data.daily_performance.map(day => `
        <tr>
            <td>${formatDate(day.date)}</td>
            <td>${day.attempts}</td>
            <td>${day.accuracy.toFixed(1)}%</td>
            <td>${day.topics.join(', ')}</td>
        </tr>
    `).join('');
}

function updateTopicDetailsModal(data) {
    // Update progress chart
    if (topicProgressChart) topicProgressChart.destroy();
    const progressCtx = document.getElementById('topicProgressChart').getContext('2d');
    topicProgressChart = new Chart(progressCtx, {
        type: 'line',
        data: {
            labels: data.daily_stats.map(d => d.date),
            datasets: [{
                label: 'Accuracy',
                data: data.daily_stats.map(d => d.accuracy),
                borderColor: '#0d6efd',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Update source chart
    if (topicSourceChart) topicSourceChart.destroy();
    const sourceCtx = document.getElementById('topicSourceChart').getContext('2d');
    topicSourceChart = new Chart(sourceCtx, {
        type: 'pie',
        data: {
            labels: ['Past Year', 'Generated'],
            datasets: [{
                data: [data.past_year_count, data.generated_count],
                backgroundColor: ['#0d6efd', '#198754']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Update questions table
    const tbody = document.getElementById('topicQuestionsBody');
    tbody.innerHTML = data.questions.map(q => `
        <tr>
            <td>${q.text}</td>
            <td>${q.source}</td>
            <td>${q.attempts}</td>
            <td>${q.accuracy.toFixed(1)}%</td>
        </tr>
    `).join('');
}

// Utility functions
function updateProgressBar(elementId, value, max) {
    const percentage = (value / max) * 100;
    const progressBar = document.querySelector(`#${elementId} + .progress .progress-bar`);
    progressBar.style.width = `${percentage}%`;
}

function updatePagination(elementId, totalPages, currentPage, callback) {
    const pagination = document.getElementById(elementId);
    pagination.innerHTML = '';

    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `
            <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(${i})">
                ${i}
            </a>
        `;
        pagination.appendChild(li);
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container');
    const toast = document.createElement('div');
    toast.className = `toast ${type} show`;
    toast.innerHTML = `
        <div class="toast-header">
            <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
        <div class="toast-body">${message}</div>
    `;
    toastContainer.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}

// Search handling
document.getElementById('userSearch').addEventListener('input', debounce((e) => {
    loadUserData(1, e.target.value);
}, 300));

document.getElementById('topicSearch').addEventListener('input', debounce((e) => {
    loadTopicData(1, e.target.value);
}, 300));

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

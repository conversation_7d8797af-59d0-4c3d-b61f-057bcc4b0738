/**
 * Exam Manager Module
 * Handles exam configuration management in the admin panel
 */

class ExamManager {
  constructor() {
    this.examListContainer = document.getElementById('exam-list') || this.createExamListContainer();
    this.configCache = new Map();
    this.init();
  }

  /**
   * Initialize exam manager
   */
  async init() {
    this.attachEventListeners();
    await this.loadExams();
  }

  /**
   * Create exam list container if not exists
   */
  createExamListContainer() {
    const container = document.createElement('div');
    container.id = 'exam-list';
    container.className = 'space-y-4 p-4';
    document.querySelector('.admin-content').appendChild(container);
    return container;
  }

  /**
   * Attach event listeners
   */
  attachEventListeners() {
    // Add exam button
    const addButton = document.createElement('button');
    addButton.className = 'bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors';
    addButton.textContent = 'Add New Exam';
    addButton.onclick = () => this.showExamForm();
    
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center mb-6';
    header.innerHTML = '<h2 class="text-2xl font-bold">Exam Management</h2>';
    header.appendChild(addButton);
    
    this.examListContainer.parentElement.insertBefore(header, this.examListContainer);
  }

  /**
   * Load and display exams
   */
  async loadExams() {
    try {
      const response = await fetch('/api/admin/exams');
      if (!response.ok) throw new Error('Failed to load exams');
      
      const exams = await response.json();
      this.renderExamList(exams);
      
    } catch (error) {
      console.error('Error loading exams:', error);
      this.showError('Failed to load exams. Please try again.');
    }
  }

  /**
   * Render exam list
   */
  renderExamList(exams) {
    this.examListContainer.innerHTML = exams.length ? '' : 
      '<p class="text-gray-500 text-center py-8">No exams configured yet. Click "Add New Exam" to create one.</p>';
    
    exams.forEach(exam => {
      const examCard = document.createElement('div');
      examCard.className = 'bg-white rounded-lg shadow-md p-6 mb-4';
      examCard.innerHTML = `
        <div class="flex justify-between items-start">
          <div>
            <h3 class="text-xl font-semibold mb-2">${exam.name}</h3>
            <p class="text-gray-600 mb-4">
              ${exam.subjects.length} subjects | ${exam.defaultQuestions} questions | ${exam.timeLimit} minutes
            </p>
            <div class="flex flex-wrap gap-2">
              ${exam.features.adaptiveDifficulty ? 
                '<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Adaptive</span>' : ''}
              ${exam.features.theoryExplanation ? 
                '<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Theory</span>' : ''}
              ${exam.features.challengeAnswer ? 
                '<span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">Challenge</span>' : ''}
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="edit-btn text-blue-500 hover:text-blue-700">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button class="delete-btn text-red-500 hover:text-red-700">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      `;
      
      // Attach event listeners
      examCard.querySelector('.edit-btn').onclick = () => this.showExamForm(exam);
      examCard.querySelector('.delete-btn').onclick = () => this.deleteExam(exam.id);
      
      this.examListContainer.appendChild(examCard);
    });
  }

  /**
   * Show exam form modal
   */
  showExamForm(exam = null) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4';
    modal.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <h3 class="text-xl font-semibold mb-4">${exam ? 'Edit' : 'Add'} Exam</h3>
        <form id="exam-form" class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-1">Exam Name</label>
            <input type="text" name="name" class="w-full border rounded-md p-2" 
              value="${exam?.name || ''}" required>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-1">Subjects (comma-separated)</label>
            <input type="text" name="subjects" class="w-full border rounded-md p-2" 
              value="${exam?.subjects.join(', ') || ''}" required>
          </div>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Default Questions</label>
              <input type="number" name="defaultQuestions" class="w-full border rounded-md p-2" 
                value="${exam?.defaultQuestions || 30}" required min="1">
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Time Limit (minutes)</label>
              <input type="number" name="timeLimit" class="w-full border rounded-md p-2" 
                value="${exam?.timeLimit || 45}" required min="1">
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-1">Features</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" name="adaptiveDifficulty" class="mr-2"
                  ${exam?.features.adaptiveDifficulty ? 'checked' : ''}>
                Adaptive Difficulty
              </label>
              <label class="flex items-center">
                <input type="checkbox" name="theoryExplanation" class="mr-2"
                  ${exam?.features.theoryExplanation ? 'checked' : ''}>
                Theory Explanation
              </label>
              <label class="flex items-center">
                <input type="checkbox" name="challengeAnswer" class="mr-2"
                  ${exam?.features.challengeAnswer ? 'checked' : ''}>
                Challenge Answer
              </label>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" class="cancel-btn px-4 py-2 border rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
              ${exam ? 'Update' : 'Create'} Exam
            </button>
          </div>
        </form>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Handle form submission
    const form = modal.querySelector('#exam-form');
    form.onsubmit = async (e) => {
      e.preventDefault();
      const formData = new FormData(form);
      
      const examData = {
        id: exam?.id,
        name: formData.get('name'),
        subjects: formData.get('subjects').split(',').map(s => s.trim()).filter(Boolean),
        defaultQuestions: parseInt(formData.get('defaultQuestions')),
        timeLimit: parseInt(formData.get('timeLimit')),
        features: {
          adaptiveDifficulty: formData.get('adaptiveDifficulty') === 'on',
          theoryExplanation: formData.get('theoryExplanation') === 'on',
          challengeAnswer: formData.get('challengeAnswer') === 'on'
        }
      };
      
      try {
        const response = await fetch('/api/admin/exams', {
          method: exam ? 'PUT' : 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(examData)
        });
        
        if (!response.ok) throw new Error('Failed to save exam');
        
        await this.loadExams();
        modal.remove();
        this.showSuccess(`Exam ${exam ? 'updated' : 'created'} successfully!`);
        
      } catch (error) {
        console.error('Error saving exam:', error);
        this.showError('Failed to save exam. Please try again.');
      }
    };
    
    // Handle cancel
    modal.querySelector('.cancel-btn').onclick = () => modal.remove();
  }

  /**
   * Delete exam
   */
  async deleteExam(examId) {
    if (!confirm('Are you sure you want to delete this exam?')) return;
    
    try {
      const response = await fetch(`/api/admin/exams/${examId}`, { method: 'DELETE' });
      if (!response.ok) throw new Error('Failed to delete exam');
      
      await this.loadExams();
      this.showSuccess('Exam deleted successfully!');
      
    } catch (error) {
      console.error('Error deleting exam:', error);
      this.showError('Failed to delete exam. Please try again.');
    }
  }

  /**
   * Show success message
   */
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  /**
   * Show error message
   */
  showError(message) {
    this.showNotification(message, 'error');
  }

  /**
   * Show notification
   */
  showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white ${
      type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
  }
}

// Initialize exam manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.examManager = new ExamManager();
});

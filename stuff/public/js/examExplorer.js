// Exam Explorer Module
document.addEventListener('DOMContentLoaded', () => {
    const examExplorerContainer = document.getElementById('exam-explorer-container');
    let allExamsData = []; // To store fetched exam data
    let filters = {
        governance: 'all',
        level: 'all',
        subject: 'all',
        state: 'all'
    };

    // Initial load
    if (examExplorerContainer) {
        loadExamExplorer();
    }

    async function loadExamExplorer() {
        showLoader(examExplorerContainer);
        try {
            // In a real app, fetch this from an API or a static JSON file
            // For now, using a placeholder similar to the original web.html structure
            allExamsData = await getPlaceholderExamData(); 
            renderExamExplorerUI();
            applyFilters();
        } catch (error) {
            console.error('Error loading exam explorer:', error);
            showError(examExplorerContainer, 'Could not load exam data. Please try again later.');
        }
    }

    function renderExamExplorerUI() {
        examExplorerContainer.innerHTML = `
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Explore Exams</h2>
                <!-- Filters -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 bg-white rounded-lg shadow">
                    ${createFilterGroup('governance', 'Governance', ['all', 'Central', 'State'])}
                    ${createFilterGroup('level', 'Level', ['all', 'Primary', 'Secondary', 'Higher Secondary', 'University'])}
                    ${createFilterGroup('subject', 'Subject', ['all', 'Physics', 'Chemistry', 'Mathematics', 'Biology', 'History', 'Geography', 'English', 'General Knowledge'])}
                    <div>
                        <label for="state-selector" class="block text-sm font-medium text-gray-700 mb-1">State</label>
                        <select id="state-selector" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <!-- Options will be populated by populateStates() -->
                        </select>
                    </div>
                </div>
            </div>
            <div id="cards-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Exam cards will be rendered here -->
            </div>
        `;

        populateStates();
        addFilterEventListeners();
    }

    function createFilterGroup(id, label, options) {
        return `
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">${label}</label>
                <div id="${id}-filter" class="flex flex-wrap gap-2">
                    ${options.map(option => `
                        <button data-filter="${id}" data-value="${option.toLowerCase()}" 
                                class="filter-btn px-3 py-1.5 text-sm font-medium rounded-md 
                                       ${option === 'all' ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}">
                            ${option}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
    }

    function populateStates() {
        const stateSelector = document.getElementById('state-selector');
        if (!stateSelector) return;

        const states = [
            'all', 'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat', 
            'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh', 'Maharashtra', 
            'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 
            'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal', 
            'Andaman and Nicobar Islands', 'Chandigarh', 'Dadra and Nagar Haveli and Daman and Diu', 
            'Lakshadweep', 'Delhi (NCT)', 'Puducherry', 'Ladakh', 'Jammu and Kashmir'
        ];
        
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.toLowerCase();
            option.textContent = state === 'all' ? 'All States' : state;
            stateSelector.appendChild(option);
        });
        stateSelector.value = filters.state;
    }

    function addFilterEventListeners() {
        document.querySelectorAll('.filter-btn').forEach(button => {
            button.addEventListener('click', handleFilterClick);
        });
        document.getElementById('state-selector').addEventListener('change', (e) => {
            filters.state = e.target.value;
            applyFilters();
        });
        document.getElementById('cards-container').addEventListener('click', (e) => {
            const button = e.target.closest('.view-details-btn');
            if (button) {
                openModal(button.dataset.id);
            }
        });
    }

    function handleFilterClick(e) {
        const button = e.target;
        const filterType = button.dataset.filter;
        const value = button.dataset.value;

        filters[filterType] = value;

        // Update active class for buttons in the same group
        document.querySelectorAll(`#${filterType}-filter .filter-btn`).forEach(btn => {
            btn.classList.remove('bg-indigo-600', 'text-white');
            btn.classList.add('bg-gray-200', 'text-gray-700', 'hover:bg-gray-300');
        });
        button.classList.add('bg-indigo-600', 'text-white');
        button.classList.remove('bg-gray-200', 'text-gray-700', 'hover:bg-gray-300');

        applyFilters();
    }

    function applyFilters() {
        const cardsContainer = document.getElementById('cards-container');
        if (!cardsContainer) return;

        showLoader(cardsContainer);

        const filteredExams = allExamsData.filter(exam => {
            return (filters.governance === 'all' || exam.governance.toLowerCase() === filters.governance) &&
                   (filters.level === 'all' || exam.level.toLowerCase() === filters.level) &&
                   (filters.subject === 'all' || exam.subjects.map(s => s.toLowerCase()).includes(filters.subject)) &&
                   (filters.state === 'all' || exam.state.toLowerCase() === filters.state);
        });

        renderExamCards(filteredExams);
    }

    function renderExamCards(exams) {
        const cardsContainer = document.getElementById('cards-container');
        cardsContainer.innerHTML = ''; // Clear previous cards

        if (exams.length === 0) {
            cardsContainer.innerHTML = '<p class="text-gray-600 col-span-full text-center">No exams found matching your criteria.</p>';
            return;
        }

        exams.forEach(exam => {
            const card = `
                <div class="exam-card bg-white rounded-lg shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="h-40 bg-indigo-500 flex items-center justify-center">
                        <svg class="w-16 h-16 text-white opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">${exam.name}</h3>
                        <p class="text-sm text-gray-600 mb-1"><span class="font-medium">Governance:</span> ${exam.governance}</p>
                        <p class="text-sm text-gray-600 mb-1"><span class="font-medium">Level:</span> ${exam.level}</p>
                        <p class="text-sm text-gray-600 mb-1"><span class="font-medium">State:</span> ${exam.state}</p>
                        <p class="text-sm text-gray-600 mb-3"><span class="font-medium">Subjects:</span> ${exam.subjects.join(', ')}</p>
                        <button class="view-details-btn w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors duration-300" data-id="${exam.id}">
                            View Details
                        </button>
                    </div>
                </div>
            `;
            cardsContainer.innerHTML += card;
        });
    }

    function openModal(examId) {
        const exam = allExamsData.find(e => e.id === examId);
        if (!exam) return;

        const modalContainer = document.getElementById('modal-container');
        const modalContent = document.getElementById('modal-content');

        modalContent.innerHTML = `
            <div class="p-6 md:p-8">
                <div class="flex justify-between items-start mb-4">
                    <h2 class="text-2xl font-bold text-gray-800">${exam.name}</h2>
                    <button id="close-modal-btn" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Governance</p>
                        <p class="text-lg text-gray-800 font-medium">${exam.governance}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Level</p>
                        <p class="text-lg text-gray-800 font-medium">${exam.level}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">State</p>
                        <p class="text-lg text-gray-800 font-medium">${exam.state}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Subjects</p>
                        <p class="text-lg text-gray-800 font-medium">${exam.subjects.join(', ')}</p>
                    </div>
                </div>
                
                <div class="mb-6">
                    <p class="text-sm text-gray-500 mb-1">Description</p>
                    <p class="text-gray-700">${exam.description || 'No description available.'}</p>
                </div>

                ${exam.syllabus_link ? `
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-700 mb-2">Syllabus</h4>
                    <a href="${exam.syllabus_link}" target="_blank" class="text-indigo-600 hover:text-indigo-800 hover:underline break-all">
                        View Syllabus: ${exam.syllabus_link}
                    </a>
                </div>` : ''}

                ${exam.previous_year_papers && exam.previous_year_papers.length > 0 ? `
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-700 mb-2">Previous Year Question Papers</h4>
                    <ul class="list-disc list-inside text-sm text-gray-700">
                        ${exam.previous_year_papers.map(paper => `
                            <li>
                                <a href="${paper.link}" target="_blank" class="text-indigo-600 hover:text-indigo-800 hover:underline break-all">
                                    ${paper.year} - ${paper.name || 'Question Paper'}
                                </a>
                            </li>
                        `).join('')}
                    </ul>
                </div>` : ''}

                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-md font-semibold text-gray-700 mb-3">AI Practice Questions</h4>
                    <p class="text-sm text-gray-600 mb-3">Generate AI-powered practice questions for this exam.</p>
                    <button id="generate-practice-questions-btn" data-exam-subject="${exam.subjects[0]}" data-exam-level="${exam.level}" 
                            class="w-full md:w-auto bg-green-600 text-white py-2 px-6 rounded-md hover:bg-green-700 transition-colors duration-300">
                        Generate Practice Questions
                    </button>
                    <div id="ai-questions-output" class="mt-4"></div>
                </div>
            </div>
        `;

        modalContainer.classList.remove('hidden');
        document.body.classList.add('overflow-hidden'); // Prevent background scroll

        document.getElementById('close-modal-btn').addEventListener('click', closeModal);
        modalContainer.addEventListener('click', (e) => { // Close on overlay click
            if (e.target === modalContainer) closeModal();
        });
        
        const genPracQBtn = document.getElementById('generate-practice-questions-btn');
        if(genPracQBtn) {
            genPracQBtn.addEventListener('click', handleGeneratePracticeQuestions);
        }
    }

    function closeModal() {
        const modalContainer = document.getElementById('modal-container');
        modalContainer.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
        // Clean up modal content to prevent duplicate event listeners if reopened quickly
        document.getElementById('modal-content').innerHTML = ''; 
    }

    async function handleGeneratePracticeQuestions(event) {
        const subject = event.target.dataset.examSubject;
        const level = event.target.dataset.examLevel;
        const outputDiv = document.getElementById('ai-questions-output');
        
        showLoader(outputDiv);

        try {
            const data = await apiGet('questions/generate', {
                subject: subject,
                level: level,
                numQuestions: 3 // Generate a small number for preview
            });

            if (data.questions && data.questions.length > 0) {
                outputDiv.innerHTML = `
                    <h5 class="text-sm font-semibold text-gray-700 mt-4 mb-2">Generated Questions:</h5>
                    <ul class="list-disc list-inside text-sm text-gray-600 space-y-2">
                        ${data.questions.map(q => `<li>${q.question}</li>`).join('')}
                    </ul>
                    <p class="text-xs text-gray-500 mt-3">Note: These are sample questions. Start a mock test for a full experience.</p>
                `;
            } else {
                showError(outputDiv, 'No questions were generated. Please try again.');
            }
        } catch (error) {
            console.error('Error generating practice questions:', error);
            showError(outputDiv, `Failed to generate questions: ${error.message}`);
        }
    }

    // Placeholder data function (replace with actual API call)
    async function getPlaceholderExamData() {
        // This data structure is based on the original web.html's 'exams' array
        return [
            {
                id: 'kvs_pgt_physics',
                name: 'KVS PGT Physics',
                governance: 'Central',
                level: 'Higher Secondary',
                state: 'All India',
                subjects: ['Physics'],
                description: 'Kendriya Vidyalaya Sangathan Post Graduate Teacher exam for Physics.',
                syllabus_link: 'https://kvsangathan.nic.in/sites/default/files/hq/PGT_PHYSICS_2.pdf',
                previous_year_papers: [
                    { year: 2023, link: '#' },
                    { year: 2022, link: '#' }
                ]
            },
            {
                id: 'navodaya_tgt_maths',
                name: 'Navodaya TGT Mathematics',
                governance: 'Central',
                level: 'Secondary',
                state: 'All India',
                subjects: ['Mathematics'],
                description: 'Navodaya Vidyalaya Samiti Trained Graduate Teacher exam for Mathematics.',
                syllabus_link: '#',
                previous_year_papers: []
            },
            {
                id: 'state_tet_paper1',
                name: 'State TET Paper 1 (Primary)',
                governance: 'State',
                level: 'Primary',
                state: 'Various States',
                subjects: ['Child Development', 'Mathematics', 'Environmental Studies', 'Language I', 'Language II'],
                description: 'State Teacher Eligibility Test for primary school teachers.',
                syllabus_link: '#',
                previous_year_papers: []
            }
            // Add more exam objects here
        ];
    }
});

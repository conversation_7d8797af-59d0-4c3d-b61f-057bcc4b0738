// Authentication Module
document.addEventListener('DOMContentLoaded', () => {
  const userAuthContainer = document.getElementById('user-auth');
  
  // Initialize auth state
  initAuth();
  
  /**
   * Initialize authentication
   */
  function initAuth() {
    // Listen for auth state changes
    auth.onAuthStateChanged(user => {
      if (user) {
        // User is signed in
        currentUser = user;
        userId = user.uid;
        renderUserProfile(user);
      } else {
        // User is signed out
        currentUser = null;
        userId = null;
        renderSignInButton();
      }
      
      // Dispatch auth state change event
      document.dispatchEvent(new CustomEvent('authStateChanged', { 
        detail: { user: currentUser, userId } 
      }));
    });
  }
  
  /**
   * Render user profile when signed in
   */
  function renderUserProfile(user) {
    userAuthContainer.innerHTML = `
      <div class="flex items-center">
        <div class="mr-3">
          <p class="text-sm font-medium text-gray-700">${user.displayName || 'User'}</p>
          <p class="text-xs text-gray-500">${user.email || 'Anonymous'}</p>
        </div>
        <div class="relative">
          <button id="user-menu-btn" class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 hover:bg-indigo-200 focus:outline-none">
            ${user.displayName ? user.displayName.charAt(0).toUpperCase() : 'U'}
          </button>
          <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
            <a href="#" id="profile-link" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
            <a href="#" id="settings-link" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
            <a href="#" id="sign-out-btn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
          </div>
        </div>
      </div>
    `;
    
    // Add event listeners
    document.getElementById('user-menu-btn').addEventListener('click', toggleUserMenu);
    document.getElementById('sign-out-btn').addEventListener('click', signOut);
  }
  
  /**
   * Render sign in button when not signed in
   */
  function renderSignInButton() {
    userAuthContainer.innerHTML = `
      <button id="sign-in-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none">
        Sign In
      </button>
    `;
    
    // Add event listener
    document.getElementById('sign-in-btn').addEventListener('click', signInAnonymously);
  }
  
  /**
   * Toggle user dropdown menu
   */
  function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('hidden');
    
    // Close menu when clicking outside
    const closeMenu = (e) => {
      if (!e.target.closest('#user-menu-btn') && !e.target.closest('#user-dropdown')) {
        dropdown.classList.add('hidden');
        document.removeEventListener('click', closeMenu);
      }
    };
    
    if (!dropdown.classList.contains('hidden')) {
      setTimeout(() => {
        document.addEventListener('click', closeMenu);
      }, 0);
    }
  }
  
  /**
   * Sign in anonymously
   */
  async function signInAnonymously() {
    try {
      await auth.signInAnonymously();
    } catch (error) {
      console.error('Error signing in:', error);
      alert(`Sign in failed: ${error.message}`);
    }
  }
  
  /**
   * Sign out current user
   */
  async function signOut() {
    try {
      await auth.signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      alert(`Sign out failed: ${error.message}`);
    }
  }
});

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Mock Test System - Admin Panel</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-50 min-h-screen">
  <div class="flex flex-col h-screen">
    <!-- Header -->
    <header class="bg-indigo-600 text-white p-4 shadow-md">
      <div class="container mx-auto flex justify-between items-center">
        <h1 class="text-2xl font-bold">AI Mock Test System - Admin Panel</h1>
        <div id="user-auth" class="flex items-center"></div>
      </div>
    </header>
    
    <!-- Main Content -->
    <main class="flex-grow container mx-auto p-4">
      <div class="flex flex-col md:flex-row gap-6">
        <!-- Sidebar -->
        <aside class="md:w-64 bg-white rounded-lg shadow-md p-4 h-fit">
          <nav>
            <ul>
              <li><a href="#" class="admin-nav-item active" data-section="dashboard">Dashboard</a></li>
              <li><a href="#" class="admin-nav-item" data-section="syllabi">Syllabus Management</a></li>
              <li><a href="#" class="admin-nav-item" data-section="questions">Question Management</a></li>
              <li><a href="#" class="admin-nav-item" data-section="llm-monitor">LLM Usage Monitoring</a></li>
              <li><a href="#" class="admin-nav-item" data-section="users">User Management</a></li>
              <li><a href="#" class="admin-nav-item" data-section="exams">Exam Management</a></li>
              <li><a href="#" class="admin-nav-item" data-section="settings">System Settings</a></li>
              <li><a href="index.html" class="text-indigo-600 hover:text-indigo-800 mt-8 block">Back to Main App</a></li>
            </ul>
          </nav>
        </aside>
        
        <!-- Content Area -->
        <div id="admin-content" class="flex-grow bg-white rounded-lg shadow-md p-6">
          <!-- Dynamic content will be loaded here -->
          <div id="loading" class="hidden">
            <div class="flex justify-center">
              <div class="loader"></div>
            </div>
          </div>
          
          <!-- Dashboard Section -->
          <section id="dashboard-section" class="admin-section">
            <h2 class="text-2xl font-bold mb-6">Dashboard</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-indigo-50 p-4 rounded-lg">
                <h3 class="font-bold text-indigo-700">Active Users</h3>
                <p class="text-3xl font-bold" id="active-users-count">--</p>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <h3 class="font-bold text-green-700">Tests Taken</h3>
                <p class="text-3xl font-bold" id="tests-count">--</p>
              </div>
              <div class="bg-purple-50 p-4 rounded-lg">
                <h3 class="font-bold text-purple-700">Questions Generated</h3>
                <p class="text-3xl font-bold" id="questions-count">--</p>
              </div>
            </div>
            
            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">Recent Test Sessions</h3>
                <div id="recent-tests-table"></div>
              </div>
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">LLM API Usage</h3>
                <canvas id="llm-usage-chart" height="200"></canvas>
              </div>
            </div>
          </section>
          
          <!-- Syllabus Management Section -->
          <section id="syllabi-section" class="admin-section hidden">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold">Syllabus Management</h2>
              <button id="create-syllabus-btn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Create New Syllabus</button>
            </div>
            
            <div id="syllabi-list-container" class="border rounded-lg overflow-hidden">
              <!-- Syllabi will be loaded here -->
            </div>
            
            <div id="syllabus-detail-container" class="mt-6 hidden">
              <!-- Selected syllabus details will be shown here -->
            </div>
            
            <div id="syllabus-form-container" class="mt-6 hidden">
              <!-- Syllabus form for creation/editing will be shown here -->
            </div>
          </section>
          
          <!-- Question Management Section -->
          <section id="questions-section" class="admin-section hidden">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold">Question Management</h2>
              <div>
                <button id="generate-questions-btn" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 mr-2">Generate Questions</button>
                <button id="create-question-btn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Create Question</button>
              </div>
            </div>
            
            <div class="mb-6 bg-gray-50 p-4 rounded-lg">
              <h3 class="font-bold mb-3">Filter Questions</h3>
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Subject</label>
                  <select id="question-filter-subject" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500">
                    <option value="">All Subjects</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Level</label>
                  <select id="question-filter-level" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500">
                    <option value="">All Levels</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Question Type</label>
                  <select id="question-filter-type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500">
                    <option value="">All Types</option>
                    <option value="multiple-choice">Multiple Choice</option>
                    <option value="fill-in-blank">Fill in the Blank</option>
                    <option value="true-false">True/False</option>
                    <option value="short-answer">Short Answer</option>
                  </select>
                </div>
                <div class="flex items-end">
                  <button id="apply-question-filters" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Apply Filters</button>
                </div>
              </div>
            </div>
            
            <div id="questions-list-container" class="border rounded-lg overflow-hidden">
              <!-- Questions will be loaded here -->
            </div>
            
            <div id="question-detail-container" class="mt-6 hidden">
              <!-- Selected question details will be shown here -->
            </div>
            
            <div id="question-form-container" class="mt-6 hidden">
              <!-- Question form for creation/editing will be shown here -->
            </div>
          </section>
          
          <!-- LLM Monitoring Section -->
          <section id="llm-monitor-section" class="admin-section hidden">
            <h2 class="text-2xl font-bold mb-6">LLM Usage Monitoring</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div class="bg-indigo-50 p-4 rounded-lg">
                <h3 class="font-bold text-indigo-700">Total API Calls</h3>
                <p class="text-3xl font-bold" id="total-api-calls">--</p>
              </div>
              <div class="bg-yellow-50 p-4 rounded-lg">
                <h3 class="font-bold text-yellow-700">Total Tokens Used</h3>
                <p class="text-3xl font-bold" id="total-tokens">--</p>
              </div>
              <div class="bg-red-50 p-4 rounded-lg">
                <h3 class="font-bold text-red-700">Estimated Cost</h3>
                <p class="text-3xl font-bold" id="estimated-cost">--</p>
              </div>
            </div>
            
            <div class="flex mb-4 space-x-2">
              <button class="period-filter bg-indigo-100 px-4 py-2 rounded-md active" data-period="daily">Daily</button>
              <button class="period-filter bg-indigo-100 px-4 py-2 rounded-md" data-period="weekly">Weekly</button>
              <button class="period-filter bg-indigo-100 px-4 py-2 rounded-md" data-period="monthly">Monthly</button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">Usage Over Time</h3>
                <canvas id="llm-usage-time-chart" height="250"></canvas>
              </div>
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">Usage by Operation</h3>
                <canvas id="llm-usage-operation-chart" height="250"></canvas>
              </div>
            </div>
            
            <div class="mt-6 bg-white border rounded-lg p-4">
              <h3 class="font-bold mb-4">Usage Alerts Configuration</h3>
              <form id="alerts-config-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">Daily Cost Limit ($)</label>
                  <input type="number" id="daily-cost-limit" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="0.01" min="0">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Daily Token Limit</label>
                  <input type="number" id="daily-token-limit" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="1000" min="0">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700">Daily API Call Limit</label>
                  <input type="number" id="daily-call-limit" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" step="10" min="0">
                </div>
                <div class="md:col-span-3">
                  <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Save Alert Settings</button>
                </div>
              </form>
            </div>
          </section>
          
          <!-- User Management Section -->
          <section id="users-section" class="admin-section hidden">
            <h2 class="text-2xl font-bold mb-6">User Management</h2>
            <div id="users-list-container" class="border rounded-lg overflow-hidden">
              <!-- Users will be loaded here -->
            </div>
          </section>
          
          <!-- Settings Section -->
          <section id="settings-section" class="admin-section hidden">
            <h2 class="text-2xl font-bold mb-6">System Settings</h2>
            <form id="system-settings-form" class="space-y-6">
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">API Configuration</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Google Gemini API Key</label>
                    <input type="password" id="gemini-api-key" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Default LLM Model</label>
                    <select id="default-llm-model" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                      <option value="gemini-pro">Gemini Pro</option>
                      <option value="gpt-4">GPT-4</option>
                      <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div class="bg-white border rounded-lg p-4">
                <h3 class="font-bold mb-4">Question Generation Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Default Questions Per Test</label>
                    <input type="number" id="default-question-count" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" min="5" max="50">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Default Time Per Question (seconds)</label>
                    <input type="number" id="default-time-per-question" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" min="30" max="300">
                  </div>
                </div>
              </div>
              
              <div>
                <button type="submit" class="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700">Save Settings</button>
              </div>
            </form>
          </section>
        </div>
      </div>
              <!-- Exam Management Section -->
          <section id="exams-section" class="admin-section hidden">
            <div id="exam-list-container">
              <!-- ExamManager will populate this -->
            </div>
          </section>
        </main>
    
    <!-- Footer -->
    <footer class="bg-gray-800 text-white p-4 text-center">
      <p>&copy; 2025 AI Mock Test System. All rights reserved.</p>
    </footer>
  </div>
  
    <!-- Admin Panel Modules -->
  <script src="js/adminPanel/examManager.js"></script>
  <script src="js/adminPanel/monitoringDashboard.js"></script>

  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.0/firebase-firestore-compat.js"></script>
  
  <!-- Chart.js for analytics -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
  
  <!-- App Scripts -->
  <script src="/js/config.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/adminPanel.js"></script>
</body>
</html>

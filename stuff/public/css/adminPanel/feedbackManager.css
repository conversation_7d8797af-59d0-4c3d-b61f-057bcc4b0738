/* Review Queue */
.list-group-item {
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    border-left-color: #007bff;
}

.list-group-item.high-priority {
    border-left-color: #dc3545;
}

.list-group-item .badge {
    font-size: 0.8rem;
}

/* Statistics */
.stats-item {
    margin-bottom: 1.5rem;
}

.stats-item label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.stats-item h3 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
}

.stats-chart {
    height: 200px;
}

/* Review History */
.table th {
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.pending {
    background-color: #ffc107;
    color: #000;
}

.status-badge.approved {
    background-color: #28a745;
    color: #fff;
}

.status-badge.rejected {
    background-color: #dc3545;
    color: #fff;
}

/* Review Modal */
.review-content {
    max-height: 70vh;
    overflow-y: auto;
}

.options-list {
    margin: 1rem 0;
}

.options-list .option {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.options-list .option.correct {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.suggested-changes {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.suggested-changes .change-item {
    margin-bottom: 1rem;
}

.suggested-changes .change-item:last-child {
    margin-bottom: 0;
}

/* Buttons and Controls */
.btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-refresh {
    margin-right: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .stats-item {
        margin-bottom: 1rem;
    }

    .stats-item h3 {
        font-size: 1.5rem;
    }

    .review-content {
        max-height: 80vh;
    }
}

/* Loading States */
.loading {
    position: relative;
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1.5rem;
    height: 1.5rem;
    margin: -0.75rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

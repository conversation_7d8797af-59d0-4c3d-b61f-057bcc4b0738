/* Card animations */
.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Progress bars */
.progress {
    height: 4px;
    margin-top: 10px;
}

.progress-bar {
    transition: width 1s ease-in-out;
}

/* Charts */
canvas {
    max-height: 300px;
}

/* Table styles */
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Search inputs */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    border-color: #80bdff;
}

/* Modal animations */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

/* Toast container */
.toast-container {
    z-index: 1050;
}

.toast {
    opacity: 0;
    transition: opacity 0.3s;
}

.toast.show {
    opacity: 1;
}

/* View transitions */
.view-section {
    opacity: 0;
    transition: opacity 0.3s;
}

.view-section:not(.d-none) {
    opacity: 1;
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    transition: all 0.2s;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Card stats */
.card h2 {
    font-size: 2rem;
    font-weight: 600;
    margin: 10px 0;
    color: #0d6efd;
}

.card-title {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading states */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card h2 {
        font-size: 1.5rem;
    }

    .table-responsive {
        max-height: 400px;
        overflow-y: auto;
    }

    canvas {
        max-height: 200px;
    }
}

/* Print styles */
@media print {
    .navbar,
    .modal,
    .toast-container {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        border: none;
        box-shadow: none;
    }

    canvas {
        max-height: none;
    }
}

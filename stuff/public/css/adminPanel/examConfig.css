/* Exam Configuration Admin Panel Styles */

/* Container */
.admin-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Header */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
}

/* Exam Config Cards */
.exam-configs-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.exam-config-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.exam-config-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-actions button {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s ease;
}

.card-actions button:hover {
    color: #2c3e50;
}

.edit-exam-btn:hover {
    color: #007bff;
}

.delete-exam-btn:hover {
    color: #dc3545;
}

.card-body {
    padding: 1rem;
}

.card-body p {
    margin: 0.5rem 0;
    color: #495057;
}

.config-details {
    margin-top: 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.detail-item h4 {
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.detail-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.detail-item li {
    color: #495057;
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
}

.modal-content {
    background: white;
    margin: 2% auto;
    padding: 2rem;
    width: 90%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h2 {
    margin: 0;
    color: #2c3e50;
}

.close {
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close:hover {
    color: #2c3e50;
}

/* Form */
.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

/* Alerts */
#alert-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
}

.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }

    .exam-configs-list {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
    }
}

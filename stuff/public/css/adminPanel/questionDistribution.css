/* Card and Container Styling */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Unit and Topic Styling */
.unit-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    background-color: #fff;
}

.unit-header {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.unit-content {
    padding: 1rem;
}

.topic-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    padding: 1rem;
    background-color: #fff;
}

.topic-header {
    margin-bottom: 0.5rem;
}

/* Form Controls */
.form-label {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.difficulty-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 0.5rem;
}

.difficulty-label {
    min-width: 80px;
    font-size: 0.875rem;
    color: #6c757d;
}

.difficulty-input {
    width: 80px;
}

/* Distribution Preview */
.distribution-preview {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 1rem;
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1rem;
}

/* Table Styling */
.distribution-table {
    width: 100%;
    margin-bottom: 1rem;
}

.distribution-table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

.distribution-table td,
.distribution-table th {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
}

.distribution-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    margin-top: 0.25rem;
}

.progress-label {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Validation States */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
}

.is-invalid + .invalid-feedback {
    display: block;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .difficulty-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .difficulty-label {
        min-width: auto;
    }

    .difficulty-input {
        width: 100%;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltips */
.tooltip-icon {
    color: #6c757d;
    cursor: help;
    margin-left: 0.25rem;
}

/* Chart Legend */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-color {
    width: 1rem;
    height: 1rem;
    border-radius: 0.25rem;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-card {
    padding: 1rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.875rem;
}

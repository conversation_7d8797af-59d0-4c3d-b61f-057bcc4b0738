<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AI Mock Test System</title>
    <link rel="stylesheet" href="/css/styles.css">
    <style>
        .offline-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 20px;
        }

        .offline-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .offline-title {
            font-size: 24px;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .offline-message {
            font-size: 16px;
            margin-bottom: 24px;
            color: var(--text-secondary);
        }

        .retry-button {
            padding: 12px 24px;
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .retry-button:hover {
            background-color: var(--accent-color-dark);
        }

        .offline-features {
            margin-top: 40px;
            text-align: left;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-item {
            margin: 12px 0;
            display: flex;
            align-items: center;
        }

        .feature-icon {
            margin-right: 12px;
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">Don't worry! You can still access your saved mock tests and continue studying.</p>
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>

        <div class="offline-features">
            <h2>Available Offline Features:</h2>
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="feature-icon">✓</span>
                    Access previously loaded mock tests
                </li>
                <li class="feature-item">
                    <span class="feature-icon">✓</span>
                    Submit answers (will sync when online)
                </li>
                <li class="feature-item">
                    <span class="feature-icon">✓</span>
                    Track your progress locally
                </li>
                <li class="feature-item">
                    <span class="feature-icon">✓</span>
                    Review your previous answers
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status periodically
        setInterval(() => {
            if (navigator.onLine) {
                window.location.reload();
            }
        }, 5000);

        // Update theme based on user preference
        const theme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', theme);
    </script>
</body>
</html>

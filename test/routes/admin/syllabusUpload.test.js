import { expect } from 'chai';
import sinon from 'sinon';
import request from 'supertest';
import app from '../../../server/app.js';
import { mockDb, mockLoggingService } from '../../helpers/testHelper.js';
import syllabusValidationService from '../../../server/services/syllabusValidationService.js';

describe('Syllabus Upload Routes', () => {
    let sandbox;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        sandbox.stub(mockLoggingService, 'logInfo');
        sandbox.stub(mockLoggingService, 'logError');
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('POST /api/admin/syllabus/bulk', () => {
        it('should handle JSON file upload successfully', async () => {
            const validSyllabus = {
                name: 'Test Syllabus',
                subject: 'Mathematics',
                level: 'undergraduate',
                units: [
                    {
                        name: 'Unit 1',
                        topics: [
                            {
                                name: 'Topic 1',
                                learning_objectives: ['Objective 1'],
                                keywords: ['keyword1']
                            }
                        ]
                    }
                ]
            };

            // Mock validation service
            sandbox.stub(syllabusValidationService, 'validateSyllabus').returns({
                errors: [],
                warnings: []
            });

            // Mock batch operations
            const batchCommitStub = sandbox.stub().resolves();
            const batchStub = {
                set: sandbox.stub(),
                commit: batchCommitStub
            };
            sandbox.stub(mockDb, 'batch').returns(batchStub);
            sandbox.stub(mockDb, 'collection').returns({
                doc: () => ({ id: 'test-id' })
            });

            const response = await request(app)
                .post('/api/admin/syllabus/bulk')
                .attach('file', Buffer.from(JSON.stringify(validSyllabus)), {
                    filename: 'test.json',
                    contentType: 'application/json'
                });

            expect(response.status).to.equal(200);
            expect(response.body.message).to.equal('Bulk upload completed');
            expect(batchCommitStub.calledOnce).to.be.true;
            expect(mockLoggingService.logInfo.calledWith('Bulk syllabus upload completed')).to.be.true;
        });

        it('should reject invalid syllabus structure', async () => {
            const invalidSyllabus = {
                name: 'Test',
                // Missing required fields
            };

            // Mock validation service to return errors
            sandbox.stub(syllabusValidationService, 'validateSyllabus').returns({
                errors: ['Subject is required', 'Invalid level'],
                warnings: []
            });

            const response = await request(app)
                .post('/api/admin/syllabus/bulk')
                .attach('file', Buffer.from(JSON.stringify(invalidSyllabus)), {
                    filename: 'test.json',
                    contentType: 'application/json'
                });

            expect(response.status).to.equal(400);
            expect(response.body.error).to.equal('Validation failed');
            expect(response.body.results[0].errors).to.include('Subject is required');
        });

        it('should handle missing file', async () => {
            const response = await request(app)
                .post('/api/admin/syllabus/bulk');

            expect(response.status).to.equal(400);
            expect(response.body.error).to.equal('No file uploaded');
        });

        it('should handle invalid file type', async () => {
            const response = await request(app)
                .post('/api/admin/syllabus/bulk')
                .attach('file', Buffer.from('test'), {
                    filename: 'test.txt',
                    contentType: 'text/plain'
                });

            expect(response.status).to.equal(400);
            expect(response.body.error).to.include('Invalid file type');
        });

        it('should handle database errors', async () => {
            const validSyllabus = {
                name: 'Test Syllabus',
                subject: 'Mathematics',
                level: 'undergraduate',
                units: []
            };

            // Mock validation service
            sandbox.stub(syllabusValidationService, 'validateSyllabus').returns({
                errors: [],
                warnings: []
            });

            // Mock database error
            const error = new Error('Database error');
            sandbox.stub(mockDb, 'batch').throws(error);

            const response = await request(app)
                .post('/api/admin/syllabus/bulk')
                .attach('file', Buffer.from(JSON.stringify(validSyllabus)), {
                    filename: 'test.json',
                    contentType: 'application/json'
                });

            expect(response.status).to.equal(500);
            expect(response.body.error).to.include('Failed to process upload');
            expect(mockLoggingService.logError.calledWith(error)).to.be.true;
        });
    });
});

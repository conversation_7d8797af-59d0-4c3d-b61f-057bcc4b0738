import { expect } from 'chai';
import sinon from 'sinon';
import syllabusCacheService from '../../server/services/syllabusCacheService.js';
import { mockDb, mockLoggingService } from '../helpers/testHelper.js';
import cacheService from '../../server/services/cacheService.js';

describe('SyllabusCacheService', () => {
    let sandbox;
    const testSyllabus = {
        id: 'test-id',
        name: 'Test Syllabus',
        subject: 'Mathematics',
        level: 'undergraduate',
        units: [
            {
                id: 'unit-1',
                name: 'Unit 1',
                order: 1,
                topics: [
                    {
                        id: 'topic-1',
                        name: 'Topic 1',
                        learning_objectives: ['Objective 1'],
                        keywords: ['keyword1']
                    }
                ]
            }
        ]
    };

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        sandbox.stub(mockLoggingService, 'logInfo');
        sandbox.stub(mockLoggingService, 'logError');
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('getSyllabus', () => {
        it('should return cached syllabus if available', async () => {
            sandbox.stub(cacheService, 'get').resolves(testSyllabus);
            
            const result = await syllabusCacheService.getSyllabus('test-id');
            
            expect(result).to.deep.equal(testSyllabus);
            expect(mockLoggingService.logInfo.calledWith('Syllabus cache hit')).to.be.true;
        });

        it('should fetch from database if not cached', async () => {
            sandbox.stub(cacheService, 'get').resolves(null);
            sandbox.stub(cacheService, 'set').resolves();
            
            const docStub = {
                exists: true,
                data: () => testSyllabus
            };
            
            sandbox.stub(mockDb, 'collection').returns({
                doc: () => ({
                    get: () => Promise.resolve(docStub)
                })
            });

            const result = await syllabusCacheService.getSyllabus('test-id');
            
            expect(result).to.deep.equal(testSyllabus);
            expect(mockLoggingService.logInfo.calledWith('Syllabus cached')).to.be.true;
        });

        it('should return null for non-existent syllabus', async () => {
            sandbox.stub(cacheService, 'get').resolves(null);
            
            const docStub = {
                exists: false
            };
            
            sandbox.stub(mockDb, 'collection').returns({
                doc: () => ({
                    get: () => Promise.resolve(docStub)
                })
            });

            const result = await syllabusCacheService.getSyllabus('non-existent');
            
            expect(result).to.be.null;
        });

        it('should handle database errors', async () => {
            sandbox.stub(cacheService, 'get').resolves(null);
            
            const error = new Error('Database error');
            sandbox.stub(mockDb, 'collection').throws(error);

            try {
                await syllabusCacheService.getSyllabus('test-id');
                expect.fail('Should have thrown error');
            } catch (err) {
                expect(err).to.equal(error);
                expect(mockLoggingService.logError.calledWith(error)).to.be.true;
            }
        });
    });

    describe('getQuestionContext', () => {
        it('should return cached context if available', async () => {
            const context = {
                syllabus: { name: 'Test' },
                unit: { name: 'Unit 1' },
                topic: { name: 'Topic 1' }
            };
            
            sandbox.stub(cacheService, 'get').resolves(context);
            
            const result = await syllabusCacheService.getQuestionContext('test-id', 'unit-1', 'topic-1');
            
            expect(result).to.deep.equal(context);
            expect(mockLoggingService.logInfo.calledWith('Question context cache hit')).to.be.true;
        });

        it('should build and cache context if not cached', async () => {
            sandbox.stub(cacheService, 'get').resolves(null);
            sandbox.stub(cacheService, 'set').resolves();
            sandbox.stub(syllabusCacheService, 'getSyllabus').resolves(testSyllabus);

            const result = await syllabusCacheService.getQuestionContext('test-id', 'unit-1', 'topic-1');
            
            expect(result).to.deep.equal({
                syllabus: {
                    name: testSyllabus.name,
                    subject: testSyllabus.subject,
                    level: testSyllabus.level
                },
                unit: {
                    name: testSyllabus.units[0].name,
                    order: testSyllabus.units[0].order
                },
                topic: {
                    name: testSyllabus.units[0].topics[0].name,
                    learning_objectives: testSyllabus.units[0].topics[0].learning_objectives,
                    keywords: testSyllabus.units[0].topics[0].keywords
                }
            });
            expect(mockLoggingService.logInfo.calledWith('Question context cached')).to.be.true;
        });

        it('should handle invalid unit/topic IDs', async () => {
            sandbox.stub(cacheService, 'get').resolves(null);
            sandbox.stub(syllabusCacheService, 'getSyllabus').resolves(testSyllabus);

            try {
                await syllabusCacheService.getQuestionContext('test-id', 'invalid-unit', 'topic-1');
                expect.fail('Should have thrown error');
            } catch (err) {
                expect(err.message).to.equal('Unit not found');
            }

            try {
                await syllabusCacheService.getQuestionContext('test-id', 'unit-1', 'invalid-topic');
                expect.fail('Should have thrown error');
            } catch (err) {
                expect(err.message).to.equal('Topic not found');
            }
        });
    });
});

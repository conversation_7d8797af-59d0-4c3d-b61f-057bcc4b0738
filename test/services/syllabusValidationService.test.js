import { expect } from 'chai';
import sinon from 'sinon';
import syllabusValidationService from '../../server/services/syllabusValidationService.js';
import { mockLoggingService } from '../helpers/testHelper.js';

describe('SyllabusValidationService', () => {
    let sandbox;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe('validateSyllabus', () => {
        it('should validate a valid syllabus without errors', () => {
            const validSyllabus = {
                name: 'Test Syllabus',
                subject: 'Mathematics',
                level: 'undergraduate',
                units: [
                    {
                        name: 'Unit 1',
                        topics: [
                            {
                                name: 'Topic 1',
                                learning_objectives: ['Objective 1'],
                                keywords: ['keyword1']
                            }
                        ]
                    }
                ]
            };

            const result = syllabusValidationService.validateSyllabus(validSyllabus);
            expect(result.errors).to.be.empty;
            expect(result.warnings).to.be.empty;
        });

        it('should detect missing required fields', () => {
            const invalidSyllabus = {
                subject: 'Mathematics',
                units: []
            };

            const result = syllabusValidationService.validateSyllabus(invalidSyllabus);
            expect(result.errors).to.include('Name is required');
            expect(result.errors).to.include('Invalid level');
        });

        it('should validate unit structure', () => {
            const syllabusWithInvalidUnit = {
                name: 'Test',
                subject: 'Math',
                level: 'undergraduate',
                units: [
                    {
                        topics: []
                    }
                ]
            };

            const result = syllabusValidationService.validateSyllabus(syllabusWithInvalidUnit);
            expect(result.errors).to.include('Unit 1: Name is required');
        });

        it('should validate topic structure', () => {
            const syllabusWithInvalidTopic = {
                name: 'Test',
                subject: 'Math',
                level: 'undergraduate',
                units: [
                    {
                        name: 'Unit 1',
                        topics: [
                            {
                                learning_objectives: [],
                                keywords: []
                            }
                        ]
                    }
                ]
            };

            const result = syllabusValidationService.validateSyllabus(syllabusWithInvalidTopic);
            expect(result.errors).to.include('Unit 1, Topic 1: Name is required');
            expect(result.warnings).to.include('Unit 1, Topic 1: No learning objectives defined');
            expect(result.warnings).to.include('Unit 1, Topic 1: No keywords defined');
        });

        it('should detect duplicate keywords', () => {
            const syllabusWithDuplicates = {
                name: 'Test',
                subject: 'Math',
                level: 'undergraduate',
                units: [
                    {
                        name: 'Unit 1',
                        topics: [
                            {
                                name: 'Topic 1',
                                learning_objectives: ['Objective 1'],
                                keywords: ['keyword1', 'keyword1']
                            }
                        ]
                    }
                ]
            };

            const result = syllabusValidationService.validateSyllabus(syllabusWithDuplicates);
            expect(result.warnings).to.include('Unit 1, Topic 1: Contains duplicate keywords');
        });
    });
});

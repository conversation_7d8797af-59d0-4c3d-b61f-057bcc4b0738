import { expect } from 'chai';
import sinon from 'sinon';
import questionGenerationService from '../../server/services/questionGenerationService.js';
import llmService from '../../server/services/llmService.js';
import questionAnalysisService from '../../server/services/questionAnalysisService.js';
import cacheService from '../../server/services/cacheService.js';
import { mockLoggingService, mockDb, testData } from '../helpers/testHelper.js';

// Mock services
sinon.stub(questionGenerationService, 'loggingService').value(mockLoggingService);
sinon.stub(questionGenerationService, 'db').value(mockDb);

describe('QuestionGenerationService', () => {
  let sandbox;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generateQuestions', () => {
    it('should generate questions successfully', async () => {
      const mockQuestions = [
        {
          id: '1',
          question: 'Test question?',
          options: ['A', 'B', 'C', 'D'],
          correct_answer: 'A',
          type: 'multiple_choice',
          explanation: 'This is the explanation for the correct answer.'
        }
      ];

      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium',
        numQuestions: 1,
        questionType: 'multiple_choice'
      };

      sandbox.stub(llmService, 'generateWithOptimalModel').resolves(mockQuestions);
      sandbox.stub(questionAnalysisService, 'validateSyllabusAlignment').resolves({ isValid: true });
      sandbox.stub(cacheService, 'get').returns(null);
      sandbox.stub(cacheService, 'set');

      const result = await questionGenerationService.generateQuestions(params);

      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(1);
      expect(result[0]).to.have.property('question', 'Test question?');
      expect(result[0]).to.have.property('type', 'multiple_choice');
    });

    it('should return cached questions when available', async () => {
      const cachedQuestions = [
        {
          id: '1',
          question: 'Cached question?',
          options: ['A', 'B', 'C', 'D'],
          correct_answer: 'A',
          type: 'multiple_choice'
        }
      ];

      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium',
        numQuestions: 1,
        questionType: 'multiple_choice'
      };

      sandbox.stub(cacheService, 'get').returns(cachedQuestions);
      sandbox.stub(llmService, 'generateWithOptimalModel');

      const result = await questionGenerationService.generateQuestions(params);

      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(1);
      expect(result[0]).to.have.property('question', 'Cached question?');
      expect(llmService.generateWithOptimalModel.called).to.be.false;
    });

    it('should handle syllabus-based question generation', async () => {
      const mockQuestions = [
        {
          id: '1',
          question: 'Syllabus question?',
          options: ['A', 'B', 'C', 'D'],
          correct_answer: 'A',
          type: 'multiple_choice',
          explanation: 'This is a test explanation',
          quality_score: 0.85,
          keywords: ['algebra', 'equations'],
          cognitive_level: 'understand',
          learning_objective: 'Solve linear equations',
          difficulty: 'medium'
        }
      ];

      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium',
        numQuestions: 1,
        questionType: 'multiple_choice',
        syllabusId: 'syllabus1',
        unitId: 'unit1',
        topicId: 'topic1'
      };

      sandbox.stub(llmService, 'generateWithOptimalModel').resolves(mockQuestions);
      sandbox.stub(questionAnalysisService, 'validateSyllabusAlignment').resolves({ isValid: true });
      sandbox.stub(cacheService, 'get').returns(null);
      sandbox.stub(cacheService, 'set');

      const result = await questionGenerationService.generateQuestions(params);

      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(1);
      expect(result[0]).to.have.property('syllabusId', 'syllabus1');
      expect(result[0]).to.have.property('unitId', 'unit1');
      expect(result[0]).to.have.property('topicId', 'topic1');
    });

    it('should filter out invalid questions based on syllabus alignment', async () => {
      const mockQuestions = [
        {
          id: '1',
          question: 'Valid question?',
          options: ['A', 'B', 'C', 'D'],
          correct_answer: 'A',
          type: 'multiple_choice',
          explanation: 'This is a valid explanation.',
          quality_score: 0.85,
          keywords: ['algebra', 'equations'],
          cognitive_level: 'understand',
          learning_objective: 'Solve linear equations',
          difficulty: 'medium'
        },
        {
          id: '2',
          question: 'Invalid question?',
          options: ['A', 'B', 'C', 'D'],
          correct_answer: 'B',
          type: 'multiple_choice',
          explanation: 'This is an invalid explanation.',
          quality_score: 0.45,
          keywords: ['algebra'],
          cognitive_level: 'remember',
          learning_objective: 'Basic algebra',
          difficulty: 'easy'
        }
      ];

      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium',
        numQuestions: 2,
        questionType: 'multiple_choice',
        syllabusId: 'syllabus1',
        unitId: 'unit1',
        topicId: 'topic1'
      };

      sandbox.stub(llmService, 'generateWithOptimalModel').resolves(mockQuestions);
      const validateStub = sandbox.stub(questionAnalysisService, 'validateSyllabusAlignment');
      validateStub.onFirstCall().resolves({ isValid: true });
      validateStub.onSecondCall().resolves({ isValid: false });
      sandbox.stub(cacheService, 'get').returns(null);
      sandbox.stub(cacheService, 'set');

      const result = await questionGenerationService.generateQuestions(params);

      expect(result).to.be.an('array');
      expect(result).to.have.lengthOf(1);
      expect(result[0]).to.have.property('question', 'Valid question?');
    });

    it('should handle errors gracefully', async () => {
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium',
        numQuestions: 1,
        questionType: 'multiple_choice'
      };

      sandbox.stub(llmService, 'generateWithOptimalModel').rejects(new Error('LLM error'));
      sandbox.stub(cacheService, 'get').returns(null);

      try {
        await questionGenerationService.generateQuestions(params);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.an('error');
        expect(error.message).to.equal('LLM error');
      }
    });
  });

  // Add more test suites for other methods
  describe('generateMultipleChoice', () => {
    // Test cases for multiple choice questions
  });

  describe('generateFillInBlanks', () => {
    // Test cases for fill in blanks questions
  });

  describe('generateTrueFalse', () => {
    // Test cases for true/false questions
  });

  describe('generateShortAnswer', () => {
    // Test cases for short answer questions
  });
});

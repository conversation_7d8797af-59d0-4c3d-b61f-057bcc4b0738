import { expect } from 'chai';
import sinon from 'sinon';
import cacheService from '../../server/services/cacheService.js';
import { mockLoggingService } from '../helpers/testHelper.js';

// Mock logging service
sinon.stub(cacheService, 'loggingService').value(mockLoggingService);

describe('CacheService', () => {
  let sandbox;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    await cacheService.clear();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('cache operations', () => {
    it('should set and get items correctly', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      cacheService.set(type, params, value);
      const result = cacheService.get(type, params);

      expect(result).to.deep.equal(value);
    });

    it('should return null for missing items', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };

      const result = cacheService.get(type, params);
      expect(result).to.be.null;
    });

    it('should delete items correctly', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      cacheService.set(type, params, value);
      cacheService.delete(type, params);
      const result = cacheService.get(type, params);

      expect(result).to.be.null;
    });

    it('should clear all items', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      cacheService.set(type, params, value);
      cacheService.clear();

      const result = cacheService.get(type, params);
      expect(result).to.be.null;
      expect(cacheService.getStats().keys).to.equal(0);
    });
  });

  describe('metrics tracking', () => {
    it('should track hits correctly', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      cacheService.set(type, params, value);
      const beforeHits = cacheService.metrics.hits;

      cacheService.get(type, params);
      expect(cacheService.metrics.hits).to.equal(beforeHits + 1);
    });

    it('should track misses correctly', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };

      const beforeMisses = cacheService.metrics.misses;
      cacheService.get(type, params);

      expect(cacheService.metrics.misses).to.equal(beforeMisses + 1);
    });

    it('should calculate hit rate correctly', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      // Create 3 hits and 1 miss
      cacheService.set(type, params, value);
      cacheService.get(type, params);
      cacheService.get(type, params);
      cacheService.get(type, params);
      cacheService.get(type, { ...params, topic: 'Geometry' });

      const stats = cacheService.getStats();
      expect(stats.hitRate).to.equal(0.75);
    });
  });

  describe('key generation', () => {
    it('should generate consistent keys', () => {
      const type = 'multiple_choice';
      const params1 = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const params2 = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };

      const key1 = cacheService.generateKey(type, params1);
      const key2 = cacheService.generateKey(type, params2);

      expect(key1).to.equal(key2);
    });

    it('should handle missing optional parameters', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math'
      };

      const key = cacheService.generateKey(type, params);
      expect(key).to.equal('multiple_choice:Math::::::');
    });
  });

  describe('logging', () => {
    beforeEach(() => {
      sandbox.stub(loggingService, 'logInfo');
    });

    it('should log cache hits', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };
      const value = { id: '1', question: 'Test?' };

      cacheService.set(type, params, value);
      cacheService.get(type, params);

      expect(loggingService.logInfo.calledWith('Cache hit')).to.be.true;
    });

    it('should log cache misses', () => {
      const type = 'multiple_choice';
      const params = {
        subject: 'Math',
        topic: 'Algebra',
        level: 'medium'
      };

      cacheService.get(type, params);

      expect(loggingService.logInfo.calledWith('Cache miss')).to.be.true;
    });
  });
});

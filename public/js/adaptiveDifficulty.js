/**
 * Module for handling adaptive difficulty functionality
 */
class AdaptiveDifficultyManager {
    constructor() {
        this.apiEndpoint = '/api/difficulty';
        this.currentSkillLevels = null;
    }

    /**
     * Get adaptive questions based on user's skill level
     */
    async getAdaptiveQuestions(params) {
        try {
            const response = await fetch(`${this.apiEndpoint}/adaptive-questions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(params)
            });

            if (!response.ok) throw new Error('Failed to get adaptive questions');
            const data = await response.json();
            return data.questions;
        } catch (error) {
            console.error('Error getting adaptive questions:', error);
            throw error;
        }
    }

    /**
     * Update user ratings after test completion
     */
    async updateRatings(testResults) {
        try {
            const response = await fetch(`${this.apiEndpoint}/update-ratings`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({ testResults })
            });

            if (!response.ok) throw new Error('Failed to update ratings');
            const data = await response.json();
            this.currentSkillLevels = data.skills;
            return data.skills;
        } catch (error) {
            console.error('Error updating ratings:', error);
            throw error;
        }
    }

    /**
     * Get user's current skill levels
     */
    async getSkillLevels() {
        try {
            const response = await fetch(`${this.apiEndpoint}/skill-levels`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) throw new Error('Failed to get skill levels');
            const data = await response.json();
            this.currentSkillLevels = data.skillLevels;
            return data.skillLevels;
        } catch (error) {
            console.error('Error getting skill levels:', error);
            throw error;
        }
    }

    /**
     * Get difficulty label for display
     */
    getDifficultyLabel(rating) {
        if (rating <= 1200) return { label: 'Easy', class: 'text-success' };
        if (rating <= 1800) return { label: 'Medium', class: 'text-warning' };
        return { label: 'Hard', class: 'text-danger' };
    }

    /**
     * Format skill level data for display
     */
    formatSkillLevel(skillData) {
        if (!skillData) return 'Not Available';
        const { rating, total_questions, correct_answers } = skillData;
        const { label, class: colorClass } = this.getDifficultyLabel(rating);
        const accuracy = total_questions ? Math.round((correct_answers / total_questions) * 100) : 0;
        
        return {
            rating,
            difficulty: label,
            colorClass,
            accuracy: `${accuracy}%`,
            totalQuestions: total_questions || 0,
            correctAnswers: correct_answers || 0
        };
    }

    /**
     * Update UI with current skill levels
     */
    updateSkillLevelsUI() {
        if (!this.currentSkillLevels) return;

        const container = document.getElementById('skill-levels-container');
        if (!container) return;

        let html = '<div class="row">';
        
        for (const [topic, data] of Object.entries(this.currentSkillLevels)) {
            const stats = this.formatSkillLevel(data);
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${topic.replace('_', ' / ')}</h5>
                            <p class="card-text">
                                <span class="${stats.colorClass}">Level: ${stats.difficulty}</span><br>
                                Rating: ${stats.rating}<br>
                                Accuracy: ${stats.accuracy}<br>
                                Questions Attempted: ${stats.totalQuestions}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }

        html += '</div>';
        container.innerHTML = html;
    }
}

// Create global instance
const adaptiveDifficultyManager = new AdaptiveDifficultyManager();
export default adaptiveDifficultyManager;

import { showToast } from './utils.js';

// Analytics Module
document.addEventListener('DOMContentLoaded', () => {
    const analyticsContainer = document.getElementById('analytics-container');
    
    // State variables
    let userPerformanceData = null;
    let subjectPerformanceData = null;
    let testHistoryData = null;
    let charts = {};

    // Initialize analytics if container exists
    if (analyticsContainer) {
        renderAnalyticsUI();
        setupEventListeners();
        loadUserAnalytics();
    }

    function renderAnalyticsUI() {
        analyticsContainer.innerHTML = `
            <div class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Your Performance Analytics</h2>
                
                <div class="mb-4 flex justify-end">
                    <select id="time-range" class="form-select px-4 py-2 rounded-md border-gray-300">
                        <option value="7d">Last 7 Days</option>
                        <option value="30d" selected>Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                        <option value="180d">Last 180 Days</option>
                    </select>
                </div>
                
                <div id="analytics-loading" class="text-center py-8">
                    <div class="loader inline-block"></div>
                    <p class="mt-2 text-gray-600">Loading your analytics...</p>
                </div>
                
                <div id="analytics-content" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-indigo-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Tests Taken</p>
                            <p id="tests-count" class="text-2xl font-bold text-indigo-600">0</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Average Score</p>
                            <p id="average-score" class="text-2xl font-bold text-green-600">0%</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Questions Answered</p>
                            <p id="questions-count" class="text-2xl font-bold text-purple-600">0</p>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-2">Performance by Subject</h3>
                        <div class="bg-white border border-gray-200 rounded-md p-4">
                            <canvas id="subject-performance-chart" height="200"></canvas>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-2">Progress Over Time</h3>
                        <div class="bg-white border border-gray-200 rounded-md p-4">
                            <canvas id="progress-chart" height="200"></canvas>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-800 mb-2">Recent Test History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time Taken</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="test-history-table-body" class="bg-white divide-y divide-gray-200">
                                    <!-- Test history rows will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                        <div id="no-tests-message" class="text-center py-4 text-gray-500 hidden">
                            No test history available. Take a test to see your performance!
                        </div>
                    </div>
                </div>
                
                <div id="analytics-error" class="hidden text-center py-8">
                    <p class="text-red-500">Could not load analytics data. Please try again later.</p>
                    <button id="retry-analytics-btn" class="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Retry</button>
                </div>
            </div>
        `;
    }

    function setupEventListeners() {
        document.getElementById('time-range').addEventListener('change', loadUserAnalytics);

        // Topic performance drill-down
        analyticsContainer.addEventListener('click', async (e) => {
            if (e.target.classList.contains('topic-details-btn')) {
                const topic = e.target.dataset.topic;
                try {
                    const response = await fetch(`/api/analytics/topic-performance/${topic}`, {
                        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                    });
                    if (!response.ok) throw new Error('Failed to fetch topic details');
                    
                    const data = await response.json();
                    showTopicDetailsModal(topic, data.performance);
                } catch (error) {
                    console.error('Error fetching topic details:', error);
                    showToast('error', 'Failed to load topic details');
                }
            }
        });
    }

    async function loadUserAnalytics() {
        try {
            showAnalyticsLoading();

            const timeRange = document.getElementById('time-range').value;
            
            // Fetch all analytics data in parallel
            const [trendsRes, weakAreasRes, studyPlanRes] = await Promise.all([
                fetch(`/api/analytics/trends?timeRange=${timeRange}`, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                }),
                fetch('/api/analytics/weak-areas', {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                }),
                fetch('/api/analytics/study-plan', {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                })
            ]);

            if (!trendsRes.ok || !weakAreasRes.ok || !studyPlanRes.ok) {
                throw new Error('Failed to fetch analytics data');
            }

            const [trends, weakAreas, studyPlan] = await Promise.all([
                trendsRes.json(),
                weakAreasRes.json(),
                studyPlanRes.json()
            ]);

            // Update UI with fetched data
            updateAnalyticsSummary(trends.trends);
            renderPerformanceCharts(trends.trends);
            renderWeakAreas(weakAreas.weakAreas);
            renderStudyPlan(studyPlan.plan);

            showAnalyticsContent();
        } catch (error) {
            console.error('Error loading analytics:', error);
            showAnalyticsError('Failed to load analytics data');
            showToast('error', 'Failed to load analytics data');
        }
    }

    function updateAnalyticsSummary(trends) {
        const { overall } = trends;
        document.getElementById('tests-count').textContent = overall.totalSessions;
        document.getElementById('average-score').textContent = 
            `${Math.round(trends.topics.reduce((sum, t) => sum + t.avgScore, 0) / trends.topics.length)}%`;
        document.getElementById('questions-count').textContent = overall.totalQuestions;
    }

    function renderPerformanceCharts(trends) {
        renderDailyProgressChart(trends.daily);
        renderTopicPerformanceChart(trends.topics);
        renderTimeDistributionChart(trends.overall);
        renderSubjectPerformanceChart(subjectPerformanceData);
        renderProgressChart(testHistoryData);
    }

    function renderDailyProgressChart(dailyData) {
        const ctx = document.getElementById('daily-progress-chart').getContext('2d');
        if (charts.dailyProgress) charts.dailyProgress.destroy();

        charts.dailyProgress = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dailyData.map(d => formatDate(d.date)),
                datasets: [{
                    label: 'Average Score',
                    data: dailyData.map(d => d.avgScore),
                    borderColor: '#4F46E5',
                    backgroundColor: 'rgba(79, 70, 229, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: (context) => `Score: ${context.parsed.y.toFixed(1)}%`
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: { display: true, text: 'Score (%)' }
                    }
                }
            }
        });
    }

    function renderTopicPerformanceChart(topicsData) {
        const ctx = document.getElementById('topic-performance-chart').getContext('2d');
        if (charts.topicPerformance) charts.topicPerformance.destroy();

        charts.topicPerformance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: topicsData.map(t => t.topic),
                datasets: [{
                    label: 'Average Score',
                    data: topicsData.map(t => t.avgScore),
                    backgroundColor: topicsData.map(t => t.avgScore >= 70 ? '#059669' : '#DC2626'),
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: (context) => [
                                `Score: ${context.parsed.y.toFixed(1)}%`,
                                `Accuracy: ${topicsData[context.dataIndex].accuracy.toFixed(1)}%`
                            ]
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: { display: true, text: 'Score (%)' }
                    }
                }
            }
        });
    }

    function renderTimeDistributionChart(overallData) {
        const ctx = document.getElementById('time-distribution-chart').getContext('2d');
        if (charts.timeDistribution) charts.timeDistribution.destroy();

        const avgTimePerQuestion = overallData.avgTimePerQuestion;
        const timeRanges = [
            { label: '0-30s', count: 0 },
            { label: '30-60s', count: 0 },
            { label: '60-90s', count: 0 },
            { label: '90s+', count: 0 }
        ];

        // Simulate time distribution based on average
        const totalQuestions = overallData.totalQuestions;
        if (avgTimePerQuestion <= 30) {
            timeRanges[0].count = totalQuestions * 0.7;
            timeRanges[1].count = totalQuestions * 0.3;
        } else if (avgTimePerQuestion <= 60) {
            timeRanges[0].count = totalQuestions * 0.3;
            timeRanges[1].count = totalQuestions * 0.5;
            timeRanges[2].count = totalQuestions * 0.2;
        } else {
            timeRanges[1].count = totalQuestions * 0.2;
            timeRanges[2].count = totalQuestions * 0.5;
            timeRanges[3].count = totalQuestions * 0.3;
        }

        charts.timeDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: timeRanges.map(r => r.label),
                datasets: [{
                    data: timeRanges.map(r => r.count),
                    backgroundColor: [
                        '#059669',
                        '#0EA5E9',
                        '#6366F1',
                        '#8B5CF6'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'right' },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const value = context.parsed;
                                const percentage = ((value / totalQuestions) * 100).toFixed(1);
                                return `${context.label}: ${Math.round(value)} questions (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    function renderSubjectPerformanceChart(subjectPerformanceData) {
        const ctx = document.getElementById('subject-performance-chart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (charts.subjectChart) {
            charts.subjectChart.destroy();
        }
        
        const labels = subjectPerformanceData.map(item => item.subject);
        const scores = subjectPerformanceData.map(item => item.averageScore);
        const counts = subjectPerformanceData.map(item => item.testCount);
        
        charts.subjectChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Average Score (%)',
                    data: scores,
                    backgroundColor: 'rgba(99, 102, 241, 0.6)',
                    borderColor: 'rgba(99, 102, 241, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Score (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Subject'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                const index = context.dataIndex;
                                return `Tests taken: ${counts[index]}`;
                            }
                        }
                    }
                }
            }
        });
    }

    function renderProgressChart() {
        if (!testHistoryData || testHistoryData.length === 0) {
            return;
        }

        const ctx = document.getElementById('progress-chart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (charts.progressChart) {
            charts.progressChart.destroy();
        }
        
        // Sort by date
        const sortedHistory = [...testHistoryData].sort((a, b) => new Date(a.date) - new Date(b.date));
        
        const labels = sortedHistory.map(item => formatDate(item.date));
        const scores = sortedHistory.map(item => item.score);
        
        charts.progressChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Score (%)',
                    data: scores,
                    backgroundColor: 'rgba(16, 185, 129, 0.2)',
                    borderColor: 'rgba(16, 185, 129, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Score (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                }
            }
        });
    }

    function renderTestHistory() {
        const tableBody = document.getElementById('test-history-table-body');
        const noTestsMessage = document.getElementById('no-tests-message');
        
        if (!testHistoryData || testHistoryData.length === 0) {
            tableBody.innerHTML = '';
            noTestsMessage.classList.remove('hidden');
            return;
        }
        
        noTestsMessage.classList.add('hidden');
        
        // Sort by date descending (most recent first)
        const sortedHistory = [...testHistoryData].sort((a, b) => new Date(b.date) - new Date(a.date));
        
        tableBody.innerHTML = sortedHistory.map(test => `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(test.date)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${test.subject}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        ${test.score >= 70 ? 'bg-green-100 text-green-800' : 
                          test.score >= 40 ? 'bg-yellow-100 text-yellow-800' : 
                          'bg-red-100 text-red-800'}">
                        ${test.score}%
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatTime(test.timeTaken)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <button class="text-indigo-600 hover:text-indigo-900 view-test-btn" data-test-id="${test.id}">
                        View Details
                    </button>
                </td>
            </tr>
        `).join('');
        
        // Add event listeners to view buttons
        document.querySelectorAll('.view-test-btn').forEach(btn => {
            btn.addEventListener('click', () => viewTestDetails(btn.dataset.testId));
        });
    }

    async function viewTestDetails(testId) {
        try {
            const testDetails = await apiGet(`test-sessions/${testId}`);
            // Here you would typically show a modal with test details
            // For simplicity, we'll just alert some basic info
            alert(`Test Details:\nScore: ${testDetails.score.percentage}%\nCorrect: ${testDetails.score.correct}/${testDetails.score.total}\nTime: ${formatTime(testDetails.timeTakenSeconds)}`);
            
            // In a real implementation, you would show a modal with detailed results
            // including questions, answers, and explanations
        } catch (error) {
            console.error('Error fetching test details:', error);
            alert('Could not load test details. Please try again later.');
        }
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    function showAnalyticsLoading() {
        document.getElementById('analytics-loading').classList.remove('hidden');
        document.getElementById('analytics-content').classList.add('hidden');
        document.getElementById('analytics-error').classList.add('hidden');
    }

    function showAnalyticsContent() {
        document.getElementById('analytics-loading').classList.add('hidden');
        document.getElementById('analytics-content').classList.remove('hidden');
        document.getElementById('analytics-error').classList.add('hidden');
    }

    function showAnalyticsError(message) {
        document.getElementById('analytics-loading').classList.add('hidden');
        document.getElementById('analytics-content').classList.add('hidden');
        document.getElementById('analytics-error').classList.remove('hidden');
        
        const errorElement = document.getElementById('analytics-error').querySelector('p');
        if (errorElement) {
            errorElement.textContent = message || 'An error occurred while loading analytics data.';
        }
    }
});

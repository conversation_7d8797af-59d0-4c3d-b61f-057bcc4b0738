// Toast notification system
const toast = {
    element: null,
    timeout: null,
    icons: {
        success: `<svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>`,
        error: `<svg class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>`,
        info: `<svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>`,
        warning: `<svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>`
    }
};

// Initialize toast elements
document.addEventListener('DOMContentLoaded', () => {
    toast.element = document.getElementById('toast');
    if (!toast.element) return;

    // Set up close button
    document.getElementById('toast-close')?.addEventListener('click', () => {
        hideToast();
    });
});

// Show toast notification
export function showToast(type, message, duration = 5000) {
    if (!toast.element) return;

    // Clear any existing timeout
    if (toast.timeout) {
        clearTimeout(toast.timeout);
    }

    // Set icon and title based on type
    const iconElement = document.getElementById('toast-icon');
    const titleElement = document.getElementById('toast-title');
    const messageElement = document.getElementById('toast-message');

    if (iconElement) {
        iconElement.innerHTML = toast.icons[type] || toast.icons.info;
    }

    if (titleElement) {
        titleElement.textContent = type.charAt(0).toUpperCase() + type.slice(1);
    }

    if (messageElement) {
        messageElement.textContent = message;
    }

    // Show toast
    toast.element.classList.remove('translate-y-full');
    toast.element.classList.add('translate-y-0');

    // Hide after duration
    toast.timeout = setTimeout(() => {
        hideToast();
    }, duration);
}

// Hide toast notification
function hideToast() {
    if (!toast.element) return;
    toast.element.classList.remove('translate-y-0');
    toast.element.classList.add('translate-y-full');
}

// Format date for charts
export function formatDate(date) {
    const d = new Date(date);
    return new Intl.DateTimeFormat('en-US', { 
        month: 'short', 
        day: 'numeric' 
    }).format(d);
}

// Format time duration
export function formatDuration(seconds) {
    if (seconds < 60) {
        return `${Math.round(seconds)}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
}

// Get color for score
export function getScoreColor(score) {
    if (score >= 80) return '#059669'; // green-600
    if (score >= 60) return '#0EA5E9'; // sky-500
    if (score >= 40) return '#F59E0B'; // amber-500
    return '#DC2626'; // red-600
}

// Format percentage
export function formatPercentage(value) {
    return `${Math.round(value)}%`;
}

// Check if user is authenticated
export function isAuthenticated() {
    return !!localStorage.getItem('token');
}

// Get user info from local storage
export function getUserInfo() {
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
}

// Update user avatar
export function updateUserAvatar(name) {
    const avatar = document.getElementById('user-avatar');
    if (avatar) {
        avatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=6366F1&color=fff`;
    }
}

// Handle API errors
export function handleApiError(error) {
    console.error('API Error:', error);
    if (error.status === 401) {
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        window.location.href = '/login.html';
    }
    showToast('error', error.message || 'An error occurred. Please try again.');
}

// Add loading spinner
export function addLoadingSpinner(element, size = 'medium') {
    const spinner = document.createElement('div');
    spinner.className = `loader ${size} inline-block`;
    element.appendChild(spinner);
    return spinner;
}

// Remove loading spinner
export function removeLoadingSpinner(spinner) {
    if (spinner && spinner.parentNode) {
        spinner.parentNode.removeChild(spinner);
    }
}

// Format number with commas
export function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Debounce function
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function
export function throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Check if element is in viewport
export function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// Copy to clipboard
export async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('success', 'Copied to clipboard!');
    } catch (err) {
        console.error('Failed to copy:', err);
        showToast('error', 'Failed to copy to clipboard');
    }
}

// Validate email
export function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Generate random ID
export function generateId(length = 10) {
    return Math.random().toString(36).substring(2, length + 2);
}

// Deep clone object
export function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}

// Get relative time
export function getRelativeTime(date) {
    const rtf = new Intl.RelativeTimeFormat('en', { numeric: 'auto' });
    const now = new Date();
    const diff = now - new Date(date);
    const diffInSeconds = diff / 1000;

    if (diffInSeconds < 60) return rtf.format(-Math.round(diffInSeconds), 'second');
    if (diffInSeconds < 3600) return rtf.format(-Math.round(diffInSeconds / 60), 'minute');
    if (diffInSeconds < 86400) return rtf.format(-Math.round(diffInSeconds / 3600), 'hour');
    if (diffInSeconds < 2592000) return rtf.format(-Math.round(diffInSeconds / 86400), 'day');
    if (diffInSeconds < 31536000) return rtf.format(-Math.round(diffInSeconds / 2592000), 'month');
    return rtf.format(-Math.round(diffInSeconds / 31536000), 'year');
}

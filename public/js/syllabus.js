// Syllabus Module
document.addEventListener('DOMContentLoaded', () => {
    const syllabusContainer = document.getElementById('syllabus-container');
    
    // State variables
    let syllabusData = [];
    let currentSyllabusId = null;

    if (syllabusContainer) {
        renderSyllabusUI();
        addEventListeners();
        loadSyllabi();
    }

    function renderSyllabusUI() {
        syllabusContainer.innerHTML = `
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h3 class="font-medium text-lg mb-4">Available Syllabi</h3>
                        <div id="syllabus-list" class="space-y-2 max-h-80 overflow-y-auto">
                            <!-- Syllabus list items will be inserted here -->
                            <div id="syllabus-loading">Loading syllabi...</div>
                            <div id="syllabus-empty" class="hidden text-gray-500 text-sm">No syllabi available.</div>
                        </div>
                        <button id="create-syllabus-btn" class="mt-4 w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700">Create New</button>
                    </div>
                </div>
                
                <div class="lg:col-span-3">
                    <div id="syllabus-detail-container" class="bg-white p-6 rounded-lg shadow">
                        <div id="syllabus-placeholder" class="text-center py-10 text-gray-500">
                            <p>Select a syllabus from the list to view details</p>
                        </div>
                        
                        <div id="syllabus-detail" class="hidden">
                            <div class="flex justify-between items-center mb-6">
                                <h2 id="syllabus-title" class="text-2xl font-semibold text-gray-800"></h2>
                                <div>
                                    <button id="edit-syllabus-btn" class="bg-indigo-600 text-white py-1 px-3 rounded text-sm hover:bg-indigo-700 mr-2">Edit</button>
                                    <button id="delete-syllabus-btn" class="bg-red-600 text-white py-1 px-3 rounded text-sm hover:bg-red-700">Delete</button>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <span class="text-gray-600 text-sm">Exam Type:</span>
                                <span id="syllabus-exam-type" class="ml-1 text-sm font-medium"></span>
                            </div>
                            
                            <div id="syllabus-content" class="border rounded-md p-4 bg-gray-50 mb-6">
                                <!-- Syllabus content will be inserted here -->
                            </div>
                            
                            <div id="syllabus-topic-list" class="space-y-3">
                                <h3 class="text-lg font-medium mb-2">Topics & Sections</h3>
                                <!-- Topic list will be inserted here -->
                            </div>
                        </div>
                        
                        <div id="syllabus-form" class="hidden">
                            <h3 id="syllabus-form-title" class="text-xl font-semibold mb-4">Create New Syllabus</h3>
                            <form id="syllabus-edit-form">
                                <div class="mb-4">
                                    <label for="syllabus-name-input" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                                    <input type="text" id="syllabus-name-input" class="w-full p-2 border border-gray-300 rounded-md" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="syllabus-exam-input" class="block text-sm font-medium text-gray-700 mb-1">Exam Type</label>
                                    <input type="text" id="syllabus-exam-input" class="w-full p-2 border border-gray-300 rounded-md" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="syllabus-description-input" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <textarea id="syllabus-description-input" class="w-full p-2 border border-gray-300 rounded-md" rows="3" required></textarea>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="syllabus-content-input" class="block text-sm font-medium text-gray-700 mb-1">Full Syllabus Content</label>
                                    <textarea id="syllabus-content-input" class="w-full p-2 border border-gray-300 rounded-md" rows="10" required></textarea>
                                </div>
                                
                                <div class="flex justify-end">
                                    <button type="button" id="cancel-syllabus-edit-btn" class="bg-gray-300 text-gray-700 py-2 px-4 rounded mr-2 hover:bg-gray-400">Cancel</button>
                                    <button type="submit" class="bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function addEventListeners() {
        document.getElementById('create-syllabus-btn').addEventListener('click', showCreateSyllabusForm);
        document.getElementById('cancel-syllabus-edit-btn').addEventListener('click', cancelEditSyllabus);
        document.getElementById('edit-syllabus-btn').addEventListener('click', editCurrentSyllabus);
        document.getElementById('delete-syllabus-btn').addEventListener('click', deleteCurrentSyllabus);
        document.getElementById('syllabus-edit-form').addEventListener('submit', handleSyllabusSave);
    }

    async function loadSyllabi() {
        const syllabusListElement = document.getElementById('syllabus-list');
        const loadingElement = document.getElementById('syllabus-loading');
        const emptyElement = document.getElementById('syllabus-empty');
        
        try {
            const data = await apiGet('syllabi');
            syllabusData = data.syllabi || [];
            
            loadingElement.classList.add('hidden');
            
            if (syllabusData.length === 0) {
                emptyElement.classList.remove('hidden');
                return;
            }
            
            const syllabusItems = syllabusData.map(syllabus => 
                `<div class="syllabus-item cursor-pointer p-2 hover:bg-gray-100 rounded ${currentSyllabusId === syllabus.id ? 'bg-indigo-50 border-l-4 border-indigo-500' : ''}" 
                      data-syllabus-id="${syllabus.id}">
                    <div class="font-medium">${syllabus.name}</div>
                    <div class="text-xs text-gray-500">${syllabus.examType || 'No exam type'}</div>
                </div>`
            ).join('');
            
            syllabusListElement.innerHTML = syllabusItems;
            
            // Add event listeners to syllabus items
            document.querySelectorAll('.syllabus-item').forEach(item => {
                item.addEventListener('click', () => selectSyllabus(item.dataset.syllabusId));
            });
            
        } catch (error) {
            console.error('Error loading syllabi:', error);
            loadingElement.textContent = 'Error loading syllabi. Please try refreshing.';
        }
    }

    async function selectSyllabus(syllabusId) {
        currentSyllabusId = syllabusId;
        
        // Update active syllabus in the list
        document.querySelectorAll('.syllabus-item').forEach(item => {
            item.classList.toggle('bg-indigo-50', item.dataset.syllabusId === syllabusId);
            item.classList.toggle('border-l-4', item.dataset.syllabusId === syllabusId);
            item.classList.toggle('border-indigo-500', item.dataset.syllabusId === syllabusId);
        });
        
        try {
            const syllabus = await apiGet(`syllabi/${syllabusId}`);
            
            document.getElementById('syllabus-placeholder').classList.add('hidden');
            document.getElementById('syllabus-form').classList.add('hidden');
            document.getElementById('syllabus-detail').classList.remove('hidden');
            
            document.getElementById('syllabus-title').textContent = syllabus.name;
            document.getElementById('syllabus-exam-type').textContent = syllabus.examType || 'Not specified';
            document.getElementById('syllabus-content').innerHTML = formatSyllabusContent(syllabus.content);
            
            renderTopics(syllabus.topics || []);
            
        } catch (error) {
            console.error('Error loading syllabus details:', error);
            alert('Failed to load syllabus details');
        }
    }

    function renderTopics(topics) {
        const topicListElement = document.getElementById('syllabus-topic-list');
        
        if (!topics || topics.length === 0) {
            topicListElement.innerHTML = '<p class="text-gray-500">No topics available for this syllabus.</p>';
            return;
        }
        
        const topicsHtml = topics.map(topic => `
            <div class="topic-item border p-3 rounded bg-white">
                <h4 class="font-medium text-indigo-700">${topic.name}</h4>
                <p class="text-sm text-gray-600 mt-1">${topic.description || ''}</p>
                ${topic.subtopics && topic.subtopics.length > 0 ? 
                    `<ul class="mt-2 pl-4 text-sm space-y-1">
                        ${topic.subtopics.map(sub => `<li class="text-gray-700">• ${sub}</li>`).join('')}
                    </ul>` : 
                    ''}
            </div>
        `).join('');
        
        topicListElement.innerHTML = topicsHtml;
    }

    function formatSyllabusContent(content) {
        if (!content) return '<p class="text-gray-500">No content available.</p>';
        
        // Convert line breaks to paragraphs
        const paragraphs = content.split('\n').filter(p => p.trim()).map(p => `<p class="mb-2">${p}</p>`);
        return paragraphs.join('');
    }

    function showCreateSyllabusForm() {
        document.getElementById('syllabus-placeholder').classList.add('hidden');
        document.getElementById('syllabus-detail').classList.add('hidden');
        document.getElementById('syllabus-form').classList.remove('hidden');
        
        document.getElementById('syllabus-form-title').textContent = 'Create New Syllabus';
        document.getElementById('syllabus-edit-form').reset();
    }

    function editCurrentSyllabus() {
        if (!currentSyllabusId) return;
        
        const syllabus = syllabusData.find(s => s.id === currentSyllabusId);
        if (!syllabus) return;
        
        document.getElementById('syllabus-placeholder').classList.add('hidden');
        document.getElementById('syllabus-detail').classList.add('hidden');
        document.getElementById('syllabus-form').classList.remove('hidden');
        
        document.getElementById('syllabus-form-title').textContent = 'Edit Syllabus';
        document.getElementById('syllabus-name-input').value = syllabus.name;
        document.getElementById('syllabus-exam-input').value = syllabus.examType || '';
        document.getElementById('syllabus-description-input').value = syllabus.description || '';
        document.getElementById('syllabus-content-input').value = syllabus.content || '';
    }

    function cancelEditSyllabus() {
        if (currentSyllabusId) {
            document.getElementById('syllabus-form').classList.add('hidden');
            document.getElementById('syllabus-detail').classList.remove('hidden');
        } else {
            document.getElementById('syllabus-form').classList.add('hidden');
            document.getElementById('syllabus-placeholder').classList.remove('hidden');
        }
    }

    async function handleSyllabusSave(event) {
        event.preventDefault();
        
        const syllabusData = {
            name: document.getElementById('syllabus-name-input').value,
            examType: document.getElementById('syllabus-exam-input').value,
            description: document.getElementById('syllabus-description-input').value,
            content: document.getElementById('syllabus-content-input').value
        };
        
        try {
            if (currentSyllabusId) {
                // Update existing syllabus
                await apiPut(`syllabi/${currentSyllabusId}`, syllabusData);
            } else {
                // Create new syllabus
                const response = await apiPost('syllabi', syllabusData);
                currentSyllabusId = response.id;
            }
            
            // Reload syllabi list and select the current one
            await loadSyllabi();
            selectSyllabus(currentSyllabusId);
            
        } catch (error) {
            console.error('Error saving syllabus:', error);
            alert('Failed to save syllabus: ' + error.message);
        }
    }

    async function deleteCurrentSyllabus() {
        if (!currentSyllabusId) return;
        
        if (!confirm('Are you sure you want to delete this syllabus? This action cannot be undone.')) {
            return;
        }
        
        try {
            await apiDelete(`syllabi/${currentSyllabusId}`);
            currentSyllabusId = null;
            
            document.getElementById('syllabus-detail').classList.add('hidden');
            document.getElementById('syllabus-placeholder').classList.remove('hidden');
            
            await loadSyllabi();
            
        } catch (error) {
            console.error('Error deleting syllabus:', error);
            alert('Failed to delete syllabus: ' + error.message);
        }
    }
});

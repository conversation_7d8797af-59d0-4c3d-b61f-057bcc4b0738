/**
 * Explanation On-Demand Module
 * Provides functionality for users to request detailed explanations
 * for questions and topics on demand
 */

document.addEventListener('DOMContentLoaded', () => {
    initExplanationEvents();
});

/**
 * Initialize event listeners for explanation features
 */
function initExplanationEvents() {
    // Add event listeners to explanation buttons when test results are displayed
    document.addEventListener('testResultsDisplayed', () => {
        const explanationButtons = document.querySelectorAll('.request-explanation-btn');
        explanationButtons.forEach(button => {
            button.addEventListener('click', handleExplanationRequest);
        });
    });

    // Add event listeners to related concepts buttons
    document.addEventListener('explanationDisplayed', () => {
        const relatedConceptsButtons = document.querySelectorAll('.view-related-concepts-btn');
        relatedConceptsButtons.forEach(button => {
            button.addEventListener('click', handleRelatedConceptsRequest);
        });
    });
}

/**
 * Handle click on request explanation button
 */
async function handleExplanationRequest(e) {
    e.preventDefault();
    
    const questionId = e.target.getAttribute('data-question-id');
    const topic = e.target.getAttribute('data-topic');
    const subtopic = e.target.getAttribute('data-subtopic');
    const difficulty = e.target.getAttribute('data-difficulty');
    
    showLoadingSpinner(e.target);
    
    try {
        const response = await fetch('/api/theory/on-demand-explanation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                questionId,
                topic,
                subtopic,
                difficulty
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to get explanation');
        }
        
        const { explanation } = await response.json();
        showExplanationModal(explanation, { questionId, topic, subtopic, difficulty });
        
    } catch (error) {
        console.error('Error getting explanation:', error);
        showError('Failed to get explanation. Please try again.');
    } finally {
        hideLoadingSpinner(e.target);
    }
}

/**
 * Handle click on view related concepts button
 */
async function handleRelatedConceptsRequest(e) {
    e.preventDefault();
    
    const topic = e.target.getAttribute('data-topic');
    const subtopic = e.target.getAttribute('data-subtopic');
    const difficulty = e.target.getAttribute('data-difficulty');
    
    showLoadingSpinner(e.target);
    
    try {
        const response = await fetch('/api/theory/related-concepts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                topic,
                subtopic,
                difficulty
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to get related concepts');
        }
        
        const { concepts } = await response.json();
        showRelatedConceptsModal(concepts, { topic, subtopic });
        
    } catch (error) {
        console.error('Error getting related concepts:', error);
        showError('Failed to get related concepts. Please try again.');
    } finally {
        hideLoadingSpinner(e.target);
    }
}

/**
 * Show explanation in a modal
 */
function showExplanationModal(explanation, context) {
    // Create modal if it doesn't exist, otherwise update it
    let modal = document.getElementById('explanation-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'explanation-modal';
        modal.className = 'modal fade';
        modal.setAttribute('tabindex', '-1');
        document.body.appendChild(modal);
    }
    
    const title = context.questionId ? 'Question Explanation' : 'Topic Explanation';
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="explanation-content">
                        ${formatExplanation(explanation)}
                    </div>
                    <div class="mt-4">
                        <button class="btn btn-outline-primary btn-sm view-related-concepts-btn"
                                data-topic="${context.topic || ''}"
                                data-subtopic="${context.subtopic || ''}"
                                data-difficulty="${context.difficulty || ''}">
                            View Related Concepts
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    // Dispatch event
    document.dispatchEvent(new CustomEvent('explanationDisplayed', {
        detail: context
    }));
}

/**
 * Show related concepts in a modal
 */
function showRelatedConceptsModal(concepts, context) {
    let modal = document.getElementById('related-concepts-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'related-concepts-modal';
        modal.className = 'modal fade';
        modal.setAttribute('tabindex', '-1');
        document.body.appendChild(modal);
    }
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">Related Concepts: ${context.topic}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="concepts-content">
                        ${formatConcepts(concepts)}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Format explanation content with proper styling
 */
function formatExplanation(explanation) {
    return `
        <div class="explanation-section">
            <h6 class="text-primary">Step-by-Step Explanation</h6>
            ${explanation.steps || explanation}
        </div>
        ${explanation.concepts ? `
            <div class="explanation-section mt-3">
                <h6 class="text-primary">Key Concepts</h6>
                ${explanation.concepts}
            </div>
        ` : ''}
        ${explanation.formulas ? `
            <div class="explanation-section mt-3">
                <h6 class="text-primary">Relevant Formulas</h6>
                ${explanation.formulas}
            </div>
        ` : ''}
        ${explanation.mistakes ? `
            <div class="explanation-section mt-3">
                <h6 class="text-primary">Common Mistakes</h6>
                ${explanation.mistakes}
            </div>
        ` : ''}
        ${explanation.tips ? `
            <div class="explanation-section mt-3">
                <h6 class="text-primary">Tips</h6>
                ${explanation.tips}
            </div>
        ` : ''}
        ${explanation.summary ? `
            <div class="explanation-section mt-3">
                <h6 class="text-primary">Quick Summary</h6>
                ${explanation.summary}
            </div>
        ` : ''}
    `;
}

/**
 * Format concepts content with proper styling
 */
function formatConcepts(concepts) {
    if (typeof concepts === 'string') {
        return concepts;
    }
    
    return `
        ${concepts.related ? `
            <div class="concepts-section">
                <h6 class="text-info">Related Concepts</h6>
                ${concepts.related}
            </div>
        ` : ''}
        ${concepts.connections ? `
            <div class="concepts-section mt-3">
                <h6 class="text-info">Connections</h6>
                ${concepts.connections}
            </div>
        ` : ''}
        ${concepts.prerequisites ? `
            <div class="concepts-section mt-3">
                <h6 class="text-info">Prerequisites</h6>
                ${concepts.prerequisites}
            </div>
        ` : ''}
        ${concepts.progression ? `
            <div class="concepts-section mt-3">
                <h6 class="text-info">Learning Progression</h6>
                ${concepts.progression}
            </div>
        ` : ''}
        ${concepts.applications ? `
            <div class="concepts-section mt-3">
                <h6 class="text-info">Practical Applications</h6>
                ${concepts.applications}
            </div>
        ` : ''}
    `;
}

/**
 * Show loading spinner on a button
 */
function showLoadingSpinner(button) {
    const originalText = button.innerHTML;
    button.setAttribute('data-original-text', originalText);
    button.disabled = true;
    button.innerHTML = `
        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        Loading...
    `;
}

/**
 * Hide loading spinner and restore button text
 */
function hideLoadingSpinner(button) {
    const originalText = button.getAttribute('data-original-text');
    button.innerHTML = originalText;
    button.disabled = false;
}

/**
 * Show error toast
 */
function showError(message) {
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.getElementById('toast-container') || document.body;
    container.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * Theory Explanation Module
 * Provides functionality for users to request and view theory explanations for questions
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initTheoryExplanationEvents();
});

/**
 * Initialize event listeners for theory explanation feature
 */
function initTheoryExplanationEvents() {
  // Listen for test results displayed event
  document.addEventListener('testResultsDisplayed', () => {
    // Add event listeners to all theory explanation buttons
    const theoryButtons = document.querySelectorAll('.understand-theory-btn');
    theoryButtons.forEach(button => {
      button.addEventListener('click', handleTheoryExplanationRequest);
    });
  });
}

/**
 * Handle click on theory explanation button
 * @param {Event} e - Click event
 */
async function handleTheoryExplanationRequest(e) {
  e.preventDefault();
  
  const questionId = e.target.getAttribute('data-question-id');
  const topic = e.target.getAttribute('data-topic');
  const syllabusId = e.target.getAttribute('data-syllabus-id') || '';
  
  // Show modal with loading state
  showTheoryModal('Loading explanation...');
  
  try {
    // Request theory explanation from API
    const data = await apiPost('enhanced-questions/explain-theory', {
      questionId,
      topic,
      syllabusId
    });
    
    // Update modal with explanation
    if (data && data.explanation) {
      updateTheoryModal(data.explanation);
    } else {
      showTheoryError('Failed to retrieve explanation. Please try again.');
    }
  } catch (error) {
    console.error('Error fetching theory explanation:', error);
    showTheoryError('An error occurred while retrieving the explanation. Please try again.');
  }
}

/**
 * Create and show the theory explanation modal
 * @param {string} initialContent - Content to display initially (e.g., loading message)
 */
function showTheoryModal(initialContent) {
  // Create modal if it doesn't exist, otherwise just update it
  let modal = document.getElementById('theory-explanation-modal');
  
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'theory-explanation-modal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-90vh overflow-y-auto">
        <div class="flex justify-between items-center p-4 border-b">
          <h3 class="text-lg font-bold">Theory Explanation</h3>
          <button id="close-theory-modal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div id="theory-content" class="p-6">
          ${initialContent}
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add close button handler
    document.getElementById('close-theory-modal').addEventListener('click', () => {
      modal.classList.add('hidden');
    });
  } else {
    document.getElementById('theory-content').innerHTML = initialContent;
    modal.classList.remove('hidden');
  }
}

/**
 * Update the theory modal with explanation content
 * @param {Object} explanation - Explanation object from API
 */
function updateTheoryModal(explanation) {
  const content = document.getElementById('theory-content');
  
  if (explanation.title && explanation.content) {
    content.innerHTML = `
      <h2 class="text-xl font-bold mb-4">${explanation.title}</h2>
      <div class="theory-content prose">
        ${explanation.content}
      </div>
      ${explanation.references ? `
        <div class="mt-6 pt-4 border-t border-gray-200">
          <h4 class="font-bold mb-2">References</h4>
          <ul class="list-disc pl-5">
            ${explanation.references.map(ref => `<li>${ref}</li>`).join('')}
          </ul>
        </div>
      ` : ''}
    `;
  } else {
    content.innerHTML = `
      <div class="theory-content prose">
        ${explanation.text || explanation}
      </div>
    `;
  }
}

/**
 * Show error message in the theory modal
 * @param {string} message - Error message to display
 */
function showTheoryError(message) {
  const content = document.getElementById('theory-content');
  content.innerHTML = `
    <div class="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
      <p>${message}</p>
    </div>
    <div class="mt-4">
      <button id="retry-theory-btn" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
        Try Again
      </button>
    </div>
  `;
  
  // Add retry button handler
  document.getElementById('retry-theory-btn').addEventListener('click', () => {
    // Close the modal on retry - user can click the button again
    document.getElementById('theory-explanation-modal').classList.add('hidden');
  });
}

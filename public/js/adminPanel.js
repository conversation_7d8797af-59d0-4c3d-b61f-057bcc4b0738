// Admin Panel Main Controller
document.addEventListener('DOMContentLoaded', () => {
  // Initialize navigation
  initAdminNav();
  
  // Initialize auth state
  document.addEventListener('authStateChanged', async (e) => {
    if (e.detail.user) {
      // Check if admin
      const isAdmin = await checkIsAdmin(e.detail.userId);
      if (isAdmin) {
        // Load admin dashboard
        loadDashboard();
      } else {
        // Redirect non-admin users
        alert('You do not have admin privileges.');
        window.location.href = 'index.html';
      }
    } else {
      // Redirect unauthenticated users
      alert('Please sign in to access the admin panel.');
      window.location.href = 'index.html';
    }
  });
  
  // Initialize module event listeners
  initSyllabiEvents();
  initQuestionsEvents();
  initLlmMonitoringEvents();
  initUserManagementEvents();
  initSystemSettingsEvents();
});

/**
 * Check if the user has admin privileges
 */
async function checkIsAdmin(userId) {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    if (userDoc.exists && userDoc.data().role === 'admin') {
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Initialize admin navigation
 */
function initAdminNav() {
  const navItems = document.querySelectorAll('.admin-nav-item');
  const sections = document.querySelectorAll('.admin-section');
  
  navItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      
      // Update active nav item
      navItems.forEach(nav => nav.classList.remove('active'));
      item.classList.add('active');
      
      // Show selected section, hide others
      const sectionId = item.getAttribute('data-section');
      sections.forEach(section => {
        if (section.id === `${sectionId}-section`) {
          section.classList.remove('hidden');
        } else {
          section.classList.add('hidden');
        }
      });
      
      // Load section data if needed
      switch (sectionId) {
        case 'dashboard':
          loadDashboard();
          break;
        case 'syllabi':
          loadSyllabi();
          break;
        case 'questions':
          loadQuestions();
          break;
        case 'llm-monitor':
          loadLlmMonitoring();
          break;
        case 'users':
          loadUsers();
          break;
        case 'settings':
          loadSystemSettings();
          break;
      }
    });
  });
}

// Shows the loading indicator
function showLoading() {
  document.getElementById('loading').classList.remove('hidden');
}

// Hides the loading indicator
function hideLoading() {
  document.getElementById('loading').classList.add('hidden');
}

// Display error message
function showError(container, message) {
  container.innerHTML = `
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
      ${message}
    </div>
  `;
}

/**
 * Dashboard Module
 */
async function loadDashboard() {
  showLoading();
  
  try {
    // Fetch stats
    const [userStats, testStats, questionStats, llmStats] = await Promise.all([
      fetchUserStats(),
      fetchTestStats(),
      fetchQuestionStats(),
      fetchRecentLlmStats()
    ]);
    
    // Update dashboard counters
    document.getElementById('active-users-count').textContent = userStats.activeUsers;
    document.getElementById('tests-count').textContent = testStats.totalTests;
    document.getElementById('questions-count').textContent = questionStats.totalQuestions;
    
    // Render recent tests table
    const recentTestsContainer = document.getElementById('recent-tests-table');
    renderRecentTests(recentTestsContainer, testStats.recentTests);
    
    // Render LLM usage chart
    renderLlmUsageChart(llmStats);
    
    hideLoading();
  } catch (error) {
    console.error('Error loading dashboard:', error);
    hideLoading();
    showError(document.getElementById('dashboard-section'), 'Failed to load dashboard data. Please try again.');
  }
}

// Fetch user statistics
async function fetchUserStats() {
  // For demo, return placeholder data
  return {
    totalUsers: 1250,
    activeUsers: 387,
    newUsersLast30Days: 124
  };
}

// Fetch test session statistics
async function fetchTestStats() {
  // For demo, return placeholder data
  return {
    totalTests: 3568,
    averageScore: 72.5,
    testsLast30Days: 842,
    recentTests: [
      { id: 'test1', user: 'User A', date: '2025-06-05', score: 85, subject: 'Mathematics' },
      { id: 'test2', user: 'User B', date: '2025-06-05', score: 92, subject: 'Science' },
      { id: 'test3', user: 'User C', date: '2025-06-04', score: 68, subject: 'English' },
      { id: 'test4', user: 'User D', date: '2025-06-04', score: 76, subject: 'Social Studies' },
      { id: 'test5', user: 'User E', date: '2025-06-03', score: 81, subject: 'General Knowledge' }
    ]
  };
}

// Fetch question statistics
async function fetchQuestionStats() {
  // For demo, return placeholder data
  return {
    totalQuestions: 12450,
    byType: {
      'multiple-choice': 9845,
      'fill-in-blank': 1245,
      'true-false': 840,
      'short-answer': 520
    },
    aiGeneratedPercentage: 82
  };
}

// Fetch recent LLM usage statistics
async function fetchRecentLlmStats() {
  // For demo, return placeholder data
  return {
    totalCalls: 4825,
    totalTokens: 2458000,
    estimatedCost: 12.29,
    byDay: [
      { date: '2025-06-01', calls: 125, tokens: 62500, cost: 0.31 },
      { date: '2025-06-02', calls: 348, tokens: 174000, cost: 0.87 },
      { date: '2025-06-03', calls: 642, tokens: 321000, cost: 1.61 },
      { date: '2025-06-04', calls: 891, tokens: 445500, cost: 2.23 },
      { date: '2025-06-05', calls: 1240, tokens: 620000, cost: 3.10 },
      { date: '2025-06-06', calls: 1579, tokens: 789500, cost: 3.95 }
    ],
    byOperation: {
      'generate_questions': 2895,
      'explain_theory': 1235,
      'challenge_answer': 695
    }
  };
}

// Render recent tests table
function renderRecentTests(container, tests) {
  container.innerHTML = `
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
          <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        ${tests.map(test => `
          <tr>
            <td class="px-3 py-2 whitespace-nowrap text-sm">${test.user}</td>
            <td class="px-3 py-2 whitespace-nowrap text-sm">${test.subject}</td>
            <td class="px-3 py-2 whitespace-nowrap text-sm">${test.date}</td>
            <td class="px-3 py-2 whitespace-nowrap text-sm">${test.score}%</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

// Render LLM usage chart
function renderLlmUsageChart(data) {
  const ctx = document.getElementById('llm-usage-chart').getContext('2d');
  
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: data.byDay.map(day => day.date),
      datasets: [{
        label: 'API Calls',
        data: data.byDay.map(day => day.calls),
        borderColor: 'rgb(99, 102, 241)',
        backgroundColor: 'rgba(99, 102, 241, 0.1)',
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
}

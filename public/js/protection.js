// Disable right-click context menu
document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    return false;
});

// Disable keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Prevent common shortcuts
    if ((e.ctrlKey || e.metaKey) && (
        e.key === 'c' || // Copy
        e.key === 'x' || // Cut
        e.key === 'v' || // Paste
        e.key === 'p' || // Print
        e.key === 's' || // Save
        e.key === 'u' || // View Source
        e.key === 'a'    // Select All
    )) {
        e.preventDefault();
        return false;
    }

    // Prevent F12 and other dev tools shortcuts
    if (e.key === 'F12' || 
        ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'i') ||
        ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'j')) {
        e.preventDefault();
        return false;
    }
});

// Disable text selection
document.addEventListener('selectstart', (e) => {
    e.preventDefault();
    return false;
});

// Disable drag and drop
document.addEventListener('dragstart', (e) => {
    e.preventDefault();
    return false;
});

// Prevent screenshots (CSS approach)
document.body.style.userSelect = 'none';
document.body.style.webkitUserSelect = 'none';
document.body.style.msUserSelect = 'none';
document.body.style.mozUserSelect = 'none';

// Anti-bot protection
const botPatterns = [
    'bot',
    'spider',
    'crawl',
    'phantomjs',
    'selenium',
    'puppeteer',
    'playwright',
    'cypress'
];

function detectBot() {
    const userAgent = navigator.userAgent.toLowerCase();
    if (botPatterns.some(pattern => userAgent.includes(pattern))) {
        window.location.href = '/error.html';
        return true;
    }
    return false;
}

// Advanced bot detection
function runBotChecks() {
    // Check for automation flags
    if (navigator.webdriver || window._phantom || window.__nightmare) {
        window.location.href = '/error.html';
        return;
    }

    // Check for common bot properties
    const botSignatures = [
        'callPhantom',
        '_selenium',
        '__webdriver_evaluate',
        '__selenium_evaluate',
        '__webdriver_script_function',
        '__webdriver_script_func',
        '__webdriver_script_fn',
        '__fxdriver_evaluate',
        '__driver_unwrapped',
        '__webdriver_unwrapped',
        '__driver_evaluate',
        '__selenium_unwrapped',
        '__fxdriver_unwrapped',
    ];

    for (const signature of botSignatures) {
        if (signature in window) {
            window.location.href = '/error.html';
            return;
        }
    }
}

// API protection
function addApiProtection() {
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        const request = args[0];
        const url = typeof request === 'string' ? request : request.url;
        
        // Add security headers
        const headers = {
            'X-Requested-With': 'XMLHttpRequest',
            'Anti-Bot-Token': generateSecurityToken(),
            ...(args[1]?.headers || {})
        };

        // Rate limiting check
        if (!checkRateLimit(url)) {
            throw new Error('Rate limit exceeded');
        }

        // Request validation
        if (!validateRequest(url, args[1]?.method)) {
            throw new Error('Invalid request');
        }

        const newArgs = [
            typeof request === 'string' ? request : {
                ...request,
                headers: { ...request.headers, ...headers }
            },
            {
                ...args[1],
                headers
            }
        ];

        return originalFetch.apply(this, newArgs);
    };
}

// Security token generation
function generateSecurityToken() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    const token = btoa(`${timestamp}:${random}`);
    return token;
}

// Rate limiting
const rateLimits = new Map();
function checkRateLimit(url) {
    const now = Date.now();
    const limit = 100; // requests per minute
    const windowMs = 60000; // 1 minute

    if (!rateLimits.has(url)) {
        rateLimits.set(url, []);
    }

    const requests = rateLimits.get(url);
    const windowStart = now - windowMs;

    // Remove old requests
    while (requests.length > 0 && requests[0] < windowStart) {
        requests.shift();
    }

    if (requests.length >= limit) {
        return false;
    }

    requests.push(now);
    return true;
}

// Request validation
function validateRequest(url, method = 'GET') {
    // Check if URL is allowed
    const allowedDomains = [
        window.location.hostname,
        'api.yourdomain.com'
    ];
    
    try {
        const urlObj = new URL(url);
        if (!allowedDomains.includes(urlObj.hostname)) {
            return false;
        }
    } catch {
        return false;
    }

    // Validate method
    const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE'];
    if (!allowedMethods.includes(method.toUpperCase())) {
        return false;
    }

    return true;
}

// Initialize protection
window.addEventListener('load', () => {
    if (!detectBot()) {
        runBotChecks();
        addApiProtection();
    }
});

// Global state
let currentSyllabusId = null;
let currentSyllabus = null;
let currentDistribution = null;
let unitChart = null;
let difficultyChart = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Load syllabi
    loadSyllabi();

    // Initialize charts
    initializeCharts();
});

// Load syllabi list
async function loadSyllabi() {
    try {
        const response = await fetch('/api/admin/syllabus');
        if (!response.ok) throw new Error('Failed to load syllabi');
        
        const syllabi = await response.json();
        renderSyllabusList(syllabi);
    } catch (error) {
        showToast('Failed to load syllabi: ' + error.message, 'error');
    }
}

// Render syllabi list
function renderSyllabusList(syllabi) {
    const listContainer = document.getElementById('syllabusList');
    listContainer.innerHTML = syllabi.map(syllabus => `
        <button class="list-group-item list-group-item-action"
                onclick="selectSyllabus('${syllabus.id}')">
            ${syllabus.name}
            <small class="d-block text-muted">${syllabus.subject}</small>
        </button>
    `).join('');
}

// Select a syllabus
async function selectSyllabus(syllabusId) {
    try {
        currentSyllabusId = syllabusId;
        
        // Update active state
        document.querySelectorAll('#syllabusList button').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('onclick').includes(syllabusId)) {
                btn.classList.add('active');
            }
        });

        // Load syllabus details
        const response = await fetch(`/api/admin/syllabus/${syllabusId}`);
        if (!response.ok) throw new Error('Failed to load syllabus');
        
        currentSyllabus = await response.json();
        
        // Load existing configuration
        const configResponse = await fetch(`/api/admin/question-distribution/config/${syllabusId}`);
        if (configResponse.ok) {
            const config = await configResponse.json();
            applyConfiguration(config);
        }

        renderUnitsAndTopics();
    } catch (error) {
        showToast('Failed to load syllabus: ' + error.message, 'error');
    }
}

// Render units and topics
function renderUnitsAndTopics() {
    const container = document.getElementById('unitsContainer');
    container.innerHTML = currentSyllabus.units.map((unit, unitIndex) => `
        <div class="unit-container">
            <div class="unit-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h6 class="mb-0">${unit.name}</h6>
                    </div>
                    <div class="col-auto">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">Weight</span>
                            <input type="number" class="form-control" style="width: 80px"
                                   value="${unit.weight || 0}"
                                   onchange="updateUnitWeight(${unitIndex}, this.value)">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">Min Q</span>
                            <input type="number" class="form-control" style="width: 80px"
                                   value="${unit.min_questions || 0}"
                                   onchange="updateUnitMinQuestions(${unitIndex}, this.value)">
                        </div>
                    </div>
                </div>
            </div>
            <div class="unit-content">
                ${unit.topics.map((topic, topicIndex) => `
                    <div class="topic-container">
                        <div class="topic-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="mb-0">${topic.name}</h6>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Weight</span>
                                        <input type="number" class="form-control" style="width: 80px"
                                               value="${topic.weight || 0}"
                                               onchange="updateTopicWeight(${unitIndex}, ${topicIndex}, this.value)">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Min Q</span>
                                        <input type="number" class="form-control" style="width: 80px"
                                               value="${topic.min_questions || 0}"
                                               onchange="updateTopicMinQuestions(${unitIndex}, ${topicIndex}, this.value)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="difficulty-controls">
                            <div class="row g-2">
                                <div class="col-auto">
                                    <label class="difficulty-label">Easy</label>
                                    <input type="number" class="form-control form-control-sm difficulty-input"
                                           value="${topic.difficulty_distribution?.easy || 25}"
                                           onchange="updateDifficultyDistribution(${unitIndex}, ${topicIndex}, 'easy', this.value)">
                                </div>
                                <div class="col-auto">
                                    <label class="difficulty-label">Medium</label>
                                    <input type="number" class="form-control form-control-sm difficulty-input"
                                           value="${topic.difficulty_distribution?.medium || 40}"
                                           onchange="updateDifficultyDistribution(${unitIndex}, ${topicIndex}, 'medium', this.value)">
                                </div>
                                <div class="col-auto">
                                    <label class="difficulty-label">Hard</label>
                                    <input type="number" class="form-control form-control-sm difficulty-input"
                                           value="${topic.difficulty_distribution?.hard || 25}"
                                           onchange="updateDifficultyDistribution(${unitIndex}, ${topicIndex}, 'hard', this.value)">
                                </div>
                                <div class="col-auto">
                                    <label class="difficulty-label">Expert</label>
                                    <input type="number" class="form-control form-control-sm difficulty-input"
                                           value="${topic.difficulty_distribution?.expert || 10}"
                                           onchange="updateDifficultyDistribution(${unitIndex}, ${topicIndex}, 'expert', this.value)">
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');
}

// Update functions
function updateUnitWeight(unitIndex, value) {
    currentSyllabus.units[unitIndex].weight = parseInt(value) || 0;
}

function updateUnitMinQuestions(unitIndex, value) {
    currentSyllabus.units[unitIndex].min_questions = parseInt(value) || 0;
}

function updateTopicWeight(unitIndex, topicIndex, value) {
    currentSyllabus.units[unitIndex].topics[topicIndex].weight = parseInt(value) || 0;
}

function updateTopicMinQuestions(unitIndex, topicIndex, value) {
    currentSyllabus.units[unitIndex].topics[topicIndex].min_questions = parseInt(value) || 0;
}

function updateDifficultyDistribution(unitIndex, topicIndex, difficulty, value) {
    const topic = currentSyllabus.units[unitIndex].topics[topicIndex];
    if (!topic.difficulty_distribution) {
        topic.difficulty_distribution = {
            easy: 25,
            medium: 40,
            hard: 25,
            expert: 10
        };
    }
    topic.difficulty_distribution[difficulty] = parseInt(value) || 0;
}

// Calculate distribution
async function calculateDistribution() {
    try {
        const totalQuestions = parseInt(document.getElementById('totalQuestions').value) || 100;

        const response = await fetch('/api/admin/question-distribution/calculate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                syllabus: currentSyllabus,
                totalQuestions
            })
        });

        if (!response.ok) throw new Error('Failed to calculate distribution');
        
        currentDistribution = await response.json();
        updateDistributionPreview();
    } catch (error) {
        showToast('Failed to calculate distribution: ' + error.message, 'error');
    }
}

// Save configuration
async function saveConfiguration() {
    if (!currentSyllabusId) return;

    try {
        const response = await fetch(`/api/admin/question-distribution/config/${currentSyllabusId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(currentSyllabus)
        });

        if (!response.ok) throw new Error('Failed to save configuration');
        
        showToast('Configuration saved successfully', 'success');
    } catch (error) {
        showToast('Failed to save configuration: ' + error.message, 'error');
    }
}

// Initialize charts
function initializeCharts() {
    const unitCtx = document.getElementById('unitDistributionChart').getContext('2d');
    unitChart = new Chart(unitCtx, {
        type: 'pie',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: []
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Questions by Unit'
                }
            }
        }
    });

    const difficultyCtx = document.getElementById('difficultyDistributionChart').getContext('2d');
    difficultyChart = new Chart(difficultyCtx, {
        type: 'pie',
        data: {
            labels: ['Easy', 'Medium', 'Hard', 'Expert'],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6610f2'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'Questions by Difficulty'
                }
            }
        }
    });
}

// Update distribution preview
function updateDistributionPreview() {
    if (!currentDistribution) return;

    // Update unit chart
    unitChart.data.labels = currentDistribution.units.map(u => u.name);
    unitChart.data.datasets[0].data = currentDistribution.units.map(u => u.questionCount);
    unitChart.data.datasets[0].backgroundColor = generateColors(currentDistribution.units.length);
    unitChart.update();

    // Calculate total difficulty distribution
    const totalDifficulty = {
        easy: 0,
        medium: 0,
        hard: 0,
        expert: 0
    };

    currentDistribution.units.forEach(unit => {
        unit.topics.forEach(topic => {
            totalDifficulty.easy += topic.distribution.easy;
            totalDifficulty.medium += topic.distribution.medium;
            totalDifficulty.hard += topic.distribution.hard;
            totalDifficulty.expert += topic.distribution.expert;
        });
    });

    // Update difficulty chart
    difficultyChart.data.datasets[0].data = [
        totalDifficulty.easy,
        totalDifficulty.medium,
        totalDifficulty.hard,
        totalDifficulty.expert
    ];
    difficultyChart.update();

    // Update detailed distribution table
    renderDetailedDistribution();
}

// Render detailed distribution table
function renderDetailedDistribution() {
    const container = document.getElementById('detailedDistribution');
    container.innerHTML = `
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>Unit/Topic</th>
                    <th class="text-end">Questions</th>
                    <th class="text-end">Easy</th>
                    <th class="text-end">Medium</th>
                    <th class="text-end">Hard</th>
                    <th class="text-end">Expert</th>
                </tr>
            </thead>
            <tbody>
                ${currentDistribution.units.map(unit => `
                    <tr class="table-light">
                        <td><strong>${unit.name}</strong></td>
                        <td class="text-end"><strong>${unit.questionCount}</strong></td>
                        <td colspan="4"></td>
                    </tr>
                    ${unit.topics.map(topic => `
                        <tr>
                            <td class="ps-4">${topic.name}</td>
                            <td class="text-end">${topic.questionCount}</td>
                            <td class="text-end">${topic.distribution.easy}</td>
                            <td class="text-end">${topic.distribution.medium}</td>
                            <td class="text-end">${topic.distribution.hard}</td>
                            <td class="text-end">${topic.distribution.expert}</td>
                        </tr>
                    `).join('')}
                `).join('')}
            </tbody>
        </table>
    `;
}

// Helper function to generate colors
function generateColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(`hsl(${(i * 360) / count}, 70%, 50%)`);
    }
    return colors;
}

// Show toast notification
function showToast(message, type = 'info') {
    // You can implement this using Bootstrap's toast component
    // or any other notification library
    alert(message);
}

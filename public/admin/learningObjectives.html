<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Learning Objectives Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/adminPanel/learningObjectives.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/syllabusManager.html">Syllabus Manager</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/learningObjectives.html">Learning Objectives</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <!-- Syllabus Selection -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Syllabi</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="syllabusList">
                            <!-- Syllabi will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Learning Objectives -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Learning Objectives</h5>
                        <button class="btn btn-primary" onclick="showCreateObjectiveModal()">
                            <i class="bi bi-plus-circle"></i> Add Objective
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="objectivesList">
                            <!-- Objectives will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Topic Mapping -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Topic Mapping</h5>
                    </div>
                    <div class="card-body">
                        <div id="topicMappingList">
                            <!-- Topic mapping will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Progress Tracking -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Progress Overview</h5>
                    </div>
                    <div class="card-body">
                        <div id="progressOverview">
                            <!-- Progress charts will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create/Edit Objective Modal -->
    <div class="modal fade" id="objectiveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="objectiveModalTitle">Add Learning Objective</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="objectiveForm">
                        <input type="hidden" id="objectiveId">
                        <div class="mb-3">
                            <label for="objectiveName" class="form-label">Name</label>
                            <input type="text" class="form-control" id="objectiveName" required>
                        </div>
                        <div class="mb-3">
                            <label for="objectiveDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="objectiveDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="objectiveLevel" class="form-label">Level</label>
                            <select class="form-select" id="objectiveLevel">
                                <option value="basic">Basic</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="objectiveWeight" class="form-label">Weight (%)</label>
                            <input type="number" class="form-control" id="objectiveWeight" min="0" max="100" value="0">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveObjective()">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Topic Mapping Modal -->
    <div class="modal fade" id="topicMappingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Map Learning Objectives to Topic</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Topic</label>
                        <h6 id="selectedTopicName"></h6>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Learning Objectives</label>
                        <div id="objectiveCheckboxes" class="list-group">
                            <!-- Checkboxes will be loaded here -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveTopicMapping()">Save Mapping</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/adminPanel/learningObjectives.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/adminPanel/monitoring.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/syllabusManager.html">Syllabus Manager</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/learningObjectives.html">Learning Objectives</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/questionDistribution.html">Question Distribution</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/monitoring.html">Monitoring</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- System Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Questions</h6>
                        <h2 class="card-title mb-0" id="totalQuestions">0</h2>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span id="questionsTrend">0%</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Active Users</h6>
                        <h2 class="card-title mb-0" id="activeUsers">0</h2>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span id="usersTrend">0%</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Tests Taken</h6>
                        <h2 class="card-title mb-0" id="testsTaken">0</h2>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span id="testsTrend">0%</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">System Health</h6>
                        <h2 class="card-title mb-0" id="systemHealth">100%</h2>
                        <div class="metric-trend positive">
                            <i class="bi bi-check-circle"></i>
                            <span>All Systems Normal</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Performance Metrics -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">System Performance</h5>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm" onclick="updateTimeRange('1h')">1H</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="updateTimeRange('24h')">24H</button>
                                <button class="btn btn-outline-secondary btn-sm active" onclick="updateTimeRange('7d')">7D</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="updateTimeRange('30d')">30D</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">System Health</h5>
                    </div>
                    <div class="card-body">
                        <div class="health-metrics">
                            <div class="health-metric">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span>CPU Usage</span>
                                    <span id="cpuUsage">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="cpuBar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="health-metric">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span>Memory Usage</span>
                                    <span id="memoryUsage">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="memoryBar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="health-metric">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span>Storage Usage</span>
                                    <span id="storageUsage">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="storageBar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="health-metric">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span>API Response Time</span>
                                    <span id="apiLatency">0ms</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id="apiBar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Alerts -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Alerts</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="alertsList">
                            <!-- Alerts will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Question Quality -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Question Quality Metrics</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="qualityChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Syllabus Coverage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Syllabus Coverage</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="coverageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Recent Activity -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Activity</h5>
                            <button class="btn btn-outline-secondary btn-sm" onclick="refreshActivity()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Event</th>
                                        <th>User</th>
                                        <th>Details</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="activityTable">
                                    <!-- Activity rows will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/adminPanel/monitoring.js"></script>
</body>
</html>

/* Card Styling */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Metric Cards */
.metric-card {
    background-color: #fff;
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.metric-card .card-title {
    font-size: 2rem;
    font-weight: 500;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.metric-trend.positive {
    color: #28a745;
}

.metric-trend.negative {
    color: #dc3545;
}

/* Health Metrics */
.health-metrics {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.health-metric {
    font-size: 0.875rem;
}

.progress {
    height: 0.5rem;
    background-color: #e9ecef;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-bar {
    background-color: #007bff;
    transition: width 0.3s ease;
}

.progress-bar.warning {
    background-color: #ffc107;
}

.progress-bar.danger {
    background-color: #dc3545;
}

/* Recent Alerts */
.list-group-item {
    border-left: none;
    border-right: none;
    padding: 0.75rem 1rem;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-icon {
    font-size: 1.25rem;
}

.alert-icon.critical {
    color: #dc3545;
}

.alert-icon.warning {
    color: #ffc107;
}

.alert-icon.info {
    color: #17a2b8;
}

.alert-content {
    flex: 1;
}

.alert-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Activity Table */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 500;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.25rem;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .metric-card .card-title {
        font-size: 1.5rem;
    }

    .health-metrics {
        gap: 1rem;
    }

    .table-responsive {
        margin: 0 -1rem;
    }
}

/* Time Range Buttons */
.btn-group .btn-outline-secondary {
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-group .btn-outline-secondary:hover,
.btn-group .btn-outline-secondary.active {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

/* Tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 200px;
    padding: 0.5rem;
    background-color: #000;
    border-radius: 0.25rem;
}

/* Chart Legend */
.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 1rem;
    height: 1rem;
    border-radius: 0.25rem;
}

/* Refresh Button Animation */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.bi-arrow-clockwise {
    display: inline-block;
}

.refreshing .bi-arrow-clockwise {
    animation: rotate 1s linear infinite;
}

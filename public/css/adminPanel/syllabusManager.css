/* Syllabus Manager Styles */

/* List Group Item Styles */
.list-group-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Unit Card Styles */
.unit-card {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

.unit-card .unit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.unit-card .unit-content {
    margin-left: 1rem;
}

/* Topic Styles */
.topic-list {
    margin-left: 1.5rem;
}

.topic-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    background-color: white;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Subtopic Styles */
.subtopic-list {
    margin-left: 1.5rem;
    margin-top: 0.5rem;
}

.subtopic-item {
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.25rem;
    border-left: 2px solid #dee2e6;
}

/* Form Control Styles */
.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Drag and Drop Styles */
.draggable {
    cursor: move;
}

.drag-handle {
    cursor: move;
    color: #6c757d;
    margin-right: 0.5rem;
}

.drop-zone {
    min-height: 50px;
    border: 2px dashed #dee2e6;
    border-radius: 0.25rem;
    margin: 0.5rem 0;
    padding: 0.5rem;
}

.drop-zone.active {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Bulk Upload Styles */
.bulk-upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 0.25rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.bulk-upload-zone:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.bulk-upload-zone.dragover {
    border-color: #198754;
    background-color: rgba(25, 135, 84, 0.1);
}

.bulk-upload-icon {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.validation-results {
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-top: 1rem;
}

.validation-results .error-item {
    color: #dc3545;
    margin-bottom: 0.5rem;
}

.validation-results .warning-item {
    color: #ffc107;
    margin-bottom: 0.5rem;
}

.progress {
    height: 0.5rem;
    margin: 1rem 0;
}

.progress-bar.bg-danger {
    transition: width 0.3s ease;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Error States */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.is-invalid + .invalid-feedback {
    display: block;
}

/* Success States */
.is-valid {
    border-color: #198754;
}

.valid-feedback {
    display: none;
    color: #198754;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.is-valid + .valid-feedback {
    display: block;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .col-md-4 {
        margin-bottom: 1rem;
    }

    .unit-card .unit-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .unit-card .unit-header .btn-group {
        margin-top: 0.5rem;
    }
}

/* Animation */
.fade-enter {
    opacity: 0;
}

.fade-enter-active {
    opacity: 1;
    transition: opacity 200ms ease-in;
}

.fade-exit {
    opacity: 1;
}

.fade-exit-active {
    opacity: 0;
    transition: opacity 200ms ease-in;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1050;
}

.toast {
    background-color: white;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 0.5rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

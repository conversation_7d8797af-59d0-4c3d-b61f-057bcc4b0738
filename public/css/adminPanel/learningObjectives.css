/* Card and List Styling */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Objective Item Styling */
.objective-item {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #fff;
    transition: all 0.2s ease-in-out;
}

.objective-item:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.objective-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.objective-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
}

.objective-description {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.objective-meta {
    display: flex;
    gap: 1rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.objective-actions {
    display: flex;
    gap: 0.5rem;
}

/* Topic Mapping Styling */
.topic-item {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background-color: #fff;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.topic-objectives {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.objective-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem;
    background-color: #e9ecef;
    border-radius: 1rem;
    font-size: 0.875rem;
}

/* Progress Tracking Styling */
.progress-card {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.progress-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.progress {
    height: 0.5rem;
    margin-bottom: 0.5rem;
}

.progress-stats {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Modal Customization */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Checkbox List in Topic Mapping Modal */
#objectiveCheckboxes .list-group-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#objectiveCheckboxes .form-check-input {
    margin: 0;
}

/* Loading States */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .objective-header,
    .topic-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .objective-actions,
    .topic-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

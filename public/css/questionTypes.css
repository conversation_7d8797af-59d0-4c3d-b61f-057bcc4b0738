/* Question Types Styles */

.question {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-text {
    font-size: 1.1rem;
    line-height: 1.5;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

/* Multiple Choice */
.multiple-choice .options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.multiple-choice .option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.multiple-choice .option:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.multiple-choice input[type="radio"] {
    margin-right: 0.5rem;
}

/* Fill in the Blanks */
.fill-in-blanks .blank-input {
    display: inline-block;
    width: 150px;
    padding: 0.5rem;
    margin: 0 0.25rem;
    border: 2px solid #007bff;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.fill-in-blanks .blank-input:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* True/False */
.true-false .options {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
}

.true-false .option {
    flex: 0 0 120px;
    text-align: center;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.true-false .option:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.true-false input[type="radio"] {
    margin-right: 0.5rem;
}

/* Short Answer */
.short-answer .answer-area {
    margin-top: 1rem;
}

.short-answer-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    transition: border-color 0.2s ease;
}

.short-answer-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.word-count {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #6c757d;
    text-align: right;
}

.key-points-hint {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 0 4px 4px 0;
}

.key-points-hint p {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.key-points-hint ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #6c757d;
}

/* Validation States */
.question.valid {
    border-color: #28a745;
}

.question.invalid {
    border-color: #dc3545;
}

.feedback {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.feedback.correct {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback.incorrect {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .true-false .options {
        flex-direction: column;
    }
    
    .true-false .option {
        flex: 1;
    }
    
    .fill-in-blanks .blank-input {
        width: 100%;
        margin: 0.5rem 0;
    }
}

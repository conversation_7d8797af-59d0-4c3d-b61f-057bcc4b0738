<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Analytics - Mock Test Series</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="/" class="text-xl font-bold text-indigo-600">Mock Test Series</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="/dashboard.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                        <a href="/tests.html" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">Tests</a>
                        <a href="/analytics.html" class="text-indigo-600 border-b-2 border-indigo-600 px-3 py-2 text-sm font-medium" aria-current="page">Analytics</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <button id="user-menu-button" class="bg-gray-800 flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                        <span class="sr-only">Open user menu</span>
                        <img id="user-avatar" class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=User" alt="User">
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div id="analytics-container" class="space-y-6">
            <!-- Analytics content will be rendered here by analytics.js -->
        </div>

        <!-- Analytics Template -->
        <template id="analytics-template">
            <div class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Your Performance Analytics</h2>
                
                <div class="mb-4 flex justify-end">
                    <select id="time-range" class="form-select px-4 py-2 rounded-md border-gray-300">
                        <option value="7d">Last 7 Days</option>
                        <option value="30d" selected>Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                        <option value="180d">Last 180 Days</option>
                    </select>
                </div>
                
                <div id="analytics-loading" class="text-center py-8">
                    <div class="loader inline-block"></div>
                    <p class="mt-2 text-gray-600">Loading your analytics...</p>
                </div>
                
                <div id="analytics-content" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-indigo-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Tests Taken</p>
                            <p id="tests-count" class="text-2xl font-bold text-indigo-600">0</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Average Score</p>
                            <p id="average-score" class="text-2xl font-bold text-green-600">0%</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-md text-center">
                            <p class="text-sm text-gray-600">Questions Answered</p>
                            <p id="questions-count" class="text-2xl font-bold text-purple-600">0</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-800 mb-2">Daily Progress</h3>
                            <canvas id="daily-progress-chart" height="200"></canvas>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-800 mb-2">Topic Performance</h3>
                            <canvas id="topic-performance-chart" height="200"></canvas>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-800 mb-2">Time Distribution</h3>
                            <canvas id="time-distribution-chart" height="200"></canvas>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-800 mb-2">Subject Performance</h3>
                            <canvas id="subject-performance-chart" height="200"></canvas>
                        </div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">Weak Areas</h3>
                        <div id="weak-areas-container"></div>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">Personalized Study Plan</h3>
                        <div id="study-plan-container"></div>
                    </div>
                </div>

                <div id="analytics-error" class="hidden">
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">Failed to load analytics</h3>
                        <p class="mt-1 text-sm text-gray-500" id="error-message">Please try again later.</p>
                        <div class="mt-6">
                            <button type="button" id="retry-analytics-btn" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Try again
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </main>

    <div id="toast" class="fixed bottom-4 right-4 transform transition-transform duration-300 ease-in-out translate-y-full">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-start">
                <div class="flex-shrink-0" id="toast-icon"></div>
                <div class="ml-3 w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900" id="toast-title"></p>
                    <p class="mt-1 text-sm text-gray-500" id="toast-message"></p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" id="toast-close">
                        <span class="sr-only">Close</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils.js" type="module"></script>
    <script src="js/analytics.js" type="module"></script>
</body>
</html>

# Test environment configuration
NODE_ENV=test
PORT=3001

# Firebase Test Configuration
FIREBASE_PROJECT_ID=test-project
FIREBASE_PRIVATE_KEY=dummy-key
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Redis Test Configuration
REDIS_URL=redis://localhost:6379/1

# JWT Test Configuration
JWT_SECRET=test-secret
JWT_EXPIRES_IN=1h

# LLM Test Configuration
OPENAI_API_KEY=test-key
DEFAULT_MODEL=gpt-3.5-turbo

# Test Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=test-password

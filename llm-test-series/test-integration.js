#!/usr/bin/env node

/**
 * Integration Test Script
 * Tests the complete flow from authentication to test generation
 */

const API_BASE_URL = 'http://localhost:5000/api';

async function testIntegration() {
    console.log('🚀 Starting Integration Test...\n');

    try {
        // Step 1: Test Anonymous Authentication
        console.log('1️⃣ Testing Anonymous Authentication...');
        const authResponse = await fetch(`${API_BASE_URL}/auth/anonymous`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!authResponse.ok) {
            throw new Error(`Auth failed: ${authResponse.status}`);
        }

        const authData = await authResponse.json();
        console.log('✅ Authentication successful');
        console.log(`   User ID: ${authData.user.id}`);
        console.log(`   Token: ${authData.token.substring(0, 20)}...`);

        const token = authData.token;

        // Step 2: Test Question Generation
        console.log('\n2️⃣ Testing Question Generation...');
        const questionsResponse = await fetch(`${API_BASE_URL}/questions/generate?subject=Physics&level=Beginner&numQuestions=2`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!questionsResponse.ok) {
            throw new Error(`Questions failed: ${questionsResponse.status}`);
        }

        const questionsData = await questionsResponse.json();
        console.log('✅ Question generation successful');
        console.log(`   Questions received: ${questionsData.questions.length}`);
        console.log(`   First question: ${questionsData.questions[0].question.substring(0, 50)}...`);

        // Step 3: Test Test Session Creation
        console.log('\n3️⃣ Testing Test Session Creation...');
        const sessionResponse = await fetch(`${API_BASE_URL}/test-sessions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                testConfig: {
                    subject: 'Physics',
                    level: 'Beginner',
                    numQuestions: 2
                }
            })
        });

        if (!sessionResponse.ok) {
            throw new Error(`Session creation failed: ${sessionResponse.status}`);
        }

        const sessionData = await sessionResponse.json();
        console.log('✅ Test session creation successful');
        console.log(`   Session ID: ${sessionData.sessionId}`);
        console.log(`   Status: ${sessionData.session.status}`);

        // Step 4: Test User Profile
        console.log('\n4️⃣ Testing User Profile...');
        const profileResponse = await fetch(`${API_BASE_URL}/auth/profile`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!profileResponse.ok) {
            throw new Error(`Profile failed: ${profileResponse.status}`);
        }

        const profileData = await profileResponse.json();
        console.log('✅ User profile retrieval successful');
        console.log(`   Name: ${profileData.user.name}`);
        console.log(`   Level: ${profileData.user.profile.level}`);
        console.log(`   XP: ${profileData.user.profile.xp}`);

        console.log('\n🎉 ALL TESTS PASSED! Integration is working correctly.');
        console.log('\n📊 Summary:');
        console.log('   ✅ Authentication: Working');
        console.log('   ✅ Question Generation: Working');
        console.log('   ✅ Test Sessions: Working');
        console.log('   ✅ User Profile: Working');
        console.log('\n🚀 The backend API is fully functional and ready for frontend integration!');

    } catch (error) {
        console.error('\n❌ Integration test failed:', error.message);
        console.error('\n🔧 Check that:');
        console.error('   - Backend is running on http://localhost:5000');
        console.error('   - All routes are properly configured');
        console.error('   - Database connections are working');
        process.exit(1);
    }
}

// Run the test
testIntegration();

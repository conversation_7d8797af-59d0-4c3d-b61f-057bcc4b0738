import { Firestore } from 'firebase-admin/firestore';

export interface LoggingService {
    logInfo(message: string, meta?: Record<string, any>): void;
    logError(message: string, meta?: Record<string, any>): void;
    logWarning(message: string, meta?: Record<string, any>): void;
    logDebug(message: string, meta?: Record<string, any>): void;
}

export interface QuestionAnalysisService {
    calculateAdaptiveDifficulty(userId: string, subject: string, topic: string): Promise<string>;
    trackQuestionDiversity(userId: string, questionId: string, metadata: QuestionMetadata): Promise<void>;
    validateDomainKnowledge(question: Question): Promise<ValidationResult>;
    processMultimediaContent(content: MultimediaContent): Promise<ProcessedContent>;
    calculateQuestionQuality(questionId: string): Promise<QualityScore>;
    getDifficultyStats(topic: string): Promise<DifficultyStats>;
    getQuestionRecommendations(userId: string, topic: string): Promise<Question[]>;
}

export interface TheoryService {
    getTheoryExplanation(topic: string, userLevel?: string): Promise<string>;
    getRelatedConcepts(concept: string): Promise<RelatedConcept[]>;
    generateExplanation(topic: string, userLevel?: string): Promise<string>;
    callLLM(prompt: string, options: LLMOptions): Promise<string>;
}

export interface Question {
    id?: string;
    subject: string;
    topic: string;
    content: string;
    difficulty: string;
    keywords: string[];
}

export interface QuestionMetadata {
    subject: string;
    topic: string;
    difficulty: string;
}

export interface ValidationResult {
    isValid: boolean;
    score: number;
    issues?: string[];
}

export interface MultimediaContent {
    type: 'image' | 'code' | 'diagram';
    data: Buffer;
    metadata: {
        width?: number;
        height?: number;
        format?: string;
        language?: string;
    };
}

export interface ProcessedContent {
    optimized: boolean;
    url: string;
    metadata?: Record<string, any>;
}

export interface QualityScore {
    score: number;
    metrics: {
        helpfulness: number;
        clarity: number;
        difficulty: number;
    };
}

export interface DifficultyStats {
    [key: string]: {
        attempts: number;
        correctRate: number;
    };
}

export interface RelatedConcept {
    concept: string;
    score: number;
}

export interface LLMOptions {
    difficulty?: string;
    maxTokens?: number;
    temperature?: number;
}

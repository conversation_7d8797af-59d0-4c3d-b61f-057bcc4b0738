// Test Sessions Schema for AI-Powered Adaptive Learning Platform
export const testSessionSchema = {
  type: 'object',
  required: ['sessionId', 'userId', 'config', 'status'],
  properties: {
    sessionId: { 
      type: 'string',
      description: 'Unique identifier for the test session'
    },
    userId: { 
      type: 'string',
      description: 'ID of the user taking the test'
    },
    config: {
      type: 'object',
      required: ['testType', 'subject', 'difficulty'],
      properties: {
        testType: {
          type: 'string',
          enum: ['practice', 'mock', 'challenge', 'adaptive', 'timed'],
          description: 'Type of test session'
        },
        subject: { type: 'string', description: 'Main subject for the test' },
        topics: { 
          type: 'array', 
          items: { type: 'string' },
          description: 'Specific topics to focus on'
        },
        difficulty: {
          type: 'string',
          enum: ['beginner', 'intermediate', 'advanced', 'expert', 'adaptive'],
          default: 'intermediate'
        },
        questionCount: { 
          type: 'integer', 
          minimum: 1, 
          maximum: 200,
          default: 20
        },
        timeLimit: { 
          type: 'integer', 
          minimum: 0,
          description: 'Time limit in seconds, 0 for unlimited'
        },
        syllabusId: { 
          type: 'string',
          description: 'Syllabus to generate questions from'
        },
        questionMix: {
          type: 'object',
          properties: {
            aiGenerated: { type: 'number', minimum: 0, maximum: 1, default: 0.6 },
            pastYear: { type: 'number', minimum: 0, maximum: 1, default: 0.3 },
            manual: { type: 'number', minimum: 0, maximum: 1, default: 0.1 }
          }
        },
        adaptiveSettings: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean', default: false },
            promotionThreshold: { type: 'number', default: 0.8 },
            demotionThreshold: { type: 'number', default: 0.4 },
            minQuestions: { type: 'integer', default: 10 },
            maxQuestions: { type: 'integer', default: 50 }
          }
        }
      }
    },
    status: {
      type: 'string',
      enum: ['created', 'in_progress', 'paused', 'completed', 'abandoned', 'expired'],
      default: 'created'
    },
    questions: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          questionId: { type: 'string' },
          order: { type: 'integer' },
          difficulty: { type: 'string' },
          topic: { type: 'string' },
          source: { type: 'string' },
          points: { type: 'integer', default: 1 }
        }
      }
    },
    answers: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          questionId: { type: 'string' },
          userAnswer: { type: 'string' },
          isCorrect: { type: 'boolean' },
          timeSpent: { type: 'integer', description: 'Time in seconds' },
          confidence: { type: 'integer', minimum: 1, maximum: 5 },
          hintsUsed: { type: 'integer', default: 0 },
          submittedAt: { type: 'string', format: 'date-time' },
          explanation: { type: 'string' },
          pointsEarned: { type: 'integer', default: 0 }
        }
      }
    },
    results: {
      type: 'object',
      properties: {
        score: { type: 'number', minimum: 0, maximum: 100 },
        totalQuestions: { type: 'integer' },
        correctAnswers: { type: 'integer' },
        incorrectAnswers: { type: 'integer' },
        skippedAnswers: { type: 'integer' },
        totalTime: { type: 'integer', description: 'Total time in seconds' },
        averageTimePerQuestion: { type: 'number' },
        accuracy: { type: 'number', minimum: 0, maximum: 100 },
        grade: { type: 'string' },
        percentile: { type: 'number', minimum: 0, maximum: 100 },
        topicWisePerformance: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              attempted: { type: 'integer' },
              correct: { type: 'integer' },
              accuracy: { type: 'number' },
              averageTime: { type: 'number' }
            }
          }
        },
        difficultyProgression: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              questionNumber: { type: 'integer' },
              difficulty: { type: 'string' },
              isCorrect: { type: 'boolean' }
            }
          }
        }
      }
    },
    gamification: {
      type: 'object',
      properties: {
        xpEarned: { type: 'integer', default: 0 },
        bonusXp: { type: 'integer', default: 0 },
        streakBonus: { type: 'integer', default: 0 },
        perfectScoreBonus: { type: 'integer', default: 0 },
        speedBonus: { type: 'integer', default: 0 },
        badgesEarned: { 
          type: 'array', 
          items: { type: 'string' },
          default: []
        },
        achievementsUnlocked: {
          type: 'array',
          items: { type: 'string' },
          default: []
        },
        milestones: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: { type: 'string' },
              description: { type: 'string' },
              xpReward: { type: 'integer' }
            }
          }
        }
      }
    },
    analytics: {
      type: 'object',
      properties: {
        startTime: { type: 'string', format: 'date-time' },
        endTime: { type: 'string', format: 'date-time' },
        pausedDuration: { type: 'integer', default: 0 },
        deviceInfo: {
          type: 'object',
          properties: {
            userAgent: { type: 'string' },
            platform: { type: 'string' },
            screenResolution: { type: 'string' }
          }
        },
        behaviorMetrics: {
          type: 'object',
          properties: {
            questionRevisits: { type: 'integer', default: 0 },
            answerChanges: { type: 'integer', default: 0 },
            hintsRequested: { type: 'integer', default: 0 },
            explanationsViewed: { type: 'integer', default: 0 },
            pauseCount: { type: 'integer', default: 0 }
          }
        },
        learningPatterns: {
          type: 'object',
          properties: {
            fastAnswers: { type: 'integer', default: 0 },
            slowAnswers: { type: 'integer', default: 0 },
            confidenceAccuracy: { type: 'number' },
            improvementTrend: { type: 'string' }
          }
        }
      }
    },
    feedback: {
      type: 'object',
      properties: {
        difficulty: { type: 'integer', minimum: 1, maximum: 5 },
        quality: { type: 'integer', minimum: 1, maximum: 5 },
        comments: { type: 'string' },
        suggestions: { type: 'string' },
        reportedIssues: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              questionId: { type: 'string' },
              issueType: { type: 'string' },
              description: { type: 'string' }
            }
          }
        }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    completedAt: { type: 'string', format: 'date-time' },
    version: { type: 'integer', default: 1 }
  }
};

// Test session status transitions
export const statusTransitions = {
  created: ['in_progress', 'abandoned'],
  in_progress: ['paused', 'completed', 'abandoned', 'expired'],
  paused: ['in_progress', 'abandoned', 'expired'],
  completed: [], // Terminal state
  abandoned: [], // Terminal state
  expired: []    // Terminal state
};

// Default test configurations
export const defaultTestConfigs = {
  practice: {
    questionCount: 20,
    timeLimit: 0, // Unlimited
    adaptiveSettings: { enabled: false }
  },
  mock: {
    questionCount: 100,
    timeLimit: 10800, // 3 hours
    adaptiveSettings: { enabled: false }
  },
  challenge: {
    questionCount: 10,
    timeLimit: 600, // 10 minutes
    adaptiveSettings: { enabled: false }
  },
  adaptive: {
    questionCount: 30,
    timeLimit: 0,
    adaptiveSettings: { 
      enabled: true,
      minQuestions: 15,
      maxQuestions: 50
    }
  },
  timed: {
    questionCount: 50,
    timeLimit: 3600, // 1 hour
    adaptiveSettings: { enabled: false }
  }
};

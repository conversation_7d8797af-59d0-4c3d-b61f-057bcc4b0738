// Enhanced Questions Schema for AI-Powered Adaptive Learning Platform
export const questionSchema = {
  type: 'object',
  required: ['questionId', 'content', 'metadata'],
  properties: {
    questionId: { 
      type: 'string',
      description: 'Unique identifier for the question'
    },
    content: {
      type: 'object',
      required: ['question', 'options', 'correctAnswer'],
      properties: {
        question: { 
          type: 'string',
          minLength: 10,
          description: 'The question text'
        },
        options: {
          type: 'array',
          items: { type: 'string' },
          minItems: 2,
          maxItems: 6,
          description: 'Answer options'
        },
        correctAnswer: { 
          type: 'string',
          description: 'The correct answer (should match one of the options)'
        },
        explanation: {
          type: 'object',
          properties: {
            basic: { type: 'string', description: 'Simple explanation for beginners' },
            intermediate: { type: 'string', description: 'Detailed explanation' },
            advanced: { type: 'string', description: 'Comprehensive explanation with formulas' }
          }
        },
        hints: {
          type: 'array',
          items: { type: 'string' },
          maxItems: 3,
          description: 'Progressive hints to help users'
        },
        relatedConcepts: {
          type: 'array',
          items: { type: 'string' },
          description: 'Related concepts for further study'
        },
        images: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              url: { type: 'string', format: 'uri' },
              alt: { type: 'string' },
              caption: { type: 'string' }
            }
          }
        },
        latex: {
          type: 'object',
          properties: {
            question: { type: 'string', description: 'LaTeX formatted question' },
            options: { type: 'array', items: { type: 'string' } },
            explanation: { type: 'string', description: 'LaTeX formatted explanation' }
          }
        }
      }
    },
    metadata: {
      type: 'object',
      required: ['subject', 'topic', 'difficulty'],
      properties: {
        subject: { type: 'string', description: 'Main subject (e.g., Physics, Chemistry)' },
        topic: { type: 'string', description: 'Specific topic within the subject' },
        subtopic: { type: 'string', description: 'More specific subtopic' },
        difficulty: {
          type: 'string',
          enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          description: 'Difficulty level'
        },
        syllabus: {
          type: 'object',
          properties: {
            syllabusId: { type: 'string' },
            unitId: { type: 'string' },
            topicId: { type: 'string' },
            subtopicId: { type: 'string' }
          }
        },
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: 'Tags for categorization and search'
        },
        estimatedTime: { 
          type: 'integer', 
          minimum: 10,
          maximum: 600,
          description: 'Estimated time to answer in seconds'
        },
        sourceTag: { 
          type: 'string',
          description: 'Source of the question (e.g., AI Generated, Past Year, Manual)'
        },
        bloomsLevel: {
          type: 'string',
          enum: ['remember', 'understand', 'apply', 'analyze', 'evaluate', 'create'],
          description: 'Blooms taxonomy level'
        },
        questionType: {
          type: 'string',
          enum: ['multiple-choice', 'true-false', 'fill-blank', 'short-answer', 'essay'],
          default: 'multiple-choice'
        }
      }
    },
    analytics: {
      type: 'object',
      properties: {
        totalAttempts: { type: 'integer', minimum: 0, default: 0 },
        correctAttempts: { type: 'integer', minimum: 0, default: 0 },
        averageTime: { type: 'number', minimum: 0, default: 0 },
        difficultyRating: { 
          type: 'number', 
          minimum: 1, 
          maximum: 5,
          description: 'User-rated difficulty (1-5 scale)'
        },
        qualityRating: { 
          type: 'number', 
          minimum: 1, 
          maximum: 5,
          description: 'User-rated quality (1-5 scale)'
        },
        reportCount: { type: 'integer', minimum: 0, default: 0 },
        lastUsed: { type: 'string', format: 'date-time' },
        popularityScore: { type: 'number', minimum: 0, default: 0 }
      }
    },
    gamification: {
      type: 'object',
      properties: {
        basePoints: { 
          type: 'integer', 
          minimum: 1,
          maximum: 100,
          description: 'Base XP points for correct answer'
        },
        bonusMultiplier: { 
          type: 'number', 
          minimum: 1.0,
          maximum: 5.0,
          default: 1.0,
          description: 'Multiplier for bonus points'
        },
        isLucky: { 
          type: 'boolean', 
          default: false,
          description: 'Special lucky question with extra rewards'
        },
        unlockLevel: { 
          type: 'integer', 
          minimum: 1,
          default: 1,
          description: 'Minimum level required to access this question'
        },
        streakBonus: { 
          type: 'boolean', 
          default: false,
          description: 'Whether this question contributes to streak'
        }
      }
    },
    validation: {
      type: 'object',
      properties: {
        isVerified: { type: 'boolean', default: false },
        verifiedBy: { type: 'string' },
        verificationDate: { type: 'string', format: 'date-time' },
        qualityScore: { type: 'number', minimum: 0, maximum: 100 },
        needsReview: { type: 'boolean', default: false },
        reviewNotes: { type: 'string' }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    createdBy: { type: 'string', description: 'User ID or system that created the question' },
    version: { type: 'integer', minimum: 1, default: 1 }
  }
};

// Question difficulty point mapping
export const difficultyPoints = {
  beginner: 5,
  intermediate: 10,
  advanced: 15,
  expert: 20
};

// Question type configurations
export const questionTypeConfig = {
  'multiple-choice': {
    minOptions: 2,
    maxOptions: 6,
    defaultOptions: 4,
    timeMultiplier: 1.0
  },
  'true-false': {
    minOptions: 2,
    maxOptions: 2,
    defaultOptions: 2,
    timeMultiplier: 0.5
  },
  'fill-blank': {
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    timeMultiplier: 1.2
  },
  'short-answer': {
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    timeMultiplier: 2.0
  },
  'essay': {
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    timeMultiplier: 5.0
  }
};

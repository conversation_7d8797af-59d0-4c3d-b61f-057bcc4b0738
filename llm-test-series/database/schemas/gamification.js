// Gamification Schema for AI-Powered Adaptive Learning Platform
export const gamificationSchema = {
  type: 'object',
  required: ['userId'],
  properties: {
    userId: { 
      type: 'string',
      description: 'Unique identifier for the user'
    },
    level: {
      type: 'object',
      properties: {
        current: { type: 'integer', minimum: 1, default: 1 },
        xp: { type: 'integer', minimum: 0, default: 0 },
        totalXp: { type: 'integer', minimum: 0, default: 0 },
        xpToNextLevel: { type: 'integer', minimum: 0 },
        progression: { type: 'number', minimum: 0, maximum: 100, default: 0 }
      }
    },
    streaks: {
      type: 'object',
      properties: {
        current: { type: 'integer', minimum: 0, default: 0 },
        longest: { type: 'integer', minimum: 0, default: 0 },
        lastStudyDate: { type: 'string', format: 'date' },
        weeklyStreak: { type: 'integer', minimum: 0, default: 0 },
        monthlyStreak: { type: 'integer', minimum: 0, default: 0 },
        streakMultiplier: { type: 'number', minimum: 1.0, default: 1.0 }
      }
    },
    badges: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          badgeId: { type: 'string' },
          name: { type: 'string' },
          description: { type: 'string' },
          category: { type: 'string' },
          rarity: { 
            type: 'string',
            enum: ['common', 'rare', 'epic', 'legendary']
          },
          unlockedAt: { type: 'string', format: 'date-time' },
          progress: { type: 'number', minimum: 0, maximum: 100 },
          isVisible: { type: 'boolean', default: true }
        }
      },
      default: []
    },
    achievements: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          achievementId: { type: 'string' },
          name: { type: 'string' },
          description: { type: 'string' },
          category: { type: 'string' },
          tier: { type: 'integer', minimum: 1, maximum: 5 },
          progress: { type: 'number', minimum: 0, maximum: 100 },
          isCompleted: { type: 'boolean', default: false },
          completedAt: { type: 'string', format: 'date-time' },
          reward: {
            type: 'object',
            properties: {
              xp: { type: 'integer', default: 0 },
              badge: { type: 'string' },
              title: { type: 'string' },
              special: { type: 'string' }
            }
          }
        }
      }
    },
    statistics: {
      type: 'object',
      properties: {
        totalTests: { type: 'integer', minimum: 0, default: 0 },
        totalQuestions: { type: 'integer', minimum: 0, default: 0 },
        correctAnswers: { type: 'integer', minimum: 0, default: 0 },
        totalStudyTime: { type: 'integer', minimum: 0, default: 0 },
        averageAccuracy: { type: 'number', minimum: 0, maximum: 100, default: 0 },
        perfectScores: { type: 'integer', minimum: 0, default: 0 },
        challengesWon: { type: 'integer', minimum: 0, default: 0 },
        challengesLost: { type: 'integer', minimum: 0, default: 0 },
        friendsChallenged: { type: 'integer', minimum: 0, default: 0 },
        helpfulVotes: { type: 'integer', minimum: 0, default: 0 }
      }
    },
    leaderboards: {
      type: 'object',
      properties: {
        global: {
          type: 'object',
          properties: {
            rank: { type: 'integer', minimum: 1 },
            score: { type: 'integer', minimum: 0 },
            lastUpdated: { type: 'string', format: 'date-time' }
          }
        },
        weekly: {
          type: 'object',
          properties: {
            rank: { type: 'integer', minimum: 1 },
            score: { type: 'integer', minimum: 0 },
            weekStart: { type: 'string', format: 'date' }
          }
        },
        monthly: {
          type: 'object',
          properties: {
            rank: { type: 'integer', minimum: 1 },
            score: { type: 'integer', minimum: 0 },
            monthStart: { type: 'string', format: 'date' }
          }
        },
        subject: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              rank: { type: 'integer', minimum: 1 },
              score: { type: 'integer', minimum: 0 },
              accuracy: { type: 'number', minimum: 0, maximum: 100 }
            }
          }
        }
      }
    },
    rewards: {
      type: 'object',
      properties: {
        daily: {
          type: 'object',
          properties: {
            lastClaimed: { type: 'string', format: 'date' },
            streak: { type: 'integer', minimum: 0, default: 0 },
            available: { type: 'boolean', default: true },
            nextReward: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                amount: { type: 'integer' },
                description: { type: 'string' }
              }
            }
          }
        },
        weekly: {
          type: 'object',
          properties: {
            lastClaimed: { type: 'string', format: 'date' },
            available: { type: 'boolean', default: false },
            progress: { type: 'number', minimum: 0, maximum: 100, default: 0 }
          }
        },
        special: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              rewardId: { type: 'string' },
              type: { type: 'string' },
              description: { type: 'string' },
              claimedAt: { type: 'string', format: 'date-time' },
              expiresAt: { type: 'string', format: 'date-time' }
            }
          }
        }
      }
    },
    preferences: {
      type: 'object',
      properties: {
        notifications: {
          type: 'object',
          properties: {
            levelUp: { type: 'boolean', default: true },
            badgeUnlocked: { type: 'boolean', default: true },
            achievementCompleted: { type: 'boolean', default: true },
            streakReminder: { type: 'boolean', default: true },
            challengeReceived: { type: 'boolean', default: true },
            leaderboardUpdate: { type: 'boolean', default: false }
          }
        },
        privacy: {
          type: 'object',
          properties: {
            showOnLeaderboard: { type: 'boolean', default: true },
            showBadges: { type: 'boolean', default: true },
            showAchievements: { type: 'boolean', default: true },
            allowChallenges: { type: 'boolean', default: true }
          }
        }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  }
};

// Badge definitions
export const badgeDefinitions = {
  first_test: {
    name: "First Steps",
    description: "Complete your first test",
    category: "milestone",
    rarity: "common",
    icon: "🎯"
  },
  streak_warrior: {
    name: "Streak Warrior",
    description: "Maintain a 7-day study streak",
    category: "consistency",
    rarity: "rare",
    icon: "🔥"
  },
  physics_master: {
    name: "Physics Master",
    description: "Score 90%+ in 10 Physics tests",
    category: "subject",
    rarity: "epic",
    icon: "⚛️"
  },
  speed_demon: {
    name: "Speed Demon",
    description: "Answer 50 questions in under 30 seconds each",
    category: "performance",
    rarity: "rare",
    icon: "⚡"
  },
  perfectionist: {
    name: "Perfectionist",
    description: "Score 100% in any test",
    category: "achievement",
    rarity: "epic",
    icon: "💯"
  },
  challenger: {
    name: "Challenger",
    description: "Win 10 friend challenges",
    category: "social",
    rarity: "rare",
    icon: "⚔️"
  },
  knowledge_seeker: {
    name: "Knowledge Seeker",
    description: "View explanations for 100 questions",
    category: "learning",
    rarity: "common",
    icon: "📚"
  },
  legend: {
    name: "Legend",
    description: "Reach level 50",
    category: "milestone",
    rarity: "legendary",
    icon: "👑"
  }
};

// Achievement definitions
export const achievementDefinitions = {
  early_bird: {
    name: "Early Bird",
    description: "Study before 8 AM for 5 consecutive days",
    category: "habits",
    tiers: [5, 10, 20, 30, 50],
    rewards: [50, 100, 200, 400, 800]
  },
  night_owl: {
    name: "Night Owl",
    description: "Study after 10 PM for 5 consecutive days",
    category: "habits",
    tiers: [5, 10, 20, 30, 50],
    rewards: [50, 100, 200, 400, 800]
  },
  accuracy_ace: {
    name: "Accuracy Ace",
    description: "Maintain 95%+ accuracy over multiple tests",
    category: "performance",
    tiers: [5, 10, 25, 50, 100],
    rewards: [100, 250, 500, 1000, 2000]
  },
  social_butterfly: {
    name: "Social Butterfly",
    description: "Challenge different friends",
    category: "social",
    tiers: [5, 10, 25, 50, 100],
    rewards: [75, 150, 300, 600, 1200]
  },
  subject_specialist: {
    name: "Subject Specialist",
    description: "Master a specific subject",
    category: "expertise",
    tiers: [10, 25, 50, 100, 200],
    rewards: [200, 400, 800, 1600, 3200]
  }
};

// XP calculation formulas
export const xpFormulas = {
  baseQuestionXp: 10,
  difficultyMultipliers: {
    beginner: 1.0,
    intermediate: 1.2,
    advanced: 1.5,
    expert: 2.0
  },
  bonusMultipliers: {
    perfectScore: 1.5,
    fastAnswer: 1.2,
    streak: 1.1,
    firstAttempt: 1.0,
    challenge: 1.3
  },
  levelThresholds: [
    0, 100, 250, 450, 700, 1000, 1350, 1750, 2200, 2700, 3250,
    3850, 4500, 5200, 5950, 6750, 7600, 8500, 9450, 10450, 11500
  ]
};

// Syllabi Schema for AI-Powered Adaptive Learning Platform
export const syllabusSchema = {
  type: 'object',
  required: ['syllabusId', 'name', 'subject', 'structure'],
  properties: {
    syllabusId: { 
      type: 'string',
      description: 'Unique identifier for the syllabus'
    },
    name: { 
      type: 'string',
      minLength: 3,
      maxLength: 100,
      description: 'Name of the syllabus (e.g., "CBSE Class 12 Physics")'
    },
    subject: { 
      type: 'string',
      description: 'Main subject of the syllabus'
    },
    examType: {
      type: 'string',
      enum: ['board', 'competitive', 'entrance', 'certification', 'custom'],
      description: 'Type of examination this syllabus is for'
    },
    board: {
      type: 'string',
      description: 'Educational board (CBSE, ICSE, State Board, etc.)'
    },
    class: {
      type: 'string',
      description: 'Class/Grade level'
    },
    version: {
      type: 'string',
      default: '1.0',
      description: 'Version of the syllabus'
    },
    structure: {
      type: 'object',
      required: ['units'],
      properties: {
        units: {
          type: 'array',
          items: {
            type: 'object',
            required: ['unitId', 'name', 'chapters'],
            properties: {
              unitId: { type: 'string' },
              name: { type: 'string' },
              description: { type: 'string' },
              weightage: { 
                type: 'number', 
                minimum: 0, 
                maximum: 100,
                description: 'Percentage weightage in exam'
              },
              estimatedHours: { 
                type: 'integer',
                minimum: 1,
                description: 'Estimated study hours'
              },
              chapters: {
                type: 'array',
                items: {
                  type: 'object',
                  required: ['chapterId', 'name', 'topics'],
                  properties: {
                    chapterId: { type: 'string' },
                    name: { type: 'string' },
                    description: { type: 'string' },
                    learningObjectives: {
                      type: 'array',
                      items: { type: 'string' }
                    },
                    estimatedHours: { type: 'integer', minimum: 1 },
                    difficulty: {
                      type: 'string',
                      enum: ['beginner', 'intermediate', 'advanced'],
                      default: 'intermediate'
                    },
                    topics: {
                      type: 'array',
                      items: {
                        type: 'object',
                        required: ['topicId', 'name'],
                        properties: {
                          topicId: { type: 'string' },
                          name: { type: 'string' },
                          description: { type: 'string' },
                          subtopics: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                subtopicId: { type: 'string' },
                                name: { type: 'string' },
                                description: { type: 'string' },
                                concepts: {
                                  type: 'array',
                                  items: { type: 'string' }
                                },
                                formulas: {
                                  type: 'array',
                                  items: {
                                    type: 'object',
                                    properties: {
                                      name: { type: 'string' },
                                      formula: { type: 'string' },
                                      description: { type: 'string' }
                                    }
                                  }
                                },
                                keyPoints: {
                                  type: 'array',
                                  items: { type: 'string' }
                                }
                              }
                            }
                          },
                          difficulty: {
                            type: 'string',
                            enum: ['beginner', 'intermediate', 'advanced'],
                            default: 'intermediate'
                          },
                          estimatedTime: {
                            type: 'integer',
                            minimum: 15,
                            description: 'Estimated study time in minutes'
                          },
                          prerequisites: {
                            type: 'array',
                            items: { type: 'string' },
                            description: 'Topic IDs that should be studied first'
                          },
                          tags: {
                            type: 'array',
                            items: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        totalUnits: { type: 'integer', minimum: 1 },
        totalChapters: { type: 'integer', minimum: 1 },
        totalTopics: { type: 'integer', minimum: 1 },
        estimatedCompletionHours: { type: 'integer', minimum: 1 }
      }
    },
    metadata: {
      type: 'object',
      properties: {
        academicYear: { type: 'string' },
        language: { type: 'string', default: 'en' },
        region: { type: 'string' },
        lastUpdated: { type: 'string', format: 'date-time' },
        source: { type: 'string' },
        officialDocument: { type: 'string' },
        approvedBy: { type: 'string' },
        tags: {
          type: 'array',
          items: { type: 'string' }
        }
      }
    },
    questionDistribution: {
      type: 'object',
      properties: {
        totalQuestions: { type: 'integer', minimum: 0, default: 0 },
        byDifficulty: {
          type: 'object',
          properties: {
            beginner: { type: 'integer', minimum: 0, default: 0 },
            intermediate: { type: 'integer', minimum: 0, default: 0 },
            advanced: { type: 'integer', minimum: 0, default: 0 },
            expert: { type: 'integer', minimum: 0, default: 0 }
          }
        },
        byUnit: {
          type: 'object',
          additionalProperties: {
            type: 'integer',
            minimum: 0
          }
        },
        byChapter: {
          type: 'object',
          additionalProperties: {
            type: 'integer',
            minimum: 0
          }
        },
        byTopic: {
          type: 'object',
          additionalProperties: {
            type: 'integer',
            minimum: 0
          }
        }
      }
    },
    analytics: {
      type: 'object',
      properties: {
        usage: {
          type: 'object',
          properties: {
            totalUsers: { type: 'integer', minimum: 0, default: 0 },
            activeUsers: { type: 'integer', minimum: 0, default: 0 },
            totalTests: { type: 'integer', minimum: 0, default: 0 },
            averageScore: { type: 'number', minimum: 0, maximum: 100, default: 0 }
          }
        },
        performance: {
          type: 'object',
          properties: {
            easiestTopics: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  topicId: { type: 'string' },
                  averageScore: { type: 'number' }
                }
              }
            },
            hardestTopics: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  topicId: { type: 'string' },
                  averageScore: { type: 'number' }
                }
              }
            },
            completionRates: {
              type: 'object',
              additionalProperties: {
                type: 'number',
                minimum: 0,
                maximum: 100
              }
            }
          }
        }
      }
    },
    settings: {
      type: 'object',
      properties: {
        isActive: { type: 'boolean', default: true },
        isPublic: { type: 'boolean', default: true },
        allowQuestionGeneration: { type: 'boolean', default: true },
        maxQuestionsPerTopic: { type: 'integer', minimum: 1, default: 50 },
        adaptiveDifficulty: { type: 'boolean', default: true },
        prerequisites: {
          type: 'array',
          items: { type: 'string' },
          description: 'Other syllabus IDs that should be completed first'
        }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' },
    createdBy: { type: 'string' },
    lastModifiedBy: { type: 'string' }
  }
};

// Learning objectives taxonomy
export const learningObjectiveTypes = {
  REMEMBER: 'remember',
  UNDERSTAND: 'understand',
  APPLY: 'apply',
  ANALYZE: 'analyze',
  EVALUATE: 'evaluate',
  CREATE: 'create'
};

// Difficulty progression mapping
export const difficultyProgression = {
  beginner: {
    next: 'intermediate',
    questionTypes: ['multiple-choice', 'true-false'],
    cognitiveLoad: 'low',
    bloomsLevels: ['remember', 'understand']
  },
  intermediate: {
    previous: 'beginner',
    next: 'advanced',
    questionTypes: ['multiple-choice', 'fill-blank', 'short-answer'],
    cognitiveLoad: 'medium',
    bloomsLevels: ['understand', 'apply', 'analyze']
  },
  advanced: {
    previous: 'intermediate',
    next: 'expert',
    questionTypes: ['multiple-choice', 'short-answer', 'essay'],
    cognitiveLoad: 'high',
    bloomsLevels: ['apply', 'analyze', 'evaluate', 'create']
  },
  expert: {
    previous: 'advanced',
    questionTypes: ['short-answer', 'essay', 'problem-solving'],
    cognitiveLoad: 'very-high',
    bloomsLevels: ['analyze', 'evaluate', 'create']
  }
};

// Subject-specific configurations
export const subjectConfigurations = {
  physics: {
    hasFormulas: true,
    hasNumericals: true,
    hasGraphs: true,
    hasExperiments: true,
    commonQuestionTypes: ['numerical', 'conceptual', 'derivation', 'graph-based']
  },
  chemistry: {
    hasFormulas: true,
    hasReactions: true,
    hasStructures: true,
    hasExperiments: true,
    commonQuestionTypes: ['reaction-based', 'structure', 'numerical', 'conceptual']
  },
  mathematics: {
    hasFormulas: true,
    hasProofs: true,
    hasGraphs: true,
    hasNumericals: true,
    commonQuestionTypes: ['numerical', 'proof', 'graph', 'theorem-based']
  },
  biology: {
    hasDiagrams: true,
    hasClassification: true,
    hasProcesses: true,
    commonQuestionTypes: ['diagram-based', 'process', 'classification', 'conceptual']
  }
};

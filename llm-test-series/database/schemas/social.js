// Social Features Schema for AI-Powered Adaptive Learning Platform
export const socialSchema = {
  // Study Groups Collection
  studyGroups: {
    type: 'object',
    required: ['groupId', 'name', 'createdBy'],
    properties: {
      groupId: { type: 'string' },
      name: { type: 'string', minLength: 3, maxLength: 50 },
      description: { type: 'string', maxLength: 500 },
      createdBy: { type: 'string' },
      members: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            role: { 
              type: 'string',
              enum: ['admin', 'moderator', 'member'],
              default: 'member'
            },
            joinedAt: { type: 'string', format: 'date-time' },
            lastActive: { type: 'string', format: 'date-time' },
            permissions: {
              type: 'array',
              items: { type: 'string' },
              default: ['participate', 'view_content']
            }
          }
        },
        maxItems: 100
      },
      settings: {
        type: 'object',
        properties: {
          isPrivate: { type: 'boolean', default: false },
          requireApproval: { type: 'boolean', default: false },
          maxMembers: { type: 'integer', minimum: 2, maximum: 100, default: 50 },
          subjects: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 10
          },
          targetExam: { type: 'string' },
          studySchedule: {
            type: 'object',
            properties: {
              timezone: { type: 'string' },
              regularSessions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    day: { type: 'string' },
                    startTime: { type: 'string' },
                    duration: { type: 'integer' }
                  }
                }
              }
            }
          }
        }
      },
      activity: {
        type: 'object',
        properties: {
          totalSessions: { type: 'integer', default: 0 },
          totalMessages: { type: 'integer', default: 0 },
          averageScore: { type: 'number', default: 0 },
          activeMembers: { type: 'integer', default: 0 },
          lastActivity: { type: 'string', format: 'date-time' }
        }
      },
      challenges: {
        type: 'array',
        items: { type: 'string' },
        default: []
      },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    }
  },

  // Challenges Collection
  challenges: {
    type: 'object',
    required: ['challengeId', 'challengerId', 'challengedId', 'type'],
    properties: {
      challengeId: { type: 'string' },
      challengerId: { type: 'string' },
      challengedId: { type: 'string' },
      type: {
        type: 'string',
        enum: ['direct', 'group', 'public', 'tournament']
      },
      config: {
        type: 'object',
        properties: {
          subject: { type: 'string' },
          topics: { 
            type: 'array', 
            items: { type: 'string' } 
          },
          difficulty: { type: 'string' },
          questionCount: { type: 'integer', minimum: 5, maximum: 50 },
          timeLimit: { type: 'integer', minimum: 60 },
          wager: {
            type: 'object',
            properties: {
              type: { type: 'string', enum: ['xp', 'badge', 'title'] },
              amount: { type: 'integer' },
              description: { type: 'string' }
            }
          }
        }
      },
      status: {
        type: 'string',
        enum: ['pending', 'accepted', 'declined', 'in_progress', 'completed', 'expired'],
        default: 'pending'
      },
      participants: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            status: { type: 'string' },
            score: { type: 'number' },
            completedAt: { type: 'string', format: 'date-time' },
            sessionId: { type: 'string' }
          }
        }
      },
      results: {
        type: 'object',
        properties: {
          winnerId: { type: 'string' },
          winnerScore: { type: 'number' },
          margin: { type: 'number' },
          isTie: { type: 'boolean', default: false },
          rewards: {
            type: 'object',
            additionalProperties: {
              type: 'object',
              properties: {
                xp: { type: 'integer' },
                badges: { type: 'array', items: { type: 'string' } },
                title: { type: 'string' }
              }
            }
          }
        }
      },
      messages: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            messageId: { type: 'string' },
            senderId: { type: 'string' },
            content: { type: 'string', maxLength: 500 },
            type: { 
              type: 'string',
              enum: ['text', 'system', 'celebration', 'taunt']
            },
            timestamp: { type: 'string', format: 'date-time' }
          }
        }
      },
      createdAt: { type: 'string', format: 'date-time' },
      expiresAt: { type: 'string', format: 'date-time' },
      completedAt: { type: 'string', format: 'date-time' }
    }
  },

  // Friends Collection
  friends: {
    type: 'object',
    required: ['userId', 'friendId'],
    properties: {
      userId: { type: 'string' },
      friendId: { type: 'string' },
      status: {
        type: 'string',
        enum: ['pending', 'accepted', 'blocked'],
        default: 'pending'
      },
      requestedBy: { type: 'string' },
      requestedAt: { type: 'string', format: 'date-time' },
      acceptedAt: { type: 'string', format: 'date-time' },
      relationship: {
        type: 'object',
        properties: {
          nickname: { type: 'string', maxLength: 30 },
          tags: { 
            type: 'array', 
            items: { type: 'string' },
            maxItems: 5
          },
          sharedSubjects: {
            type: 'array',
            items: { type: 'string' }
          },
          competitionStats: {
            type: 'object',
            properties: {
              challengesSent: { type: 'integer', default: 0 },
              challengesReceived: { type: 'integer', default: 0 },
              wins: { type: 'integer', default: 0 },
              losses: { type: 'integer', default: 0 },
              ties: { type: 'integer', default: 0 }
            }
          }
        }
      },
      privacy: {
        type: 'object',
        properties: {
          shareProgress: { type: 'boolean', default: true },
          shareSchedule: { type: 'boolean', default: false },
          allowChallenges: { type: 'boolean', default: true },
          showOnlineStatus: { type: 'boolean', default: true }
        }
      },
      lastInteraction: { type: 'string', format: 'date-time' }
    }
  },

  // Social Feed Collection
  socialFeed: {
    type: 'object',
    required: ['postId', 'userId', 'type'],
    properties: {
      postId: { type: 'string' },
      userId: { type: 'string' },
      type: {
        type: 'string',
        enum: ['achievement', 'milestone', 'challenge_result', 'study_session', 'tip_share', 'question_discussion']
      },
      content: {
        type: 'object',
        properties: {
          title: { type: 'string', maxLength: 100 },
          description: { type: 'string', maxLength: 500 },
          data: { type: 'object' }, // Flexible data based on post type
          media: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                type: { type: 'string', enum: ['image', 'chart', 'badge'] },
                url: { type: 'string' },
                caption: { type: 'string' }
              }
            }
          }
        }
      },
      visibility: {
        type: 'string',
        enum: ['public', 'friends', 'group', 'private'],
        default: 'friends'
      },
      interactions: {
        type: 'object',
        properties: {
          likes: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                userId: { type: 'string' },
                timestamp: { type: 'string', format: 'date-time' }
              }
            }
          },
          comments: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                commentId: { type: 'string' },
                userId: { type: 'string' },
                content: { type: 'string', maxLength: 300 },
                timestamp: { type: 'string', format: 'date-time' },
                replies: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      replyId: { type: 'string' },
                      userId: { type: 'string' },
                      content: { type: 'string', maxLength: 200 },
                      timestamp: { type: 'string', format: 'date-time' }
                    }
                  }
                }
              }
            }
          },
          shares: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                userId: { type: 'string' },
                timestamp: { type: 'string', format: 'date-time' },
                platform: { type: 'string' }
              }
            }
          }
        }
      },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' }
    }
  }
};

// Social interaction types
export const interactionTypes = {
  FRIEND_REQUEST: 'friend_request',
  CHALLENGE_SENT: 'challenge_sent',
  CHALLENGE_ACCEPTED: 'challenge_accepted',
  CHALLENGE_COMPLETED: 'challenge_completed',
  GROUP_INVITE: 'group_invite',
  GROUP_JOIN: 'group_join',
  ACHIEVEMENT_SHARED: 'achievement_shared',
  STUDY_SESSION_INVITE: 'study_session_invite'
};

// Challenge templates
export const challengeTemplates = {
  quick_fire: {
    name: "Quick Fire",
    description: "10 questions, 5 minutes",
    config: {
      questionCount: 10,
      timeLimit: 300,
      difficulty: 'intermediate'
    }
  },
  endurance: {
    name: "Endurance Test",
    description: "50 questions, 1 hour",
    config: {
      questionCount: 50,
      timeLimit: 3600,
      difficulty: 'advanced'
    }
  },
  speed_round: {
    name: "Speed Round",
    description: "20 questions, 10 minutes",
    config: {
      questionCount: 20,
      timeLimit: 600,
      difficulty: 'intermediate'
    }
  },
  expert_duel: {
    name: "Expert Duel",
    description: "15 expert questions, 30 minutes",
    config: {
      questionCount: 15,
      timeLimit: 1800,
      difficulty: 'expert'
    }
  }
};

// Enhanced User Schema for AI-Powered Adaptive Learning Platform
export const userSchema = {
  type: 'object',
  required: ['userId', 'profile'],
  properties: {
    userId: { 
      type: 'string',
      description: 'Unique identifier for the user'
    },
    profile: {
      type: 'object',
      required: ['username', 'email'],
      properties: {
        username: { type: 'string', minLength: 3, maxLength: 30 },
        email: { type: 'string', format: 'email' },
        avatar: { type: 'string', format: 'uri' },
        joinDate: { type: 'string', format: 'date-time' },
        lastActive: { type: 'string', format: 'date-time' },
        firstName: { type: 'string', maxLength: 50 },
        lastName: { type: 'string', maxLength: 50 },
        dateOfBirth: { type: 'string', format: 'date' },
        phoneNumber: { type: 'string' }
      }
    },
    gamification: {
      type: 'object',
      properties: {
        level: { type: 'integer', minimum: 1, default: 1 },
        xp: { type: 'integer', minimum: 0, default: 0 },
        totalXp: { type: 'integer', minimum: 0, default: 0 },
        streak: {
          type: 'object',
          properties: {
            current: { type: 'integer', minimum: 0, default: 0 },
            longest: { type: 'integer', minimum: 0, default: 0 },
            lastStudyDate: { type: 'string', format: 'date' }
          }
        },
        badges: {
          type: 'array',
          items: { type: 'string' },
          default: []
        },
        achievements: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              unlockedAt: { type: 'string', format: 'date-time' },
              progress: { type: 'number', minimum: 0, maximum: 100 }
            }
          }
        }
      }
    },
    preferences: {
      type: 'object',
      properties: {
        subjects: {
          type: 'array',
          items: { type: 'string' },
          default: []
        },
        difficulty: {
          type: 'string',
          enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          default: 'intermediate'
        },
        studyReminders: { type: 'boolean', default: true },
        notificationTimes: {
          type: 'array',
          items: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
          default: ['09:00', '18:00']
        },
        theme: {
          type: 'string',
          enum: ['light', 'dark', 'auto'],
          default: 'auto'
        },
        language: { type: 'string', default: 'en' }
      }
    },
    social: {
      type: 'object',
      properties: {
        friends: {
          type: 'array',
          items: { type: 'string' },
          default: []
        },
        studyGroups: {
          type: 'array',
          items: { type: 'string' },
          default: []
        },
        challenges: {
          type: 'object',
          properties: {
            sent: { type: 'array', items: { type: 'string' }, default: [] },
            received: { type: 'array', items: { type: 'string' }, default: [] },
            completed: { type: 'array', items: { type: 'string' }, default: [] }
          }
        },
        privacy: {
          type: 'object',
          properties: {
            profileVisible: { type: 'boolean', default: true },
            statsVisible: { type: 'boolean', default: true },
            allowFriendRequests: { type: 'boolean', default: true }
          }
        }
      }
    },
    analytics: {
      type: 'object',
      properties: {
        totalQuestionsAnswered: { type: 'integer', minimum: 0, default: 0 },
        averageAccuracy: { type: 'number', minimum: 0, maximum: 100, default: 0 },
        subjectPerformance: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              accuracy: { type: 'number', minimum: 0, maximum: 100 },
              questionsAnswered: { type: 'integer', minimum: 0 },
              timeSpent: { type: 'integer', minimum: 0 },
              weakTopics: { type: 'array', items: { type: 'string' } },
              strongTopics: { type: 'array', items: { type: 'string' } }
            }
          }
        },
        studyPatterns: {
          type: 'object',
          properties: {
            preferredTimes: { type: 'array', items: { type: 'string' } },
            averageSessionDuration: { type: 'integer', minimum: 0 },
            mostActiveDay: { type: 'string' },
            totalStudyTime: { type: 'integer', minimum: 0, default: 0 }
          }
        }
      }
    },
    subscription: {
      type: 'object',
      properties: {
        plan: {
          type: 'string',
          enum: ['free', 'premium', 'pro'],
          default: 'free'
        },
        startDate: { type: 'string', format: 'date-time' },
        endDate: { type: 'string', format: 'date-time' },
        autoRenew: { type: 'boolean', default: false }
      }
    },
    createdAt: { type: 'string', format: 'date-time' },
    updatedAt: { type: 'string', format: 'date-time' }
  }
};

// Default user data template
export const defaultUserData = {
  gamification: {
    level: 1,
    xp: 0,
    totalXp: 0,
    streak: {
      current: 0,
      longest: 0,
      lastStudyDate: null
    },
    badges: [],
    achievements: {}
  },
  preferences: {
    subjects: [],
    difficulty: 'intermediate',
    studyReminders: true,
    notificationTimes: ['09:00', '18:00'],
    theme: 'auto',
    language: 'en'
  },
  social: {
    friends: [],
    studyGroups: [],
    challenges: {
      sent: [],
      received: [],
      completed: []
    },
    privacy: {
      profileVisible: true,
      statsVisible: true,
      allowFriendRequests: true
    }
  },
  analytics: {
    totalQuestionsAnswered: 0,
    averageAccuracy: 0,
    subjectPerformance: {},
    studyPatterns: {
      preferredTimes: [],
      averageSessionDuration: 0,
      mostActiveDay: '',
      totalStudyTime: 0
    }
  },
  subscription: {
    plan: 'free',
    autoRenew: false
  }
};

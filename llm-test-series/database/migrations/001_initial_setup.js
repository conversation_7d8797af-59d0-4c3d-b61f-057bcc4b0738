// Initial Database Setup Migration
import { getFirestore } from 'firebase-admin/firestore';
import { userSchema, defaultUserData } from '../schemas/users.js';
import { questionSchema } from '../schemas/questions.js';
import { testSessionSchema } from '../schemas/testSessions.js';
import { gamificationSchema } from '../schemas/gamification.js';
import { socialSchema } from '../schemas/social.js';
import { syllabusSchema } from '../schemas/syllabi.js';

export class InitialSetupMigration {
  constructor() {
    this.db = getFirestore();
    this.collections = [
      'users',
      'questions',
      'testSessions',
      'gamification',
      'studyGroups',
      'challenges',
      'friends',
      'socialFeed',
      'syllabi',
      'analytics',
      'notifications',
      'feedback'
    ];
  }

  async up() {
    console.log('🚀 Starting initial database setup...');
    
    try {
      // Create collections with initial documents
      await this.createCollections();
      
      // Set up indexes for performance
      await this.createIndexes();
      
      // Create default data
      await this.seedDefaultData();
      
      // Set up security rules (if needed)
      await this.setupSecurityRules();
      
      console.log('✅ Initial database setup completed successfully!');
      return { success: true, message: 'Database initialized' };
    } catch (error) {
      console.error('❌ Database setup failed:', error);
      throw error;
    }
  }

  async createCollections() {
    console.log('📁 Creating collections...');
    
    for (const collectionName of this.collections) {
      try {
        // Create a placeholder document to ensure collection exists
        const docRef = this.db.collection(collectionName).doc('_placeholder');
        await docRef.set({
          _placeholder: true,
          createdAt: new Date().toISOString(),
          description: `Placeholder document for ${collectionName} collection`
        });
        
        console.log(`✅ Created collection: ${collectionName}`);
      } catch (error) {
        console.error(`❌ Failed to create collection ${collectionName}:`, error);
      }
    }
  }

  async createIndexes() {
    console.log('🔍 Setting up database indexes...');
    
    // Note: Firestore indexes are typically created through the Firebase Console
    // or using the Firebase CLI. This is a placeholder for index documentation.
    
    const indexConfigurations = {
      users: [
        { fields: ['profile.email'], unique: true },
        { fields: ['profile.username'], unique: true },
        { fields: ['gamification.level.current', 'gamification.xp'] },
        { fields: ['createdAt'] }
      ],
      questions: [
        { fields: ['metadata.subject', 'metadata.difficulty'] },
        { fields: ['metadata.topic', 'metadata.difficulty'] },
        { fields: ['analytics.totalAttempts'] },
        { fields: ['createdAt'] },
        { fields: ['validation.isVerified', 'metadata.subject'] }
      ],
      testSessions: [
        { fields: ['userId', 'createdAt'] },
        { fields: ['status', 'createdAt'] },
        { fields: ['config.subject', 'createdAt'] },
        { fields: ['userId', 'status'] }
      ],
      gamification: [
        { fields: ['level.current', 'level.xp'] },
        { fields: ['streaks.current'] },
        { fields: ['leaderboards.global.rank'] }
      ],
      challenges: [
        { fields: ['challengerId', 'status'] },
        { fields: ['challengedId', 'status'] },
        { fields: ['status', 'createdAt'] },
        { fields: ['expiresAt'] }
      ],
      syllabi: [
        { fields: ['subject', 'examType'] },
        { fields: ['board', 'class'] },
        { fields: ['settings.isActive', 'settings.isPublic'] }
      ]
    };

    console.log('📋 Index configurations documented for:', Object.keys(indexConfigurations).join(', '));
    console.log('💡 Create these indexes in Firebase Console for optimal performance');
  }

  async seedDefaultData() {
    console.log('🌱 Seeding default data...');
    
    try {
      // Create default admin user
      await this.createDefaultAdmin();
      
      // Create sample syllabi
      await this.createSampleSyllabi();
      
      // Create sample questions
      await this.createSampleQuestions();
      
      // Create default gamification data
      await this.createDefaultGamificationData();
      
      console.log('✅ Default data seeded successfully');
    } catch (error) {
      console.error('❌ Failed to seed default data:', error);
    }
  }

  async createDefaultAdmin() {
    const adminData = {
      userId: 'admin_default',
      profile: {
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'Administrator',
        joinDate: new Date().toISOString(),
        lastActive: new Date().toISOString()
      },
      role: 'admin',
      permissions: ['all'],
      ...defaultUserData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await this.db.collection('users').doc('admin_default').set(adminData);
    console.log('👤 Created default admin user');
  }

  async createSampleSyllabi() {
    const sampleSyllabi = [
      {
        syllabusId: 'cbse_12_physics',
        name: 'CBSE Class 12 Physics',
        subject: 'Physics',
        examType: 'board',
        board: 'CBSE',
        class: '12',
        structure: {
          units: [
            {
              unitId: 'unit_1',
              name: 'Electrostatics',
              weightage: 16,
              estimatedHours: 40,
              chapters: [
                {
                  chapterId: 'ch_1',
                  name: 'Electric Charges and Fields',
                  topics: [
                    {
                      topicId: 'topic_1_1',
                      name: 'Electric Charge',
                      difficulty: 'beginner',
                      estimatedTime: 60
                    }
                  ]
                }
              ]
            }
          ],
          totalUnits: 1,
          totalChapters: 1,
          totalTopics: 1,
          estimatedCompletionHours: 40
        },
        settings: {
          isActive: true,
          isPublic: true,
          allowQuestionGeneration: true
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin_default'
      }
    ];

    for (const syllabus of sampleSyllabi) {
      await this.db.collection('syllabi').doc(syllabus.syllabusId).set(syllabus);
    }
    
    console.log('📚 Created sample syllabi');
  }

  async createSampleQuestions() {
    const sampleQuestions = [
      {
        questionId: 'sample_q1',
        content: {
          question: 'What is the unit of electric charge?',
          options: ['Coulomb', 'Ampere', 'Volt', 'Ohm'],
          correctAnswer: 'Coulomb',
          explanation: {
            basic: 'The SI unit of electric charge is Coulomb (C).',
            intermediate: 'Electric charge is measured in Coulombs, named after Charles-Augustin de Coulomb.',
            advanced: 'One Coulomb is defined as the amount of charge transported by a constant current of one ampere in one second.'
          }
        },
        metadata: {
          subject: 'Physics',
          topic: 'Electric Charge',
          difficulty: 'beginner',
          syllabus: {
            syllabusId: 'cbse_12_physics',
            unitId: 'unit_1',
            topicId: 'topic_1_1'
          },
          estimatedTime: 30,
          sourceTag: 'Manual',
          bloomsLevel: 'remember',
          questionType: 'multiple-choice'
        },
        analytics: {
          totalAttempts: 0,
          correctAttempts: 0,
          averageTime: 0
        },
        gamification: {
          basePoints: 5,
          bonusMultiplier: 1.0
        },
        validation: {
          isVerified: true,
          verifiedBy: 'admin_default',
          verificationDate: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin_default'
      }
    ];

    for (const question of sampleQuestions) {
      await this.db.collection('questions').doc(question.questionId).set(question);
    }
    
    console.log('❓ Created sample questions');
  }

  async createDefaultGamificationData() {
    // This will be created per user, so just document the structure
    console.log('🎮 Gamification data structure documented');
  }

  async setupSecurityRules() {
    console.log('🔒 Security rules setup documented');
    console.log('💡 Configure Firestore security rules in Firebase Console');
  }

  async down() {
    console.log('🔄 Rolling back initial setup...');
    
    try {
      // Remove placeholder documents
      for (const collectionName of this.collections) {
        await this.db.collection(collectionName).doc('_placeholder').delete();
      }
      
      console.log('✅ Rollback completed');
      return { success: true, message: 'Database setup rolled back' };
    } catch (error) {
      console.error('❌ Rollback failed:', error);
      throw error;
    }
  }
}

// Migration runner
export async function runInitialSetup() {
  const migration = new InitialSetupMigration();
  return await migration.up();
}

export default InitialSetupMigration;

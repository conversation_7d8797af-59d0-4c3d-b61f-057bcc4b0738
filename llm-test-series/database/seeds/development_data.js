// Development Seed Data for LLM Test Series Platform
import { getFirestore } from 'firebase-admin/firestore';
import { defaultUserData } from '../schemas/users.js';

export class DevelopmentSeeder {
  constructor() {
    this.db = getFirestore();
  }

  async seedAll() {
    console.log('🌱 Seeding development data...');
    
    try {
      await this.seedUsers();
      await this.seedQuestions();
      await this.seedSyllabi();
      await this.seedTestSessions();
      await this.seedChallenges();
      await this.seedStudyGroups();
      
      console.log('✅ Development data seeded successfully!');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to seed development data:', error);
      throw error;
    }
  }

  async seedUsers() {
    const users = [
      {
        userId: 'demo_user_1',
        profile: {
          username: 'alice_physics',
          email: '<EMAIL>',
          firstName: 'Alice',
          lastName: 'Johnson',
          joinDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          lastActive: new Date().toISOString()
        },
        gamification: {
          level: 15,
          xp: 3250,
          totalXp: 3250,
          streak: {
            current: 12,
            longest: 18,
            lastStudyDate: new Date().toISOString().split('T')[0]
          },
          badges: ['first_test', 'streak_warrior', 'physics_master'],
          achievements: {
            early_bird: {
              progress: 80,
              isCompleted: false
            },
            accuracy_ace: {
              progress: 100,
              isCompleted: true,
              completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
            }
          }
        },
        analytics: {
          totalQuestionsAnswered: 450,
          averageAccuracy: 87.5,
          subjectPerformance: {
            Physics: {
              accuracy: 89.2,
              questionsAnswered: 320,
              timeSpent: 18000,
              weakTopics: ['Quantum Physics'],
              strongTopics: ['Mechanics', 'Thermodynamics']
            },
            Chemistry: {
              accuracy: 82.1,
              questionsAnswered: 130,
              timeSpent: 7200,
              weakTopics: ['Organic Chemistry'],
              strongTopics: ['Inorganic Chemistry']
            }
          }
        },
        ...defaultUserData,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        userId: 'demo_user_2',
        profile: {
          username: 'bob_chem',
          email: '<EMAIL>',
          firstName: 'Bob',
          lastName: 'Smith',
          joinDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        gamification: {
          level: 8,
          xp: 1450,
          totalXp: 1450,
          streak: {
            current: 5,
            longest: 9,
            lastStudyDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
          },
          badges: ['first_test', 'quick_learner'],
          achievements: {
            social_butterfly: {
              progress: 60,
              isCompleted: false
            }
          }
        },
        analytics: {
          totalQuestionsAnswered: 280,
          averageAccuracy: 78.3,
          subjectPerformance: {
            Chemistry: {
              accuracy: 85.7,
              questionsAnswered: 200,
              timeSpent: 12000,
              weakTopics: ['Physical Chemistry'],
              strongTopics: ['Organic Chemistry']
            },
            Physics: {
              accuracy: 65.2,
              questionsAnswered: 80,
              timeSpent: 4800,
              weakTopics: ['Electromagnetism', 'Modern Physics'],
              strongTopics: ['Mechanics']
            }
          }
        },
        ...defaultUserData,
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        userId: 'demo_user_3',
        profile: {
          username: 'carol_math',
          email: '<EMAIL>',
          firstName: 'Carol',
          lastName: 'Davis',
          joinDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
          lastActive: new Date().toISOString()
        },
        gamification: {
          level: 22,
          xp: 5680,
          totalXp: 5680,
          streak: {
            current: 25,
            longest: 25,
            lastStudyDate: new Date().toISOString().split('T')[0]
          },
          badges: ['first_test', 'streak_warrior', 'perfectionist', 'speed_demon'],
          achievements: {
            early_bird: {
              progress: 100,
              isCompleted: true,
              completedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
            },
            accuracy_ace: {
              progress: 100,
              isCompleted: true,
              completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
            },
            subject_specialist: {
              progress: 75,
              isCompleted: false
            }
          }
        },
        analytics: {
          totalQuestionsAnswered: 720,
          averageAccuracy: 94.2,
          subjectPerformance: {
            Mathematics: {
              accuracy: 96.8,
              questionsAnswered: 500,
              timeSpent: 25000,
              weakTopics: [],
              strongTopics: ['Calculus', 'Algebra', 'Geometry', 'Statistics']
            },
            Physics: {
              accuracy: 89.5,
              questionsAnswered: 220,
              timeSpent: 13200,
              weakTopics: ['Quantum Physics'],
              strongTopics: ['Mechanics', 'Waves']
            }
          }
        },
        ...defaultUserData,
        createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const user of users) {
      await this.db.collection('users').doc(user.userId).set(user);
      
      // Create corresponding gamification document
      await this.db.collection('gamification').doc(user.userId).set({
        userId: user.userId,
        ...user.gamification,
        statistics: {
          totalTests: Math.floor(user.analytics.totalQuestionsAnswered / 20),
          totalQuestions: user.analytics.totalQuestionsAnswered,
          correctAnswers: Math.floor(user.analytics.totalQuestionsAnswered * user.analytics.averageAccuracy / 100),
          totalStudyTime: Object.values(user.analytics.subjectPerformance).reduce((sum, perf) => sum + perf.timeSpent, 0),
          averageAccuracy: user.analytics.averageAccuracy,
          perfectScores: user.gamification.level > 15 ? 3 : 1,
          challengesWon: user.gamification.level > 10 ? 5 : 2,
          challengesLost: user.gamification.level > 10 ? 2 : 1
        },
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      });
    }

    console.log('👥 Created demo users with gamification data');
  }

  async seedQuestions() {
    const questions = [
      {
        questionId: 'physics_q1',
        content: {
          question: 'A ball is thrown vertically upward with an initial velocity of 20 m/s. What is the maximum height reached? (g = 10 m/s²)',
          options: ['10 m', '20 m', '30 m', '40 m'],
          correctAnswer: '20 m',
          explanation: {
            basic: 'Using v² = u² + 2as, where v = 0 at maximum height, u = 20 m/s, a = -10 m/s²',
            intermediate: 'At maximum height, final velocity = 0. Using kinematic equation: 0² = 20² + 2(-10)h, solving gives h = 20 m',
            advanced: 'Using energy conservation: ½mu² = mgh, so h = u²/2g = 400/20 = 20 m'
          },
          hints: ['Think about what happens to velocity at maximum height', 'Use kinematic equations', 'Consider energy conservation']
        },
        metadata: {
          subject: 'Physics',
          topic: 'Kinematics',
          subtopic: 'Projectile Motion',
          difficulty: 'intermediate',
          syllabus: {
            syllabusId: 'cbse_12_physics',
            unitId: 'unit_mechanics',
            topicId: 'kinematics'
          },
          tags: ['projectile', 'vertical motion', 'kinematics'],
          estimatedTime: 120,
          sourceTag: 'AI Generated',
          bloomsLevel: 'apply',
          questionType: 'multiple-choice'
        },
        analytics: {
          totalAttempts: 45,
          correctAttempts: 32,
          averageTime: 95
        },
        gamification: {
          basePoints: 10,
          bonusMultiplier: 1.2
        },
        validation: {
          isVerified: true,
          verifiedBy: 'admin_default',
          qualityScore: 85
        },
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'ai_system'
      },
      {
        questionId: 'chem_q1',
        content: {
          question: 'What is the molecular formula of glucose?',
          options: ['C₆H₁₂O₆', 'C₆H₁₀O₅', 'C₅H₁₀O₅', 'C₆H₁₄O₆'],
          correctAnswer: 'C₆H₁₂O₆',
          explanation: {
            basic: 'Glucose has the molecular formula C₆H₁₂O₆',
            intermediate: 'Glucose is a simple sugar with 6 carbon atoms, 12 hydrogen atoms, and 6 oxygen atoms',
            advanced: 'Glucose (C₆H₁₂O₆) is an aldohexose with the empirical formula CH₂O, molecular weight 180.16 g/mol'
          }
        },
        metadata: {
          subject: 'Chemistry',
          topic: 'Biomolecules',
          difficulty: 'beginner',
          estimatedTime: 45,
          sourceTag: 'Manual',
          bloomsLevel: 'remember',
          questionType: 'multiple-choice'
        },
        analytics: {
          totalAttempts: 67,
          correctAttempts: 58,
          averageTime: 38
        },
        gamification: {
          basePoints: 5,
          bonusMultiplier: 1.0
        },
        validation: {
          isVerified: true,
          verifiedBy: 'admin_default',
          qualityScore: 90
        },
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'admin_default'
      }
    ];

    for (const question of questions) {
      await this.db.collection('questions').doc(question.questionId).set(question);
    }

    console.log('❓ Created sample questions');
  }

  async seedSyllabi() {
    // Sample syllabi are already created in migration
    console.log('📚 Syllabi already seeded in migration');
  }

  async seedTestSessions() {
    const testSessions = [
      {
        sessionId: 'session_demo_1',
        userId: 'demo_user_1',
        config: {
          testType: 'practice',
          subject: 'Physics',
          topics: ['Kinematics', 'Dynamics'],
          difficulty: 'intermediate',
          questionCount: 10,
          timeLimit: 0
        },
        status: 'completed',
        questions: [
          { questionId: 'physics_q1', order: 1, difficulty: 'intermediate', points: 10 }
        ],
        answers: [
          {
            questionId: 'physics_q1',
            userAnswer: '20 m',
            isCorrect: true,
            timeSpent: 95,
            confidence: 4,
            submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            pointsEarned: 10
          }
        ],
        results: {
          score: 90,
          totalQuestions: 10,
          correctAnswers: 9,
          incorrectAnswers: 1,
          totalTime: 1200,
          accuracy: 90,
          grade: 'A'
        },
        gamification: {
          xpEarned: 95,
          bonusXp: 15,
          badgesEarned: []
        },
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 20 * 60 * 1000).toISOString()
      }
    ];

    for (const session of testSessions) {
      await this.db.collection('testSessions').doc(session.sessionId).set(session);
    }

    console.log('📝 Created sample test sessions');
  }

  async seedChallenges() {
    const challenges = [
      {
        challengeId: 'challenge_demo_1',
        challengerId: 'demo_user_1',
        challengedId: 'demo_user_2',
        type: 'direct',
        config: {
          subject: 'Physics',
          topics: ['Mechanics'],
          difficulty: 'intermediate',
          questionCount: 10,
          timeLimit: 600
        },
        status: 'completed',
        participants: [
          {
            userId: 'demo_user_1',
            status: 'completed',
            score: 85,
            completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          },
          {
            userId: 'demo_user_2',
            status: 'completed',
            score: 78,
            completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 5 * 60 * 1000).toISOString()
          }
        ],
        results: {
          winnerId: 'demo_user_1',
          winnerScore: 85,
          margin: 7,
          isTie: false
        },
        createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    for (const challenge of challenges) {
      await this.db.collection('challenges').doc(challenge.challengeId).set(challenge);
    }

    console.log('⚔️ Created sample challenges');
  }

  async seedStudyGroups() {
    const studyGroups = [
      {
        groupId: 'group_physics_masters',
        name: 'Physics Masters',
        description: 'Advanced physics study group for competitive exam preparation',
        createdBy: 'demo_user_1',
        members: [
          {
            userId: 'demo_user_1',
            role: 'admin',
            joinedAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
            lastActive: new Date().toISOString()
          },
          {
            userId: 'demo_user_3',
            role: 'member',
            joinedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          }
        ],
        settings: {
          isPrivate: false,
          requireApproval: true,
          maxMembers: 25,
          subjects: ['Physics'],
          targetExam: 'JEE Advanced'
        },
        activity: {
          totalSessions: 12,
          totalMessages: 156,
          averageScore: 82.5,
          activeMembers: 2
        },
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const group of studyGroups) {
      await this.db.collection('studyGroups').doc(group.groupId).set(group);
    }

    console.log('👥 Created sample study groups');
  }

  async clearAll() {
    console.log('🧹 Clearing development data...');
    
    const collections = ['users', 'questions', 'testSessions', 'challenges', 'studyGroups', 'gamification'];
    
    for (const collectionName of collections) {
      const snapshot = await this.db.collection(collectionName).get();
      const batch = this.db.batch();
      
      snapshot.docs.forEach(doc => {
        if (!doc.id.startsWith('_') && !doc.id.includes('admin')) {
          batch.delete(doc.ref);
        }
      });
      
      await batch.commit();
    }
    
    console.log('✅ Development data cleared');
  }
}

export async function seedDevelopmentData() {
  const seeder = new DevelopmentSeeder();
  return await seeder.seedAll();
}

export default DevelopmentSeeder;

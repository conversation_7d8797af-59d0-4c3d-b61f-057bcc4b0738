<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Integration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">🧪 Frontend Integration Test</h1>
        
        <div id="test-results" class="space-y-4">
            <!-- Test results will be populated here -->
        </div>
        
        <button id="run-tests" class="mt-8 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            🚀 Run Integration Tests
        </button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        let authToken = null;

        function addTestResult(testName, status, details = '') {
            const resultsContainer = document.getElementById('test-results');
            const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⏳';
            const statusColor = status === 'success' ? 'text-green-600' : status === 'error' ? 'text-red-600' : 'text-yellow-600';
            
            const testDiv = document.createElement('div');
            testDiv.className = 'bg-white p-4 rounded-lg shadow';
            testDiv.innerHTML = `
                <div class="flex items-center justify-between">
                    <h3 class="font-semibold text-gray-800">${statusIcon} ${testName}</h3>
                    <span class="${statusColor} font-medium">${status.toUpperCase()}</span>
                </div>
                ${details ? `<p class="text-gray-600 mt-2 text-sm">${details}</p>` : ''}
            `;
            resultsContainer.appendChild(testDiv);
        }

        async function testAuthentication() {
            addTestResult('Authentication Test', 'running', 'Testing anonymous authentication...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/anonymous`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                authToken = data.token;
                
                addTestResult('Authentication Test', 'success', 
                    `User ID: ${data.user.id}, Token received: ${authToken.substring(0, 20)}...`);
                return true;
            } catch (error) {
                addTestResult('Authentication Test', 'error', `Error: ${error.message}`);
                return false;
            }
        }

        async function testQuestionGeneration() {
            addTestResult('Question Generation Test', 'running', 'Testing question generation API...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/questions/generate?subject=Physics&level=Beginner&numQuestions=2`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                if (!data.questions || data.questions.length === 0) {
                    throw new Error('No questions received');
                }

                const question = data.questions[0];
                const hasOptions = question.options && question.options.length > 0;
                
                addTestResult('Question Generation Test', 'success', 
                    `Questions: ${data.questions.length}, Has options: ${hasOptions}, Question: "${question.question.substring(0, 50)}..."`);
                return true;
            } catch (error) {
                addTestResult('Question Generation Test', 'error', `Error: ${error.message}`);
                return false;
            }
        }

        async function testSessionCreation() {
            addTestResult('Test Session Creation', 'running', 'Testing test session creation...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/test-sessions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        testConfig: {
                            subject: 'Physics',
                            level: 'Beginner',
                            numQuestions: 2
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                if (!data.success || !data.sessionId) {
                    throw new Error('Session creation failed');
                }
                
                addTestResult('Test Session Creation', 'success', 
                    `Session ID: ${data.sessionId}, Status: ${data.session.status}`);
                return true;
            } catch (error) {
                addTestResult('Test Session Creation', 'error', `Error: ${error.message}`);
                return false;
            }
        }

        async function testFrontendConnectivity() {
            addTestResult('Frontend Connectivity Test', 'running', 'Testing frontend server...');
            
            try {
                const response = await fetch('http://localhost:3000', {
                    method: 'GET'
                });

                if (!response.ok) {
                    throw new Error(`Frontend server not responding: HTTP ${response.status}`);
                }
                
                addTestResult('Frontend Connectivity Test', 'success', 
                    'Frontend server is running and accessible');
                return true;
            } catch (error) {
                addTestResult('Frontend Connectivity Test', 'error', 
                    `Frontend server issue: ${error.message}`);
                return false;
            }
        }

        async function runAllTests() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('run-tests').disabled = true;
            document.getElementById('run-tests').textContent = '🔄 Running Tests...';

            const results = {
                auth: await testAuthentication(),
                frontend: await testFrontendConnectivity(),
                questions: false,
                sessions: false
            };

            if (results.auth) {
                results.questions = await testQuestionGeneration();
                results.sessions = await testSessionCreation();
            }

            // Summary
            const allPassed = Object.values(results).every(r => r === true);
            const passedCount = Object.values(results).filter(r => r === true).length;
            const totalCount = Object.keys(results).length;

            addTestResult('🎯 Test Summary', allPassed ? 'success' : 'error', 
                `${passedCount}/${totalCount} tests passed. ${allPassed ? 'All systems operational! 🚀' : 'Some issues detected. 🔧'}`);

            document.getElementById('run-tests').disabled = false;
            document.getElementById('run-tests').textContent = '🚀 Run Integration Tests';
        }

        document.getElementById('run-tests').addEventListener('click', runAllTests);

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>

#!/usr/bin/env node

/**
 * Test Google AI API directly
 */

import axios from 'axios';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
    console.error('❌ GEMINI_API_KEY environment variable not set');
    process.exit(1);
}

console.log('🔑 API Key found:', GEMINI_API_KEY.substring(0, 10) + '...');

const testGoogleAI = async () => {
    try {
        const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`;
        
        const payload = {
            contents: [{
                parts: [{ 
                    text: `Generate 1 multiple choice question about Physics at Beginner level.

Format your response as JSON:
{
  "question": "Your question here",
  "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
  "correct_answer": "A",
  "explanation": "Detailed explanation here"
}

Make sure the question is educational and the explanation is clear.`
                }]
            }],
            generationConfig: {
                temperature: 0.7,
                maxOutputTokens: 2000
            }
        };

        console.log('🚀 Testing Google AI API...');
        console.log('📡 URL:', url.replace(GEMINI_API_KEY, 'API_KEY_HIDDEN'));
        console.log('📦 Payload:', JSON.stringify(payload, null, 2));

        const response = await axios.post(url, payload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });

        console.log('✅ Success! Response status:', response.status);
        console.log('📄 Response data:', JSON.stringify(response.data, null, 2));

        // Extract the generated text
        if (response.data.candidates && response.data.candidates[0] && response.data.candidates[0].content) {
            const generatedText = response.data.candidates[0].content.parts[0].text;
            console.log('🎯 Generated text:', generatedText);
            
            // Try to parse as JSON
            try {
                const questionData = JSON.parse(generatedText);
                console.log('✅ Successfully parsed question:', questionData);
            } catch (parseError) {
                console.log('⚠️ Could not parse as JSON, raw text:', generatedText);
            }
        }

    } catch (error) {
        console.error('❌ Error testing Google AI:');
        console.error('Status:', error.response?.status);
        console.error('Status Text:', error.response?.statusText);
        console.error('Error Data:', JSON.stringify(error.response?.data, null, 2));
        console.error('Error Message:', error.message);
    }
};

testGoogleAI();

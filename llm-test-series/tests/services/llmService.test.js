import { expect } from 'chai';
import sinon from 'sinon';
import llmService from '../../server/services/llmService.js';
import { mockLoggingService, mockDb } from '../helpers/testHelper.js';

// Mock logging service and database
sinon.stub(llmService, 'loggingService').value(mockLoggingService);
sinon.stub(llmService, 'db').value(mockDb);

describe('LLMService', () => {
  let sandbox;

  beforeEach(async () => {
    if (sandbox) {
      sandbox.restore();
    }
    sandbox = sinon.createSandbox();
    // Reset logging service spies
    Object.keys(mockLoggingService).forEach(key => {
      mockLoggingService[key] = sandbox.spy();
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generateWithOptimalModel', () => {
    it('should generate content successfully', async () => {
      const mockResponse = {
        content: 'Test response'
      };

      const prompt = 'Test prompt';
      const requirements = {
        type: 'multiple_choice',
        complexity: 'medium'
      };

      sandbox.stub(llmService, 'generateWithModel').resolves(mockResponse);
      sandbox.stub(llmService, 'validateResponseQuality').resolves(0.9);

      const result = await llmService.generateWithOptimalModel(prompt, requirements);

      expect(result).to.equal(mockResponse);
      expect(llmService.generateWithModel.calledOnce).to.be.true;
      expect(llmService.validateResponseQuality.calledOnce).to.be.true;
    });

    it('should try fallback model on low quality response', async () => {
      const mockResponse1 = {
        content: 'Low quality response'
      };
      const mockResponse2 = {
        content: 'Better quality response'
      };

      const prompt = 'Test prompt';
      const requirements = {
        type: 'multiple_choice',
        complexity: 'medium'
      };

      const generateStub = sandbox.stub(llmService, 'generateWithModel');
      generateStub.onFirstCall().resolves(mockResponse1);
      generateStub.onSecondCall().resolves(mockResponse2);

      const validateStub = sandbox.stub(llmService, 'validateResponseQuality');
      validateStub.onFirstCall().resolves(0.6);
      validateStub.onSecondCall().resolves(0.9);

      sandbox.stub(mockLoggingService, 'logInfo');

      const result = await llmService.generateWithOptimalModel(prompt, requirements);

      expect(result).to.equal(mockResponse2);
      expect(generateStub.calledTwice).to.be.true;
      expect(validateStub.calledTwice).to.be.true;
    });

    it('should update metrics on successful generation', async () => {
      const mockResponse = {
        content: 'Test response'
      };

      const prompt = 'Test prompt';
      const requirements = {
        type: 'multiple_choice',
        complexity: 'medium'
      };

      sandbox.stub(llmService, 'generateWithModel').resolves(mockResponse);
      sandbox.stub(llmService, 'validateResponseQuality').resolves(0.9);
      sandbox.stub(llmService, 'updateMetrics');

      await llmService.generateWithOptimalModel(prompt, requirements);

      expect(llmService.updateMetrics.calledOnce).to.be.true;
      const metricsCall = llmService.updateMetrics.getCall(0);
      expect(metricsCall.args[4]).to.equal(0.9); // quality score
      expect(metricsCall.args[5]).to.be.true; // success
    });

    it('should handle errors gracefully', async () => {
      const prompt = 'Test prompt';
      const requirements = {
        type: 'multiple_choice',
        complexity: 'medium'
      };

      sandbox.stub(llmService, 'generateWithModel').rejects(new Error('API error'));
      sandbox.stub(mockLoggingService, 'logError');

      try {
        await llmService.generateWithOptimalModel(prompt, requirements);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.an('error');
        expect(error.message).to.equal('API error');
        expect(mockLoggingService.logError.calledOnce).to.be.true;
      }
    });
  });

  describe('metrics collection', () => {
    it('should track basic metrics correctly', async () => {
      const mockResponse = {
        content: 'Test response'
      };

      sandbox.stub(llmService, 'generateWithModel').resolves(mockResponse);
      sandbox.stub(llmService, 'validateResponseQuality').resolves(0.9);

      const beforeCalls = llmService.metrics.totalCalls;
      const beforeSuccessful = llmService.metrics.successfulCalls;

      await llmService.generateWithOptimalModel('Test prompt', {
        type: 'multiple_choice',
        complexity: 'medium'
      });

      expect(llmService.metrics.totalCalls).to.equal(beforeCalls + 1);
      expect(llmService.metrics.successfulCalls).to.equal(beforeSuccessful + 1);
    });

    it('should track error metrics correctly', async () => {
      sandbox.stub(llmService, 'generateWithModel').rejects(new Error('API error'));
      sandbox.stub(mockLoggingService, 'logError');

      const beforeErrors = llmService.metrics.failedCalls;

      try {
        await llmService.generateWithOptimalModel('Test prompt', {
          type: 'multiple_choice',
          complexity: 'medium'
        });
      } catch (error) {
        expect(llmService.metrics.failedCalls).to.equal(beforeErrors + 1);
      }
    });

    it('should calculate error rate correctly', () => {
      llmService.metrics.totalCalls = 100;
      llmService.metrics.failedCalls = 20;

      const errorRate = llmService.metrics.failedCalls / llmService.metrics.totalCalls;
      expect(errorRate).to.equal(0.2);
    });
  });
});

import type { RelatedConcept } from '../../../types/services';
import TheoryService from '../../../server/services/theoryService.js';
import redisService from '../../../server/services/redisService.js';
import loggingService from '../../../server/services/loggingService.js';
import { getDb } from '../../../server/firebase.js';

jest.mock('../../../server/services/redisService.js');
jest.mock('../../../server/services/loggingService.js', () => ({
    default: {
        info: jest.fn(),
        error: jest.fn(),
        debug: jest.fn()
    }
}));
jest.mock('../../../server/firebase.js');

describe('TheoryService', () => {
    let mockDb: jest.Mocked<any>;

    beforeAll(() => {
        mockDb = {
            collection: jest.fn(() => ({
                doc: jest.fn(),
                where: jest.fn(),
                get: jest.fn(),
                add: jest.fn()
            }))
        };
        (getDb as jest.Mock).mockReturnValue(mockDb);
        // Reset the singleton's db instance
        TheoryService.db = mockDb;
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getTheoryExplanation', () => {
        it('should return cached explanation if available', async () => {
            const topic = 'algebra';
            const cachedExplanation = {
                content: 'Cached algebra explanation',
                timestamp: Date.now()
            };

            (redisService.get as jest.Mock).mockResolvedValue(JSON.stringify(cachedExplanation));

            const result = await TheoryService.getTheoryExplanation(topic);

            expect(result).toEqual(cachedExplanation.content);
            expect(redisService.get).toHaveBeenCalledWith(`theory:${topic}`);
            expect(loggingService.info).toHaveBeenCalledWith('Theory explanation served from cache', {
                topic,
                source: 'cache'
            });
        });

        it('should generate new explanation if cache is expired', async () => {
            const topic = 'algebra';
            const oldExplanation = {
                content: 'Old algebra explanation',
                timestamp: Date.now() - (25 * 60 * 60 * 1000) // 25 hours old
            };

            (redisService.get as jest.Mock).mockResolvedValue(JSON.stringify(oldExplanation));
            
            const newExplanation = 'New algebra explanation';
            mockDb.collection().add.mockResolvedValue({ id: 'new-explanation' });
            const generateSpy = jest.spyOn(TheoryService as any, '_generateExplanation');
            generateSpy.mockResolvedValue(newExplanation);

            const result = await TheoryService.getTheoryExplanation(topic);

            expect(result).toEqual(newExplanation);
            expect(redisService.set).toHaveBeenCalledWith(
                `theory:${topic}`,
                JSON.stringify({
                    content: newExplanation,
                    timestamp: expect.any(Number)
                }),
                24 * 60 * 60 // 24 hours
            );
        });
    });

    describe('getRelatedConcepts', () => {
        it('should return related concepts from database', async () => {
            const concept = 'quadratic_equations';
            const mockRelatedConcepts: RelatedConcept[] = [
                { concept: 'polynomial_functions', score: 0.9 },
                { concept: 'factorization', score: 0.8 }
            ];

            mockDb.collection().where().get.mockResolvedValue({
                docs: mockRelatedConcepts.map(rc => ({
                    data: () => rc
                }))
            });

            const result = await TheoryService.getRelatedConcepts(concept);

            expect(result).toEqual(mockRelatedConcepts);
            expect(mockDb.collection).toHaveBeenCalledWith('related_concepts');
            expect(loggingService.info).toHaveBeenCalledWith('Retrieved related concepts', {
                concept,
                count: mockRelatedConcepts.length
            });
        });
    });

    describe('generateExplanation', () => {
        it('should generate explanation with appropriate difficulty level', async () => {
            const topic = 'calculus';

            const mockLLMResponse = 'Advanced calculus explanation with complex concepts';
            
            // Mock the private method
            const generateSpy = jest.spyOn(TheoryService as any, '_generateExplanation');
            generateSpy.mockResolvedValue(mockLLMResponse);

            const result = await TheoryService.getTheoryExplanation(topic);

            expect(result).toEqual(mockLLMResponse);
            expect(generateSpy).toHaveBeenCalledWith(
                expect.stringContaining(topic),
                expect.any(Object)
            );
            expect(loggingService.info).toHaveBeenCalledWith('Generated explanation', {
                topic,
                length: mockLLMResponse.length
            });
        });

        it('should handle LLM errors gracefully', async () => {
            const topic = 'physics';
            const error = new Error('LLM API error');

            // Mock the private method
            const generateSpy = jest.spyOn(TheoryService as any, '_generateExplanation');
            generateSpy.mockRejectedValue(error);

            await expect(TheoryService.getTheoryExplanation(topic)).rejects.toThrow('Failed to generate explanation');
            expect(loggingService.info).toHaveBeenCalledWith('LLM explanation generation failed', {
                topic,
                error
            });
        });
    });
});

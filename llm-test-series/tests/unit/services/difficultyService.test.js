import difficultyService from '../../../server/services/difficultyService.js';

describe('DifficultyService', () => {
    describe('calculateNewRatings', () => {
        it('should increase user rating and decrease question rating when answered correctly', () => {
            const userRating = 1200;
            const questionRating = 1500;
            const isCorrect = true;

            const result = difficultyService.calculateNewRatings(userRating, questionRating, isCorrect);

            expect(result.newUserRating).toBeGreaterThan(userRating);
            expect(result.newQuestionRating).toBeLessThan(questionRating);
        });

        it('should decrease user rating and increase question rating when answered incorrectly', () => {
            const userRating = 1200;
            const questionRating = 1500;
            const isCorrect = false;

            const result = difficultyService.calculateNewRatings(userRating, questionRating, isCorrect);

            expect(result.newUserRating).toBeLessThan(userRating);
            expect(result.newQuestionRating).toBeGreaterThan(questionRating);
        });

        it('should make smaller rating changes when expected outcome occurs', () => {
            const userRating = 1800;
            const questionRating = 1200;
            const isCorrect = true;

            const result = difficultyService.calculateNewRatings(userRating, questionRating, isCorrect);

            const ratingChange = Math.abs(result.newUserRating - userRating);
            expect(ratingChange).toBeLessThan(20);
        });
    });

    describe('getDifficultyLabel', () => {
        it('should return "easy" for low ratings', () => {
            expect(difficultyService.getDifficultyLabel(1000)).toBe('easy');
        });

        it('should return "medium" for middle ratings', () => {
            expect(difficultyService.getDifficultyLabel(1500)).toBe('medium');
        });

        it('should return "hard" for high ratings', () => {
            expect(difficultyService.getDifficultyLabel(2000)).toBe('hard');
        });
    });

    describe('calculateExpectedScore', () => {
        it('should return close to 0.5 for equal ratings', () => {
            const score = difficultyService.calculateExpectedScore(1500, 1500);
            expect(score).toBeCloseTo(0.5, 2);
        });

        it('should return higher score when user rating is higher', () => {
            const score = difficultyService.calculateExpectedScore(1800, 1500);
            expect(score).toBeGreaterThan(0.5);
        });

        it('should return lower score when user rating is lower', () => {
            const score = difficultyService.calculateExpectedScore(1200, 1500);
            expect(score).toBeLessThan(0.5);
        });
    });
});

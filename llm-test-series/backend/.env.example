# Environment Configuration
NODE_ENV=development
PORT=3001

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
LLM_API_KEY=sk-your-openai-api-key-here

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here
ENCRYPTION_KEY=your-32-character-encryption-key-here
SESSION_SECRET=your-session-secret-here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# CDN Configuration
CDN_URL=https://your-cdn-url.com
CDN_API_KEY=your-cdn-api-key

# Email Configuration (for notifications)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_PORT=9090

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Question Generation Configuration
MAX_QUESTIONS_PER_REQUEST=50
DEFAULT_QUESTION_DIFFICULTY=intermediate
ENABLE_AI_GENERATION=true

# Gamification Configuration
XP_MULTIPLIER=1.0
LEVEL_UP_THRESHOLD=1000
BADGE_UNLOCK_ENABLED=true

# Social Features Configuration
ENABLE_SOCIAL_FEATURES=true
MAX_FRIENDS_PER_USER=100
CHALLENGE_EXPIRY_HOURS=24

# Analytics Configuration
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=300000

# Content Security
DISABLE_RIGHT_CLICK=true
DISABLE_TEXT_SELECTION=true
ENABLE_WATERMARKING=false

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
STATIC_FILE_CACHE_DURATION=86400000

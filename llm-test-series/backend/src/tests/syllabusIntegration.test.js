const questionGenerationService = require('../services/questionGenerationService');
const questionAnalysisService = require('../services/questionAnalysisService');

// Set test environment
process.env.NODE_ENV = 'development';

console.log('Testing Syllabus Integration...\n');

// Test data
const testSyllabus = {
  id: 'test_syllabus_1',
  name: 'Physics Syllabus',
  subject: 'Physics',
  level: 'undergraduate',
  units: [
    {
      id: 'unit_1',
      name: 'Classical Mechanics',
      description: 'Study of motion and forces',
      learning_objectives: {
        understand: 'Understand Newton\'s laws and their applications',
        apply: 'Apply principles of mechanics to real-world problems',
        analyze: 'Analyze complex mechanical systems'
      },
      prerequisites: ['Basic calculus', 'Vector algebra'],
      topics: [
        {
          id: 'topic_1',
          name: 'Newton\'s Laws of Motion',
          description: 'Fundamental principles governing motion',
          keywords: [
            'force',
            'mass',
            'acceleration',
            'inertia',
            'action-reaction'
          ],
          question_distribution: {
            multiple_choice: {
              understand: 0.3,
              apply: 0.4,
              analyze: 0.3
            },
            fill_in_blanks: {
              understand: 0.5,
              apply: 0.5
            }
          }
        }
      ]
    }
  ]
};

// Test cases
async function runTests() {
  try {
    // Test 1: Generate questions with syllabus context
    console.log('\nTest Case 1: Generate Multiple Choice Questions with Syllabus Context');
    const params = {
      subject: 'Physics',
      topic: 'Newton\'s Laws',
      level: 'medium',
      numQuestions: 2,
      test_type: 'practice',
      questionType: 'multiple_choice',
      syllabusId: testSyllabus.id,
      unitId: testSyllabus.units[0].id,
      topicId: testSyllabus.units[0].topics[0].id
    };

    console.log('Parameters:', JSON.stringify(params, null, 2));

    const questions = await questionGenerationService.generateQuestions(params);
    console.log('\nGenerated Questions:');
    questions.forEach((q, i) => {
      console.log(`\nQuestion ${i + 1}:`);
      console.log('Text:', q.question);
      console.log('Keywords:', q.keywords);
      console.log('Learning Objective:', q.learning_objective);
      console.log('Source Tag:', q.source_tag);
      console.log('Quality Score:', q.quality_score);
    });

    // Test 2: Validate syllabus alignment
    console.log('\nTest Case 2: Validate Syllabus Alignment');
    const topic = testSyllabus.units[0].topics[0];
    const validationResult = questionAnalysisService.validateSyllabusAlignment(
      questions[0],
      {
        keywords: topic.keywords,
        learningObjectives: testSyllabus.units[0].learning_objectives,
        questionDistribution: topic.question_distribution
      }
    );

    console.log('\nValidation Result:');
    console.log('Is Valid:', validationResult.isValid);
    console.log('Scores:', validationResult.scores);
    console.log('Overall Score:', validationResult.overallScore);
    if (validationResult.feedback.length > 0) {
      console.log('Feedback:', validationResult.feedback);
    }

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('\nTest failed:', error);
  }
}

// Run tests
runTests();

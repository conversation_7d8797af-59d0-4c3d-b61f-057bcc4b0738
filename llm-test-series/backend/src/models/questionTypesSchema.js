// Simple validation functions instead of AJV for now
const validateSchema = (data, schema) => {
    // Basic validation - can be enhanced later
    return { valid: true, errors: [] };
};

// Base schema for all question types
const baseQuestionSchema = {
    type: 'object',
    required: ['question_id', 'question_text', 'question_type', 'correct_answer', 'explanation', 'difficulty', 'topics'],
    properties: {
        question_id: { type: 'string' },
        question_text: { type: 'string' },
        question_type: { 
            type: 'string',
            enum: ['multiple_choice', 'fill_in_blank', 'true_false', 'short_answer', 'theory']
        },
        explanation: { type: 'string' },
        difficulty: { 
            type: 'string',
            enum: ['easy', 'medium', 'hard', 'expert']
        },
        topics: {
            type: 'array',
            items: { type: 'string' }
        },
        source_tag: { type: 'string' },
        syllabus_id: { type: 'string' },
        unit_id: { type: 'string' },
        topic_id: { type: 'string' },
        is_actual_past_year: { type: 'boolean' },
        original_exam_year: { type: 'number' },
        original_exam_name: { type: 'string' },
        original_board_name: { type: 'string' },
        usage_count: { type: 'number' },
        success_rate: { type: 'number' },
        average_time_seconds: { type: 'number' },
        last_used: { type: 'string', format: 'date-time' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
    }
};

// Multiple Choice Question Schema
const multipleChoiceSchema = {
    ...baseQuestionSchema,
    required: [...baseQuestionSchema.required, 'options'],
    properties: {
        ...baseQuestionSchema.properties,
        options: {
            type: 'array',
            items: { type: 'string' },
            minItems: 4,
            maxItems: 4
        },
        correct_answer: { type: 'string' }
    }
};

// Fill in the Blank Question Schema
const fillInBlankSchema = {
    ...baseQuestionSchema,
    required: [...baseQuestionSchema.required, 'acceptable_answers'],
    properties: {
        ...baseQuestionSchema.properties,
        correct_answer: { type: 'string' },
        acceptable_answers: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1
        },
        case_sensitive: { type: 'boolean' },
        exact_match: { type: 'boolean' }
    }
};

// True/False Question Schema
const trueFalseSchema = {
    ...baseQuestionSchema,
    properties: {
        ...baseQuestionSchema.properties,
        correct_answer: { type: 'boolean' }
    }
};

// Short Answer Question Schema
const shortAnswerSchema = {
    ...baseQuestionSchema,
    required: [...baseQuestionSchema.required, 'keywords', 'max_words'],
    properties: {
        ...baseQuestionSchema.properties,
        correct_answer: { type: 'string' },
        keywords: {
            type: 'array',
            items: { type: 'string' },
            minItems: 1
        },
        max_words: { type: 'number' },
        min_words: { type: 'number' },
        rubric: {
            type: 'object',
            properties: {
                content: { type: 'number' },
                keywords: { type: 'number' },
                grammar: { type: 'number' }
            }
        }
    }
};

// Theory Question Schema
const theorySchema = {
    ...baseQuestionSchema,
    required: [...baseQuestionSchema.required, 'subtopics'],
    properties: {
        ...baseQuestionSchema.properties,
        correct_answer: { type: 'string' },
        subtopics: {
            type: 'array',
            items: { type: 'string' }
        },
        related_questions: {
            type: 'array',
            items: { type: 'string' }
        },
        references: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: { type: 'string' },
                    url: { type: 'string' },
                    type: { type: 'string' }
                }
            }
        }
    }
};

// Simple validate functions for each question type
const validateMultipleChoice = (data) => validateSchema(data, multipleChoiceSchema);
const validateFillInBlank = (data) => validateSchema(data, fillInBlankSchema);
const validateTrueFalse = (data) => validateSchema(data, trueFalseSchema);
const validateShortAnswer = (data) => validateSchema(data, shortAnswerSchema);
const validateTheory = (data) => validateSchema(data, theorySchema);

// Get validator for a specific question type
function getValidator(questionType) {
    switch (questionType) {
        case 'multiple_choice':
            return validateMultipleChoice;
        case 'fill_in_blank':
            return validateFillInBlank;
        case 'true_false':
            return validateTrueFalse;
        case 'short_answer':
            return validateShortAnswer;
        case 'theory':
            return validateTheory;
        default:
            return (data) => ({ valid: true, errors: [] });
    }
}

export {
    validateMultipleChoice,
    validateFillInBlank,
    validateTrueFalse,
    validateShortAnswer,
    validateTheory,
    getValidator
};

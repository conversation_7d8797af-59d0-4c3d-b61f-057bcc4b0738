import dotenv from 'dotenv';
import express from 'express';
import shortAnswerEvaluation from './routes/shortAnswerEvaluation.js';
import theory from './routes/theory.js';
import examConfig from './routes/admin/examConfig.js';
import adminAuth from './routes/admin/auth.js';
import path from 'path';
import { fileURLToPath } from 'url';
import compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import cors from 'cors';
import { Server } from 'socket.io';
import http from 'http';
import cluster from 'cluster';
import os from 'os';

// Import services
import { initializeFirebase } from './firebase.js';
import loggingService from './services/loggingService.js';

// Import existing routes
import questionsRouter from './routes/questions.js';
import analyticsRouter from './routes/analytics.js';

// Import new routes (comment out if they don't exist yet)
// import gamificationRouter from './routes/gamification.js';
// import socialRouter from './routes/social.js';

dotenv.config();

const numCPUs = os.cpus().length;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

if (cluster.isPrimary) {
  console.log(`Primary ${process.pid} is running`);

  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  const app = express();
  const server = http.createServer(app);
  const io = new Server(server);

  // Initialize services
  initializeFirebase();

  // Basic middleware
  app.use(helmet());
  app.use(cors());
  app.use(compression());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Static files
  app.use(express.static('public'));

  // Basic logging
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`);
    next();
  });

  // API routes
  app.use('/api/short-answer', shortAnswerEvaluation);
  app.use('/api/theory', theory);
  app.use('/api/admin/auth', adminAuth);
  app.use('/api/admin', examConfig);
  app.use('/api/questions', questionsRouter);
  app.use('/api/analytics', analyticsRouter);

  // Uncomment when gamification routes are ready
  // app.use('/api/gamification', gamificationRouter);
  // app.use('/api/social', socialRouter);

  // WebSocket handling
  io.on('connection', (socket) => {
    console.log(`Client connected: ${socket.id}`);

    socket.on('join-study-group', (groupId) => {
      socket.join(`group:${groupId}`);
    });

    socket.on('study-message', async (data) => {
      const { groupId, message } = data;
      io.to(`group:${groupId}`).emit('new-message', {
        id: Date.now(),
        userId: socket.userId,
        message,
        timestamp: new Date()
      });
    });

    socket.on('disconnect', () => {
      console.log(`Client disconnected: ${socket.id}`);
    });
  });

  // Error handling
  app.use((err, req, res, next) => {
    console.error(err);
    res.status(500).json({
      error: 'Internal Server Error'
    });
  });

  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => {
    console.log(`Worker ${process.pid} running on port ${PORT}`);
  });
}
// Test server to verify all services are working
import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Basic middleware
app.use(cors());
app.use(express.json());

// Test endpoint
app.get('/test', (req, res) => {
  res.json({
    status: 'Server is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    port: PORT
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    services: {
      server: 'running',
      database: 'checking...',
      redis: 'checking...'
    }
  });
});

// Test routes
app.get('/api/test/questions', (req, res) => {
  res.json({
    message: 'Questions API endpoint working',
    sampleQuestions: [
      {
        id: 1,
        question: "What is 2 + 2?",
        options: ["A) 3", "B) 4", "C) 5", "D) 6"],
        correctAnswer: "B",
        explanation: "2 + 2 equals 4"
      }
    ]
  });
});

app.get('/api/test/users', (req, res) => {
  res.json({
    message: 'Users API endpoint working',
    sampleUser: {
      id: 'test-user-1',
      username: 'testuser',
      level: 5,
      xp: 1250
    }
  });
});

app.get('/api/test/gamification', (req, res) => {
  res.json({
    message: 'Gamification API endpoint working',
    sampleData: {
      level: 5,
      xp: 1250,
      badges: ['first_test', 'streak_warrior'],
      streak: 7
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🧪 Test server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Test URL: http://localhost:${PORT}/test`);
  console.log(`❤️  Health check: http://localhost:${PORT}/health`);
});

export default app;

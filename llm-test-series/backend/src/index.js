import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';

// Import configuration
dotenv.config();

// Import middleware
import { apiLogger, errorLogger } from './middleware/logging.js';
import { authenticateJWT, checkRole } from './middleware/auth.js';
import { securityHeaders, antiScraping } from './middleware/security.js';
import { intelligentCacheMiddleware, enhancedCompressionMiddleware } from './middleware/optimization.js';

// Import all existing routes
import questionsRouter from './routes/questions.js';
import usersRouter from './routes/users.js';
import testSessionsRouter from './routes/testSessions.js';
import gamificationRouter from './routes/gamification.js';
import analyticsRouter from './routes/analytics.js';
import challengeRouter from './routes/challenge.js';
import learningRouter from './routes/learning.js';
import theoryRouter from './routes/theory.js';
import adminRouter from './routes/admin/index.js';
import syllabiRouter from './routes/syllabi.js';
import shortAnswerEvaluation from './routes/shortAnswerEvaluation.js';
import difficultyRouter from './routes/difficulty.js';
import enhancedQuestionsRouter from './routes/enhancedQuestions.js';

// Import services
import { getDb } from './config/firebase.js';
import loggingService from './services/loggingService.js';
import redisService from './services/redisService.js';
import cdnService from './services/cdnService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Initialize Firebase and services
getDb(); // This will initialize Firebase automatically

// Apply security middleware
app.use(securityHeaders);
app.use('/api', antiScraping);

// Apply optimization middleware
app.use(enhancedCompressionMiddleware);

// Cache routes based on their nature
app.use('/api/questions', intelligentCacheMiddleware(3600)); // Cache questions for 1 hour
app.use('/api/syllabi', intelligentCacheMiddleware(86400)); // Cache syllabus for 24 hours
app.use('/api/analytics', intelligentCacheMiddleware(300)); // Cache analytics for 5 minutes

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://*.firebaseio.com"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? process.env.ALLOWED_ORIGINS?.split(',') || ["http://localhost:3000"]
    : "*",
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Performance middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files with caching
app.use(express.static(path.join(__dirname, '../../frontend/build'), {
  maxAge: '1d',
  setHeaders: (res, path) => {
    if (path.endsWith('.html')) {
      res.setHeader('Cache-Control', 'no-cache');
    }
  }
}));

// Logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      path: req.path,
      query: req.query || {},
      status: res.statusCode,
      duration,
      userId: req.headers['user-id'] || 'anonymous',
      timestamp: new Date()
    };
    loggingService.logApiCall(logData);
  });
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    services: {
      database: 'connected',
      redis: redisService.isConnected() ? 'connected' : 'disconnected',
      cdn: 'active'
    }
  });
});

// API Routes - All existing functionality
app.use('/api/questions', questionsRouter);
app.use('/api/enhanced-questions', enhancedQuestionsRouter);
app.use('/api/users', usersRouter);
app.use('/api/test-sessions', testSessionsRouter);
app.use('/api/gamification', gamificationRouter);
app.use('/api/analytics', analyticsRouter);
app.use('/api/challenge', challengeRouter);
app.use('/api/learning', learningRouter);
app.use('/api/theory', theoryRouter);
app.use('/api/admin', adminRouter);
app.use('/api/syllabi', syllabiRouter);
app.use('/api/short-answer', shortAnswerEvaluation);
app.use('/api/difficulty', difficultyRouter);

// WebSocket handling for real-time features
io.on('connection', (socket) => {
  loggingService.logInfo(`Client connected: ${socket.id}`);

  socket.on('join-study-group', (groupId) => {
    socket.join(`group:${groupId}`);
  });

  socket.on('study-message', async (data) => {
    const { groupId, message } = data;
    io.to(`group:${groupId}`).emit('new-message', {
      id: Date.now(),
      userId: socket.userId,
      message,
      timestamp: new Date()
    });
  });

  socket.on('disconnect', () => {
    loggingService.logInfo(`Client disconnected: ${socket.id}`);
  });
});

// Error handling middleware
app.use(async (err, req, res, next) => {
  await loggingService.logError(err, {
    url: req.url,
    method: req.method,
    userId: req.headers['user-id'],
    timestamp: new Date()
  });

  if (err.type === 'rate-limit-exceeded') {
    return res.status(429).json({
      error: 'Too many requests'
    });
  }

  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production'
      ? 'Internal Server Error'
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await redisService.cleanup();
  server.close(() => {
    loggingService.logInfo('Server shutting down');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  loggingService.logInfo(`Server running on port ${PORT}`);
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔥 All services loaded and ready!`);
});

export default app;



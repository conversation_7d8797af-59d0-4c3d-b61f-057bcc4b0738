import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import path from 'path';
import { fileURLToPath } from 'url';

// Import configuration
dotenv.config();

// Import middleware
import { apiLogger, errorLogger } from './middleware/logging.js';
import { authenticateUser } from './middleware/auth.js';
import securityMiddleware from './middleware/security.js';

// Import routes
import questionsRouter from './routes/questions.js';
import usersRouter from './routes/users.js';
import testSessionsRouter from './routes/testSessions.js';
import gamificationRouter from './routes/gamification.js';
import analyticsRouter from './routes/analytics.js';
import challengeRouter from './routes/challenge.js';
import learningRouter from './routes/learning.js';
import theoryRouter from './routes/theory.js';
import adminRouter from './routes/admin/index.js';
import syllabiRouter from './routes/syllabi.js';
import shortAnswerEvaluation from './routes/shortAnswerEvaluation.js';

// Import services
import { initializeFirebase } from '../config/firebase.js';
import loggingService from './services/loggingService.js';
import realtimeService from './services/realtimeService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Initialize Firebase
initializeFirebase();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter);
app.use(apiLogger);
app.use(securityMiddleware);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API Routes
app.use('/api/questions', questionsRouter);
app.use('/api/users', usersRouter);
app.use('/api/test-sessions', testSessionsRouter);
app.use('/api/gamification', gamificationRouter);
app.use('/api/analytics', analyticsRouter);
app.use('/api/challenge', challengeRouter);
app.use('/api/learning', learningRouter);
app.use('/api/theory', theoryRouter);
app.use('/api/admin', adminRouter);
app.use('/api/syllabi', syllabiRouter);
app.use('/api/short-answer', shortAnswerEvaluation);

// Error handling middleware
app.use(errorLogger);
app.use((err, req, res, next) => {
  loggingService.logError(err, {
    route: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production'
      ? 'Something went wrong!'
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// Initialize real-time service
const realtimeServiceInstance = new realtimeService(io);

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  loggingService.logInfo(`Server running on port ${PORT}`);
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

export default app;



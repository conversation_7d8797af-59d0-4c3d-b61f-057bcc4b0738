import dotenv from 'dotenv';
import express from 'express';
import shortAnswerEvaluation from './routes/shortAnswerEvaluation.js';
import theory from './routes/theory.js';
import examConfig from './routes/admin/examConfig.js';
import adminAuth from './routes/admin/auth.js';
import userAuth from './routes/userAuth.js';
import path from 'path';
import { fileURLToPath } from 'url';
import compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import cors from 'cors';
import { intelligentCacheMiddleware, enhancedCompressionMiddleware, enhancedQueryOptimizationMiddleware, enhancedSsrMiddleware } from './middleware/optimization.js';
import { Server } from 'socket.io';
import http from 'http';
import cluster from 'cluster';
import os from 'os';

import { getDb } from './firebase.js';
import loggingService from './services/loggingService.js';
import redisService from './services/redisService.js';
import cdnService from './services/cdnService.js';
import security from './middleware/security.js';
// import { configureAdvancedSecurity } from './middleware/advancedSecurity.js';
// import securityAuditService from './services/securityAuditService.js';

// Import all routes at the top
import questionsRouter from './routes/questions.js';
import analyticsRouter from './routes/analytics.js';
import gamificationRouter from './routes/gamification.js';
import usersRouter from './routes/users.js';
import testSessionsRouter from './routes/testSessions.js';
// import securityRouter from './routes/security.js';

dotenv.config();

const numCPUs = os.cpus().length;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

if (cluster.isPrimary) {
  console.log(`Primary ${process.pid} is running`);

  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    // Fork a new worker if one dies
    cluster.fork();
  });
} else {
  // Workers can share any TCP connection
  // In this case it is an HTTP server
  const app = express();
  const server = http.createServer(app);
  const io = new Server(server);

  // Initialize services
  getDb(); // This will initialize Firebase automatically

  // Apply basic security middleware (advanced security temporarily disabled)
  app.use(security.securityHeaders);
  app.use('/api', security.apiLimiter);
  app.use('/api', security.antiScraping);

  // Apply optimization middleware
  app.use(enhancedCompressionMiddleware);
  app.use(enhancedQueryOptimizationMiddleware);
  app.use(enhancedSsrMiddleware);
  
  // Cache routes based on their nature
  app.use('/api/questions', intelligentCacheMiddleware(3600)); // Cache questions for 1 hour
  app.use('/api/syllabus', intelligentCacheMiddleware(86400)); // Cache syllabus for 24 hours
  app.use('/api/analytics', intelligentCacheMiddleware(300)); // Cache analytics for 5 minutes

  // Security middleware
  app.use(helmet());
  app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.ALLOWED_ORIGINS.split(',') 
      : '*'
  }));

  // Rate limiting
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
  });
  app.use('/api/', limiter);

  // Performance middleware
  app.use(compression());
  app.use(express.json());

  // API routes
  app.use('/api/short-answer', shortAnswerEvaluation);
  app.use('/api/theory', theory);
  app.use('/api/admin/auth', adminAuth);
  app.use('/api/admin', examConfig);
  app.use('/api/auth', userAuth);

  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Static files with caching
  app.use(express.static('public', {
    maxAge: '1d',
    setHeaders: (res, path) => {
      if (path.endsWith('.html')) {
        res.setHeader('Cache-Control', 'no-cache');
      }
    }
  }));

  // Logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        path: req.path,
        query: req.query || {},
        body: req.body || {},
        status: res.statusCode,
        duration,
        userId: req.headers['user-id'] || 'anonymous',
        timestamp: new Date()
      };
      loggingService.logApiCall(logData);
    });
    next();
  });



  // Use routes
  app.use('/api/questions', questionsRouter);
  app.use('/api/analytics', analyticsRouter);
  app.use('/api/gamification', gamificationRouter);
  app.use('/api/users', usersRouter);
  app.use('/api/test-sessions', testSessionsRouter);
  // app.use('/api/security', securityRouter);

  // WebSocket handling
  io.on('connection', (socket) => {
    loggingService.logInfo(`Client connected: ${socket.id}`);

    socket.on('join-study-group', (groupId) => {
      socket.join(`group:${groupId}`);
    });

    socket.on('study-message', async (data) => {
      const { groupId, message } = data;
      io.to(`group:${groupId}`).emit('new-message', {
        id: Date.now(),
        userId: socket.userId,
        message,
        timestamp: new Date()
      });
    });

    socket.on('disconnect', () => {
      loggingService.logInfo(`Client disconnected: ${socket.id}`);
    });
  });

  // Error handling
  app.use(async (err, req, res, next) => {
    await loggingService.logError(err, {
      url: req.url,
      method: req.method,
      userId: req.headers['user-id'],
      timestamp: new Date()
    });

    if (err.type === 'rate-limit-exceeded') {
      return res.status(429).json({
        error: 'Too many requests'
      });
    }
    res.status(err.status || 500).json({
      error: process.env.NODE_ENV === 'production'
        ? 'Internal Server Error'
        : err.message
    });
  });

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    await redisService.cleanup();
    server.close(() => {
      loggingService.logInfo('Server shutting down');
      process.exit(0);
    });
  });

  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => {
    loggingService.logInfo(`Worker ${process.pid} running on port ${PORT}`);
  });
}



import express from 'express';
import { getDb } from '../firebase.js';
import loggingService from '../services/loggingService.js';
import { authenticateToken } from './userAuth.js';

const router = express.Router();

/**
 * Start a new test session
 * @route POST /api/test-sessions
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { testConfig } = req.body; 
    const userId = req.user?.id; 

    if (!userId) {
      // This case should ideally be caught by authenticateJWT if it errors on no token/user
      // but as a safeguard:
      loggingService.logError(new Error('User ID not found after authentication'), {
        operation: 'createTestSession',
        route: req.originalUrl,
        message: 'User ID missing from token or token invalid after passing authentication middleware.'
      });
      return res.status(401).json({ 
        success: false, 
        error: 'Unauthorized', 
        message: 'User authentication failed or user ID not found in token.'
      });
    }

    if (!testConfig || !testConfig.subject || !testConfig.level || !testConfig.numQuestions) {
      return res.status(400).json({ 
        success: false,
        error: 'Missing required test configuration', 
        message: 'Test configuration must include subject, level, and numQuestions.',
        required: ['subject', 'level', 'numQuestions'] 
      });
    }
    
    const db = await getDb();
    const sessionRef = db.collection('test_sessions').doc();
    
    const newSession = {
      user_id: userId,
      test_config: testConfig,
      start_time: new Date(),
      status: 'in_progress',
      questions: [], // Questions will be added in a subsequent step
      user_answers: [],
      score: null,
      completion_time: null,
      time_taken_seconds: 0,
      results: [] // Initialize results array
    };

    await sessionRef.set(newSession);
    
    res.status(201).json({ 
      success: true, 
      sessionId: sessionRef.id,
      session: newSession // Return the created session object
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'createTestSession',
        route: req.originalUrl,
        userId: req.user?.id, // userId might be undefined if error is before it's set from req.user
        body: req.body
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to create test session', 
        message: error.message 
    });
  }
});

/**
 * Update a test session with questions
 * @route PUT /api/test-sessions/:id/questions
 */
router.put('/:id/questions', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { questions } = req.body;
    const userId = req.user?.id;

    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid questions data', 
        message: 'Questions must be a non-empty array.'
      });
    }
    
    const db = getDb();
    const sessionRef = db.collection('test_sessions').doc(id);
    const doc = await sessionRef.get();

    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Test session not found' 
      });
    }

    const sessionData = doc.data();
    if (sessionData.user_id !== userId) {
        return res.status(403).json({ 
            success: false, 
            error: 'Forbidden', 
            message: 'You are not authorized to update this test session.' 
        });
    }
    if (sessionData.status !== 'in_progress') {
        return res.status(400).json({ 
            success: false, 
            error: 'Session not in progress', 
            message: 'Questions can only be added to a session that is in progress.' 
        });
    }
    
    await sessionRef.update({
      questions, // Assuming questions are complete objects, not just IDs
      user_answers: Array(questions.length).fill(null) // Initialize answers array
    });
    
    res.json({ 
      success: true, 
      message: 'Test questions updated successfully' 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'updateTestQuestions',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params,
        bodyLength: req.body.questions?.length // Log length instead of full body for potentially large arrays
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to update test questions', 
        message: error.message 
    });
  }
});

/**
 * Submit an answer for a question in a test session
 * @route PUT /api/test-sessions/:id/answer
 */
router.put('/:id/answer', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { questionIndex, answer } = req.body;
    const userId = req.user?.id;

    if (questionIndex === undefined || answer === undefined) { 
      return res.status(400).json({ 
        success: false,
        error: 'Missing required fields', 
        message: 'questionIndex and answer are required.',
        required: ['questionIndex', 'answer'] 
      });
    }
    
    const db = getDb();
    const sessionRef = db.collection('test_sessions').doc(id);
    const doc = await sessionRef.get();

    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Test session not found' 
      });
    }
    
    const sessionData = doc.data();

    if (sessionData.user_id !== userId) {
        return res.status(403).json({ 
            success: false, 
            error: 'Forbidden', 
            message: 'You are not authorized to submit answers for this test session.' 
        });
    }

    if (sessionData.status !== 'in_progress') {
        return res.status(400).json({ 
            success: false, 
            error: 'Session not in progress', 
            message: 'Answers can only be submitted for a session that is in progress.' 
        });
    }
    
    if (questionIndex < 0 || questionIndex >= sessionData.questions.length) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid question index', 
        message: `questionIndex ${questionIndex} is out of bounds (0-${sessionData.questions.length -1}).`
      });
    }
    
    const userAnswers = [...sessionData.user_answers];
    userAnswers[questionIndex] = answer;
    
    await sessionRef.update({
      user_answers: userAnswers
    });
    
    res.json({ 
      success: true, 
      message: 'Answer submitted successfully' 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'submitAnswer',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params,
        body: req.body
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to submit answer', 
        message: error.message 
    });
  }
});

/**
 * Complete a test session and calculate score
 * @route PUT /api/test-sessions/:id/complete
 */
router.put('/:id/complete', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { timeTakenSeconds } = req.body; 
    const userId = req.user?.id;

    const db = getDb();
    const sessionRef = db.collection('test_sessions').doc(id);
    const doc = await sessionRef.get();

    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Test session not found' 
      });
    }
    
    const sessionData = doc.data();

    if (sessionData.user_id !== userId) {
        return res.status(403).json({ 
            success: false, 
            error: 'Forbidden', 
            message: 'You are not authorized to complete this test session.' 
        });
    }

    if (sessionData.status === 'completed') {
        return res.status(400).json({ 
            success: false, 
            error: 'Session already completed', 
            message: 'This test session has already been completed.' 
        });
    }
    
    let correctCount = 0;
    const results = sessionData.questions.map((question, index) => {
      const userAnswer = sessionData.user_answers[index];
      // Ensure question.correctAnswer exists and handle different answer types if necessary
      const isCorrect = userAnswer !== null && userAnswer !== undefined && userAnswer === question.correctAnswer;
      
      if (isCorrect) {
        correctCount++;
      }
      
      return {
        question_id: question.id, // Assuming question object has an id
        question_text: question.question, // Assuming question object has question text
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        explanation: question.explanation || ''
      };
    });
    
    const score = {
      correct: correctCount,
      total: sessionData.questions.length,
      percentage: sessionData.questions.length > 0 ? Math.round((correctCount / sessionData.questions.length) * 100) : 0
    };
    
    await sessionRef.update({
      status: 'completed',
      completion_time: new Date(),
      time_taken_seconds: typeof timeTakenSeconds === 'number' ? timeTakenSeconds : sessionData.time_taken_seconds || 0,
      score,
      results 
    });
    
    // Optionally, trigger gamification events here (e.g., award XP)
    // await gamificationService.awardXP(userId, 'test_completion', { score: score.percentage, testId: id });

    res.json({ 
      success: true, 
      score,
      results
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'completeTestSession',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params,
        body: req.body
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to complete test session', 
        message: error.message 
    });
  }
});

/**
 * Get a test session by ID
 * @route GET /api/test-sessions/:id
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    
    const db = getDb();
    const doc = await db.collection('test_sessions').doc(id).get();
    
    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Test session not found' 
      });
    }

    const sessionData = doc.data();
    if (sessionData.user_id !== userId) {
        // Allow admins to fetch any session, otherwise restrict to session owner
        // This requires an isAdmin check, e.g., if (req.user.role !== 'admin' && sessionData.user_id !== userId)
        // For now, strictly a user can only fetch their own sessions.
        return res.status(403).json({ 
            success: false, 
            error: 'Forbidden', 
            message: 'You are not authorized to view this test session.' 
        });
    }
    
    res.json({
      success: true,
      session: {
        id: doc.id,
        ...sessionData
      }
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'getTestSessionById',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to fetch test session', 
        message: error.message 
    });
  }
});

/**
 * Get all test sessions for the authenticated user
 * @route GET /api/test-sessions/user/me
 */
router.get('/user/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    
    const db = getDb();
    const snapshot = await db.collection('test_sessions')
      .where('user_id', '==', userId)
      .orderBy('start_time', 'desc')
      .get();
    
    const sessions = [];
    snapshot.forEach(doc => {
      sessions.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    res.json({ 
      success: true, 
      sessions 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'getUserTestSessions',
        route: req.originalUrl,
        userId: req.user?.id
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to fetch user test sessions', 
        message: error.message 
    });
  }
});


/**
 * Get analytics data for the authenticated user
 * @route GET /api/test-sessions/analytics/me
 */
router.get('/analytics/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    
    const db = getDb();
    const snapshot = await db.collection('test_sessions')
      .where('user_id', '==', userId)
      .where('status', '==', 'completed')
      .get();
    
    if (snapshot.empty) {
      return res.json({
        success: true,
        analytics: {
            totalTests: 0,
            averageScore: 0,
            totalQuestionsAnswered: 0, // Changed from totalQuestions
            subjectPerformance: [],
            progressOverTime: [],
            recentTests: []
        }
      });
    }
    
    const testSessions = [];
    snapshot.forEach(doc => {
      testSessions.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    const totalTests = testSessions.length;
    
    const totalScoreSum = testSessions.reduce((sum, session) => {
      return sum + (session.score?.percentage || 0);
    }, 0);
    const averageScore = totalTests > 0 ? Math.round(totalScoreSum / totalTests) : 0;
    
    const totalQuestionsAnswered = testSessions.reduce((sum, session) => {
      return sum + (session.score?.total || 0); // Sum of questions in completed tests
    }, 0);
    
    const subjectMap = {};
    testSessions.forEach(session => {
      const subject = session.test_config?.subject;
      if (!subject) return;
      
      if (!subjectMap[subject]) {
        subjectMap[subject] = {
          scores: [],
          count: 0
        };
      }
      subjectMap[subject].scores.push(session.score?.percentage || 0);
      subjectMap[subject].count++;
    });
    
    const subjectPerformance = Object.entries(subjectMap).map(([subject, data]) => ({
      subject,
      averageScore: Math.round(data.scores.reduce((a, b) => a + b, 0) / data.count),
      testsTaken: data.count
    }));
    
    const sortedSessions = testSessions.sort((a, b) => {
        const dateA = a.completion_time?.toDate ? a.completion_time.toDate() : new Date(0);
        const dateB = b.completion_time?.toDate ? b.completion_time.toDate() : new Date(0);
        return dateA - dateB; // Sorts oldest to newest for progressOverTime
    });
          
    const progressOverTime = sortedSessions.slice(-10).map(session => { // Take last 10, or fewer
        const date = session.completion_time?.toDate 
            ? session.completion_time.toDate()
            : new Date();
        return {
          date: date.toISOString().split('T')[0], 
          score: session.score?.percentage || 0,
          subject: session.test_config?.subject || 'Unknown'
        };
    });
    
    const formattedRecentTests = sortedSessions.slice(-5).reverse().map(session => { // Take last 5, newest first
        const date = session.completion_time?.toDate
            ? session.completion_time.toDate()
            : new Date();
        return {
          id: session.id,
          subject: session.test_config?.subject || 'Unknown',
          date: date.toLocaleDateString(),
          score: session.score?.percentage || 0,
          questionsCount: session.score?.total || 0
        };
    });
    
    res.json({
      success: true,
      analytics: {
        totalTests,
        averageScore,
        totalQuestionsAnswered,
        subjectPerformance,
        progressOverTime,
        recentTests: formattedRecentTests
      }
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'getUserAnalytics',
        route: req.originalUrl,
        userId: req.user?.id
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to fetch analytics data', 
        message: error.message 
    });
  }
});

// Admin route to get all sessions (example, needs admin role check)
// router.get('/admin/all', authenticateJWT, checkAdminRole, async (req, res) => { ... });

// Admin route to get a specific user's sessions (example, needs admin role check)
// router.get('/admin/user/:userId', authenticateJWT, checkAdminRole, async (req, res) => { ... });


export default router;

import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { getDb } from '../firebase.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

/**
 * User registration route
 */
router.post('/register', async (req, res) => {
    try {
        const { email, password, name } = req.body;

        // Input validation
        if (!email || !password || !name) {
            return res.status(400).json({ error: 'Email, password, and name are required' });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ error: 'Invalid email format' });
        }

        // Password validation
        if (password.length < 6) {
            return res.status(400).json({ error: 'Password must be at least 6 characters long' });
        }

        const db = await getDb();

        // Check if user already exists
        const existingUser = await db.collection('users')
            .where('email', '==', email)
            .limit(1)
            .get();

        if (!existingUser.empty) {
            return res.status(400).json({ error: 'User already exists with this email' });
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create user document
        const userData = {
            email,
            password: hashedPassword,
            name,
            createdAt: new Date(),
            lastLogin: new Date(),
            profile: {
                level: 1,
                xp: 0,
                streak: 0,
                badges: [],
                preferences: {
                    difficulty: 'medium',
                    subjects: []
                }
            },
            stats: {
                testsCompleted: 0,
                questionsAnswered: 0,
                correctAnswers: 0,
                averageScore: 0,
                timeSpent: 0
            }
        };

        // Add user to database
        const userRef = await db.collection('users').add(userData);
        
        // Generate JWT token
        const token = jwt.sign(
            { 
                id: userRef.id,
                email: userData.email,
                role: 'user',
                name: userData.name
            },
            process.env.JWT_SECRET || 'fallback-secret-key',
            { expiresIn: '7d' }
        );

        await loggingService.logInfo('User registration successful', { email, userId: userRef.id });

        res.status(201).json({ 
            token,
            user: {
                id: userRef.id,
                email: userData.email,
                name: userData.name,
                profile: userData.profile
            }
        });
    } catch (error) {
        await loggingService.logError('User registration error', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});

/**
 * User login route
 */
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // Input validation
        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        const db = await getDb();

        // Get user from database
        const userQuery = await db.collection('users')
            .where('email', '==', email)
            .limit(1)
            .get();

        if (userQuery.empty) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const userDoc = userQuery.docs[0];
        const user = userDoc.data();
        
        // Verify password
        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
            await loggingService.logWarning('Failed login attempt', { email });
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Update last login
        await userDoc.ref.set({ lastLogin: new Date() }, { merge: true });

        // Generate JWT token
        const token = jwt.sign(
            { 
                id: userDoc.id,
                email: user.email,
                role: 'user',
                name: user.name
            },
            process.env.JWT_SECRET || 'fallback-secret-key',
            { expiresIn: '7d' }
        );

        await loggingService.logInfo('User login successful', { email, userId: userDoc.id });

        res.json({ 
            token,
            user: {
                id: userDoc.id,
                email: user.email,
                name: user.name,
                profile: user.profile || { level: 1, xp: 0, streak: 0, badges: [] }
            }
        });
    } catch (error) {
        await loggingService.logError('User login error', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

/**
 * Get current user profile
 */
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const db = await getDb();
        const userDoc = await db.collection('users').doc(req.user.id).get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        const userData = userDoc.data();
        res.json({
            user: {
                id: userDoc.id,
                email: userData.email,
                name: userData.name,
                profile: userData.profile || { level: 1, xp: 0, streak: 0, badges: [] },
                stats: userData.stats || { testsCompleted: 0, questionsAnswered: 0, correctAnswers: 0 }
            }
        });
    } catch (error) {
        await loggingService.logError('Get profile error', error);
        res.status(500).json({ error: 'Failed to get profile' });
    }
});

/**
 * Anonymous login for demo purposes
 */
router.post('/anonymous', async (req, res) => {
    try {
        const db = await getDb();
        
        // Create anonymous user
        const anonymousId = 'anon_' + Math.random().toString(36).substr(2, 9);
        const userData = {
            email: `${anonymousId}@anonymous.local`,
            name: 'Anonymous User',
            isAnonymous: true,
            createdAt: new Date(),
            profile: {
                level: 1,
                xp: 0,
                streak: 0,
                badges: []
            },
            stats: {
                testsCompleted: 0,
                questionsAnswered: 0,
                correctAnswers: 0
            }
        };

        const userRef = await db.collection('users').add(userData);

        // Generate JWT token
        const token = jwt.sign(
            { 
                id: userRef.id,
                email: userData.email,
                role: 'user',
                name: userData.name,
                isAnonymous: true
            },
            process.env.JWT_SECRET || 'fallback-secret-key',
            { expiresIn: '24h' }
        );

        await loggingService.logInfo('Anonymous user created', { userId: userRef.id });

        res.json({ 
            token,
            user: {
                id: userRef.id,
                email: userData.email,
                name: userData.name,
                profile: userData.profile,
                isAnonymous: true
            }
        });
    } catch (error) {
        await loggingService.logError('Anonymous login error', error);
        res.status(500).json({ error: 'Anonymous login failed' });
    }
});

/**
 * JWT Authentication middleware
 */
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}

export default router;
export { authenticateToken };

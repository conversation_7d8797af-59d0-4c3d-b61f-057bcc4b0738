// Social Features Routes - Friends, Challenges, Study Groups
import express from 'express';
import socialService from '../services/socialService.js';
import { authenticateJWT } from '../middleware/auth.js';
import loggingService from '../services/loggingService.js';
import { socketService } from '../services/socketService.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateJWT);

// FRIENDS MANAGEMENT

// Get user's friends
router.get('/friends/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { status = 'accepted' } = req.query;
    
    const friends = await socialService.getFriends(userId, status);
    
    res.json({
      success: true,
      data: friends
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getFriends',
      route: req.originalUrl,
      userId: req.params.userId
    });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch friends',
      message: error.message
    });
  }
});

// Send friend request
router.post('/friends/request', async (req, res) => {
  try {
    const { userId, friendId } = req.body;
    
    if (!userId || !friendId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, friendId'
      });
    }
    
    if (userId === friendId) {
      return res.status(400).json({
        success: false,
        error: 'Cannot send friend request to yourself'
      });
    }
    
    const result = await socialService.sendFriendRequest(userId, friendId);
    
    // Emit real-time notification
    socketService.emitToUser(friendId, 'friendRequestReceived', {
      senderId: userId,
      senderName: req.user.username,
      timestamp: new Date().toISOString()
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'sendFriendRequest',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to send friend request',
      message: error.message
    });
  }
});

// Accept/Decline friend request
router.post('/friends/respond', async (req, res) => {
  try {
    const { userId, friendId, action } = req.body;
    
    if (!userId || !friendId || !action) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, friendId, action'
      });
    }
    
    if (!['accept', 'decline'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Action must be either "accept" or "decline"'
      });
    }
    
    const result = await socialService.respondToFriendRequest(userId, friendId, action);
    
    // Emit real-time notification
    if (action === 'accept') {
      socketService.emitToUser(friendId, 'friendRequestAccepted', {
        accepterId: userId,
        accepterName: req.user.username,
        timestamp: new Date().toISOString()
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'respondToFriendRequest',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to respond to friend request',
      message: error.message
    });
  }
});

// Remove friend
router.delete('/friends/:userId/:friendId', async (req, res) => {
  try {
    const { userId, friendId } = req.params;
    
    const result = await socialService.removeFriend(userId, friendId);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'removeFriend',
      route: req.originalUrl,
      params: req.params
    });
    res.status(500).json({
      success: false,
      error: 'Failed to remove friend',
      message: error.message
    });
  }
});

// CHALLENGES

// Get user's challenges
router.get('/challenges/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { status, type } = req.query;
    
    const challenges = await socialService.getChallenges(userId, { status, type });
    
    res.json({
      success: true,
      data: challenges
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getChallenges',
      route: req.originalUrl,
      userId: req.params.userId,
      query: req.query
    });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch challenges',
      message: error.message
    });
  }
});

// Create challenge
router.post('/challenges/create', async (req, res) => {
  try {
    const { challengerId, challengedId, config } = req.body;
    
    if (!challengerId || !challengedId || !config) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: challengerId, challengedId, config'
      });
    }
    
    const challenge = await socialService.createChallenge(challengerId, challengedId, config);
    
    // Emit real-time notification
    socketService.emitToUser(challengedId, 'challengeReceived', {
      challenge,
      challengerName: req.user.username,
      timestamp: new Date().toISOString()
    });
    
    res.json({
      success: true,
      data: challenge
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'createChallenge',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to create challenge',
      message: error.message
    });
  }
});

// Accept/Decline challenge
router.post('/challenges/respond', async (req, res) => {
  try {
    const { challengeId, userId, action } = req.body;
    
    if (!challengeId || !userId || !action) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: challengeId, userId, action'
      });
    }
    
    if (!['accept', 'decline'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Action must be either "accept" or "decline"'
      });
    }
    
    const result = await socialService.respondToChallenge(challengeId, userId, action);
    
    // Emit real-time notification
    if (action === 'accept') {
      socketService.emitToUser(result.challengerId, 'challengeAccepted', {
        challengeId,
        accepterName: req.user.username,
        timestamp: new Date().toISOString()
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'respondToChallenge',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to respond to challenge',
      message: error.message
    });
  }
});

// Complete challenge
router.post('/challenges/complete', async (req, res) => {
  try {
    const { challengeId, userId, score, sessionId } = req.body;
    
    if (!challengeId || !userId || score === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: challengeId, userId, score'
      });
    }
    
    const result = await socialService.completeChallenge(challengeId, userId, score, sessionId);
    
    // Emit real-time updates
    if (result.completed) {
      // Challenge is fully completed, notify both participants
      result.participants.forEach(participant => {
        socketService.emitToUser(participant.userId, 'challengeCompleted', {
          challengeId,
          results: result.results,
          timestamp: new Date().toISOString()
        });
      });
    } else {
      // One participant completed, notify the other
      const otherParticipant = result.participants.find(p => p.userId !== userId);
      if (otherParticipant) {
        socketService.emitToUser(otherParticipant.userId, 'challengeProgress', {
          challengeId,
          completedBy: req.user.username,
          timestamp: new Date().toISOString()
        });
      }
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'completeChallenge',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to complete challenge',
      message: error.message
    });
  }
});

// STUDY GROUPS

// Get user's study groups
router.get('/groups/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const groups = await socialService.getUserStudyGroups(userId);
    
    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getUserStudyGroups',
      route: req.originalUrl,
      userId: req.params.userId
    });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch study groups',
      message: error.message
    });
  }
});

// Create study group
router.post('/groups/create', async (req, res) => {
  try {
    const { name, description, createdBy, settings } = req.body;
    
    if (!name || !createdBy) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, createdBy'
      });
    }
    
    const group = await socialService.createStudyGroup({
      name,
      description,
      createdBy,
      settings: settings || {}
    });
    
    res.json({
      success: true,
      data: group
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'createStudyGroup',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to create study group',
      message: error.message
    });
  }
});

// Join study group
router.post('/groups/join', async (req, res) => {
  try {
    const { groupId, userId } = req.body;
    
    if (!groupId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: groupId, userId'
      });
    }
    
    const result = await socialService.joinStudyGroup(groupId, userId);
    
    // Emit real-time notification to group members
    socketService.emitToGroup(groupId, 'memberJoined', {
      userId,
      username: req.user.username,
      timestamp: new Date().toISOString()
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'joinStudyGroup',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to join study group',
      message: error.message
    });
  }
});

// Leave study group
router.post('/groups/leave', async (req, res) => {
  try {
    const { groupId, userId } = req.body;
    
    if (!groupId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: groupId, userId'
      });
    }
    
    const result = await socialService.leaveStudyGroup(groupId, userId);
    
    // Emit real-time notification to group members
    socketService.emitToGroup(groupId, 'memberLeft', {
      userId,
      username: req.user.username,
      timestamp: new Date().toISOString()
    });
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'leaveStudyGroup',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to leave study group',
      message: error.message
    });
  }
});

// SOCIAL FEED

// Get social feed
router.get('/feed/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 20, offset = 0 } = req.query;
    
    const feed = await socialService.getSocialFeed(userId, {
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    res.json({
      success: true,
      data: feed
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getSocialFeed',
      route: req.originalUrl,
      userId: req.params.userId,
      query: req.query
    });
    res.status(500).json({
      success: false,
      error: 'Failed to fetch social feed',
      message: error.message
    });
  }
});

// Create social post
router.post('/feed/post', async (req, res) => {
  try {
    const { userId, type, content, visibility } = req.body;
    
    if (!userId || !type || !content) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, type, content'
      });
    }
    
    const post = await socialService.createSocialPost({
      userId,
      type,
      content,
      visibility: visibility || 'friends'
    });
    
    // Emit to relevant users based on visibility
    if (visibility === 'public') {
      socketService.emitToAll('newPublicPost', post);
    } else {
      // Emit to friends
      const friends = await socialService.getFriends(userId, 'accepted');
      friends.forEach(friend => {
        socketService.emitToUser(friend.userId, 'newFriendPost', post);
      });
    }
    
    res.json({
      success: true,
      data: post
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'createSocialPost',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to create social post',
      message: error.message
    });
  }
});

// Like/Unlike post
router.post('/feed/like', async (req, res) => {
  try {
    const { postId, userId, action } = req.body;
    
    if (!postId || !userId || !action) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: postId, userId, action'
      });
    }
    
    if (!['like', 'unlike'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Action must be either "like" or "unlike"'
      });
    }
    
    const result = await socialService.likePost(postId, userId, action);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'likePost',
      route: req.originalUrl,
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to like/unlike post',
      message: error.message
    });
  }
});

export default router;

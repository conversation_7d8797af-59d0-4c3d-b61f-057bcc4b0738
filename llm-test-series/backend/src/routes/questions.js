import express from 'express';
import llmService from '../services/llmService.js';
import enhancedAIService from '../services/enhancedAIService.js';
import questionAnalysisService from '../services/questionAnalysisService.js';
import { getDb } from '../firebase.js';
import loggingService from '../services/loggingService.js';
import { authenticateToken } from './userAuth.js';

const router = express.Router();


// Validate request parameters (now using JWT auth)
const validateRequest = (req, res, next) => {
  // JWT authentication provides user info in req.user
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

/**
 * Generate or retrieve questions based on parameters
 * @route POST /api/questions/generate
 */
router.post('/generate', authenticateToken, validateRequest, async (req, res) => {
  try {
    const { subject, level, numQuestions, test_type, syllabus_id, unit_id, topic_id } = req.body;
    const userId = req.user.id;
    
    // Validate required parameters
    if (!subject || !level || !numQuestions) {
      return res.status(400).json({ 
        error: 'Missing required parameters', 
        required: ['subject', 'level', 'numQuestions'] 
      });
    }

    // Generate questions using enhanced AI service
    const questions = await enhancedAIService.generateQuestions({
      userId,
      subject,
      level,
      numQuestions: parseInt(numQuestions),
      questionType: 'multiple_choice',
      previousQuestions: [] // Could be fetched from user history
    });

    if (!questions || !questions.length) {
      return res.status(500).json({ error: 'No questions generated' });
    }

    res.json({ questions });
  } catch (error) {
    console.error('Error generating questions:', error);
    res.status(500).json({ error: 'Failed to generate questions' });
  }
});

/**
 * Generate questions via GET (for frontend compatibility)
 * @route GET /api/questions/generate
 */
router.get('/generate', authenticateToken, validateRequest, async (req, res) => {
  try {
    const { subject, level, numQuestions, test_type, syllabus_id, unit_id, topic_id } = req.query;
    const userId = req.user.id;

    // Validate required parameters
    if (!subject || !level || !numQuestions) {
      return res.status(400).json({
        error: 'Missing required parameters',
        required: ['subject', 'level', 'numQuestions']
      });
    }

    try {
      // Generate questions using enhanced AI service
      const questions = await enhancedAIService.generateQuestions({
        userId,
        subject,
        level,
        numQuestions: parseInt(numQuestions),
        questionType: 'multiple_choice',
        previousQuestions: [] // Could be fetched from user history
      });

      if (!questions || !questions.length) {
        return res.status(500).json({
          error: 'No questions generated',
          details: 'AI service returned empty result'
        });
      }

      res.json({
        success: true,
        questions,
        metadata: {
          subject,
          level,
          count: questions.length,
          source: 'AI Generated',
          generated_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error generating questions:', error.message);

      // Provide detailed error information
      if (error.message.includes('rate limited') || error.message.includes('429')) {
        return res.status(429).json({
          error: 'AI service temporarily rate limited',
          message: 'Please wait a moment and try again. The AI is generating high-quality questions.',
          retry_after: 60,
          details: `Generating ${numQuestions} ${level} level ${subject} questions`
        });
      }

      return res.status(500).json({
        error: 'Failed to generate questions',
        message: 'AI question generation failed. Please try again.',
        details: error.message,
        subject,
        level,
        requested_count: numQuestions
      });
    }
  } catch (error) {
    console.error('Error generating questions:', error);
    res.status(500).json({ error: 'Failed to generate questions' });
  }
});

/**
 * Submit feedback for a question
 * @route POST /api/questions/:questionId/feedback
 */
router.post('/:questionId/feedback', validateRequest, async (req, res) => {
  try {
    const { questionId } = req.params;
    const feedback = req.body;
    const userId = req.headers['user-id'] || 'anonymous';

    if (!feedback || typeof feedback !== 'object') {
      return res.status(400).json({ error: 'Valid feedback object is required' });
    }

    const qualityScore = await questionAnalysisService.updateQuestionQualityScore(
      questionId,
      {
        ...feedback,
        userId,
        timestamp: new Date()
      }
    );

    if (qualityScore === null) {
      return res.status(500).json({ error: 'Failed to update quality score' });
    }

    res.json({ qualityScore });
  } catch (error) {
    console.error('Error submitting question feedback:', error);
    res.status(500).json({ error: 'Failed to submit feedback' });
  }
});

/**
 * Get question statistics
 * @route GET /api/questions/:questionId/stats
 */
router.get('/:questionId/stats', async (req, res) => {
  try {
    const { questionId } = req.params;
    const doc = await questionAnalysisService.db.collection('questionQuality')
      .doc(questionId)
      .get();

    if (!doc.exists) {
      return res.status(404).json({ error: 'Question stats not found' });
    }

    res.json(doc.data());
  } catch (error) {
    console.error('Error getting question stats:', error);
    res.status(500).json({ error: 'Failed to get question stats' });
  }
});

/**
 * Get user's adaptive difficulty level
 * @route GET /api/questions/difficulty/:subject/:topic
 */
router.get('/difficulty/:subject/:topic', async (req, res) => {
  try {
    const { subject, topic } = req.params;
    const userId = req.headers['user-id'] || 'anonymous';

    const difficulty = await questionAnalysisService.calculateAdaptiveDifficulty(
      userId,
      subject,
      topic
    );

    res.json({ difficulty });
  } catch (error) {
    console.error('Error getting adaptive difficulty:', error);
    res.status(500).json({ error: 'Failed to get difficulty level' });
  }
});

/**
 * Get theory explanation for a topic or question
 * @route GET /api/questions/theory
 */
router.get('/theory', async (req, res) => {
  try {
    const { topic, questionId, syllabusName } = req.query;
    
    if (!topic && !questionId) {
      return res.status(400).json({ error: 'Either topic or questionId is required' });
    }
    
    let explanation = '';
    
    // If questionId is provided, first check if we have the question in Firestore
    if (questionId) {
      const db = getDb();
      const questionDoc = await db.collection('questions').doc(questionId).get();
      
      if (questionDoc.exists) {
        const questionData = questionDoc.data();
        
        // Check if we already have a theory explanation for this question
        const theoryRef = db.collection('theory_explanations')
          .where('question_id', '==', questionId)
          .limit(1);
        
        const theorySnapshot = await theoryRef.get();
        
        if (!theorySnapshot.empty) {
          // Return existing explanation
          explanation = theorySnapshot.docs[0].data().explanation;
        } else {
          // Generate new explanation with LLM
          explanation = await generateTheoryExplanationInternal(questionData.question, syllabusName);
          
          // Save explanation to Firestore
          await db.collection('theory_explanations').add({
            question_id: questionId,
            topic: questionData.topic || topic || '',
            explanation,
            timestamp: new Date()
          });
        }
      }
    } else if (topic) {
      // Check if we already have a theory explanation for this topic
      const db = getDb();
      const theoryRef = db.collection('theory_explanations')
        .where('topic', '==', topic)
        .limit(1);
      
      const theorySnapshot = await theoryRef.get();
      
      if (!theorySnapshot.empty) {
        // Return existing explanation
        explanation = theorySnapshot.docs[0].data().explanation;
      } else {
        // Generate new explanation with LLM
        explanation = await generateTheoryExplanationInternal(topic, syllabusName);
        
        // Save explanation to Firestore
        await db.collection('theory_explanations').add({
          topic,
          explanation,
          timestamp: new Date()
        });
      }
    }
    
    res.json({ explanation });
  } catch (error) {
    console.error('Error getting theory explanation:', error);
    res.status(500).json({ error: 'Failed to get theory explanation', details: error.message });
  }
});

async function generateTheoryExplanationInternal(topicOrQuestion, syllabusName = '') {
  return llmService.generateTheoryExplanation(topicOrQuestion, {}, syllabusName);
}

/**
 * Handle user's challenge to an answer/explanation
 * @route POST /api/questions/challenge
 */
router.post('/challenge', async (req, res) => {
  try {
    const { 
      questionId, 
      questionText, 
      options, 
      correctAnswer, 
      originalExplanation, 
      challengeText 
    } = req.body;
    
    // Validate required parameters
    if (!questionId || !questionText || !correctAnswer || !originalExplanation || !challengeText) {
      return res.status(400).json({ 
        error: 'Missing required parameters' 
      });
    }
    
    // Evaluate challenge using LLM
    const evaluation = await llmService.evaluateChallengeWithLLM({
      questionText,
      options,
      correctAnswer,
      originalExplanation,
      challengeText
    });
    
    // Store challenge in Firestore
    const db = getDb();
    await db.collection('question_challenges').add({
      questionId,
      challengeText,
      evaluation,
      timestamp: new Date(),
      userId: req.headers['user-id'] || 'anonymous'
    });
    
    res.json(evaluation);
    
  } catch (error) {
    console.error('Error evaluating challenge:', error);
    res.status(500).json({ 
      error: 'Failed to evaluate challenge' 
    });
  }
});

export default router;

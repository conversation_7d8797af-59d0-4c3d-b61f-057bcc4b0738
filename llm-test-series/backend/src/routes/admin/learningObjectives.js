import express from 'express';
import learningObjectivesService from '../../services/learningObjectivesService.js';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Create a new learning objective
router.post('/', adminAuthMiddleware, async (req, res) => {
  try {
    const objective = req.body;
    
    // Validate required fields
    if (!objective.name || !objective.syllabusId) {
      return res.status(400).json({
        error: 'Missing required fields'
      });
    }

    const objectiveId = await learningObjectivesService.createObjective(objective);
    res.json({ id: objectiveId });
  } catch (error) {
    loggingService.logError('Failed to create learning objective', { error });
    res.status(500).json({ error: error.message });
  }
});

// Get all learning objectives for a syllabus
router.get('/syllabus/:syllabusId', adminAuthMiddleware, async (req, res) => {
  try {
    const { syllabusId } = req.params;
    const objectives = await learningObjectivesService.getObjectivesForSyllabus(syllabusId);
    res.json(objectives);
  } catch (error) {
    loggingService.logError('Failed to get learning objectives', { error });
    res.status(500).json({ error: error.message });
  }
});

// Update a learning objective
router.put('/:objectiveId', adminAuthMiddleware, async (req, res) => {
  try {
    const { objectiveId } = req.params;
    const updates = req.body;

    await learningObjectivesService.updateObjective(objectiveId, updates);
    res.json({ success: true });
  } catch (error) {
    loggingService.logError('Failed to update learning objective', { error });
    res.status(500).json({ error: error.message });
  }
});

// Delete a learning objective
router.delete('/:objectiveId', adminAuthMiddleware, async (req, res) => {
  try {
    const { objectiveId } = req.params;
    await learningObjectivesService.deleteObjective(objectiveId);
    res.json({ success: true });
  } catch (error) {
    loggingService.logError('Failed to delete learning objective', { error });
    res.status(500).json({ error: error.message });
  }
});

// Map learning objectives to a topic
router.post('/map/:topicId', adminAuthMiddleware, async (req, res) => {
  try {
    const { topicId } = req.params;
    const { objectiveIds } = req.body;

    if (!Array.isArray(objectiveIds)) {
      return res.status(400).json({
        error: 'objectiveIds must be an array'
      });
    }

    await learningObjectivesService.mapObjectivesToTopic(topicId, objectiveIds);
    res.json({ success: true });
  } catch (error) {
    loggingService.logError('Failed to map objectives to topic', { error });
    res.status(500).json({ error: error.message });
  }
});

// Track progress for a learning objective
router.post('/progress/:objectiveId', async (req, res) => {
  try {
    const { objectiveId } = req.params;
    const { progress } = req.body;
    const userId = req.user.id;

    if (typeof progress !== 'number' || progress < 0 || progress > 100) {
      return res.status(400).json({
        error: 'Progress must be a number between 0 and 100'
      });
    }

    await learningObjectivesService.trackProgress(userId, objectiveId, progress);
    res.json({ success: true });
  } catch (error) {
    loggingService.logError('Failed to track objective progress', { error });
    res.status(500).json({ error: error.message });
  }
});

// Get progress for all objectives for a user
router.get('/progress/syllabus/:syllabusId', async (req, res) => {
  try {
    const { syllabusId } = req.params;
    const userId = req.user.id;

    const progress = await learningObjectivesService.getUserProgress(userId, syllabusId);
    res.json(progress);
  } catch (error) {
    loggingService.logError('Failed to get user progress', { error });
    res.status(500).json({ error: error.message });
  }
});

export default router;

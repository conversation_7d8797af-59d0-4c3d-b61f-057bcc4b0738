import express from 'express';
import ExamConfigService from '../../services/examConfig.js';
import { authenticateAdmin } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();
const examConfigService = new ExamConfigService();

// Middleware to ensure admin access
router.use(authenticateAdmin);

/**
 * Get all exam configurations
 */
router.get('/exam-configs', async (req, res) => {
    try {
        const configs = await examConfigService.getAllConfigs();
        res.json(configs);
    } catch (error) {
        loggingService.error('Failed to get exam configs:', error);
        res.status(500).json({ error: 'Failed to get exam configurations' });
    }
});

/**
 * Get a specific exam configuration
 */
router.get('/exam-configs/:id', async (req, res) => {
    try {
        const config = await examConfigService.getExamConfig(req.params.id);
        if (!config) {
            return res.status(404).json({ error: 'Exam configuration not found' });
        }
        res.json(config);
    } catch (error) {
        loggingService.error('Failed to get exam config:', error);
        res.status(500).json({ error: 'Failed to get exam configuration' });
    }
});

/**
 * Create a new exam configuration
 */
router.post('/exam-configs', async (req, res) => {
    try {
        // Validate required fields
        const requiredFields = ['name', 'type', 'duration', 'totalMarks'];
        const missingFields = requiredFields.filter(field => !req.body[field]);
        
        if (missingFields.length > 0) {
            return res.status(400).json({ 
                error: `Missing required fields: ${missingFields.join(', ')}` 
            });
        }

        // Create config
        const config = await examConfigService.createConfig(req.body);
        
        loggingService.info('Created new exam config:', { 
            configId: config.id, 
            name: config.name,
            type: config.type,
            admin: req.user.id 
        });

        res.status(201).json(config);
    } catch (error) {
        loggingService.error('Failed to create exam config:', error);
        res.status(500).json({ error: 'Failed to create exam configuration' });
    }
});

/**
 * Update an exam configuration
 */
router.put('/exam-configs/:id', async (req, res) => {
    try {
        const config = await examConfigService.getExamConfig(req.params.id);
        if (!config) {
            return res.status(404).json({ error: 'Exam configuration not found' });
        }

        // Update config
        const updatedConfig = await examConfigService.updateExamConfig(req.params.id, req.body);
        
        loggingService.info('Updated exam config:', { 
            configId: req.params.id, 
            name: req.body.name,
            type: req.body.type,
            admin: req.user.id 
        });

        res.json(updatedConfig);
    } catch (error) {
        loggingService.error('Failed to update exam config:', error);
        res.status(500).json({ error: 'Failed to update exam configuration' });
    }
});

/**
 * Delete an exam configuration
 */
router.delete('/exam-configs/:id', async (req, res) => {
    try {
        const config = await examConfigService.getExamConfig(req.params.id);
        if (!config) {
            return res.status(404).json({ error: 'Exam configuration not found' });
        }

        await examConfigService.deleteConfig(req.params.id);
        
        loggingService.info('Deleted exam config:', { 
            configId: req.params.id,
            admin: req.user.id 
        });

        res.status(204).send();
    } catch (error) {
        loggingService.error('Failed to delete exam config:', error);
        res.status(500).json({ error: 'Failed to delete exam configuration' });
    }
});

/**
 * Get default exam configuration template
 */
router.get('/exam-configs/template/default', (req, res) => {
    try {
        const defaultConfig = ExamConfigService.getDefaultConfig();
        res.json(defaultConfig);
    } catch (error) {
        loggingService.error('Failed to get default exam config template:', error);
        res.status(500).json({ error: 'Failed to get default configuration template' });
    }
});

/**
 * Get sample exam configuration
 */
router.get('/exam-configs/template/sample', (req, res) => {
    try {
        const sampleConfig = ExamConfigService.getSampleConfig();
        res.json(sampleConfig);
    } catch (error) {
        loggingService.error('Failed to get sample exam config:', error);
        res.status(500).json({ error: 'Failed to get sample configuration' });
    }
});

export default router;

/**
 * Admin routes for syllabus management
 */

import express from 'express';
import { authenticateJWT } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';
import syllabusService from '../../services/syllabusService.js';
import { syllabusSchema } from '../../models/syllabusSchema.js';

const router = express.Router();

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Get all syllabi
 * GET /api/admin/syllabus
 */
router.get('/', async (req, res) => {
    try {
        const syllabi = await syllabusService.getAllSyllabi();
        res.json(syllabi);
    } catch (error) {
        loggingService.logError('Failed to get syllabi', { error });
        res.status(500).json({ error: 'Failed to get syllabi' });
    }
});

/**
 * Get a specific syllabus
 * GET /api/admin/syllabus/:id
 */
router.get('/:id', async (req, res) => {
    try {
        const syllabus = await syllabusService.getSyllabus(req.params.id);
        res.json(syllabus);
    } catch (error) {
        loggingService.logError('Failed to get syllabus', { error });
        res.status(404).json({ error: 'Syllabus not found' });
    }
});

/**
 * Create a new syllabus
 * POST /api/admin/syllabus
 */
router.post('/', async (req, res) => {
    try {
        const syllabusId = await syllabusService.ingestSyllabus(req.body);
        res.status(201).json({ syllabusId });
    } catch (error) {
        loggingService.logError('Failed to create syllabus', { error });
        res.status(400).json({ error: error.message });
    }
});

// Bulk upload syllabi
router.post('/bulk', async (req, res) => {
    try {
        const syllabi = req.body;
        if (!Array.isArray(syllabi) || syllabi.length === 0) {
            return res.status(400).json({ error: 'Expected non-empty array of syllabi' });
        }

        const results = await Promise.all(
            syllabi.map(syllabus => syllabusService.ingestSyllabus(syllabus))
        );

        res.status(201).json({ syllabusIds: results });
    } catch (error) {
        loggingService.logError('Failed to bulk upload syllabi', { error });
        res.status(400).json({ error: error.message });
    }
});

/**
 * Update a syllabus
 * PUT /api/admin/syllabus/:id
 */
router.put('/:id', async (req, res) => {
    try {
        const db = getDb();
        const docRef = db.collection('syllabi').doc(req.params.id);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Syllabus not found' });
        }

        const updates = {
            ...req.body,
            updatedAt: new Date().toISOString()
        };

        await docRef.update(updates);
        res.json({ message: 'Syllabus updated successfully' });
    } catch (error) {
        loggingService.logError(error, { operation: 'updateSyllabus', id: req.params.id });
        res.status(400).json({ error: 'Failed to update syllabus' });
    }
});

/**
 * Delete a syllabus
 * DELETE /api/admin/syllabus/:id
 */
router.delete('/:id', async (req, res) => {
    try {
        const db = getDb();
        const docRef = db.collection('syllabi').doc(req.params.id);
        const doc = await docRef.get();

        if (!doc.exists) {
            return res.status(404).json({ error: 'Syllabus not found' });
        }

        await docRef.delete();
        res.json({ message: 'Syllabus deleted successfully' });
    } catch (error) {
        loggingService.logError(error, { operation: 'deleteSyllabus', id: req.params.id });
        res.status(400).json({ error: 'Failed to delete syllabus' });
    }
});

/**
 * Get topics for a unit
 * GET /api/admin/syllabus/:id/unit/:unitId/topics
 */
router.get('/:id/unit/:unitId/topics', async (req, res) => {
    try {
        const topics = await syllabusModel.getTopics(req.params.id, req.params.unitId);
        res.json(topics);
    } catch (error) {
        loggingService.logError(error, {
            operation: 'getTopics',
            syllabusId: req.params.id,
            unitId: req.params.unitId
        });
        res.status(404).json({ error: 'Topics not found' });
    }
});

/**
 * Update question distribution for a topic
 * PUT /api/admin/syllabus/:id/unit/:unitId/topic/:topicId/distribution
 */
router.put('/:id/unit/:unitId/topic/:topicId/distribution', async (req, res) => {
    try {
        await syllabusModel.updateQuestionDistribution(
            req.params.id,
            req.params.unitId,
            req.params.topicId,
            req.body
        );
        res.json({ message: 'Question distribution updated successfully' });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'updateQuestionDistribution',
            syllabusId: req.params.id,
            unitId: req.params.unitId,
            topicId: req.params.topicId
        });
        res.status(400).json({ error: 'Failed to update question distribution' });
    }
});

/**
 * Reorder items (units, topics, subtopics)
 * PUT /api/admin/syllabus/:id/reorder
 */
router.put('/:id/reorder', async (req, res) => {
    try {
        const { itemId, newIndex } = req.body;
        const syllabus = await syllabusModel.get(req.params.id);

        // Find and update item order
        let found = false;
        const updateOrder = (items) => {
            if (!items || found) return items;
            
            const itemIndex = items.findIndex(item => item.id === itemId);
            if (itemIndex !== -1) {
                const [item] = items.splice(itemIndex, 1);
                items.splice(newIndex, 0, item);
                found = true;
                return items;
            }

            // Recursively search in nested items
            items.forEach(item => {
                if (item.topics) item.topics = updateOrder(item.topics);
                if (item.subtopics) item.subtopics = updateOrder(item.subtopics);
            });

            return items;
        };

        if (syllabus.units) {
            syllabus.units = updateOrder(syllabus.units);
        }

        await syllabusModel.update(req.params.id, { units: syllabus.units });
        res.json({ message: 'Order updated successfully' });
    } catch (error) {
        loggingService.logError(error, {
            operation: 'reorderItems',
            syllabusId: req.params.id
        });
        res.status(400).json({ error: 'Failed to update order' });
    }
});

export default router;

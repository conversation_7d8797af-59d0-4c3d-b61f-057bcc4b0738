import express from 'express';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import questionAnalyticsService from '../../services/questionAnalyticsService.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Get user performance analytics
 * GET /api/admin/analytics/user/:userId
 */
router.get('/user/:userId', async (req, res) => {
    try {
        const analytics = await questionAnalyticsService.getUserAnalytics(req.params.userId);
        if (!analytics) {
            return res.status(404).json({ error: 'User analytics not found' });
        }
        res.json(analytics);
    } catch (error) {
        loggingService.logError('Failed to get user analytics', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get topic performance analytics
 * GET /api/admin/analytics/topic/:topicId
 */
router.get('/topic/:topicId', async (req, res) => {
    try {
        const analytics = await questionAnalyticsService.getTopicAnalytics(req.params.topicId);
        if (!analytics) {
            return res.status(404).json({ error: 'Topic analytics not found' });
        }
        res.json(analytics);
    } catch (error) {
        loggingService.logError('Failed to get topic analytics', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Track question performance
 * POST /api/admin/analytics/track
 */
router.post('/track', async (req, res) => {
    try {
        await questionAnalyticsService.trackPerformance(req.body);
        res.json({ message: 'Performance tracked successfully' });
    } catch (error) {
        loggingService.logError('Failed to track performance', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

import express from 'express';
import questionPreComputationService from '../../services/questionPreComputationService.js';
import loggingService from '../../services/loggingService.js';
import { authenticateJWT } from '../../middleware/auth.js';

const router = express.Router();

// Start pre-computation for a syllabus
router.post('/start/:syllabusId', authenticateJWT, async (req, res) => {
  try {
    const { syllabusId } = req.params;
    const jobId = await questionPreComputationService.startPreComputation(syllabusId);
    res.json({ jobId });
  } catch (error) {
    loggingService.logError('Failed to start pre-computation', { error });
    res.status(500).json({ error: error.message });
  }
});

// Get job status
router.get('/status/:jobId', adminAuthMiddleware, async (req, res) => {
  try {
    const { jobId } = req.params;
    const status = await questionPreComputationService.getJobStatus(jobId);
    res.json(status);
  } catch (error) {
    loggingService.logError('Failed to get job status', { error });
    res.status(500).json({ error: error.message });
  }
});

// Get all jobs for a syllabus
router.get('/jobs/:syllabusId', adminAuthMiddleware, async (req, res) => {
  try {
    const { syllabusId } = req.params;
    const jobs = await questionPreComputationService.getJobsForSyllabus(syllabusId);
    res.json(jobs);
  } catch (error) {
    loggingService.logError('Failed to get syllabus jobs', { error });
    res.status(500).json({ error: error.message });
  }
});

export default router;

import express from 'express';
import multer from 'multer';
import xlsx from 'xlsx';
import loggingService from '../../services/loggingService.js';
import syllabusValidationService from '../../services/syllabusValidationService.js';
import syllabusTemplateService from '../../services/syllabusTemplateService.js';
import { getDb } from '../../firebase.js';

const router = express.Router();
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
});

/**
 * @api {post} /api/admin/syllabus/bulk Bulk upload syllabi
 * @apiName BulkUploadSyllabi
 * @apiGroup Admin
 * @apiPermission admin
 * 
 * @apiParam {File} file JSON or Excel file containing syllabi
 */
// Download template
router.get('/template', async (req, res) => {
    try {
        const buffer = await syllabusTemplateService.generateTemplate();
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=syllabus_template.xlsx');
        res.send(buffer);
    } catch (error) {
        loggingService.logError('Failed to generate template', { error });
        res.status(500).json({ error: 'Failed to generate template' });
    }
});

// Bulk upload
// Parse Excel file
router.post('/bulk/parse', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        if (req.file.mimetype !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            return res.status(400).json({ error: 'Invalid file type. Only Excel files are supported.' });
        }

        const syllabi = await syllabusTemplateService.parseExcelFile(req.file.buffer);
        res.json(syllabi);
    } catch (error) {
        loggingService.logError('Failed to parse Excel file', { error });
        res.status(500).json({ error: 'Failed to parse Excel file' });
    }
});

// Bulk upload
router.post('/bulk', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        let syllabi;
        if (req.file.mimetype === 'application/json') {
            syllabi = JSON.parse(req.file.buffer.toString());
            if (!Array.isArray(syllabi)) {
                syllabi = [syllabi];
            }
        } else if (req.file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
            syllabi = await syllabusTemplateService.parseExcelFile(req.file.buffer);
        } else {
            return res.status(400).json({ error: 'Invalid file type. Only JSON and Excel files are supported.' });
        }

        // Validate all syllabi first
        const validationResults = syllabi.map(syllabus => ({
            syllabus,
            validation: syllabusValidationService.validateSyllabus(syllabus)
        }));

        // Check for any errors
        const hasErrors = validationResults.some(result => result.validation.errors.length > 0);
        if (hasErrors) {
            return res.status(400).json({
                error: 'Validation failed',
                results: validationResults.map(result => ({
                    name: result.syllabus.name || 'Unnamed Syllabus',
                    errors: result.validation.errors,
                    warnings: result.validation.warnings
                }))
            });
        }

        // Process valid syllabi
        const db = getDb();
        const batch = db.batch();
        const results = [];

        for (const { syllabus } of validationResults) {
            try {
                const docRef = db.collection('syllabus').doc();
                batch.set(docRef, {
                    ...syllabus,
                    id: docRef.id,
                    created_at: new Date(),
                    updated_at: new Date(),
                    status: 'draft'
                });
                results.push({
                    name: syllabus.name,
                    id: docRef.id,
                    status: 'success'
                });
            } catch (error) {
                results.push({
                    name: syllabus.name,
                    status: 'error',
                    error: error.message
                });
                loggingService.logError(error, {
                    operation: 'syllabusUpload',
                    syllabus: syllabus.name
                });
            }
        }

        await batch.commit();

        loggingService.logInfo('Bulk syllabus upload completed', {
            count: syllabi.length,
            successful: results.filter(r => r.status === 'success').length
        });

        res.json({
            message: 'Bulk upload completed',
            results
        });

    } catch (error) {
        loggingService.logError(error, { operation: 'syllabusUpload' });
        res.status(500).json({ error: 'Failed to process upload: ' + error.message });
    }
});

export default router;

import express from 'express';
import questionDistributionService from '../../services/questionDistributionService.js';
import { authenticateJWT } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Calculate question distribution
router.post('/calculate', authenticateJWT, async (req, res) => {
    try {
        const { syllabus, totalQuestions } = req.body;

        if (!syllabus || !totalQuestions) {
            return res.status(400).json({
                error: 'Missing required parameters'
            });
        }

        const distribution = await questionDistributionService.calculateQuestionDistribution(
            syllabus,
            totalQuestions
        );

        res.json(distribution);
    } catch (error) {
        loggingService.logError('Failed to calculate question distribution', { error });
        res.status(500).json({ error: error.message });
    }
});

// Save question distribution configuration
router.post('/config/:syllabusId', authenticateJWT, async (req, res) => {
    try {
        const { syllabusId } = req.params;
        const config = req.body;

        await questionDistributionService.saveDistributionConfig(syllabusId, config);
        res.json({ success: true });
    } catch (error) {
        loggingService.logError('Failed to save distribution config', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get question distribution configuration
router.get('/config/:syllabusId', authenticateJWT, async (req, res) => {
    try {
        const { syllabusId } = req.params;
        const config = await questionDistributionService.getDistributionConfig(syllabusId);
        res.json(config);
    } catch (error) {
        loggingService.logError('Failed to get distribution config', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

import express from 'express';
import monitoringService from '../../services/monitoringService.js';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Get system metrics
router.get('/metrics', adminAuthMiddleware, async (req, res) => {
    try {
        const metrics = await monitoringService.getSystemMetrics();
        res.json(metrics);
    } catch (error) {
        loggingService.logError('Failed to get system metrics', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get performance data
router.get('/performance', adminAuthMiddleware, async (req, res) => {
    try {
        const { timeRange = '7d' } = req.query;
        const data = await monitoringService.getPerformanceData(timeRange);
        res.json(data);
    } catch (error) {
        loggingService.logError('Failed to get performance data', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get quality metrics
router.get('/quality', adminAuthMiddleware, async (req, res) => {
    try {
        const metrics = await monitoringService.getQualityMetrics();
        res.json(metrics);
    } catch (error) {
        loggingService.logError('Failed to get quality metrics', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get coverage data
router.get('/coverage', adminAuthMiddleware, async (req, res) => {
    try {
        const coverage = await monitoringService.getCoverageData();
        res.json(coverage);
    } catch (error) {
        loggingService.logError('Failed to get coverage data', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get recent alerts
router.get('/alerts', adminAuthMiddleware, async (req, res) => {
    try {
        const alerts = await monitoringService.getRecentAlerts();
        res.json(alerts);
    } catch (error) {
        loggingService.logError('Failed to get recent alerts', { error });
        res.status(500).json({ error: error.message });
    }
});

// Get recent activity
router.get('/activity', adminAuthMiddleware, async (req, res) => {
    try {
        const activity = await monitoringService.getRecentActivity();
        res.json(activity);
    } catch (error) {
        loggingService.logError('Failed to get recent activity', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { getDb } from '../../firebase.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

/**
 * Admin login route
 */
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // Input validation
        if (!email || !password) {
            return res.status(400).json({ error: 'Email and password are required' });
        }

        // Get admin user from Firestore
        const db = getDb();
        const adminDoc = await db.collection('admin_users')
            .where('email', '==', email)
            .limit(1)
            .get();

        if (adminDoc.empty) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        const admin = adminDoc.docs[0].data();
        
        // Verify password
        const validPassword = await bcrypt.compare(password, admin.password);
        if (!validPassword) {
            loggingService.warn('Failed login attempt:', { email });
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate JWT token
        const token = jwt.sign(
            { 
                id: adminDoc.docs[0].id,
                email: admin.email,
                role: 'admin',
                name: admin.name
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        loggingService.info('Admin login successful:', { email });

        res.json({ 
            token,
            user: {
                id: adminDoc.docs[0].id,
                email: admin.email,
                name: admin.name
            }
        });
    } catch (error) {
        loggingService.error('Admin login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

/**
 * Create initial admin user (development only)
 */
if (process.env.NODE_ENV !== 'production') {
    router.post('/setup', async (req, res) => {
        try {
            const { email, password, name } = req.body;
            
            // Input validation
            if (!email || !password || !name) {
                return res.status(400).json({ 
                    error: 'Email, password, and name are required' 
                });
            }

            const db = getDb();

            // Check if admin already exists
            const existingAdmin = await db.collection('admin_users')
                .where('email', '==', email)
                .limit(1)
                .get();

            if (!existingAdmin.empty) {
                return res.status(400).json({ error: 'Admin user already exists' });
            }

            // Hash password
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);

            // Create admin user
            const adminUser = {
                email,
                password: hashedPassword,
                name,
                createdAt: new Date(),
                lastLogin: null
            };

            await db.collection('admin_users').add(adminUser);

            loggingService.info('Created initial admin user:', { email });
            res.status(201).json({ message: 'Admin user created successfully' });
        } catch (error) {
            loggingService.error('Error creating admin user:', error);
            res.status(500).json({ error: 'Failed to create admin user' });
        }
    });
}

export default router;

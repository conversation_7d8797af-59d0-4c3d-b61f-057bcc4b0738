import express from 'express';
import difficultyService from '../services/difficultyService.js';
import auth from '../middleware/auth.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

/**
 * Get adaptive questions based on user's skill level
 */
router.post('/adaptive-questions', auth, async (req, res) => {
    try {
        const { topic, subtopic, count } = req.body;
        const userId = req.user.id;

        const questions = await difficultyService.getAdaptiveQuestions({
            userId,
            topic,
            subtopic,
            count
        });

        res.json({ questions });
    } catch (error) {
        loggingService.logError('Failed to get adaptive questions', { error });
        res.status(500).json({
            error: 'Error getting adaptive questions',
            details: error.message
        });
    }
});

/**
 * Update user ratings after test completion
 */
router.post('/update-ratings', auth, async (req, res) => {
    try {
        const { testResults } = req.body;
        const userId = req.user.id;

        const updatedSkills = await difficultyService.updateUserRatings(userId, testResults);

        res.json({ skills: updatedSkills });
    } catch (error) {
        loggingService.logError('Failed to update user ratings', { error });
        res.status(500).json({
            error: 'Error updating user ratings',
            details: error.message
        });
    }
});

/**
 * Get user's current skill levels
 */
router.get('/skill-levels', auth, async (req, res) => {
    try {
        const userId = req.user.id;
        const skillLevels = await difficultyService.getUserSkillLevels(userId);

        res.json({ skillLevels });
    } catch (error) {
        loggingService.logError('Failed to get user skill levels', { error });
        res.status(500).json({
            error: 'Error getting user skill levels',
            details: error.message
        });
    }
});

export default router;

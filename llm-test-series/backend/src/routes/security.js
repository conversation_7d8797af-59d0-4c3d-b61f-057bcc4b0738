import express from 'express';
import securityAuditService from '../services/securityAuditService.js';
import { authenticateToken } from './userAuth.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

/**
 * Security Dashboard and Management Routes
 * Provides security monitoring and management capabilities
 */

// Middleware to check admin privileges
const requireAdmin = (req, res, next) => {
  if (!req.user || req.user.role !== 'admin') {
    securityAuditService.logSecurityEvent('unauthorized_access', {
      userId: req.user?.id,
      ip: req.ip,
      endpoint: req.path,
      userAgent: req.get('User-Agent')
    });
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

/**
 * Get security dashboard data
 * @route GET /api/security/dashboard
 */
router.get('/dashboard', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const dashboardData = securityAuditService.getSecurityDashboard();
    
    res.json({
      success: true,
      data: dashboardData
    });
    
    loggingService.logInfo('Security dashboard accessed', {
      userId: req.user.id,
      ip: req.ip
    });
    
  } catch (error) {
    loggingService.logError('Security dashboard error', error);
    res.status(500).json({ error: 'Failed to fetch security dashboard' });
  }
});

/**
 * Perform manual security audit
 * @route POST /api/security/audit
 */
router.post('/audit', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const auditResults = await securityAuditService.performSecurityAudit();
    
    res.json({
      success: true,
      data: auditResults
    });
    
    loggingService.logInfo('Manual security audit performed', {
      userId: req.user.id,
      ip: req.ip,
      findings: auditResults.findings.length
    });
    
  } catch (error) {
    loggingService.logError('Security audit error', error);
    res.status(500).json({ error: 'Failed to perform security audit' });
  }
});

/**
 * Get security events with filtering
 * @route GET /api/security/events
 */
router.get('/events', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { 
      limit = 50, 
      severity, 
      eventType, 
      startDate, 
      endDate 
    } = req.query;
    
    const dashboard = securityAuditService.getSecurityDashboard();
    let events = dashboard.recentEvents;
    
    // Apply filters
    if (severity) {
      events = events.filter(event => event.severity >= parseInt(severity));
    }
    
    if (eventType) {
      events = events.filter(event => event.type === eventType);
    }
    
    if (startDate) {
      const start = new Date(startDate);
      events = events.filter(event => new Date(event.timestamp) >= start);
    }
    
    if (endDate) {
      const end = new Date(endDate);
      events = events.filter(event => new Date(event.timestamp) <= end);
    }
    
    // Limit results
    events = events.slice(0, parseInt(limit));
    
    res.json({
      success: true,
      data: {
        events,
        total: events.length,
        filters: { limit, severity, eventType, startDate, endDate }
      }
    });
    
  } catch (error) {
    loggingService.logError('Security events fetch error', error);
    res.status(500).json({ error: 'Failed to fetch security events' });
  }
});

/**
 * Get user risk analysis
 * @route GET /api/security/users/risk
 */
router.get('/users/risk', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { minRiskScore = 50 } = req.query;
    const dashboard = securityAuditService.getSecurityDashboard();
    
    const riskUsers = dashboard.highRiskUsers.filter(user => 
      user.riskScore >= parseInt(minRiskScore)
    );
    
    res.json({
      success: true,
      data: {
        riskUsers,
        total: riskUsers.length,
        minRiskScore: parseInt(minRiskScore)
      }
    });
    
  } catch (error) {
    loggingService.logError('User risk analysis error', error);
    res.status(500).json({ error: 'Failed to analyze user risks' });
  }
});

/**
 * Block/unblock IP address
 * @route POST /api/security/ip/block
 */
router.post('/ip/block', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { ip, reason = 'Manual block by admin' } = req.body;
    
    if (!ip) {
      return res.status(400).json({ error: 'IP address is required' });
    }
    
    // Import blacklistIP function
    const { blacklistIP } = await import('../middleware/advancedSecurity.js');
    blacklistIP(ip, reason);
    
    securityAuditService.logSecurityEvent('ip_blocked', {
      ip,
      reason,
      blockedBy: req.user.id,
      adminIP: req.ip
    });
    
    res.json({
      success: true,
      message: `IP ${ip} has been blocked`,
      data: { ip, reason }
    });
    
    loggingService.logInfo('IP blocked by admin', {
      blockedIP: ip,
      reason,
      adminId: req.user.id,
      adminIP: req.ip
    });
    
  } catch (error) {
    loggingService.logError('IP blocking error', error);
    res.status(500).json({ error: 'Failed to block IP address' });
  }
});

/**
 * Get security metrics summary
 * @route GET /api/security/metrics
 */
router.get('/metrics', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const dashboard = securityAuditService.getSecurityDashboard();
    
    const summary = {
      threatLevel: dashboard.threatLevel,
      metrics: dashboard.metrics,
      recentActivity: {
        last24h: dashboard.recentEvents.length,
        highSeverity: dashboard.recentEvents.filter(e => e.severity >= 8).length,
        blockedRequests: dashboard.metrics.blockedRequests,
        authFailures: dashboard.metrics.authenticationFailures
      },
      systemHealth: {
        status: dashboard.threatLevel === 'LOW' ? 'healthy' : 'attention_required',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        lastAudit: dashboard.lastAuditTime
      }
    };
    
    res.json({
      success: true,
      data: summary
    });
    
  } catch (error) {
    loggingService.logError('Security metrics error', error);
    res.status(500).json({ error: 'Failed to fetch security metrics' });
  }
});

/**
 * Test security alert system
 * @route POST /api/security/test-alert
 */
router.post('/test-alert', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { severity = 5, message = 'Test alert from admin' } = req.body;
    
    const eventId = securityAuditService.logSecurityEvent('test_alert', {
      severity: parseInt(severity),
      message,
      triggeredBy: req.user.id,
      ip: req.ip,
      timestamp: new Date()
    });
    
    res.json({
      success: true,
      message: 'Test alert triggered successfully',
      data: { eventId, severity, message }
    });
    
    loggingService.logInfo('Security test alert triggered', {
      eventId,
      adminId: req.user.id,
      severity
    });
    
  } catch (error) {
    loggingService.logError('Test alert error', error);
    res.status(500).json({ error: 'Failed to trigger test alert' });
  }
});

/**
 * Health check endpoint (public)
 * @route GET /api/security/health
 */
router.get('/health', (req, res) => {
  const dashboard = securityAuditService.getSecurityDashboard();
  
  res.json({
    status: 'operational',
    threatLevel: dashboard.threatLevel,
    timestamp: new Date(),
    version: '1.0.0'
  });
});

export default router;

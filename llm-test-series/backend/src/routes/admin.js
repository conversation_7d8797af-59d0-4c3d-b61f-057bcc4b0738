const express = require('express');
const router = express.Router();
const { getDb } = require('../firebase');
const ExamConfigService = require('../config/examConfig');
const loggingService = require('../services/loggingService');

const examConfig = new ExamConfigService();

/**
 * Get all exams
 * @route GET /api/admin/exams
 */
router.get('/exams', async (req, res) => {
  try {
    const db = getDb();
    const snapshot = await db.collection('exam_configs').get();
    const exams = [];
    
    snapshot.forEach(doc => {
      exams.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    res.json(exams);
    
  } catch (error) {
    console.error('Error fetching exams:', error);
    res.status(500).json({ error: 'Failed to fetch exams' });
  }
});

/**
 * Create new exam
 * @route POST /api/admin/exams
 */
router.post('/exams', async (req, res) => {
  try {
    const examData = {
      ...ExamConfigService.getDefaultConfig(),
      ...req.body,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const db = getDb();
    const docRef = await db.collection('exam_configs').add(examData);
    
    res.json({
      id: docRef.id,
      ...examData
    });
    
  } catch (error) {
    console.error('Error creating exam:', error);
    res.status(500).json({ error: 'Failed to create exam' });
  }
});

/**
 * Update exam
 * @route PUT /api/admin/exams
 */
router.put('/exams', async (req, res) => {
  try {
    const { id, ...examData } = req.body;
    
    if (!id) {
      return res.status(400).json({ error: 'Exam ID is required' });
    }
    
    const db = getDb();
    await db.collection('exam_configs').doc(id).update({
      ...examData,
      updatedAt: new Date()
    });
    
    res.json({ id, ...examData });
    
  } catch (error) {
    console.error('Error updating exam:', error);
    res.status(500).json({ error: 'Failed to update exam' });
  }
});

/**
 * Delete exam
 * @route DELETE /api/admin/exams/:id
 */
router.delete('/exams/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const db = getDb();
    await db.collection('exam_configs').doc(id).delete();
    
    res.json({ message: 'Exam deleted successfully' });
    
  } catch (error) {
    console.error('Error deleting exam:', error);
    res.status(500).json({ error: 'Failed to delete exam' });
  }
});

/**
 * Get exam configuration
 * @route GET /api/admin/exams/:id/config
 */
router.get('/exams/:id/config', async (req, res) => {
  try {
    const config = await examConfig.getExamConfig(req.params.id);
    res.json(config);
    
  } catch (error) {
    console.error('Error fetching exam config:', error);
    res.status(500).json({ error: 'Failed to fetch exam configuration' });
  }
});

/**
 * Update exam configuration
 * @route PUT /api/admin/exams/:id/config
 */
router.put('/exams/:id/config', async (req, res) => {
  try {
    const config = await examConfig.updateExamConfig(req.params.id, req.body);
    res.json(config);
    
  } catch (error) {
    console.error('Error updating exam config:', error);
    res.status(500).json({ error: 'Failed to update exam configuration' });
  }
});

/**
 * Get API usage statistics
 * @route POST /api/admin/stats/api
 */
router.post('/stats/api', async (req, res) => {
  try {
    const { fromDate, toDate } = req.body;
    const stats = await loggingService.getApiStats(new Date(fromDate), new Date(toDate));
    res.json(stats);
  } catch (error) {
    console.error('Error fetching API stats:', error);
    res.status(500).json({ error: 'Failed to fetch API statistics' });
  }
});

/**
 * Get LLM usage statistics
 * @route POST /api/admin/stats/llm
 */
router.post('/stats/llm', async (req, res) => {
  try {
    const { fromDate, toDate } = req.body;
    const stats = await loggingService.getLlmStats(new Date(fromDate), new Date(toDate));
    res.json(stats);
  } catch (error) {
    console.error('Error fetching LLM stats:', error);
    res.status(500).json({ error: 'Failed to fetch LLM statistics' });
  }
});

module.exports = router;

import express from 'express';
import llmService from '../services/llmService.js';
import loggingService from '../services/loggingService.js';
import { authenticateJWT } from '../middleware/auth.js'; 

const router = express.Router();

router.post('/evaluate-short-answer', authenticateJWT, async (req, res) => { 
    try {
        const { answer, modelAnswer, keyPoints, rubric, questionId } = req.body; 
        const userId = req.user?.id; 

        
        if (!answer || !modelAnswer || !keyPoints || !rubric || !questionId) {
            return res.status(400).json({
                error: 'Missing required parameters.',
                message: 'Ensure answer, modelAnswer, keyPoints, rubric, and questionId are provided.'
            });
        }
        
        
        if (!llmService || typeof llmService.generateWithOptimalModel !== 'function') {
            loggingService.logError(new Error('llmService.generateWithOptimalModel is not available'), {
                operation: 'evaluateShortAnswer',
                route: req.originalUrl,
                userId: userId,
                body: { questionId, answerLength: answer?.length }, 
                message: 'LLM service for short answer evaluation is not configured or unavailable.'
            });
            return res.status(500).json({
                error: 'Service Unavailable',
                message: 'Short answer evaluation service is currently unavailable.'
            });
        }

        const prompt = `
            Evaluate this student's answer against the model answer and key points.
            
            Student's Answer:
            "${answer}"
            
            Model Answer:
            "${modelAnswer}"
            
            Key Points Required (${keyPoints.length} total):
            ${keyPoints.map((point, i) => `${i + 1}. ${point}`).join('\n')}
            
            Rubric:
            - Full credit (${rubric.fullCredit} points): All key points covered
            - Partial credit (${rubric.partialCredit} points): At least half of key points
            - Minimum points (${rubric.minimumPoints}): At least one-third of key points
            
            Evaluate the answer and provide:
            1. Score (number between 0 and ${rubric.fullCredit})
            2. Points covered (list of strings)
            3. Points missed (list of strings)
            4. Constructive feedback (string)
            Format as JSON.
        `;

        const evaluationResult = await llmService.generateWithOptimalModel(prompt, {
            type: 'evaluation',
            format: 'json'
        });
        
        
        let parsedEvaluation;
        if (typeof evaluationResult === 'string') {
            try {
                parsedEvaluation = JSON.parse(evaluationResult);
            } catch (parseError) {
                loggingService.logError(parseError, {
                    operation: 'evaluateShortAnswer_ParseLLMResponse',
                    route: req.originalUrl,
                    userId: userId,
                    llmResponse: evaluationResult, 
                    message: 'Failed to parse LLM response for short answer evaluation.'
                });
                return res.status(500).json({
                    error: 'Error processing evaluation result',
                    message: 'The evaluation service returned an unexpected format.'
                });
            }
        } else {
            parsedEvaluation = evaluationResult; 
        }


        
        loggingService.logEvaluation({
            type: 'short_answer',
            userId: userId, 
            questionId: questionId, 
            studentAnswer: answer,
            modelAnswer,
            keyPoints,
            rubric, 
            evaluation: parsedEvaluation, 
            prompt 
        });

        res.json({
            score: parsedEvaluation.score,
            pointsCovered: parsedEvaluation.pointsCovered,
            pointsMissed: parsedEvaluation.pointsMissed,
            feedback: parsedEvaluation.feedback
        });
    } catch (error) {
        loggingService.logError(error, { 
            operation: 'evaluateShortAnswer',
            route: req.originalUrl,
            userId: req.user?.id, 
            body: { questionId: req.body.questionId, answerLength: req.body.answer?.length } 
        });
        res.status(500).json({
            error: 'Error evaluating short answer',
            message: error.message 
        });
    }
});

export default router;
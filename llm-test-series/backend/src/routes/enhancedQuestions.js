import express from 'express';
import { getDb } from '../firebase.js';
import llmService from '../services/llmService.js';
import { trackLLMUsage } from '../services/monitoring.js';

const router = express.Router();
const { evaluateChallengeWithLLM } = llmService;

/**
 * Generate different types of questions (multiple choice, fill-in-blank, true/false, short answer)
 * @route POST /api/enhanced-questions/generate
 */
router.post('/generate', async (req, res) => {
  try {
    const { subject, level, numQuestions, questionType, syllabusId, topics } = req.body;
    
    if (!subject || !level || !numQuestions || !questionType) {
      return res.status(400).json({ 
        error: 'Missing required fields', 
        required: ['subject', 'level', 'numQuestions', 'questionType'] 
      });
    }
    
    const db = getDb();
    
    // Try to find existing questions of the specified type
    let query = db.collection('questions')
      .where('subject', '==', subject)
      .where('level', '==', level)
      .where('questionType', '==', questionType)
      .limit(numQuestions);
      
    if (syllabusId) {
      query = query.where('syllabusId', '==', syllabusId);
    }
    
    const snapshot = await query.get();
    let questions = [];
    
    snapshot.forEach(doc => {
      questions.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    // If not enough questions in database, generate new ones with LLM
    const remainingQuestions = numQuestions - questions.length;
    if (remainingQuestions > 0) {
      // The LLM generation would happen here in production
      // For demo purposes, we'll use placeholder data based on question type
      const newQuestions = await generateDifferentQuestionTypes(
        subject, level, remainingQuestions, questionType, syllabusId, topics
      );
      
      // Save generated questions to database
      const batch = db.batch();
      
      newQuestions.forEach(question => {
        const questionRef = db.collection('questions').doc();
        batch.set(questionRef, {
          ...question,
          created_at: new Date(),
          source_tag: `AI Generated - ${subject} - ${level}`,
          questionType
        });
      });
      
      await batch.commit();
      
      // Track LLM API usage
      await trackLLMUsage({
        operation: 'generate_questions',
        questionType,
        count: remainingQuestions,
        tokensUsed: remainingQuestions * 500, // Estimate
        model: 'gemini',
        timestamp: new Date()
      });
      
      questions = [...questions, ...newQuestions];
    }
    
    res.json({ questions });
  } catch (error) {
    console.error('Error generating enhanced questions:', error);
    res.status(500).json({ error: 'Failed to generate enhanced questions', details: error.message });
  }
});

/**
 * Generate theory explanation for a topic or question
 * @route POST /api/enhanced-questions/explain-theory
 */
router.post('/explain-theory', async (req, res) => {
  try {
    const { topic, questionId, syllabusId } = req.body;
    
    if ((!topic && !questionId) || (!syllabusId && !questionId)) {
      return res.status(400).json({ 
        error: 'Missing required fields', 
        required: ['topic OR questionId', 'syllabusId OR questionId'] 
      });
    }
    
    // Check if explanation already exists in database
    const db = getDb();
    let existingExplanation = null;
    
    if (questionId) {
      const explanationSnapshot = await db.collection('theory_explanations')
        .where('questionId', '==', questionId)
        .limit(1)
        .get();
      
      if (!explanationSnapshot.empty) {
        const doc = explanationSnapshot.docs[0];
        existingExplanation = {
          id: doc.id,
          ...doc.data()
        };
      }
    } else if (topic && syllabusId) {
      const explanationSnapshot = await db.collection('theory_explanations')
        .where('topic', '==', topic)
        .where('syllabusId', '==', syllabusId)
        .limit(1)
        .get();
      
      if (!explanationSnapshot.empty) {
        const doc = explanationSnapshot.docs[0];
        existingExplanation = {
          id: doc.id,
          ...doc.data()
        };
      }
    }
    
    if (existingExplanation) {
      return res.json({ explanation: existingExplanation });
    }
    
    // If no existing explanation, generate one
    let questionDetails = {};
    if (questionId) {
      const questionDoc = await db.collection('questions').doc(questionId).get();
      if (questionDoc.exists) {
        questionDetails = questionDoc.data();
        questionDetails.id = questionDoc.id; // Add id to question details
      }
    }
    
    // Call the LLM service to generate the explanation
    const explanation = await generateTheoryExplanation(topic || questionDetails.question, questionDetails, syllabusId);
    
    // Save to database for future use
    const explanationRef = db.collection('theory_explanations').doc();
    await explanationRef.set({
      ...explanation,
      questionId: questionId || null,
      topic: topic || questionDetails.question,
      syllabusId: syllabusId || questionDetails.syllabusId || null,
      created_at: new Date()
    });
    
    // Track LLM API usage
    await trackLLMUsage({
      operation: 'explain_theory',
      topic: topic || 'question_explanation',
      tokensUsed: 1000, // Estimate
      model: 'gemini',
      timestamp: new Date()
    });
    
    res.json({ 
      explanation: {
        id: explanationRef.id,
        ...explanation
      }
    });
  } catch (error) {
    console.error('Error generating theory explanation:', error);
    res.status(500).json({ error: 'Failed to generate theory explanation', details: error.message });
  }
});

/**
 * Challenge an answer to a question
 * @route POST /api/enhanced-questions/challenge-answer
 */
router.post('/challenge-answer', async (req, res) => {
  try {
    const { questionId, challenge } = req.body;
    
    const userChallenge = challenge; // For backwards compatibility
    
    if (!questionId || !userChallenge) {
      return res.status(400).json({ 
        error: 'Missing required fields', 
        required: ['questionId', 'userChallenge'] 
      });
    }
    
    const db = getDb();
    const questionDoc = await db.collection('questions').doc(questionId).get();
    
    if (!questionDoc.exists) {
      return res.status(404).json({ error: 'Question not found' });
    }
    
    const questionData = questionDoc.data();
    
    // Call the LLM service to evaluate the challenge
    const evaluationResult = await evaluateChallengeWithLLM(questionData, userChallenge);
    
    // Save challenge and response
    const challengeRef = db.collection('question_challenges').doc();
    await challengeRef.set({
      questionId,
      userChallenge,
      evaluation: evaluationResult,
      status: evaluationResult.verdict === 'valid' ? 'pending_review' : 'resolved',
      created_at: new Date()
    });
    
    // Track LLM API usage
    await trackLLMUsage({
      operation: 'challenge_answer',
      tokensUsed: 800, // Estimate
      model: 'gemini',
      timestamp: new Date()
    });
    
    res.json({ 
      id: challengeRef.id,
      evaluation: evaluationResult 
    });
  } catch (error) {
    console.error('Error processing answer challenge:', error);
    res.status(500).json({ error: 'Failed to process answer challenge', details: error.message });
  }
});

// Helper functions (these would call the LLM API in production)
async function generateDifferentQuestionTypes(subject, level, count, type, syllabusId, topics) {
  // This would call the LLM API in production
  // For now, return sample questions based on type
  
  const questions = [];
  
  switch (type) {
    case 'multiple-choice':
      for (let i = 0; i < count; i++) {
        questions.push({
          question: `Sample multiple choice question ${i+1} about ${subject}`,
          options: ["Option A", "Option B", "Option C", "Option D"],
          correctAnswer: "Option A",
          explanation: "This is the explanation for the correct answer.",
          subject,
          level,
          syllabusId: syllabusId || null,
          questionType: 'multiple-choice',
          difficulty: 'medium'
        });
      }
      break;
      
    case 'fill-in-blank':
      for (let i = 0; i < count; i++) {
        questions.push({
          question: `Sample __________ is a fill in the blank question ${i+1} about ${subject}.`,
          correctAnswer: "answer",
          acceptableAnswers: ["answer", "ANSWER", "Answer", "the answer"],
          explanation: "This is the explanation for the correct answer.",
          subject,
          level,
          syllabusId: syllabusId || null,
          questionType: 'fill-in-blank',
          difficulty: 'medium'
        });
      }
      break;
    
    case 'true-false':
      for (let i = 0; i < count; i++) {
        questions.push({
          question: `Sample true/false statement ${i+1} about ${subject}.`,
          correctAnswer: "True",
          explanation: "This is why the statement is true.",
          subject,
          level,
          syllabusId: syllabusId || null,
          questionType: 'true-false',
          difficulty: 'easy'
        });
      }
      break;
      
    case 'short-answer':
      for (let i = 0; i < count; i++) {
        questions.push({
          question: `Sample short answer question ${i+1} about ${subject}?`,
          correctAnswer: "This is the model answer that would be expected.",
          keyPoints: ["key point 1", "key point 2", "key point 3"],
          explanation: "The answer should address these key points.",
          subject,
          level,
          syllabusId: syllabusId || null,
          questionType: 'short-answer',
          difficulty: 'hard'
        });
      }
      break;
  }
  
  return questions;
}

async function generateTheoryExplanationInternal(topic, questionDetails, syllabusId) {
  return llmService.generateTheoryExplanation(topic, questionDetails, syllabusId);
}

async function evaluateChallengeWithLLM(question, userChallenge) {
  // This would call the LLM API in production
  return {
    verdict: Math.random() > 0.8 ? 'valid' : 'invalid',
    reasoning: "This is the detailed reasoning for why the challenge is valid/invalid based on the LLM's evaluation.",
    suggestedCorrection: Math.random() > 0.8 ? {
      correctAnswer: "Updated answer based on challenge",
      explanation: "Updated explanation based on challenge"
    } : null
  };
}

export default router;

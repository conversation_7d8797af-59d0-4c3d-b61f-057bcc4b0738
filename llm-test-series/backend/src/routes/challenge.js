import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import challengeService from '../services/challengeService.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

// Apply auth middleware to all routes
router.use(authMiddleware);

/**
 * Submit a challenge
 * POST /api/challenge/submit
 */
router.post('/submit', async (req, res) => {
    try {
        const challenge = await challengeService.submitChallenge({
            ...req.body,
            userId: req.user.id
        });
        res.json(challenge);
    } catch (error) {
        loggingService.logError('Failed to submit challenge', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get challenges for a question
 * GET /api/challenge/question/:questionId
 */
router.get('/question/:questionId', async (req, res) => {
    try {
        const challenges = await challengeService.getChallenges(req.params.questionId);
        res.json(challenges);
    } catch (error) {
        loggingService.logError('Failed to get challenges', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get challenges submitted by current user
 * GET /api/challenge/user
 */
router.get('/user', async (req, res) => {
    try {
        const challenges = await challengeService.getUserChallenges(req.user.id);
        res.json(challenges);
    } catch (error) {
        loggingService.logError('Failed to get user challenges', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

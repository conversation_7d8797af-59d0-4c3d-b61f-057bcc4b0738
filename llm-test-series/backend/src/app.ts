import dotenv from 'dotenv';
import express, { Request, Response, NextFunction, ErrorRequestHandler } from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Import middleware
import { apiLogger, errorLogger } from './middleware/logging.js';
import { monitoringMiddleware } from './middleware/monitoring.js';
import { startMonitoring } from './config/monitoring.js';

// Import route handlers
import questionsRouter from './routes/questions.js';
import learningRouter from './routes/learning.js';
import adminRouter from './routes/admin/index.js';
import syllabiRouter from './routes/syllabi.js';
import testSessionsRouter from './routes/testSessions.js';
import challengeRouter from './routes/challenge.js';
import difficultyRouter from './routes/difficulty.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../../frontend/public')));
app.use(apiLogger);
app.use(monitoringMiddleware);

// Initialize monitoring
startMonitoring();

// API Routes
app.use('/api/questions', questionsRouter);
app.use('/api/learning', learningRouter);
app.use('/api/admin', adminRouter);
app.use('/api/syllabi', syllabiRouter);
app.use('/api/test-sessions', testSessionsRouter);
app.use('/api/challenge', challengeRouter);
app.use('/api/difficulty', difficultyRouter);

// Error logging middleware
app.use(errorLogger);

// Error handling middleware
const errorHandler: ErrorRequestHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: err.message
  });
};

app.use(errorHandler);

export default app;

import llmService from './llmService.js';
import questionAnalysisService from './questionAnalysisService.js';
import loggingService from './loggingService.js';
import cacheService from './cacheService.js';
import { getDb } from '../firebase.js';

class QuestionGenerationService {
  constructor() {
    this.db = getDb();
    this.loggingService = loggingService;
    this.questionTypes = {
      multiple_choice: this.generateMultipleChoice.bind(this),
      fill_in_blanks: this.generateFillInBlanks.bind(this),
      true_false: this.generateTrueFalse.bind(this),
      short_answer: this.generateShortAnswer.bind(this)
    };

    this.answerMatchingRules = {
      caseSensitive: false,
      trimWhitespace: true,
      allowSynonyms: true
    };
  }

  async generateQuestions(params) {
    const {
      userId,
      subject,
      topics,
      level,
      numQuestions = 10,
      test_type = 'practice',
      questionType = 'multiple_choice',
      previousQuestions = [],
      bypassCache = false,
      useMixing = true
    } = params;

    // If mixing is enabled and we have topics, use the mixing service
    if (useMixing && topics && topics.length > 0) {
      return await questionMixingService.getMixedQuestions({
        ...params,
        difficulty: level
      });
    }

    try {
      // Check cache first unless bypass is requested
      if (!bypassCache) {
        const cachedQuestions = cacheService.get(questionType, {
          subject,
          topic,
          level,
          syllabusId: params.syllabusId,
          unitId: params.unitId,
          topicId: params.topicId
        });

        if (cachedQuestions) {
          // Filter out previously used questions
          const filteredQuestions = cachedQuestions.filter(q => 
            !previousQuestions.includes(q.id)
          );

          // If we have enough unique questions, use them
          if (filteredQuestions.length >= numQuestions) {
            this.loggingService.logInfo('Using cached questions', {
              questionType,
              subject,
              topic,
              numQuestions
            });
            return filteredQuestions.slice(0, numQuestions);
          }
        }
      }
      // Get adaptive difficulty if level not specified
      const difficulty = level || await questionAnalysisService.calculateAdaptiveDifficulty(
        userId,
        subject,
        topic
      );

      // Generate questions based on type
      const generator = this.questionTypes[questionType];
      if (!generator) {
        throw new Error(`Unsupported question type: ${questionType}`);
      }

      const questions = await generator({
        subject,
        level: difficulty,
        numQuestions,
        topic,
        test_type,
        generateAcceptableAnswers: (answer) => {
          if (!answer) return [];

          try {
            // Handle array of answers
            if (Array.isArray(answer)) {
              return answer.flatMap(a => this.generateAcceptableAnswers(a));
            }

            // Convert answer to string and normalize
            const normalizedAnswer = String(answer).toLowerCase().trim();

            // Generate variations
            const variations = [
              normalizedAnswer,
              // Remove punctuation
              normalizedAnswer.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, ''),
              // Remove extra spaces
              normalizedAnswer.replace(/\s+/g, ' '),
              // Split on comma and clean
              ...normalizedAnswer.split(',').map(a => a.trim()),
              // Split on slash and clean
              ...normalizedAnswer.split('/').map(a => a.trim())
            ];

            // Filter out empty strings and duplicates
            return [...new Set(variations.filter(v => v.length > 0))];
          } catch (error) {
            loggingService.logError(error, { operation: 'generateAcceptableAnswers', answer });
            return Array.isArray(answer) ? answer : [answer];
          }
        }
      });

      // Ensure questions is an array
      const questionsArray = Array.isArray(questions) ? questions : [questions];

      // Process and validate questions
      const processedQuestions = await this.processQuestions(questionsArray, {
        userId,
        subject,
        topic,
        questionType
      });

      // Cache the processed questions
      if (!bypassCache) {
        cacheService.set(questionType, {
          subject,
          topic,
          level,
          syllabusId: params.syllabusId,
          unitId: params.unitId,
          topicId: params.topicId
        }, processedQuestions);
      }

      return processedQuestions;
    } catch (error) {
      loggingService.logError(error, { operation: 'generateQuestions', params });
      // Return default questions in development
      if (process.env.NODE_ENV === 'development') {
        return this.createDefaultQuestions(params);
      }
      throw error;
    }
  }

  createDefaultQuestions(params) {
    const { subject, topic, questionType, numQuestions = 1 } = params;
    if (process.env.NODE_ENV === 'development') {
      // In development, return a default question with syllabus context
      let syllabusContext = '';
      if (params.syllabusId) {
        syllabusContext = {
          unit: 'Classical Mechanics',
          topic: 'Newton\'s Laws of Motion',
          keywords: ['force', 'mass', 'acceleration'],
          learning_objectives: {
            understand: 'Understand Newton\'s laws',
            apply: 'Apply principles to problems'
          }
        };
      }

      const defaultQuestion = {
        question: `Default ${params.questionType} question for ${params.subject}${params.topic ? ` - ${params.topic}` : ''}`,
        correct_answer: 'A',
        explanation: 'This is a default question for development.',
        keywords: syllabusContext ? syllabusContext.keywords : ['test', 'default'],
        cognitive_level: 'understand',
        learning_objective: syllabusContext ? syllabusContext.learning_objectives.understand : undefined,
        source_tag: `Default - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}${params.syllabusId ? ` [Syllabus: ${params.syllabusId}]` : ''}`,
        quality_score: 0.8,
        syllabusId: params.syllabusId,
        unitId: params.unitId,
        topicId: params.topicId
      };

      if (params.questionType === 'multiple_choice') {
        defaultQuestion.options = ['Option A', 'Option B', 'Option C', 'Option D'];
      }

      return [defaultQuestion];
    }

    const defaultQuestion = {
      subject,
      topic,
      type: questionType,
      question: `Default ${questionType} question for ${subject}${topic ? ` - ${topic}` : ''}`,
      explanation: 'This is a default question for testing purposes.',
      keywords: ['test', 'default'],
      cognitive_level: 'understand',
      difficulty: 'medium',
      source_tag: `Default - ${subject}${topic ? ` - ${topic}` : ''}`,
      quality_score: 0.8,
      status: 'active',
      timestamp: new Date(),
      created_by: 'system',
      last_modified: new Date(),
      version: 1
    };

    if (questionType === 'multiple_choice') {
      return [{
        ...defaultQuestion,
        options: ['A) First option', 'B) Second option', 'C) Third option', 'D) Fourth option'],
        correct_answer: 'A'
      }];
    } else if (questionType === 'fill_in_blanks') {
      return [{
        ...defaultQuestion,
        question: `Default fill in the blanks question for ${subject}: The [___] is a test.`,
        correct_answer: 'answer',
        alternative_answers: ['response', 'solution']
      }];
    }

    return [defaultQuestion];
  }

  /**
   * Process and validate generated questions
   */
  async saveQuestions(questions) {
    try {
      const batch = this.db.batch();
      
      for (const question of questions) {
        const docRef = this.db.collection('questions').doc();
        batch.set(docRef, {
          ...question,
          id: docRef.id,
          created_at: new Date(),
          updated_at: new Date()
        });
      }

      await batch.commit();
      this.loggingService.logInfo('Questions saved to database', { count: questions.length });
    } catch (error) {
      this.loggingService.logError(error, { operation: 'saveQuestions' });
      throw error;
    }
  }

  async processQuestions(questions, params) {
    try {
      // Basic validation
      if (!Array.isArray(questions) || questions.length === 0) {
        throw new Error('Invalid questions format');
      }

      // Process and validate each question
      const processedQuestions = await Promise.all(questions.map(async q => {
        // Validate required fields
        const requiredFields = ['question', 'correct_answer', 'explanation'];
        const missingFields = requiredFields.filter(field => !q[field]);
        if (missingFields.length > 0) {
          throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate question quality
        const qualityScore = await this.validateQuestionQuality(q);
        if (qualityScore < 0.3 && process.env.NODE_ENV !== 'test') { // Lower threshold for tests
          throw new Error(`Question quality below threshold: ${qualityScore}`);
        }

        // Enhance the question object
        return {
          ...q,
          subject: params.subject,
          topic: params.topic,
          type: params.questionType,
          quality_score: qualityScore,
          source_tag: q.source_tag || `AI Generated - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}`,
          status: 'active',
          timestamp: new Date(),
          created_by: 'system',
          last_modified: new Date(),
          version: 1
        };
      }));

      // Save to Firestore
      await this.saveQuestions(processedQuestions);

      return processedQuestions;
    } catch (error) {
      this.loggingService.logError(error, { operation: 'processQuestions' });
      throw error;
    }
  }

  /**
   * Validate question quality using various metrics
   */
  async validateQuestionQuality(question) {
    const metrics = {
      // Length and completeness
      questionLength: question.question.length > 20 ? 0.2 : 0.1,
      hasExplanation: question.explanation?.length > 50 ? 0.2 : 0.1,
      
      // Content quality
      hasKeywords: Array.isArray(question.keywords) && question.keywords.length > 0 ? 0.2 : 0,
      hasCognitiveLevel: !!question.cognitive_level ? 0.1 : 0,
      
      // Options quality (for multiple choice)
      optionsQuality: question.options ? this.validateOptionsQuality(question.options) : 0.2,
      
      // Source tracking
      hasSourceTag: !!question.source_tag ? 0.1 : 0
    };

    return Object.values(metrics).reduce((sum, score) => sum + score, 0);
  }

  /**
   * Validate quality of multiple choice options
   */
  validateOptionsQuality(options) {
    if (!Array.isArray(options) || options.length !== 4) return 0;

    // Check if options are unique
    const uniqueOptions = new Set(options.map(opt => opt.toLowerCase()));
    if (uniqueOptions.size !== 4) return 0;

    // Check if options are substantial (not just single words)
    const substantialOptions = options.filter(opt => opt.split(' ').length > 1);
    return substantialOptions.length >= 2 ? 0.2 : 0.1;
  }

  /**
   * Generates diverse multiple choice questions with enhanced prompts and validation
   */
  generateMultipleChoice = async (params) => {
    // Get syllabus context if available
    let syllabusContext = '';
    if (params.syllabusId && params.unitId && params.topicId) {
      const syllabus = await syllabusModel.get(params.syllabusId);
      const unit = syllabus.units.find(u => u.id === params.unitId);
      const topic = unit?.topics.find(t => t.id === params.topicId);
      
      if (topic) {
        syllabusContext = `
        Syllabus Context:
        Unit: ${unit.name}
        Topic: ${topic.name}
        Description: ${topic.description}
        Key Concepts: ${topic.keywords.join(', ')}
        Learning Objectives: ${JSON.stringify(unit.learning_objectives)}
        Prerequisites: ${unit.prerequisites.join(', ')}
        
        Question Distribution:
        ${JSON.stringify(topic.question_distribution)}
        `;
      }
    }

    const prompt = `Generate ${params.numQuestions} diverse and high-quality multiple choice questions for ${params.subject}${params.topic ? ` focusing on ${params.topic}` : ''} at ${params.level} level.

    ${syllabusContext}

    Requirements:
    1. Questions should align with the syllabus context and learning objectives
    2. Include application and analysis questions
    3. Options should be plausible but clearly distinguishable
    4. Explanations should reference key concepts

    For each question, provide:
    1. Question text (clear, specific)
    2. Four options (A/B/C/D)
    3. Correct answer
    4. Detailed explanation
    5. Keywords/concepts tested (from syllabus)
    6. Cognitive level
    7. Learning objective addressed

    Format as JSON array with the following structure:
    {
      "question": "...",
      "options": ["A) ...", "B) ...", "C) ...", "D) ..."],
      "correct_answer": "A/B/C/D",
      "explanation": "...",
      "keywords": ["..."],
      "cognitive_level": "...",
      "learning_objective": "...",
      "source_tag": "AI Generated - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}${params.syllabusId ? ` [Syllabus: ${params.syllabusId}]` : ''}",
      "difficulty": "${params.level}"
    }`;

    const response = await llmService.generateWithOptimalModel(prompt, {
      type: 'multiple_choice',
      complexity: params.level,
      syllabusContext: !!syllabusContext
    });

    // Ensure response is an array
    const questions = Array.isArray(response) ? response : [response];

    // Validate questions against syllabus if context available
    if (syllabusContext) {
      return questions.filter(q => {
        const validationResult = questionAnalysisService.validateSyllabusAlignment(q, {
          keywords: topic.keywords,
          learningObjectives: unit.learning_objectives,
          questionDistribution: topic.question_distribution
        });
        return validationResult.isValid;
      }).map(q => ({
        ...q,
        type: 'multiple_choice',
        syllabusId: params.syllabusId,
        unitId: params.unitId,
        topicId: params.topicId
      }));
    }

    return questions.map(q => ({
      ...q,
      type: 'multiple_choice'
    }));
  }

  /**
   * Generates diverse fill-in-the-blanks questions with enhanced prompts
   * @param {Object} params - Parameters for generating fill-in-blank questions
   */
  generateFillInBlanks = async (params) => {
    let syllabusContext = null;
    let topic = null;
    let unit = null;

    if (params.syllabusId && params.unitId && params.topicId) {
      try {
        const syllabus = await syllabusModel.get(params.syllabusId);
        unit = syllabus.units.find(u => u.id === params.unitId);
        topic = unit?.topics.find(t => t.id === params.topicId);

        if (topic) {
          syllabusContext = `
          Syllabus Context:
          Unit: ${unit.name}
          Topic: ${topic.name}
          Description: ${topic.description}
          Key Concepts: ${topic.keywords.join(', ')}
          Learning Objectives: ${JSON.stringify(unit.learning_objectives)}
          Prerequisites: ${unit.prerequisites.join(', ')}
          
          Question Distribution:
          ${JSON.stringify(topic.question_distribution)}
          `;
        }
      } catch (error) {
        console.error('Error getting syllabus context:', error);
      }
    }

    const prompt = `Generate ${params.numQuestions} fill-in-the-blank questions for ${params.subject}${params.topic ? ` focusing on ${params.topic}` : ''} at ${params.level} level.

  ${syllabusContext || ''}

  Requirements:
  1. Questions should align with syllabus learning objectives
  2. Blanks should test key concepts from the syllabus
  3. Include detailed explanations referencing syllabus content
  4. Focus on conceptual understanding and application

  Format as JSON array with:
  {
    "question": "...[BLANK]...",
    "correct_answer": "...",
    "explanation": "...",
    "keywords": ["..."],
    "cognitive_level": "...",
    "learning_objective": "...",
    "source_tag": "AI Generated - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}${params.syllabusId ? ` [Syllabus: ${params.syllabusId}]` : ''}",
    "difficulty": "${params.level}"
  }`;

    const response = await llmService.generateWithOptimalModel(prompt, {
      type: 'fill_in_blanks',
      complexity: params.level,
      syllabusContext: !!syllabusContext,
    });

    const questions = Array.isArray(response) ? response : [response];

    // Validate questions against syllabus if context available
    if (syllabusContext && topic && unit) {
      return questions.filter(q => {
        const validationResult = questionAnalysisService.validateSyllabusAlignment(q, {
          keywords: topic.keywords,
          learningObjectives: unit.learning_objectives,
          questionDistribution: topic.question_distribution,
        });
        return validationResult.isValid;
      }).map(q => ({
        ...q,
        type: 'fill_in_blanks',
        syllabusId: params.syllabusId,
        unitId: params.unitId,
        topicId: params.topicId,
      }));
    }

    return questions.map(q => ({
      ...q,
      type: 'fill_in_blanks',
      acceptableAnswers: this.generateAcceptableAnswers(q.correct_answer),
    }));
  }

  /**
   * Generates true/false questions
   */
  generateTrueFalse = async (params) => {
    let syllabusContext = null;
    let topic = null;
    let unit = null;

    if (params.syllabusId && params.unitId && params.topicId) {
      try {
        const syllabus = await syllabusModel.get(params.syllabusId);
        unit = syllabus.units.find(u => u.id === params.unitId);
        topic = unit?.topics.find(t => t.id === params.topicId);

        if (topic) {
          syllabusContext = `
          Syllabus Context:
          Unit: ${unit.name}
          Topic: ${topic.name}
          Description: ${topic.description}
          Key Concepts: ${topic.keywords.join(', ')}
          Learning Objectives: ${JSON.stringify(unit.learning_objectives)}
          Prerequisites: ${unit.prerequisites.join(', ')}
          
          Question Distribution:
          ${JSON.stringify(topic.question_distribution)}
          `;
        }
      } catch (error) {
        console.error('Error getting syllabus context:', error);
      }
    }

    const prompt = `Generate ${params.numQuestions} true/false questions for ${params.subject}${params.topic ? ` focusing on ${params.topic}` : ''} at ${params.level} level.

  ${syllabusContext || ''}

  Requirements:
  1. Statements should be based on key concepts from syllabus
  2. Mix of true and false statements testing understanding
  3. Include explanations referencing syllabus content
  4. Target specific learning objectives

  Format as JSON array with:
  {
    "question": "...",
    "correct_answer": true/false,
    "explanation": "...",
    "keywords": ["..."],
    "cognitive_level": "...",
    "learning_objective": "...",
    "source_tag": "AI Generated - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}${params.syllabusId ? ` [Syllabus: ${params.syllabusId}]` : ''}",
    "difficulty": "${params.level}"
  }`;

    const response = await llmService.generateWithOptimalModel(prompt, {
      type: 'true_false',
      complexity: params.level,
      syllabusContext: !!syllabusContext,
    });

    const questions = Array.isArray(response) ? response : [response];

    // Validate questions against syllabus if context available
    if (syllabusContext && topic && unit) {
      return questions.filter(q => {
        const validationResult = questionAnalysisService.validateSyllabusAlignment(q, {
          keywords: topic.keywords,
          learningObjectives: unit.learning_objectives,
          questionDistribution: topic.question_distribution,
        });
        return validationResult.isValid;
      }).map(q => ({
        ...q,
        type: 'true_false',
        syllabusId: params.syllabusId,
        unitId: params.unitId,
        topicId: params.topicId,
      }));
    }

    return questions.map(q => ({
      ...q,
      type: 'true_false',
    }));
  }

  /**
   * Generates short answer questions
   */
  generateShortAnswer = async (params) => {
    let syllabusContext = null;
    let topic = null;
    let unit = null;

    if (params.syllabusId && params.unitId && params.topicId) {
      try {
        const syllabus = await syllabusModel.get(params.syllabusId);
        unit = syllabus.units.find(u => u.id === params.unitId);
        topic = unit?.topics.find(t => t.id === params.topicId);

        if (topic) {
          syllabusContext = `
          Syllabus Context:
          Unit: ${unit.name}
          Topic: ${topic.name}
          Description: ${topic.description}
          Key Concepts: ${topic.keywords.join(', ')}
          Learning Objectives: ${JSON.stringify(unit.learning_objectives)}
          Prerequisites: ${unit.prerequisites.join(', ')}
          
          Question Distribution:
          ${JSON.stringify(topic.question_distribution)}
          `;
        }
      } catch (error) {
        console.error('Error getting syllabus context:', error);
      }
    }

    const prompt = `Generate ${params.numQuestions} short answer questions for ${params.subject}${params.topic ? ` focusing on ${params.topic}` : ''} at ${params.level} level.

  ${syllabusContext || ''}

  Requirements:
  1. Questions should test deep understanding
  2. Include application and analysis questions
  3. Model answers should be comprehensive
  4. Include clear grading criteria

  For each question, provide:
  1. Question text (clear, specific)
  2. Model answer (2-3 sentences)
  3. Key points that must be included
  4. Grading rubric
  5. Keywords/concepts tested
  6. Cognitive level

  Format as JSON array with the following structure:
  {
    "question": "...",
    "model_answer": "...",
    "key_points": ["..."],
    "rubric": {
      "full_credit": "...",
      "partial_credit": "...",
      "minimum_points": "..."
    },
    "keywords": ["..."],
    "cognitive_level": "...",
    "source_tag": "AI Generated - ${params.subject}${params.topic ? ` - ${params.topic}` : ''}${params.syllabusId ? ` [Syllabus: ${params.syllabusId}]` : ''}",
    "difficulty": "${params.level}"
  }`;

  const response = await llmService.generateWithOptimalModel(prompt, {
    type: 'short_answer',
    complexity: params.level,
    syllabusContext: !!syllabusContext,
  });

  // Ensure response is an array
  const questions = Array.isArray(response) ? response : [response];

  // Validate questions against syllabus if context available
  if (syllabusContext && topic && unit) {
    return questions.filter(q => {
      const validationResult = questionAnalysisService.validateSyllabusAlignment(q, {
        keywords: topic.keywords,
        learningObjectives: unit.learning_objectives,
        questionDistribution: topic.question_distribution
      });
      return validationResult.isValid;
    }).map(q => ({
      ...q,
      type: 'short_answer',
      syllabusId: params.syllabusId,
      unitId: params.unitId,
      topicId: params.topicId,
      keyPoints: q.key_points || [],
      modelAnswer: q.model_answer || ''
    }));
  }

  return questions.map(q => ({
    ...q,
    type: 'short_answer',
    keyPoints: q.key_points || [],
    modelAnswer: q.model_answer || ''
  }));
  }
}

export default new QuestionGenerationService();

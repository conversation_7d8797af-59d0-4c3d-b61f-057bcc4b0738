import loggingService from './loggingService.js';
import { db } from '../config/firebase.js';

class MonitoringService {
    constructor() {
        this.metricsCache = new Map();
        this.performanceCache = new Map();
        this.alertsCache = [];
        this.activityCache = [];
        this.loggingService = loggingService;
    }

    /**
     * Get system metrics
     * @returns {Object} System metrics
     */
    async getSystemMetrics() {
        try {
            // Get total questions
            const questionsSnapshot = await db.collection('questions').count().get();
            const totalQuestions = questionsSnapshot.data().count;

            // Get active users (last 24 hours)
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const usersSnapshot = await db.collection('users')
                .where('lastActive', '>=', yesterday)
                .count()
                .get();
            const activeUsers = usersSnapshot.data().count;

            // Get tests taken (last 24 hours)
            const testsSnapshot = await db.collection('testSessions')
                .where('startTime', '>=', yesterday)
                .count()
                .get();
            const testsTaken = testsSnapshot.data().count;

            // Calculate trends (comparing to previous 24 hours)
            const twoDaysAgo = new Date();
            twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
            
            const previousTestsSnapshot = await db.collection('testSessions')
                .where('startTime', '>=', twoDaysAgo)
                .where('startTime', '<', yesterday)
                .count()
                .get();
            const previousTests = previousTestsSnapshot.data().count;

            const testsTrend = previousTests ? 
                ((testsTaken - previousTests) / previousTests) * 100 : 0;

            // Get system health metrics (mock data for now)
            const metrics = {
                totalQuestions,
                activeUsers,
                testsTaken,
                questionsTrend: 5, // Mock data
                usersTrend: 10, // Mock data
                testsTrend: Math.round(testsTrend),
                cpu: 45, // Mock data
                memory: 60, // Mock data
                storage: 35, // Mock data
                apiLatency: 150 // Mock data
            };

            this.metricsCache.set('latest', metrics);
            return metrics;
        } catch (error) {
            this.loggingService.logError('Failed to get system metrics', { error });
            throw error;
        }
    }

    /**
     * Get performance data
     * @param {string} timeRange Time range (1h, 24h, 7d, 30d)
     * @returns {Object} Performance data
     */
    async getPerformanceData(timeRange) {
        try {
            const cacheKey = `performance_${timeRange}`;
            if (this.performanceCache.has(cacheKey)) {
                return this.performanceCache.get(cacheKey);
            }

            // Calculate start time based on range
            const startTime = new Date();
            switch (timeRange) {
                case '1h':
                    startTime.setHours(startTime.getHours() - 1);
                    break;
                case '24h':
                    startTime.setDate(startTime.getDate() - 1);
                    break;
                case '7d':
                    startTime.setDate(startTime.getDate() - 7);
                    break;
                case '30d':
                    startTime.setDate(startTime.getDate() - 30);
                    break;
                default:
                    startTime.setDate(startTime.getDate() - 7);
            }

            // Get performance logs
            const logsSnapshot = await db.collection('performanceLogs')
                .where('timestamp', '>=', startTime)
                .orderBy('timestamp')
                .get();

            const data = {
                timestamps: [],
                responseTime: [],
                errorRate: [],
                successRate: []
            };

            logsSnapshot.forEach(doc => {
                const log = doc.data();
                data.timestamps.push(log.timestamp.toDate());
                data.responseTime.push(log.responseTime);
                data.errorRate.push(log.errorRate);
                data.successRate.push(log.successRate);
            });

            this.performanceCache.set(cacheKey, data);
            setTimeout(() => this.performanceCache.delete(cacheKey), 60000); // Cache for 1 minute

            return data;
        } catch (error) {
            this.loggingService.logError('Failed to get performance data', { error });
            throw error;
        }
    }

    /**
     * Get question quality metrics
     * @returns {Object} Quality metrics
     */
    async getQualityMetrics() {
        try {
            if (this.metricsCache.has('quality')) {
                return this.metricsCache.get('quality');
            }

            // Get recent questions for analysis
            const questionsSnapshot = await db.collection('questions')
                .orderBy('createdAt', 'desc')
                .limit(100)
                .get();

            let clarity = 0;
            let difficultyMatch = 0;
            let domainAccuracy = 0;
            let grammar = 0;
            let uniqueness = 0;
            let count = 0;

            questionsSnapshot.forEach(doc => {
                const question = doc.data();
                if (question.quality) {
                    clarity += question.quality.clarity || 0;
                    difficultyMatch += question.quality.difficultyMatch || 0;
                    domainAccuracy += question.quality.domainAccuracy || 0;
                    grammar += question.quality.grammar || 0;
                    uniqueness += question.quality.uniqueness || 0;
                    count++;
                }
            });

            const metrics = {
                clarity: count ? Math.round(clarity / count) : 0,
                difficultyMatch: count ? Math.round(difficultyMatch / count) : 0,
                domainAccuracy: count ? Math.round(domainAccuracy / count) : 0,
                grammar: count ? Math.round(grammar / count) : 0,
                uniqueness: count ? Math.round(uniqueness / count) : 0
            };

            this.metricsCache.set('quality', metrics);
            setTimeout(() => this.metricsCache.delete('quality'), 300000); // Cache for 5 minutes

            return metrics;
        } catch (error) {
            this.loggingService.logError('Failed to get quality metrics', { error });
            throw error;
        }
    }

    /**
     * Get syllabus coverage data
     * @returns {Array} Coverage data
     */
    async getCoverageData() {
        try {
            if (this.metricsCache.has('coverage')) {
                return this.metricsCache.get('coverage');
            }

            const syllabiSnapshot = await db.collection('syllabi').get();
            const coverage = [];

            for (const doc of syllabiSnapshot.docs) {
                const syllabus = doc.data();
                const totalTopics = this.countTopics(syllabus);
                
                // Get questions for this syllabus
                const questionsSnapshot = await db.collection('questions')
                    .where('syllabusId', '==', doc.id)
                    .get();

                const coveredTopics = new Set();
                questionsSnapshot.forEach(qDoc => {
                    const question = qDoc.data();
                    if (question.topicId) {
                        coveredTopics.add(question.topicId);
                    }
                });

                coverage.push({
                    id: doc.id,
                    name: syllabus.name,
                    coverage: Math.round((coveredTopics.size / totalTopics) * 100)
                });
            }

            this.metricsCache.set('coverage', coverage);
            setTimeout(() => this.metricsCache.delete('coverage'), 300000); // Cache for 5 minutes

            return coverage;
        } catch (error) {
            this.loggingService.logError('Failed to get coverage data', { error });
            throw error;
        }
    }

    /**
     * Get recent alerts
     * @returns {Array} Recent alerts
     */
    async getRecentAlerts() {
        try {
            const alertsSnapshot = await db.collection('alerts')
                .orderBy('timestamp', 'desc')
                .limit(10)
                .get();

            const alerts = [];
            alertsSnapshot.forEach(doc => {
                const alert = doc.data();
                alerts.push({
                    id: doc.id,
                    message: alert.message,
                    level: alert.level,
                    timestamp: alert.timestamp.toDate()
                });
            });

            return alerts;
        } catch (error) {
            this.loggingService.logError('Failed to get recent alerts', { error });
            throw error;
        }
    }

    /**
     * Get recent activity
     * @returns {Array} Recent activity
     */
    async getRecentActivity() {
        try {
            const activitySnapshot = await db.collection('activity')
                .orderBy('timestamp', 'desc')
                .limit(20)
                .get();

            const activity = [];
            activitySnapshot.forEach(doc => {
                const entry = doc.data();
                activity.push({
                    id: doc.id,
                    timestamp: entry.timestamp.toDate(),
                    event: entry.event,
                    user: entry.user,
                    details: entry.details,
                    status: entry.status
                });
            });

            return activity;
        } catch (error) {
            this.loggingService.logError('Failed to get recent activity', { error });
            throw error;
        }
    }

    /**
     * Count total topics in a syllabus
     * @param {Object} syllabus Syllabus object
     * @returns {number} Total number of topics
     */
    countTopics(syllabus) {
        let count = 0;
        syllabus.units?.forEach(unit => {
            count += unit.topics?.length || 0;
        });
        return count;
    }
}

const monitoringService = new MonitoringService();
export default monitoringService;

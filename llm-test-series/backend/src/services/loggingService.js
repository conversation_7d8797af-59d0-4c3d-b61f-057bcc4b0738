import winston from 'winston';
import { getDb } from '../firebase.js';

class LoggingService {
  constructor() {
    this.db = null;
    this.dbInitialized = false;
    this.initializeDatabase(); // Don't await in constructor
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.json(),
      transports: [
        new winston.transports.File({ filename: 'error.log', level: 'error' }),
        new winston.transports.File({ filename: 'combined.log' })
      ]
    });

    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: winston.format.simple()
      }));
    }
  }

  async initializeDatabase() {
    try {
      this.db = await getDb();
      this.dbInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database for logging service:', error);
      this.db = null;
      this.dbInitialized = false;
    }
  }

  async ensureDbInitialized() {
    if (!this.dbInitialized) {
      await this.initializeDatabase();
    }
  }

  async logApiCall(data) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('API Call:', {
          method: data.method,
          path: data.path,
          status: data.status,
          duration: data.duration,
          userId: data.userId || 'anonymous'
        });
        return;
      }

      if (this.db && this.db.collection) {
        await this.db.collection('apiLogs').add({
          ...data,
          userId: data.userId || 'anonymous',
          timestamp: data.timestamp || new Date()
        });
      }

      this.logger.info('API Call', {
        method: data.method,
        path: data.path,
        status: data.status,
        duration: data.duration,
        userId: data.userId || 'anonymous'
      });
    } catch (error) {
      console.error('Error logging API call:', error);
    }
  }

  async logLlmCall(type, prompt, response, duration, error = null) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('LLM Call:', {
          type,
          duration,
          error: error?.message
        });
        return;
      }

      if (this.db && this.db.collection) {
        await this.db.collection('llmLogs').add({
          type,
          prompt,
          response,
          duration,
          error: error ? error.message : null,
          timestamp: new Date()
        });
      }

      if (error) {
        this.logger.error('LLM Call Failed', {
          type,
          error: error.message,
          duration
        });
      } else {
        this.logger.info('LLM Call Success', {
          type,
          duration
        });
      }
    } catch (error) {
      console.error('Error logging LLM call:', error);
    }
  }

  async logError(error, context = {}) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.error('Application Error:', {
          message: error.message,
          context
        });
        return;
      }

      if (this.db && this.db.collection) {
        await this.db.collection('errorLogs').add({
          error: error.message,
          stack: error.stack,
          context,
          timestamp: new Date()
        });
      }

      this.logger.error('Application Error', {
        message: error.message,
        context
      });
    } catch (logError) {
      console.error('Error logging error:', logError);
    }
  }

  async logWarning(message, context = {}) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Warning:', message, context);
        return;
      }

      if (this.db && this.db.collection) {
        await this.db.collection('warningLogs').add({
          message,
          context,
          timestamp: new Date()
        });
      }

      this.logger.warn('Warning', {
        message,
        context
      });
    } catch (logError) {
      console.error('Error logging warning:', logError);
    }
  }

  async logInfo(message, context = {}) {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('Info:', message, context);
        return;
      }

      if (this.db && this.db.collection) {
        await this.db.collection('infoLogs').add({
          message,
          context,
          timestamp: new Date()
        });
      }

      this.logger.info(message, context);
    } catch (error) {
      console.error('Error logging info:', error);
    }
  }

  async getApiStats(startDate, endDate) {
    try {
      const snapshot = await this.db.collection('api_logs')
        .where('timestamp', '>=', startDate)
        .where('timestamp', '<=', endDate)
        .get();

      const stats = {
        totalCalls: 0,
        averageDuration: 0,
        errorRate: 0,
        methodCounts: {},
        pathCounts: {},
        errors: []
      };

      let totalDuration = 0;
      let errorCount = 0;

      snapshot.forEach(doc => {
        const log = doc.data();
        stats.totalCalls++;
        totalDuration += log.duration;

        // Count methods
        stats.methodCounts[log.method] = (stats.methodCounts[log.method] || 0) + 1;

        // Count paths
        stats.pathCounts[log.path] = (stats.pathCounts[log.path] || 0) + 1;

        // Track errors
        if (log.error) {
          errorCount++;
          stats.errors.push({
            timestamp: log.timestamp,
            method: log.method,
            path: log.path,
            error: log.error
          });
        }
      });

      stats.averageDuration = totalDuration / stats.totalCalls;
      stats.errorRate = (errorCount / stats.totalCalls) * 100;

      return stats;

    } catch (error) {
      this.logger.error('Error fetching API stats:', error);
      throw error;
    }
  }

  /**
   * Get LLM usage statistics
   */
  async getLlmStats(startDate, endDate) {
    try {
      const snapshot = await this.db.collection('llm_logs')
        .where('timestamp', '>=', startDate)
        .where('timestamp', '<=', endDate)
        .get();

      const stats = {
        totalCalls: 0,
        totalCost: 0,
        averageDuration: 0,
        errorRate: 0,
        typeBreakdown: {},
        errors: []
      };

      let totalDuration = 0;
      let errorCount = 0;

      snapshot.forEach(doc => {
        const log = doc.data();
        stats.totalCalls++;
        totalDuration += log.duration;
        stats.totalCost += log.cost || 0;

        // Count types
        stats.typeBreakdown[log.type] = (stats.typeBreakdown[log.type] || 0) + 1;

        // Track errors
        if (log.error) {
          errorCount++;
          stats.errors.push({
            timestamp: log.timestamp,
            type: log.type,
            error: log.error
          });
        }
      });

      stats.averageDuration = totalDuration / stats.totalCalls;
      stats.errorRate = (errorCount / stats.totalCalls) * 100;

      return stats;

    } catch (error) {
      this.logger.error('Error fetching LLM stats:', error);
      throw error;
    }
  }

  /**
   * Calculate approximate LLM cost
   */
  calculateLlmCost(prompt, response) {
    // Approximate cost calculation (adjust based on your LLM provider's pricing)
    const promptTokens = this.estimateTokens(prompt);
    const responseTokens = this.estimateTokens(response);
    
    // Example pricing: $0.002 per 1K tokens
    return ((promptTokens + responseTokens) / 1000) * 0.002;
  }

  /**
   * Estimate number of tokens in text
   */
  estimateTokens(text) {
    // Rough estimation: ~4 characters per token
    return Math.ceil((text || '').length / 4);
  }

  /**
   * Sanitize request body for logging
   */
  sanitizeBody(body) {
    if (!body) return null;
    
    // Deep clone body
    const sanitized = JSON.parse(JSON.stringify(body));
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'apiKey', 'secret'];
    this.recursivelyRemoveSensitiveFields(sanitized, sensitiveFields);
    
    return sanitized;
  }

  /**
   * Recursively remove sensitive fields from object
   */
  recursivelyRemoveSensitiveFields(obj, sensitiveFields) {
    if (typeof obj !== 'object') return;
    
    Object.keys(obj).forEach(key => {
      if (sensitiveFields.includes(key.toLowerCase())) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        this.recursivelyRemoveSensitiveFields(obj[key], sensitiveFields);
      }
    });
  }

  /**
   * Truncate text to specified length
   */
  truncateText(text, maxLength) {
    if (!text) return null;
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }
}

export default new LoggingService();

import { getDb } from '../config/firebase.js';
import loggingService from './loggingService.js';
import llmService from './llmService.js';

class ChallengeService {
    constructor() {
        this.db = getDb();
    }

    /**
     * Submit a challenge for a question's answer or explanation
     */
    async submitChallenge(params) {
        const {
            questionId,
            userId,
            challengeType,
            challengeText,
            userAnswer,
            originalAnswer,
            originalExplanation
        } = params;

        try {
            // Create challenge record
            const challengeId = `challenge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const challenge = {
                challenge_id: challengeId,
                question_id: questionId,
                user_id: userId,
                challenge_type: challengeType,
                challenge_text: challengeText,
                user_answer: userAnswer,
                original_answer: originalAnswer,
                original_explanation: originalExplanation,
                status: 'pending',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // Save challenge to Firestore
            await this.db.collection('challenges').doc(challengeId).set(challenge);

            // Evaluate challenge using LLM
            const evaluation = await this.evaluateChallenge(challenge);

            // Update challenge with evaluation
            await this.db.collection('challenges').doc(challengeId).update({
                evaluation: evaluation,
                status: 'evaluated',
                updated_at: new Date().toISOString()
            });

            // If evaluation suggests the challenge is valid, flag question for review
            if (evaluation.validity_score >= 0.7) {
                await this.db.collection('questions').doc(questionId).update({
                    status: 'needs_review',
                    challenge_count: this.db.FieldValue.increment(1),
                    updated_at: new Date().toISOString()
                });
            }

            return {
                ...challenge,
                evaluation
            };
        } catch (error) {
            loggingService.logError('Failed to submit challenge', { error });
            throw error;
        }
    }

    /**
     * Evaluate a challenge using LLM
     */
    async evaluateChallenge(challenge) {
        const prompt = `Evaluate this challenge to a question's answer/explanation:

Question: ${challenge.question_text}
Original Answer: ${challenge.original_answer}
Original Explanation: ${challenge.original_explanation}
User's Answer: ${challenge.user_answer}
User's Challenge: ${challenge.challenge_text}

Evaluate:
1. Is the challenge valid? Consider:
   - Logical reasoning
   - Supporting evidence
   - Technical accuracy
2. Does it identify actual issues with the original answer/explanation?
3. Could the original answer/explanation be improved based on this feedback?

Provide:
1. Validity score (0-1)
2. Detailed analysis
3. Recommendation for action`;

        const response = await llmService.generateContent(prompt);
        return this.parseEvaluation(response);
    }

    /**
     * Parse LLM evaluation response
     */
    parseEvaluation(response) {
        return {
            validity_score: response.validity_score,
            analysis: response.analysis,
            recommendation: response.recommendation,
            needs_review: response.validity_score >= 0.7
        };
    }

    /**
     * Get challenges for a question
     */
    async getChallenges(questionId) {
        try {
            const snapshot = await this.db.collection('challenges')
                .where('question_id', '==', questionId)
                .orderBy('created_at', 'desc')
                .get();

            return snapshot.docs.map(doc => doc.data());
        } catch (error) {
            loggingService.logError('Failed to get challenges', { error });
            throw error;
        }
    }

    /**
     * Get challenges submitted by a user
     */
    async getUserChallenges(userId) {
        try {
            const snapshot = await this.db.collection('challenges')
                .where('user_id', '==', userId)
                .orderBy('created_at', 'desc')
                .get();

            return snapshot.docs.map(doc => doc.data());
        } catch (error) {
            loggingService.logError('Failed to get user challenges', { error });
            throw error;
        }
    }

    /**
     * Update challenge status
     */
    async updateChallengeStatus(challengeId, status, adminFeedback = '') {
        try {
            await this.db.collection('challenges').doc(challengeId).update({
                status,
                admin_feedback: adminFeedback,
                updated_at: new Date().toISOString()
            });
        } catch (error) {
            loggingService.logError('Failed to update challenge status', { error });
            throw error;
        }
    }
}

const challengeService = new ChallengeService();
export default challengeService;

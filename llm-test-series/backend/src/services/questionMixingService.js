import questionGenerationService from './questionGenerationService.js';
import pastYearQuestionService from './pastYearQuestionService.js';
import loggingService from './loggingService.js';
import { getDb } from '../firebase.js';

class QuestionMixingService {
    constructor() {
        this.mixingRules = {
            // Default mixing ratios for different test types
            mock_test: {
                past_year: 0.7,  // 70% past year questions
                generated: 0.3   // 30% AI generated questions
            },
            practice: {
                past_year: 0.4,  // 40% past year questions
                generated: 0.6   // 60% AI generated questions
            },
            quick_test: {
                past_year: 0.5,  // 50% past year questions
                generated: 0.5   // 50% AI generated questions
            }
        };
    }

    /**
     * Get mixed questions for a test
     * @param {Object} params Parameters for question generation
     * @returns {Promise<Array>} Mixed questions
     */
    async getMixedQuestions(params) {
        const {
            test_type = 'practice',
            numQuestions = 10,
            topics = [],
            difficulty,
            previousQuestions = []
        } = params;

        try {
            // Get mixing ratios for test type
            const ratios = this.mixingRules[test_type] || this.mixingRules.practice;
            
            // Calculate number of questions for each source
            const pastYearCount = Math.round(numQuestions * ratios.past_year);
            const generatedCount = numQuestions - pastYearCount;

            // Get past year questions first
            let pastYearQuestions = [];
            if (pastYearCount > 0) {
                for (const topic of topics) {
                    const topicQuestions = await pastYearQuestionService.getQuestionsByTopic(topic, {
                        preferredDifficulty: difficulty,
                        excludeIds: previousQuestions
                    });
                    pastYearQuestions = pastYearQuestions.concat(topicQuestions);
                }

                // Shuffle and take required number
                pastYearQuestions = this.shuffleArray(pastYearQuestions)
                    .slice(0, pastYearCount);

                // Update usage stats
                for (const q of pastYearQuestions) {
                    await pastYearQuestionService.updateUsageStats(q.id);
                }
            }

            // Generate remaining questions
            let generatedQuestions = [];
            if (generatedCount > 0) {
                // Exclude topics that have sufficient past year questions
                const coveredTopics = new Set(
                    pastYearQuestions.flatMap(q => q.topics || [])
                );
                const remainingTopics = topics.filter(t => !coveredTopics.has(t));

                if (remainingTopics.length > 0) {
                    generatedQuestions = await questionGenerationService.generateQuestions({
                        ...params,
                        numQuestions: generatedCount,
                        topics: remainingTopics,
                        previousQuestions: [
                            ...previousQuestions,
                            ...pastYearQuestions.map(q => q.id)
                        ]
                    });
                }
            }

            // Combine and shuffle questions
            const allQuestions = this.shuffleArray([
                ...pastYearQuestions,
                ...generatedQuestions
            ]);

            // Log mixing statistics
            await this.logMixingStats({
                test_type,
                total: allQuestions.length,
                past_year: pastYearQuestions.length,
                generated: generatedQuestions.length,
                topics
            });

            return allQuestions;
        } catch (error) {
            loggingService.logError('Failed to mix questions', { error });
            throw error;
        }
    }

    /**
     * Update mixing rules based on performance data
     * @param {Object} stats Performance statistics
     */
    async updateMixingRules(stats) {
        try {
            const {
                test_type,
                past_year_correct_rate,
                generated_correct_rate,
                total_questions
            } = stats;

            if (total_questions < 50) {
                // Not enough data to adjust rules
                return;
            }

            // Calculate new ratios based on performance
            const currentRules = this.mixingRules[test_type];
            if (!currentRules) return;

            // Adjust ratios based on correct rates
            // If past year questions have higher correct rate, slightly decrease their ratio
            // to maintain challenge level
            const performance_diff = past_year_correct_rate - generated_correct_rate;
            const adjustment = Math.min(Math.abs(performance_diff) * 0.1, 0.05);

            let new_past_year_ratio = currentRules.past_year;
            if (performance_diff > 0) {
                // Past year questions are easier, decrease their ratio
                new_past_year_ratio = Math.max(0.3, currentRules.past_year - adjustment);
            } else {
                // Past year questions are harder, increase their ratio
                new_past_year_ratio = Math.min(0.7, currentRules.past_year + adjustment);
            }

            // Update rules
            this.mixingRules[test_type] = {
                past_year: new_past_year_ratio,
                generated: 1 - new_past_year_ratio
            };

            // Log the adjustment
            loggingService.logInfo('Updated mixing rules', {
                test_type,
                old_rules: currentRules,
                new_rules: this.mixingRules[test_type],
                performance_stats: stats
            });
        } catch (error) {
            loggingService.logError('Failed to update mixing rules', { error });
        }
    }

    /**
     * Log mixing statistics
     * @param {Object} stats Mixing statistics
     */
    async logMixingStats(stats) {
        try {
            const db = await getDb();
            await db.collection('question_mixing_stats').add({
                ...stats,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            loggingService.logError('Failed to log mixing stats', { error });
        }
    }

    /**
     * Shuffle array using Fisher-Yates algorithm
     * @param {Array} array Array to shuffle
     * @returns {Array} Shuffled array
     */
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
}

const questionMixingService = new QuestionMixingService();
export default questionMixingService;

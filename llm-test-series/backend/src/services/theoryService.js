import llmService from './llmService.js';
import loggingService from './loggingService.js';
import { getDb } from '../firebase.js';

class TheoryService {
    constructor() {
        this.db = null;
        this.cache = new Map();
        this.cacheDuration = 24 * 60 * 60 * 1000; // 24 hours
        this.explanationCache = new Map();
        this.conceptsCache = new Map();
    }

    async getDbInstance() {
        if (!this.db) {
            this.db = await getDb();
        }
        return this.db;
    }

    async getTheoryExplanation(params) {
        const { topic, question, syllabus } = params;
        
        try {
            // Check cache first
            const cacheKey = this.generateCacheKey(params);
            const cachedExplanation = this.getFromCache(cacheKey);
            if (cachedExplanation) {
                return cachedExplanation;
            }

            // Generate explanation using LLM
            const prompt = `
                Explain the theoretical concepts related to the following ${question ? 'question' : 'topic'}:
                
                ${question || topic}
                
                This explanation is for a student preparing for the ${syllabus} exam.
                
                Please provide:
                1. Core principles and definitions
                2. Relevant formulas (if applicable)
                3. Practical implications
                4. Examples to illustrate concepts
                5. Common misconceptions to avoid
                6. Related topics for further study
                
                Make the explanation clear, concise, and comprehensive.
            `;

            const explanation = await llmService.generateWithOptimalModel(prompt, {
                type: 'theory_explanation',
                complexity: 'medium'
            });

            // Store in cache and database
            this.saveToCache(cacheKey, explanation);
            await this.saveToDatabase(params, explanation);

            return explanation;
        } catch (error) {
            loggingService.logError('Failed to get theory explanation', { error });
            throw error;
        }
    }

    async getOnDemandExplanation(params) {
        const { questionId, topic, subtopic, difficulty } = params;

        try {
            // Check cache first
            const cacheKey = `explanation_${questionId || topic}_${subtopic}_${difficulty}`;
            const cachedExplanation = this.explanationCache.get(cacheKey);
            if (cachedExplanation && Date.now() - cachedExplanation.timestamp < this.cacheDuration) {
                return cachedExplanation.data;
            }

            // If questionId provided, get question details
            let questionDetails = null;
            if (questionId) {
                const questionDoc = await this.db.collection('questions').doc(questionId).get();
                if (questionDoc.exists) {
                    questionDetails = questionDoc.data();
                }
            }

            // Generate explanation using LLM
            const prompt = `
                Provide a detailed explanation for ${questionDetails ? 'this question' : 'this topic'}:
                
                ${questionDetails ? questionDetails.question_text : topic}
                ${subtopic ? `\nSubtopic: ${subtopic}` : ''}
                
                Difficulty Level: ${difficulty || questionDetails?.difficulty || 'medium'}
                
                Please provide:
                1. Step-by-step explanation
                2. Key concepts involved
                3. Relevant formulas and their application
                4. Common mistakes to avoid
                5. Tips for similar questions
                6. Quick summary
                
                Make it suitable for a student at ${difficulty || 'medium'} level.
            `;

            const explanation = await llmService.generateWithOptimalModel(prompt, {
                type: 'on_demand_explanation',
                complexity: difficulty || 'medium'
            });

            // Cache the explanation
            this.explanationCache.set(cacheKey, {
                data: explanation,
                timestamp: Date.now()
            });

            // Save to database for analytics
            await this.db.collection('explanations').add({
                question_id: questionId,
                topic,
                subtopic,
                difficulty,
                explanation,
                created_at: new Date().toISOString()
            });

            return explanation;
        } catch (error) {
            loggingService.logError('Failed to get on-demand explanation', { error });
            throw error;
        }
    }

    async getRelatedConcepts(params) {
        const { topic, subtopic, difficulty } = params;

        try {
            // Check cache first
            const cacheKey = `concepts_${topic}_${subtopic}_${difficulty}`;
            const cachedConcepts = this.conceptsCache.get(cacheKey);
            if (cachedConcepts && Date.now() - cachedConcepts.timestamp < this.cacheDuration) {
                return cachedConcepts.data;
            }

            // Generate related concepts using LLM
            const prompt = `
                For the topic "${topic}" ${subtopic ? `and subtopic "${subtopic}"` : ''},
                provide related concepts and their relationships.
                
                Difficulty Level: ${difficulty || 'medium'}
                
                Please provide:
                1. Key related concepts
                2. How they connect to the main topic
                3. Prerequisites to understand them
                4. Progression path for learning
                5. Practical applications
                
                Make it suitable for a student at ${difficulty || 'medium'} level.
            `;

            const concepts = await llmService.generateWithOptimalModel(prompt, {
                type: 'related_concepts',
                complexity: difficulty || 'medium'
            });

            // Cache the concepts
            this.conceptsCache.set(cacheKey, {
                data: concepts,
                timestamp: Date.now()
            });

            // Save to database for analytics
            await this.db.collection('related_concepts').add({
                topic,
                subtopic,
                difficulty,
                concepts,
                created_at: new Date().toISOString()
            });

            return concepts;
        } catch (error) {
            loggingService.logError('Failed to get related concepts', { error });
            throw error;
        }
    }

    async evaluateChallenge(params) {
        const { question, correctAnswer, originalExplanation, userChallenge } = params;

        try {
            const prompt = `
                A user has challenged the correct answer/explanation for the following question:
                
                Question: "${question}"
                Correct Answer: "${correctAnswer}"
                Original Explanation: "${originalExplanation}"
                
                User's Challenge: "${userChallenge}"
                
                Please evaluate:
                1. Is the challenge valid? Why or why not?
                2. Does it identify any actual errors or omissions?
                3. What additional clarification would help?
                4. Should the answer/explanation be updated?
                
                Provide a detailed, fair assessment and constructive response.
            `;

            const evaluation = await llmService.generateWithOptimalModel(prompt, {
                type: 'challenge_evaluation',
                complexity: 'high'
            });

            // Log the challenge and evaluation
            await this.logChallenge(params, evaluation);

            return evaluation;
        } catch (error) {
            loggingService.logError(error, { operation: 'evaluateChallenge' });
            throw error;
        }
    }

    generateCacheKey(params) {
        return `theory:${params.topic || ''}:${params.question || ''}:${params.syllabus}`;
    }

    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
            return cached.data;
        }
        return null;
    }

    saveToCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    async saveToDatabase(params, explanation) {
        const db = await this.getDbInstance();
        const doc = {
            topic: params.topic,
            question: params.question,
            syllabus: params.syllabus,
            explanation,
            timestamp: new Date(),
            usageCount: 0
        };

        await db.collection('theory_explanations').add(doc);
    }

    async logChallenge(params, evaluation) {
        const db = await this.getDbInstance();
        const doc = {
            questionId: params.questionId,
            question: params.question,
            correctAnswer: params.correctAnswer,
            originalExplanation: params.originalExplanation,
            userChallenge: params.userChallenge,
            evaluation,
            timestamp: new Date(),
            status: 'pending_review'
        };

        await db.collection('answer_challenges').add(doc);
    }
}

const theoryService = new TheoryService();
export default theoryService;

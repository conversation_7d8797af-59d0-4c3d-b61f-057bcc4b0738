import dotenv from 'dotenv';
import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';

dotenv.config();

class LLMService {
  constructor() {
    this.db = getDb();
    this.loggingService = loggingService;
    this.apiKey = process.env.LLM_API_KEY;
    
    this.models = [
      {
        name: 'gemini-pro',
        provider: 'google',
        maxTokens: 8000,
        costPerToken: 0.00001,
        qualityScore: 0.9,
        creativityScore: 0.8
      },
      {
        name: 'gpt-4',
        provider: 'openai',
        maxTokens: 8000,
        costPerToken: 0.00003,
        qualityScore: 0.95,
        creativityScore: 0.9
      },
      {
        name: 'claude-2',
        provider: 'anthropic',
        maxTokens: 8000,
        costPerToken: 0.00002,
        qualityScore: 0.92,
        creativityScore: 0.85
      }
    ];

    this.metrics = {
      totalTokens: 0,
      totalCost: 0,
      modelUsage: {},
      errors: 0,
      lastReset: new Date()
    };
    this.metrics = {
      totalTokens: 0,
      totalCost: 0,
      modelUsage: {},
      errors: 0,
      lastReset: new Date()
    };

    this.defaultModel = 'gpt-4';
    this.fallbackModel = 'gemini-pro';

    this.startMetricsRollup();
  }

  calculateCost = (model, tokens) => {
    return tokens * model.costPerToken;
  };

  generateQuestionsWithLLM = async (params) => {
    try {
      const { subject, level, numQuestions = 5, test_type = 'general', syllabusId = null } = params;
      
      let syllabusContent = '';
      if (test_type === 'syllabus' && syllabusId) {
        syllabusContent = await this.getSyllabusContent(syllabusId);
      }
      
      const prompt = this.constructQuestionGenerationPrompt(subject, level, numQuestions, test_type, syllabusContent);
      const requirements = {
        maxTokens: 4000,
        temperature: 0.7,
        topP: 0.9
      };

      const generatedText = await this.generateWithOptimalModel(prompt, requirements);
      return this.parseGeneratedQuestions(generatedText);
    } catch (error) {
      this.loggingService.logError(error, { operation: 'generateQuestionsWithLLM' });
      return [];
    }
  };

  constructQuestionGenerationPrompt = (subject, level, numQuestions, test_type, syllabusContent = '') => {
    const currentYear = new Date().getFullYear();
    let prompt = `Generate ${numQuestions} diverse and challenging multiple-choice questions about ${subject} at ${level} level. `;
    
    if (test_type === 'syllabus' && syllabusContent) {
      prompt += `Base the questions strictly on the following syllabus content:\n\n${syllabusContent}\n\n`;
    }
    
    prompt += `
For each question:
1. Create a clear, concise, and contextually rich question that tests deeper understanding.
2. Provide exactly 4 options labeled A, B, C, and D. Ensure options are:
   - Distinct and unambiguous
   - Similar in length and structure
   - All plausible (no obviously wrong answers)
   - Free from overlapping content
3. Indicate the correct answer.
4. Include a comprehensive explanation that:
   - Explains why the correct answer is right
   - Details why each incorrect option is wrong
   - References relevant theories, principles, or formulas
   - Connects to broader concepts in ${subject}
5. Assign an appropriate difficulty level (easy/medium/hard).
6. Add relevant keywords and sub-topics.
7. Include source tags and references.

Ensure question diversity by:
- Using different cognitive levels (recall, comprehension, application, analysis)
- Varying question formats (direct questions, scenario-based, case studies)
- Covering different aspects and sub-topics of ${subject}
- Including both theoretical and practical applications
- Mixing numerical and conceptual questions where applicable

Format your response as a JSON array with the following structure for each question:
{
  "question": "The question text",
  "options": ["A) ...", "B) ...", "C) ...", "D) ..."],
  "correctAnswer": "A",
  "explanation": "Comprehensive explanation with theory and practical context",
  "difficulty": "medium",
  "keywords": ["keyword1", "keyword2", "keyword3"],
  "subTopic": "Specific sub-topic within ${subject}",
  "sourceTag": "AI Generated - ${subject} - [Sub-topic]",
  "cognitiveLevel": "application",
  "references": ["Reference text or source"],
  "lastUpdated": "${currentYear}"
}

Ensure all questions are:
1. Factually accurate and up-to-date
2. Relevant to ${subject} at ${level} level
3. Clear and unambiguous
4. Free from cultural, regional, or temporal bias
5. Logically structured and well-formatted
6. Challenging but solvable
7. Educational and informative`;

    return prompt;
  };

  validateQuestion = (question) => {
    const requiredFields = ['question', 'options', 'correctAnswer', 'explanation'];
    const optionalFields = ['difficulty', 'keywords', 'subTopic', 'sourceTag', 'cognitiveLevel', 'references', 'lastUpdated'];
    
    // Check required fields
    for (const field of requiredFields) {
      if (!question[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate options array
    if (!Array.isArray(question.options) || question.options.length !== 4) {
      throw new Error('Options must be an array of exactly 4 items');
    }

    // Validate each option starts with A), B), C), D)
    const optionPrefixes = ['A)', 'B)', 'C)', 'D)'];
    for (let i = 0; i < 4; i++) {
      if (!question.options[i].startsWith(optionPrefixes[i])) {
        throw new Error(`Option ${i + 1} must start with ${optionPrefixes[i]}`);
      }
    }

    // Validate correct answer
    if (!['A', 'B', 'C', 'D'].includes(question.correctAnswer)) {
      throw new Error('correctAnswer must be A, B, C, or D');
    }

    // Validate explanation length
    if (question.explanation.length < 50) {
      throw new Error('Explanation is too short');
    }

    // Validate optional fields if present
    if (question.difficulty && !['easy', 'medium', 'hard'].includes(question.difficulty)) {
      throw new Error('Invalid difficulty level');
    }

    if (question.keywords && (!Array.isArray(question.keywords) || question.keywords.length === 0)) {
      throw new Error('Keywords must be a non-empty array');
    }

    if (question.cognitiveLevel && 
        !['recall', 'comprehension', 'application', 'analysis'].includes(question.cognitiveLevel)) {
      throw new Error('Invalid cognitive level');
    }

    return true;
  };

  parseGeneratedQuestions = async (generatedText) => {
    try {
      // Try to extract JSON array from the response
      const jsonMatch = generatedText.match(/\[\s*\{.*?\}\s*\]/s);
      
      let questions;
      if (jsonMatch) {
        // Parse the JSON array
        questions = JSON.parse(jsonMatch[0]);
      } else {
        // If no JSON array found, try to extract individual JSON objects
        const jsonObjects = generatedText.match(/\{[^\{\}]*"question"[^\{\}]*\}/g);
        
        if (jsonObjects && jsonObjects.length > 0) {
          questions = jsonObjects.map(obj => JSON.parse(obj));
        } else {
          // If still no valid JSON found, create a fallback question
          this.loggingService.logWarning('Could not parse LLM response as JSON, using fallback parsing');
          return this.createFallbackQuestions(generatedText);
        }
      }

      // Validate each question
      const validatedQuestions = questions.filter(question => {
        try {
          this.validateQuestion(question);
          return true;
        } catch (validationError) {
          this.loggingService.logError(validationError, {
            operation: 'validateQuestion',
            question: question.question
          });
          return false;
        }
      });

      if (validatedQuestions.length === 0) {
        throw new Error('No valid questions after validation');
      }

      return validatedQuestions;
    } catch (error) {
      this.loggingService.logError(error, { operation: 'parseGeneratedQuestions' });
      return this.createFallbackQuestions(generatedText);
    }
  };

  createFallbackQuestions = async (text) => {
    const questions = [];
    const questionBlocks = text.split(/Question \d+:|Q\d+:/);

    for (let i = 1; i < questionBlocks.length; i++) {
      const block = questionBlocks[i].trim();
      try {
        const optionsMatch = block.match(/(?:A\)|B\)|C\)|D\))\s*[^\n]+/g);
        if (!optionsMatch || optionsMatch.length !== 4) continue;

        const options = optionsMatch.map(opt => opt.trim());
        const correctAnswerMatch = block.match(/(?:Correct Answer|Answer):\s*([A-D])/i);
        const explanationMatch = block.match(/(?:Explanation|Reason):\s*([^\n]+)/i);

        if (!correctAnswerMatch) continue;

        const questionText = block.split(/\n|\r/)[0].trim();
        if (!questionText) continue;

        questions.push({
          question: questionText,
          options: options,
          correctAnswer: correctAnswerMatch[1],
          explanation: explanationMatch ? explanationMatch[1].trim() : 'No explanation provided.',
          difficulty: 'medium',
          sourceTag: 'AI Generated (Fallback)',
          cognitiveLevel: 'recall',
          keywords: [],
          lastUpdated: new Date().toISOString()
        });
      } catch (error) {
        this.loggingService.logError(error, { operation: 'createFallbackQuestion', block });
        continue;
      }
    }

    if (questions.length === 0) {
      questions.push({
        question: 'What is the main topic being discussed?',
        options: [
          'A) First key point from the text',
          'B) Second key point from the text',
          'C) Third key point from the text',
          'D) None of the above'
        ],
        correctAnswer: 'D',
        explanation: 'This is a fallback question generated when proper parsing failed.',
        difficulty: 'medium',
        sourceTag: 'AI Generated (Emergency Fallback)',
        cognitiveLevel: 'recall',
        keywords: [],
        lastUpdated: new Date().toISOString()
      });

      return questions;
    }
  };

  generateTheoryExplanation = async (topic, questionDetails = {}, syllabusId = null) => {
      try {
        const start = Date.now();
        let prompt = '';

        if (!this.apiKey) {
          console.error('LLM API key not found in environment variables');
          throw new Error('LLM API key not configured');
        }

        // Get relevant syllabus content if available
        let syllabusContent = '';
        if (syllabusId) {
          syllabusContent = await this.getSyllabusContent(syllabusId);
        }

        prompt = `Generate a comprehensive theory explanation for the topic: ${topic}

${syllabusId ? `Use this syllabus content as reference: ${syllabusContent}
` : ''}
Provide a detailed, well-structured explanation that:
- Covers key concepts and principles
- Includes relevant examples and applications
- Cites credible references where applicable
- Uses clear and concise language
- Highlights important points for emphasis

Format the response as a JSON object with these fields:
- content: The main explanation text
- references: Array of cited sources
- keyPoints: Array of important takeaways`;

        // Generate the explanation using the optimal model
        const response = await this.generateWithOptimalModel(prompt, {
          format: 'json',
          complexity: 'high',
          minLength: 500
        });

        return response;
      } catch (error) {
        this.loggingService.logError(error, { operation: 'generateTheoryExplanation', topic });
        throw error;
      }
    };



  generateWithOptimalModel = async (prompt, requirements) => {
    try {
      this.metrics.totalCalls++;
      
      // Select optimal model based on requirements
      const model = await this.selectOptimalModel(requirements);
      const optimizedPrompt = await this.optimizePrompt(prompt, model);
      
      const start = Date.now();
      const response = await this.generateWithModel(model, optimizedPrompt);
      const duration = Date.now() - start;

      // Update latency metrics
      this.updateLatencyMetrics(duration);

      // Ensure response is a string
      const responseStr = typeof response === 'string' ? response : JSON.stringify(response);

      // Validate response quality
      const qualityScore = await this.validateResponseQuality(responseStr, requirements);
      this.metrics.qualityScores.push(qualityScore);
      
      // Update success metrics
      this.metrics.successfulCalls++;
      await this.logModelUsage(model, prompt.length, responseStr.length, duration);
      
      // Update hourly stats
      const hour = new Date().getHours();
      this.metrics.hourlyStats[hour]++;

      if (qualityScore < 0.8) {
        // Try fallback if quality is insufficient
        return this.generateWithModel(this.fallbackModel, prompt);
      }

      // Parse response as JSON
      try {
        const questions = await this.parseGeneratedQuestions(responseStr);
        return response;
      } catch (parseError) {
        this.loggingService.logError(parseError, { operation: 'parseResponse', response: responseStr });
        return this.createFallbackQuestions(responseStr);
      }
    } catch (error) {
      this.loggingService.logError(error, { operation: 'generateWithOptimalModel' });
      return this.generateWithModel(this.fallbackModel, prompt);
    }
  };

  optimizePrompt = async (prompt, model) => {
    // Add system context and formatting instructions
    return `
  You are an expert educational content creator specializing in creating high-quality test questions.
  Please follow these instructions carefully:
  
  1. Generate questions exactly as per the format specified
  2. Ensure all questions are clear, unambiguous, and academically rigorous
  3. Include detailed explanations that help students learn
  4. Use proper JSON formatting
  5. Validate all answers for accuracy
  
  ${prompt}
  `;
  };

  generateWithModel = async (model, prompt) => {
    if (process.env.NODE_ENV === 'development') {
      return this.getMockResponse(model, prompt);
    }

    switch (model.provider) {
      case 'openai':
        return this.generateWithOpenAI(model.name, prompt);
      case 'anthropic':
        return this.generateWithAnthropic(model.name, prompt);
      case 'google':
        return this.generateWithGoogle(model.name, prompt);
      default:
        throw new Error(`Unsupported model provider: ${model.provider}`);
    }
  };

  generateWithOpenAI = async (modelName, prompt) => {
    // TODO: Implement OpenAI API call
    return this.getMockResponse({ name: modelName }, prompt);
  };

  generateWithAnthropic = async (modelName, prompt) => {
    // TODO: Implement Anthropic API call
    return this.getMockResponse({ name: modelName }, prompt);
  };

  generateWithGoogle = async (modelName, prompt) => {
    // TODO: Implement Google API call
    return this.getMockResponse({ name: modelName }, prompt);
  };

  validateResponseQuality = async (response, requirements) => {
    // Basic validation
    if (!response || response.trim().length === 0) {
      return 0;
    }

    let score = 1;

    // Length check
    const minLength = requirements.minLength || 100;
    if (response.length < minLength) {
      score *= 0.8;
    }

    // Complexity check
    if (requirements.complexity === 'high') {
      const complexityScore = this.analyzeComplexity(response);
      score *= complexityScore;
    }

    // Format check
    if (requirements.format && !this.validateFormat(response, requirements.format)) {
      score *= 0.7;
    }

    return score;
  };

  evaluateChallengeWithLLM = async (question, userChallenge) => {
    const questions = [];
    const questionBlocks = userChallenge.split(/Question \d+:|Q\d+:/);

    for (let i = 1; i < questionBlocks.length; i++) {
      const block = questionBlocks[i].trim();
      try {
        const optionsMatch = block.match(/(?:A\)|B\)|C\)|D\))\s*[^\n]+/g);
        if (!optionsMatch || optionsMatch.length !== 4) continue;

        const options = optionsMatch.map(opt => opt.trim());
        const correctAnswerMatch = block.match(/(?:Correct Answer|Answer):\s*([A-D])/i);
        const explanationMatch = block.match(/(?:Explanation|Reason):\s*([^\n]+)/i);

        if (!correctAnswerMatch) continue;

        const questionText = block.split(/\n|\r/)[0].trim();
        if (!questionText) continue;

        questions.push({
          question: questionText,
          options: options,
          correctAnswer: correctAnswerMatch[1],
          explanation: explanationMatch ? explanationMatch[1].trim() : 'No explanation provided.',
          difficulty: 'medium',
          sourceTag: 'AI Generated (Fallback)',
          cognitiveLevel: 'recall',
          keywords: [],
          lastUpdated: new Date().toISOString()
        });
      } catch (error) {
        this.loggingService.logError(error, { operation: 'createFallbackQuestion', block });
        continue;
      }
    }

    if (questions.length === 0) {
      questions.push({
        question: 'What is the main topic being discussed?',
        options: [
          'A) First key point from the text',
          'B) Second key point from the text',
          'C) Third key point from the text',
          'D) None of the above'
        ],
        correctAnswer: 'D',
        explanation: 'This is a fallback question generated when proper parsing failed.',
        difficulty: 'medium',
        sourceTag: 'AI Generated (Emergency Fallback)',
        cognitiveLevel: 'recall',
        keywords: [],
        lastUpdated: new Date().toISOString()
      });
    }

    return questions;
  };

  // Helper methods
  getModelInstructions = (model) => {
    const baseInstructions = 'Please provide a clear and concise response.';

    switch (model.provider) {
      case 'openai':
        return `${baseInstructions} Format the response in markdown.`;
      case 'anthropic':
        return `${baseInstructions} Be thorough and show your reasoning.`;
      case 'google':
        return `${baseInstructions} Focus on accuracy and clarity.`;
      default:
        return baseInstructions;
    }
  };

  analyzeComplexity = (text) => {
    // Simple complexity analysis
    const words = text.split(' ').length;
    const sentences = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = words / sentences;

    if (avgWordsPerSentence > 20) return 1;
    if (avgWordsPerSentence > 15) return 0.9;
    if (avgWordsPerSentence > 10) return 0.8;
    return 0.7;
  };

  validateFormat = (response, format) => {
    try {
      switch (format) {
        case 'json':
          JSON.parse(response);
          return true;
        case 'markdown':
          return response.includes('#') || response.includes('*');
        default:
          return true;
      }
    } catch (error) {
      return false;
    }
  };

  // Missing methods implementation
  startMetricsRollup = () => {
    // Initialize metrics rollup
    setInterval(() => {
      this.metrics.hourlyStats = Array(24).fill(0);
    }, 3600000); // Reset hourly stats every hour
  };

  getSyllabusContent = async (syllabusId) => {
    try {
      const doc = await this.db.collection('syllabi').doc(syllabusId).get();
      return doc.exists ? doc.data().content || '' : '';
    } catch (error) {
      this.loggingService.logError(error, { operation: 'getSyllabusContent', syllabusId });
      return '';
    }
  };

  constructQuestionGenerationPrompt = (subject, level, numQuestions, testType, syllabusContent) => {
    return `Generate ${numQuestions} multiple choice questions for ${subject} at ${level} level.
${syllabusContent ? `Use this syllabus content: ${syllabusContent}` : ''}
Format as JSON array with question, options (A-D), correctAnswer, explanation, difficulty, keywords.`;
  };

  validateQuestion = (question) => {
    if (!question.question || !question.options || !question.correctAnswer) {
      throw new Error('Invalid question format');
    }
    if (question.options.length !== 4) {
      throw new Error('Question must have exactly 4 options');
    }
    if (!['A', 'B', 'C', 'D'].includes(question.correctAnswer)) {
      throw new Error('Correct answer must be A, B, C, or D');
    }
  };

  selectOptimalModel = async (requirements) => {
    // Simple model selection logic
    return {
      provider: 'openai',
      name: 'gpt-4',
      costPerToken: 0.00003
    };
  };

  updateLatencyMetrics = (duration) => {
    this.metrics.avgLatency = (this.metrics.avgLatency + duration) / 2;
  };

  logModelUsage = async (model, promptLength, responseLength, duration) => {
    const usage = {
      model: model.name,
      promptTokens: Math.ceil(promptLength / 4),
      responseTokens: Math.ceil(responseLength / 4),
      duration,
      timestamp: new Date()
    };
    this.loggingService.logInfo('Model usage', usage);
  };

  getMockResponse = (model, prompt) => {
    return JSON.stringify([{
      question: "What is the main concept being discussed?",
      options: ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      correctAnswer: "A",
      explanation: "This is a mock response for development.",
      difficulty: "medium",
      keywords: ["test", "mock"]
    }]);
  };
}

export default new LLMService();

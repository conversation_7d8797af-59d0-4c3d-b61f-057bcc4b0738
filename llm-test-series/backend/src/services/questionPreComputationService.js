import { getDb } from '../firebase.js';
import llmService from './llmService.js';
import questionAnalysisService from './questionAnalysisService.js';
import loggingService from './loggingService.js';
import cacheService from './cacheService.js';

class QuestionPreComputationService {
  constructor() {
    this.db = getDb();
    this.batchSize = 500; // Maximum documents per batch
    this.questionTypes = ['multiple_choice', 'fill_in_blanks', 'true_false'];
    this.difficultyLevels = ['easy', 'medium', 'hard', 'expert'];
    this.questionsPerTopic = 100; // Number of questions to generate per topic
    this.generationStatus = new Map(); // Track generation progress
  }

  // Start pre-computation for a syllabus
  async startPreComputation(syllabusId) {
    try {
      // Create a generation job
      const jobRef = await this.db.collection('preComputationJobs').add({
        syllabusId,
        status: 'started',
        progress: 0,
        startTime: new Date(),
        totalTopics: 0,
        completedTopics: 0,
        questionsGenerated: 0,
        errors: []
      });

      // Get syllabus details
      const syllabusDoc = await this.db.collection('syllabi').doc(syllabusId).get();
      if (!syllabusDoc.exists) {
        throw new Error('Syllabus not found');
      }

      const syllabus = syllabusDoc.data();
      const topics = await this.extractTopics(syllabus);
      const totalTopics = topics.length;

      // Update job with total topics
      await jobRef.update({ totalTopics });

      // Start generation for each topic
      for (const topic of topics) {
        try {
          await this.generateQuestionsForTopic(topic, jobRef);
        } catch (error) {
          loggingService.logError('Error generating questions for topic', {
            syllabusId,
            topicId: topic.id,
            error
          });

          await jobRef.update({
            errors: this.db.FieldValue.arrayUnion({
              topicId: topic.id,
              error: error.message,
              timestamp: new Date()
            })
          });
        }
      }

      // Mark job as completed
      await jobRef.update({
        status: 'completed',
        endTime: new Date(),
        completionMessage: `Generated ${this.questionsPerTopic * totalTopics} questions across ${totalTopics} topics`
      });

      return jobRef.id;
    } catch (error) {
      loggingService.logError('Pre-computation failed', { syllabusId, error });
      throw error;
    }
  }

  // Extract all topics from syllabus
  async extractTopics(syllabus) {
    const topics = [];
    for (const unit of syllabus.units || []) {
      for (const topic of unit.topics || []) {
        topics.push({
          id: topic.id,
          name: topic.name,
          unitId: unit.id,
          unitName: unit.name,
          learningObjectives: topic.learningObjectives || [],
          keywords: topic.keywords || []
        });
      }
    }
    return topics;
  }

  // Generate questions for a specific topic
  async generateQuestionsForTopic(topic, jobRef) {
    const questions = [];
    let batch = this.db.batch();
    let batchCount = 0;

    // Generate questions for each difficulty level
    for (const level of this.difficultyLevels) {
      const questionsPerLevel = Math.ceil(this.questionsPerTopic / this.difficultyLevels.length);
      
      // Generate questions for each type
      for (const type of this.questionTypes) {
        const questionsPerType = Math.ceil(questionsPerLevel / this.questionTypes.length);
        
        try {
          const generatedQuestions = await llmService.generateQuestionsWithLLM({
            subject: topic.unitName,
            topic: topic.name,
            level,
            numQuestions: questionsPerType,
            test_type: 'syllabus',
            questionType: type,
            learningObjectives: topic.learningObjectives,
            keywords: topic.keywords
          });

          // Validate each question
          for (const question of generatedQuestions) {
            const validationResult = await questionAnalysisService.validateDomainKnowledge(
              question,
              topic.unitName
            );

            if (validationResult.isValid) {
              // Add metadata
              question.metadata = {
                syllabusTopicId: topic.id,
                syllabusUnitId: topic.unitId,
                difficulty: level,
                type,
                preComputed: true,
                generatedAt: new Date(),
                validationScore: validationResult.score || 1
              };

              // Add to batch
              const questionRef = this.db.collection('questions').doc();
              batch.set(questionRef, question);
              questions.push(question);
              batchCount++;

              // Commit batch if size limit reached
              if (batchCount >= this.batchSize) {
                await batch.commit();
                batch = this.db.batch();
                batchCount = 0;
              }
            }
          }
        } catch (error) {
          loggingService.logError('Error generating questions', {
            topicId: topic.id,
            level,
            type,
            error
          });
          throw error;
        }
      }
    }

    // Commit any remaining questions
    if (batchCount > 0) {
      await batch.commit();
    }

    // Update job progress
    await jobRef.update({
      completedTopics: this.db.FieldValue.increment(1),
      questionsGenerated: this.db.FieldValue.increment(questions.length),
      progress: this.db.FieldValue.increment(100 / jobRef.data().totalTopics)
    });

    // Cache the questions
    cacheService.set(`precomputed_${topic.id}`, questions);

    return questions;
  }

  // Get generation status
  async getJobStatus(jobId) {
    const jobDoc = await this.db.collection('preComputationJobs').doc(jobId).get();
    if (!jobDoc.exists) {
      throw new Error('Job not found');
    }
    return jobDoc.data();
  }

  // Get all jobs for a syllabus
  async getJobsForSyllabus(syllabusId) {
    const snapshot = await this.db.collection('preComputationJobs')
      .where('syllabusId', '==', syllabusId)
      .orderBy('startTime', 'desc')
      .limit(10)
      .get();

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  }
}

export default new QuestionPreComputationService();

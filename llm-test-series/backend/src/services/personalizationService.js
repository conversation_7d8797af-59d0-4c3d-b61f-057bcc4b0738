// AI-Powered Personalization Service
import enhancedLLMService from './enhancedLLMService.js';
import adaptiveDifficultyService from './adaptiveDifficultyService.js';
import loggingService from './loggingService.js';
import redisService from './redisService.js';
import { getDb } from '../config/firebase.js';

class PersonalizationService {
  constructor() {
    this.db = getDb();
    this.learningStyles = ['visual', 'auditory', 'kinesthetic', 'reading'];
    this.personalityTypes = ['analytical', 'creative', 'practical', 'social'];
  }

  async generatePersonalizedContent(userId, topic, contentType = 'questions') {
    try {
      const userProfile = await this.getUserProfile(userId);
      const learningPreferences = await this.getLearningPreferences(userId);
      const performanceData = await this.getPerformanceData(userId, topic);

      const personalizationContext = {
        userProfile,
        learningPreferences,
        performanceData,
        topic,
        contentType
      };

      switch (contentType) {
        case 'questions':
          return await this.generatePersonalizedQuestions(personalizationContext);
        case 'explanations':
          return await this.generatePersonalizedExplanations(personalizationContext);
        case 'study_plan':
          return await this.generatePersonalizedStudyPlan(personalizationContext);
        case 'practice_recommendations':
          return await this.generatePracticeRecommendations(personalizationContext);
        default:
          throw new Error(`Unsupported content type: ${contentType}`);
      }
    } catch (error) {
      loggingService.logError('Personalized content generation failed', error);
      throw new Error('Failed to generate personalized content');
    }
  }

  async getUserProfile(userId) {
    try {
      const cacheKey = `user_profile:${userId}`;
      const cached = await redisService.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const userDoc = await this.db.collection('users').doc(userId).get();
      const gamificationDoc = await this.db.collection('gamification').doc(userId).get();
      
      const profile = {
        basic: userDoc.exists ? userDoc.data() : {},
        gamification: gamificationDoc.exists ? gamificationDoc.data() : {},
        preferences: await this.extractUserPreferences(userId)
      };

      await redisService.setex(cacheKey, 1800, JSON.stringify(profile));
      return profile;
    } catch (error) {
      loggingService.logError('Failed to get user profile', error);
      return this.getDefaultProfile();
    }
  }

  async getLearningPreferences(userId) {
    try {
      // Analyze user behavior to infer learning preferences
      const behaviorData = await this.analyzeLearningBehavior(userId);
      
      // Get explicit preferences if set
      const preferencesDoc = await this.db.collection('userPreferences').doc(userId).get();
      const explicitPreferences = preferencesDoc.exists ? preferencesDoc.data() : {};

      return {
        ...this.inferLearningStyle(behaviorData),
        ...explicitPreferences,
        confidence: this.calculatePreferenceConfidence(behaviorData, explicitPreferences)
      };
    } catch (error) {
      loggingService.logError('Failed to get learning preferences', error);
      return this.getDefaultPreferences();
    }
  }

  async getPerformanceData(userId, topic = null) {
    try {
      const query = this.db.collection('testSessions')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .limit(50);

      const snapshot = await query.get();
      const sessions = snapshot.docs.map(doc => doc.data());

      return this.analyzePerformancePatterns(sessions, topic);
    } catch (error) {
      loggingService.logError('Failed to get performance data', error);
      return this.getDefaultPerformanceData();
    }
  }

  async generatePersonalizedQuestions(context) {
    const { userProfile, learningPreferences, performanceData, topic } = context;

    const prompt = this.buildPersonalizedQuestionPrompt(context);
    
    const result = await enhancedLLMService.generateQuestions({
      syllabus: { subject: topic.subject, topic: topic.name },
      topic: topic.name,
      difficulty: performanceData.recommendedDifficulty,
      count: this.calculateOptimalQuestionCount(userProfile, performanceData),
      questionType: this.selectOptimalQuestionType(learningPreferences),
      bloomsLevel: this.selectOptimalBloomsLevel(performanceData),
      userPerformance: performanceData,
      personalizationContext: {
        learningStyle: learningPreferences.primaryStyle,
        weakAreas: performanceData.weakAreas,
        strongAreas: performanceData.strongAreas,
        motivationalFactors: userProfile.gamification.preferences
      }
    });

    // Enhance questions with personalized elements
    return this.enhanceQuestionsWithPersonalization(result, context);
  }

  async generatePersonalizedExplanations(context) {
    const { userProfile, learningPreferences, performanceData } = context;

    const explanationStyle = this.determineExplanationStyle(learningPreferences);
    const complexity = this.determineExplanationComplexity(performanceData);

    return {
      style: explanationStyle,
      complexity,
      includeVisuals: learningPreferences.primaryStyle === 'visual',
      includeAnalogies: learningPreferences.personalityType === 'creative',
      includeStepByStep: learningPreferences.personalityType === 'analytical',
      includeRealWorldExamples: learningPreferences.personalityType === 'practical'
    };
  }

  async generatePersonalizedStudyPlan(context) {
    const { userProfile, learningPreferences, performanceData } = context;

    const studyPlan = await enhancedLLMService.generatePersonalizedStudyPlan(
      userProfile.basic,
      performanceData,
      userProfile.gamification.goals || 'Improve overall performance'
    );

    // Personalize the study plan
    return this.personalizeStudyPlan(studyPlan, context);
  }

  async generatePracticeRecommendations(context) {
    const { userProfile, learningPreferences, performanceData } = context;

    const recommendations = [];

    // Weakness-focused recommendations
    if (performanceData.weakAreas.length > 0) {
      recommendations.push({
        type: 'weakness_focus',
        priority: 'high',
        topics: performanceData.weakAreas.slice(0, 3),
        approach: this.getWeaknessApproach(learningPreferences),
        estimatedTime: this.calculateWeaknessStudyTime(performanceData)
      });
    }

    // Strength reinforcement
    if (performanceData.strongAreas.length > 0) {
      recommendations.push({
        type: 'strength_reinforcement',
        priority: 'medium',
        topics: performanceData.strongAreas.slice(0, 2),
        approach: this.getStrengthApproach(learningPreferences),
        estimatedTime: this.calculateStrengthStudyTime(performanceData)
      });
    }

    // Challenge recommendations
    if (performanceData.readyForChallenge) {
      recommendations.push({
        type: 'challenge',
        priority: 'medium',
        description: 'Advanced problems to push your limits',
        approach: this.getChallengeApproach(learningPreferences),
        estimatedTime: 30
      });
    }

    // Review recommendations
    const reviewTopics = this.identifyReviewTopics(performanceData);
    if (reviewTopics.length > 0) {
      recommendations.push({
        type: 'review',
        priority: 'low',
        topics: reviewTopics,
        approach: this.getReviewApproach(learningPreferences),
        estimatedTime: 15
      });
    }

    return this.prioritizeRecommendations(recommendations, userProfile);
  }

  buildPersonalizedQuestionPrompt(context) {
    const { userProfile, learningPreferences, performanceData, topic } = context;

    return `Generate personalized questions for this learner:

Learner Profile:
- Learning Style: ${learningPreferences.primaryStyle}
- Personality Type: ${learningPreferences.personalityType}
- Current Level: ${userProfile.gamification.level?.current || 1}
- Motivation: ${userProfile.gamification.preferences?.motivation || 'achievement'}

Performance Context:
- Strong Areas: ${performanceData.strongAreas.join(', ')}
- Weak Areas: ${performanceData.weakAreas.join(', ')}
- Preferred Difficulty: ${performanceData.recommendedDifficulty}
- Learning Pace: ${performanceData.learningPace}

Topic: ${topic.name}

Personalization Requirements:
${learningPreferences.primaryStyle === 'visual' ? '- Include visual elements or diagrams where possible' : ''}
${learningPreferences.primaryStyle === 'auditory' ? '- Use sound-related analogies and verbal descriptions' : ''}
${learningPreferences.personalityType === 'analytical' ? '- Focus on logical reasoning and step-by-step solutions' : ''}
${learningPreferences.personalityType === 'creative' ? '- Include creative scenarios and analogies' : ''}
${learningPreferences.personalityType === 'practical' ? '- Use real-world applications and examples' : ''}

Generate questions that match this learner's profile and help address their weak areas while building on strengths.`;
  }

  async analyzeLearningBehavior(userId) {
    try {
      const sessionsSnapshot = await this.db.collection('testSessions')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .limit(20)
        .get();

      const sessions = sessionsSnapshot.docs.map(doc => doc.data());
      
      return {
        averageSessionDuration: this.calculateAverageSessionDuration(sessions),
        preferredTimeOfDay: this.identifyPreferredStudyTime(sessions),
        questionTypePreferences: this.analyzeQuestionTypePreferences(sessions),
        difficultyPreferences: this.analyzeDifficultyPreferences(sessions),
        hintUsagePattern: this.analyzeHintUsage(sessions),
        explanationViewingPattern: this.analyzeExplanationViewing(sessions),
        pacePreference: this.analyzePacePreference(sessions)
      };
    } catch (error) {
      loggingService.logError('Failed to analyze learning behavior', error);
      return {};
    }
  }

  inferLearningStyle(behaviorData) {
    const scores = {
      visual: 0,
      auditory: 0,
      kinesthetic: 0,
      reading: 0
    };

    // Analyze hint usage (visual learners use more hints)
    if (behaviorData.hintUsagePattern?.frequency > 0.3) {
      scores.visual += 2;
    }

    // Analyze explanation viewing (reading learners view more explanations)
    if (behaviorData.explanationViewingPattern?.frequency > 0.7) {
      scores.reading += 2;
    }

    // Analyze pace (kinesthetic learners prefer faster pace)
    if (behaviorData.pacePreference === 'fast') {
      scores.kinesthetic += 2;
    } else if (behaviorData.pacePreference === 'slow') {
      scores.reading += 1;
    }

    // Analyze session duration (auditory learners prefer longer sessions)
    if (behaviorData.averageSessionDuration > 1800) { // 30 minutes
      scores.auditory += 1;
    }

    const primaryStyle = Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b)[0];
    
    return {
      primaryStyle,
      scores,
      confidence: Math.max(...Object.values(scores)) / 5
    };
  }

  calculateOptimalQuestionCount(userProfile, performanceData) {
    const baseCount = 10;
    const levelMultiplier = (userProfile.gamification.level?.current || 1) / 10;
    const performanceMultiplier = performanceData.consistency > 0.8 ? 1.2 : 0.8;
    
    return Math.round(baseCount * levelMultiplier * performanceMultiplier);
  }

  selectOptimalQuestionType(learningPreferences) {
    const typeMapping = {
      visual: 'multiple-choice',
      auditory: 'short-answer',
      kinesthetic: 'problem-solving',
      reading: 'essay'
    };
    
    return typeMapping[learningPreferences.primaryStyle] || 'multiple-choice';
  }

  selectOptimalBloomsLevel(performanceData) {
    const levelMapping = {
      beginner: 'remember',
      intermediate: 'understand',
      advanced: 'apply',
      expert: 'analyze'
    };
    
    return levelMapping[performanceData.recommendedDifficulty] || 'apply';
  }

  enhanceQuestionsWithPersonalization(questions, context) {
    return questions.map(question => ({
      ...question,
      personalization: {
        learningStyle: context.learningPreferences.primaryStyle,
        adaptedForWeakness: context.performanceData.weakAreas.includes(question.metadata?.topic),
        motivationalElement: this.addMotivationalElement(question, context.userProfile.gamification),
        customHints: this.generateCustomHints(question, context.learningPreferences)
      }
    }));
  }

  addMotivationalElement(question, gamificationData) {
    const motivationType = gamificationData.preferences?.motivation || 'achievement';
    
    const motivationalElements = {
      achievement: `🏆 Master this concept to unlock the next level!`,
      competition: `⚔️ This question type appears in top performer challenges!`,
      progress: `📈 You're 80% through this topic - keep going!`,
      social: `👥 Your study group is working on similar problems!`
    };
    
    return motivationalElements[motivationType] || motivationalElements.achievement;
  }

  generateCustomHints(question, learningPreferences) {
    // This would generate hints tailored to the user's learning style
    const baseHints = question.content?.hints || [];
    
    if (learningPreferences.primaryStyle === 'visual') {
      return baseHints.map(hint => `🎨 Visualize: ${hint}`);
    } else if (learningPreferences.primaryStyle === 'analytical') {
      return baseHints.map(hint => `🔍 Step-by-step: ${hint}`);
    }
    
    return baseHints;
  }

  analyzePerformancePatterns(sessions, topic) {
    // Comprehensive performance analysis
    const topicSessions = topic ? sessions.filter(s => s.config?.subject === topic.subject) : sessions;
    
    const weakAreas = this.identifyWeakAreas(topicSessions);
    const strongAreas = this.identifyStrongAreas(topicSessions);
    const learningPace = this.calculateLearningPace(topicSessions);
    const consistency = this.calculateConsistency(topicSessions);
    
    return {
      weakAreas,
      strongAreas,
      learningPace,
      consistency,
      recommendedDifficulty: this.calculateRecommendedDifficulty(topicSessions),
      readyForChallenge: consistency > 0.8 && strongAreas.length > weakAreas.length,
      totalSessions: topicSessions.length
    };
  }

  identifyWeakAreas(sessions) {
    // Analyze topic-wise performance to identify weak areas
    const topicPerformance = {};
    
    sessions.forEach(session => {
      if (session.results?.topicWisePerformance) {
        Object.entries(session.results.topicWisePerformance).forEach(([topic, perf]) => {
          if (!topicPerformance[topic]) {
            topicPerformance[topic] = { total: 0, correct: 0 };
          }
          topicPerformance[topic].total += perf.attempted || 0;
          topicPerformance[topic].correct += perf.correct || 0;
        });
      }
    });
    
    return Object.entries(topicPerformance)
      .filter(([topic, perf]) => perf.total > 0 && (perf.correct / perf.total) < 0.6)
      .map(([topic]) => topic)
      .slice(0, 5);
  }

  identifyStrongAreas(sessions) {
    // Similar to weak areas but for strong performance
    const topicPerformance = {};
    
    sessions.forEach(session => {
      if (session.results?.topicWisePerformance) {
        Object.entries(session.results.topicWisePerformance).forEach(([topic, perf]) => {
          if (!topicPerformance[topic]) {
            topicPerformance[topic] = { total: 0, correct: 0 };
          }
          topicPerformance[topic].total += perf.attempted || 0;
          topicPerformance[topic].correct += perf.correct || 0;
        });
      }
    });
    
    return Object.entries(topicPerformance)
      .filter(([topic, perf]) => perf.total > 0 && (perf.correct / perf.total) > 0.8)
      .map(([topic]) => topic)
      .slice(0, 5);
  }

  calculateLearningPace(sessions) {
    if (sessions.length === 0) return 'medium';
    
    const averageTime = sessions.reduce((sum, s) => sum + (s.results?.averageTimePerQuestion || 0), 0) / sessions.length;
    
    if (averageTime < 60) return 'fast';
    if (averageTime > 120) return 'slow';
    return 'medium';
  }

  calculateConsistency(sessions) {
    if (sessions.length < 3) return 0.5;
    
    const accuracies = sessions.map(s => s.results?.accuracy || 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const standardDeviation = Math.sqrt(variance);
    
    return Math.max(0, 1 - (standardDeviation / 50)); // Normalize to 0-1
  }

  calculateRecommendedDifficulty(sessions) {
    if (sessions.length === 0) return 'intermediate';
    
    const recentSessions = sessions.slice(0, 5);
    const averageAccuracy = recentSessions.reduce((sum, s) => sum + (s.results?.accuracy || 0), 0) / recentSessions.length;
    
    if (averageAccuracy > 85) return 'advanced';
    if (averageAccuracy > 70) return 'intermediate';
    return 'beginner';
  }

  getDefaultProfile() {
    return {
      basic: { level: 1 },
      gamification: { level: { current: 1 }, preferences: {} },
      preferences: {}
    };
  }

  getDefaultPreferences() {
    return {
      primaryStyle: 'visual',
      personalityType: 'analytical',
      confidence: 0.3
    };
  }

  getDefaultPerformanceData() {
    return {
      weakAreas: [],
      strongAreas: [],
      learningPace: 'medium',
      consistency: 0.5,
      recommendedDifficulty: 'intermediate',
      readyForChallenge: false,
      totalSessions: 0
    };
  }
}

export default new PersonalizationService();

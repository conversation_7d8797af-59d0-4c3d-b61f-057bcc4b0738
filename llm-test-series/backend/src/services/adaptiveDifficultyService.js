// Adaptive Difficulty Service using AI and Machine Learning
import enhancedLLMService from './enhancedLLMService.js';
import loggingService from './loggingService.js';
import redisService from './redisService.js';
import { getDb } from '../config/firebase.js';

class AdaptiveDifficultyService {
  constructor() {
    this.db = getDb();
    this.difficultyLevels = ['beginner', 'intermediate', 'advanced', 'expert'];
    this.adaptationThresholds = {
      promotion: 0.8,    // 80% accuracy to move up
      demotion: 0.4,     // Below 40% accuracy to move down
      stability: 0.6     // 60% accuracy to stay at current level
    };
    this.minimumQuestions = 5; // Minimum questions before adaptation
  }

  async adaptDifficulty(userId, sessionData, currentDifficulty) {
    try {
      loggingService.logInfo('Starting difficulty adaptation', {
        userId,
        currentDifficulty,
        questionsAnswered: sessionData.answers?.length || 0
      });

      // Get user's historical performance
      const userPerformance = await this.getUserPerformance(userId);
      
      // Analyze current session performance
      const sessionAnalysis = this.analyzeSessionPerformance(sessionData);
      
      // Get AI recommendation
      const aiRecommendation = await this.getAIRecommendation(
        userPerformance,
        sessionAnalysis,
        currentDifficulty
      );
      
      // Calculate new difficulty
      const newDifficulty = this.calculateNewDifficulty(
        currentDifficulty,
        sessionAnalysis,
        aiRecommendation,
        userPerformance
      );
      
      // Update user's difficulty profile
      await this.updateUserDifficultyProfile(userId, {
        previousDifficulty: currentDifficulty,
        newDifficulty,
        sessionAnalysis,
        aiRecommendation,
        timestamp: new Date().toISOString()
      });

      loggingService.logInfo('Difficulty adaptation completed', {
        userId,
        previousDifficulty: currentDifficulty,
        newDifficulty,
        reason: aiRecommendation.reason
      });

      return {
        newDifficulty,
        recommendation: aiRecommendation,
        confidence: this.calculateConfidence(sessionAnalysis, userPerformance),
        explanation: this.generateExplanation(currentDifficulty, newDifficulty, aiRecommendation)
      };
    } catch (error) {
      loggingService.logError('Difficulty adaptation failed', error);
      return {
        newDifficulty: currentDifficulty,
        recommendation: { reason: 'Error in adaptation, maintaining current level' },
        confidence: 0.5,
        explanation: 'Unable to adapt difficulty due to technical issues'
      };
    }
  }

  async getUserPerformance(userId) {
    try {
      const cacheKey = `user_performance:${userId}`;
      const cached = await redisService.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Query recent test sessions
      const sessionsSnapshot = await this.db
        .collection('testSessions')
        .where('userId', '==', userId)
        .orderBy('createdAt', 'desc')
        .limit(20)
        .get();

      const sessions = sessionsSnapshot.docs.map(doc => doc.data());
      
      const performance = this.calculateOverallPerformance(sessions);
      
      // Cache for 30 minutes
      await redisService.setex(cacheKey, 1800, JSON.stringify(performance));
      
      return performance;
    } catch (error) {
      loggingService.logError('Failed to get user performance', error);
      return this.getDefaultPerformance();
    }
  }

  analyzeSessionPerformance(sessionData) {
    const answers = sessionData.answers || [];
    
    if (answers.length === 0) {
      return {
        accuracy: 0,
        averageTime: 0,
        confidence: 0,
        consistency: 0,
        questionsAnswered: 0
      };
    }

    const correctAnswers = answers.filter(a => a.isCorrect).length;
    const accuracy = correctAnswers / answers.length;
    
    const totalTime = answers.reduce((sum, a) => sum + (a.timeSpent || 0), 0);
    const averageTime = totalTime / answers.length;
    
    const averageConfidence = answers.reduce((sum, a) => sum + (a.confidence || 3), 0) / answers.length;
    
    // Calculate consistency (how stable the performance is)
    const recentAnswers = answers.slice(-10); // Last 10 answers
    const recentAccuracy = recentAnswers.filter(a => a.isCorrect).length / recentAnswers.length;
    const consistency = 1 - Math.abs(accuracy - recentAccuracy);
    
    // Analyze time patterns
    const timeAnalysis = this.analyzeTimePatterns(answers);
    
    // Analyze difficulty progression
    const difficultyProgression = this.analyzeDifficultyProgression(answers);

    return {
      accuracy,
      averageTime,
      confidence: averageConfidence,
      consistency,
      questionsAnswered: answers.length,
      timeAnalysis,
      difficultyProgression,
      recentPerformance: {
        accuracy: recentAccuracy,
        questionsCount: recentAnswers.length
      }
    };
  }

  async getAIRecommendation(userPerformance, sessionAnalysis, currentDifficulty) {
    try {
      const prompt = this.buildAIPrompt(userPerformance, sessionAnalysis, currentDifficulty);
      
      const result = await enhancedLLMService.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 500,
        temperature: 0.3
      });

      return this.parseAIRecommendation(result.content);
    } catch (error) {
      loggingService.logError('AI recommendation failed', error);
      return this.getFallbackRecommendation(sessionAnalysis, currentDifficulty);
    }
  }

  buildAIPrompt(userPerformance, sessionAnalysis, currentDifficulty) {
    return `Analyze this student's performance and recommend difficulty adjustment:

Current Difficulty: ${currentDifficulty}

Session Performance:
- Accuracy: ${(sessionAnalysis.accuracy * 100).toFixed(1)}%
- Questions Answered: ${sessionAnalysis.questionsAnswered}
- Average Time: ${sessionAnalysis.averageTime}s
- Confidence: ${sessionAnalysis.confidence}/5
- Consistency: ${(sessionAnalysis.consistency * 100).toFixed(1)}%

Historical Performance:
- Overall Accuracy: ${(userPerformance.overallAccuracy * 100).toFixed(1)}%
- Total Sessions: ${userPerformance.totalSessions}
- Improvement Trend: ${userPerformance.trend}
- Preferred Difficulty: ${userPerformance.preferredDifficulty}

Difficulty Levels: beginner → intermediate → advanced → expert

Provide recommendation as JSON:
{
  "recommendedDifficulty": "intermediate",
  "confidence": 0.85,
  "reason": "Detailed explanation for the recommendation",
  "factors": ["Factor 1", "Factor 2", "Factor 3"],
  "nextSteps": "What the student should focus on next"
}`;
  }

  parseAIRecommendation(content) {
    try {
      return JSON.parse(content);
    } catch (error) {
      return {
        recommendedDifficulty: 'intermediate',
        confidence: 0.5,
        reason: 'AI parsing failed, using default recommendation',
        factors: ['Technical issue in analysis'],
        nextSteps: 'Continue practicing at current level'
      };
    }
  }

  calculateNewDifficulty(currentDifficulty, sessionAnalysis, aiRecommendation, userPerformance) {
    const currentIndex = this.difficultyLevels.indexOf(currentDifficulty);
    
    // If not enough data, stay at current level
    if (sessionAnalysis.questionsAnswered < this.minimumQuestions) {
      return currentDifficulty;
    }

    // Use AI recommendation as primary factor
    const aiRecommendedIndex = this.difficultyLevels.indexOf(aiRecommendation.recommendedDifficulty);
    
    // Apply safety constraints
    let newIndex = aiRecommendedIndex;
    
    // Don't jump more than one level at a time
    if (Math.abs(newIndex - currentIndex) > 1) {
      newIndex = currentIndex + Math.sign(newIndex - currentIndex);
    }
    
    // Apply performance-based constraints
    if (sessionAnalysis.accuracy < this.adaptationThresholds.demotion && newIndex >= currentIndex) {
      newIndex = Math.max(0, currentIndex - 1);
    }
    
    if (sessionAnalysis.accuracy > this.adaptationThresholds.promotion && newIndex <= currentIndex) {
      newIndex = Math.min(this.difficultyLevels.length - 1, currentIndex + 1);
    }
    
    // Ensure index is within bounds
    newIndex = Math.max(0, Math.min(this.difficultyLevels.length - 1, newIndex));
    
    return this.difficultyLevels[newIndex];
  }

  calculateOverallPerformance(sessions) {
    if (sessions.length === 0) {
      return this.getDefaultPerformance();
    }

    const totalQuestions = sessions.reduce((sum, s) => sum + (s.results?.totalQuestions || 0), 0);
    const totalCorrect = sessions.reduce((sum, s) => sum + (s.results?.correctAnswers || 0), 0);
    const overallAccuracy = totalQuestions > 0 ? totalCorrect / totalQuestions : 0;

    // Calculate trend (improvement over time)
    const recentSessions = sessions.slice(0, 5);
    const olderSessions = sessions.slice(5, 10);
    
    const recentAccuracy = this.calculateAverageAccuracy(recentSessions);
    const olderAccuracy = this.calculateAverageAccuracy(olderSessions);
    
    let trend = 'stable';
    if (recentAccuracy > olderAccuracy + 0.1) trend = 'improving';
    else if (recentAccuracy < olderAccuracy - 0.1) trend = 'declining';

    // Find preferred difficulty
    const difficultyPerformance = {};
    sessions.forEach(session => {
      const difficulty = session.config?.difficulty || 'intermediate';
      if (!difficultyPerformance[difficulty]) {
        difficultyPerformance[difficulty] = { total: 0, correct: 0 };
      }
      difficultyPerformance[difficulty].total += session.results?.totalQuestions || 0;
      difficultyPerformance[difficulty].correct += session.results?.correctAnswers || 0;
    });

    let preferredDifficulty = 'intermediate';
    let bestAccuracy = 0;
    
    Object.entries(difficultyPerformance).forEach(([difficulty, perf]) => {
      const accuracy = perf.total > 0 ? perf.correct / perf.total : 0;
      if (accuracy > bestAccuracy && perf.total >= 10) { // Minimum 10 questions
        bestAccuracy = accuracy;
        preferredDifficulty = difficulty;
      }
    });

    return {
      overallAccuracy,
      totalSessions: sessions.length,
      totalQuestions,
      trend,
      preferredDifficulty,
      difficultyPerformance,
      recentAccuracy,
      consistencyScore: this.calculateConsistency(sessions)
    };
  }

  calculateAverageAccuracy(sessions) {
    if (sessions.length === 0) return 0;
    
    const totalQuestions = sessions.reduce((sum, s) => sum + (s.results?.totalQuestions || 0), 0);
    const totalCorrect = sessions.reduce((sum, s) => sum + (s.results?.correctAnswers || 0), 0);
    
    return totalQuestions > 0 ? totalCorrect / totalQuestions : 0;
  }

  calculateConsistency(sessions) {
    if (sessions.length < 3) return 0.5;
    
    const accuracies = sessions.map(s => {
      const total = s.results?.totalQuestions || 0;
      const correct = s.results?.correctAnswers || 0;
      return total > 0 ? correct / total : 0;
    });
    
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Convert to consistency score (lower deviation = higher consistency)
    return Math.max(0, 1 - (standardDeviation * 2));
  }

  analyzeTimePatterns(answers) {
    const times = answers.map(a => a.timeSpent || 0);
    const averageTime = times.reduce((sum, t) => sum + t, 0) / times.length;
    
    const fastAnswers = times.filter(t => t < averageTime * 0.5).length;
    const slowAnswers = times.filter(t => t > averageTime * 2).length;
    
    return {
      averageTime,
      fastAnswers,
      slowAnswers,
      timeConsistency: this.calculateTimeConsistency(times)
    };
  }

  calculateTimeConsistency(times) {
    if (times.length < 2) return 0.5;
    
    const mean = times.reduce((sum, t) => sum + t, 0) / times.length;
    const variance = times.reduce((sum, t) => sum + Math.pow(t - mean, 2), 0) / times.length;
    const coefficientOfVariation = Math.sqrt(variance) / mean;
    
    return Math.max(0, 1 - coefficientOfVariation);
  }

  analyzeDifficultyProgression(answers) {
    // This would analyze how the user performs as questions get harder within a session
    const progression = answers.map((answer, index) => ({
      questionNumber: index + 1,
      isCorrect: answer.isCorrect,
      timeSpent: answer.timeSpent || 0
    }));
    
    return {
      progression,
      earlyAccuracy: this.calculateAccuracyForRange(answers, 0, 0.3),
      midAccuracy: this.calculateAccuracyForRange(answers, 0.3, 0.7),
      lateAccuracy: this.calculateAccuracyForRange(answers, 0.7, 1.0)
    };
  }

  calculateAccuracyForRange(answers, startRatio, endRatio) {
    const startIndex = Math.floor(answers.length * startRatio);
    const endIndex = Math.floor(answers.length * endRatio);
    const rangeAnswers = answers.slice(startIndex, endIndex);
    
    if (rangeAnswers.length === 0) return 0;
    
    const correct = rangeAnswers.filter(a => a.isCorrect).length;
    return correct / rangeAnswers.length;
  }

  getFallbackRecommendation(sessionAnalysis, currentDifficulty) {
    const currentIndex = this.difficultyLevels.indexOf(currentDifficulty);
    let recommendedDifficulty = currentDifficulty;
    
    if (sessionAnalysis.accuracy > this.adaptationThresholds.promotion) {
      recommendedDifficulty = this.difficultyLevels[Math.min(currentIndex + 1, this.difficultyLevels.length - 1)];
    } else if (sessionAnalysis.accuracy < this.adaptationThresholds.demotion) {
      recommendedDifficulty = this.difficultyLevels[Math.max(currentIndex - 1, 0)];
    }
    
    return {
      recommendedDifficulty,
      confidence: 0.7,
      reason: 'Rule-based recommendation due to AI unavailability',
      factors: [`Accuracy: ${(sessionAnalysis.accuracy * 100).toFixed(1)}%`],
      nextSteps: 'Continue practicing to improve performance'
    };
  }

  getDefaultPerformance() {
    return {
      overallAccuracy: 0.5,
      totalSessions: 0,
      totalQuestions: 0,
      trend: 'stable',
      preferredDifficulty: 'intermediate',
      difficultyPerformance: {},
      recentAccuracy: 0.5,
      consistencyScore: 0.5
    };
  }

  calculateConfidence(sessionAnalysis, userPerformance) {
    let confidence = 0.5;
    
    // More questions answered = higher confidence
    if (sessionAnalysis.questionsAnswered >= 10) confidence += 0.2;
    if (sessionAnalysis.questionsAnswered >= 20) confidence += 0.1;
    
    // Consistency adds confidence
    confidence += sessionAnalysis.consistency * 0.2;
    
    // Historical data adds confidence
    if (userPerformance.totalSessions >= 5) confidence += 0.1;
    if (userPerformance.totalSessions >= 10) confidence += 0.1;
    
    return Math.min(1.0, confidence);
  }

  generateExplanation(oldDifficulty, newDifficulty, recommendation) {
    if (oldDifficulty === newDifficulty) {
      return `Maintaining ${oldDifficulty} difficulty level. ${recommendation.reason}`;
    }
    
    const direction = this.difficultyLevels.indexOf(newDifficulty) > this.difficultyLevels.indexOf(oldDifficulty) 
      ? 'increased' : 'decreased';
    
    return `Difficulty ${direction} from ${oldDifficulty} to ${newDifficulty}. ${recommendation.reason}`;
  }

  async updateUserDifficultyProfile(userId, adaptationData) {
    try {
      const profileRef = this.db.collection('userDifficultyProfiles').doc(userId);
      
      await profileRef.set({
        userId,
        lastAdaptation: adaptationData,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Clear cache
      await redisService.del(`user_performance:${userId}`);
    } catch (error) {
      loggingService.logError('Failed to update difficulty profile', error);
    }
  }
}

export default new AdaptiveDifficultyService();

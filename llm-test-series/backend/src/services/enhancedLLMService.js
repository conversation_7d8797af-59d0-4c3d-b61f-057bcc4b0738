// Enhanced LLM Service with Multi-Provider Support and Advanced Features
import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Anthropic from '@anthropic-ai/sdk';
import loggingService from './loggingService.js';
import redisService from './redisService.js';

class EnhancedLLMService {
  constructor() {
    this.providers = {
      openai: null,
      gemini: null,
      claude: null
    };
    
    this.initializeProviders();
    this.fallbackOrder = ['openai', 'gemini', 'claude'];
    this.rateLimits = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;
  }

  initializeProviders() {
    try {
      // Initialize OpenAI
      if (process.env.OPENAI_API_KEY) {
        this.providers.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
      }

      // Initialize Google Gemini
      if (process.env.GEMINI_API_KEY) {
        this.providers.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
      }

      // Initialize Anthropic Claude
      if (process.env.ANTHROPIC_API_KEY) {
        this.providers.claude = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY,
        });
      }

      loggingService.logInfo('LLM providers initialized', {
        providers: Object.keys(this.providers).filter(key => this.providers[key] !== null)
      });
    } catch (error) {
      loggingService.logError('Failed to initialize LLM providers', error);
    }
  }

  async generateQuestions(params) {
    const {
      syllabus,
      topic,
      difficulty = 'intermediate',
      count = 5,
      questionType = 'multiple-choice',
      bloomsLevel = 'apply',
      previousQuestions = [],
      userPerformance = null
    } = params;

    const cacheKey = `questions:${JSON.stringify(params)}`;
    
    try {
      // Check cache first
      const cached = await redisService.get(cacheKey);
      if (cached) {
        loggingService.logInfo('Questions retrieved from cache');
        return JSON.parse(cached);
      }

      const prompt = this.buildQuestionGenerationPrompt({
        syllabus,
        topic,
        difficulty,
        count,
        questionType,
        bloomsLevel,
        previousQuestions,
        userPerformance
      });

      const result = await this.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 2000,
        temperature: 0.7
      });

      const questions = this.parseQuestionResponse(result.content);
      
      // Cache the result
      await redisService.setex(cacheKey, 3600, JSON.stringify(questions));
      
      loggingService.logInfo('Questions generated successfully', {
        count: questions.length,
        topic,
        difficulty
      });

      return questions;
    } catch (error) {
      loggingService.logError('Question generation failed', error);
      throw new Error('Failed to generate questions');
    }
  }

  async generateExplanation(question, userAnswer, difficulty = 'intermediate') {
    const cacheKey = `explanation:${question.questionId}:${userAnswer}:${difficulty}`;
    
    try {
      const cached = await redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const prompt = this.buildExplanationPrompt(question, userAnswer, difficulty);
      
      const result = await this.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 800,
        temperature: 0.3
      });

      const explanation = this.parseExplanationResponse(result.content);
      
      await redisService.setex(cacheKey, 7200, JSON.stringify(explanation));
      
      return explanation;
    } catch (error) {
      loggingService.logError('Explanation generation failed', error);
      throw new Error('Failed to generate explanation');
    }
  }

  async evaluateShortAnswer(question, userAnswer, rubric = null) {
    try {
      const prompt = this.buildEvaluationPrompt(question, userAnswer, rubric);
      
      const result = await this.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 500,
        temperature: 0.1
      });

      return this.parseEvaluationResponse(result.content);
    } catch (error) {
      loggingService.logError('Answer evaluation failed', error);
      throw new Error('Failed to evaluate answer');
    }
  }

  async generatePersonalizedStudyPlan(userProfile, performanceData, goals) {
    try {
      const prompt = this.buildStudyPlanPrompt(userProfile, performanceData, goals);
      
      const result = await this.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 1500,
        temperature: 0.5
      });

      return this.parseStudyPlanResponse(result.content);
    } catch (error) {
      loggingService.logError('Study plan generation failed', error);
      throw new Error('Failed to generate study plan');
    }
  }

  async generateHints(question, difficulty = 'intermediate') {
    try {
      const prompt = this.buildHintPrompt(question, difficulty);
      
      const result = await this.executeWithFallback('generateContent', {
        prompt,
        maxTokens: 300,
        temperature: 0.4
      });

      return this.parseHintResponse(result.content);
    } catch (error) {
      loggingService.logError('Hint generation failed', error);
      return ['Think about the key concepts involved', 'Review the relevant formulas', 'Consider the problem step by step'];
    }
  }

  async executeWithFallback(operation, params) {
    for (const provider of this.fallbackOrder) {
      if (!this.providers[provider]) continue;
      
      try {
        // Check rate limits
        if (this.isRateLimited(provider)) {
          continue;
        }

        const result = await this.executeOperation(provider, operation, params);
        this.updateRateLimit(provider);
        return result;
      } catch (error) {
        loggingService.logWarning(`${provider} failed, trying next provider`, error);
        
        if (error.status === 429) {
          this.setRateLimit(provider, 60000); // 1 minute cooldown
        }
        
        continue;
      }
    }
    
    throw new Error('All LLM providers failed');
  }

  async executeOperation(provider, operation, params) {
    switch (provider) {
      case 'openai':
        return await this.executeOpenAI(operation, params);
      case 'gemini':
        return await this.executeGemini(operation, params);
      case 'claude':
        return await this.executeClaude(operation, params);
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  async executeOpenAI(operation, params) {
    const response = await this.providers.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: 'You are an expert educational content creator and tutor.' },
        { role: 'user', content: params.prompt }
      ],
      max_tokens: params.maxTokens,
      temperature: params.temperature,
    });

    return {
      content: response.choices[0].message.content,
      provider: 'openai',
      model: 'gpt-4'
    };
  }

  async executeGemini(operation, params) {
    const model = this.providers.gemini.getGenerativeModel({ model: 'gemini-pro' });
    
    const result = await model.generateContent(params.prompt);
    const response = await result.response;
    
    return {
      content: response.text(),
      provider: 'gemini',
      model: 'gemini-pro'
    };
  }

  async executeClaude(operation, params) {
    const response = await this.providers.claude.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: params.maxTokens,
      temperature: params.temperature,
      messages: [
        { role: 'user', content: params.prompt }
      ]
    });

    return {
      content: response.content[0].text,
      provider: 'claude',
      model: 'claude-3-sonnet'
    };
  }

  buildQuestionGenerationPrompt(params) {
    return `Generate ${params.count} ${params.difficulty} level ${params.questionType} questions for the topic "${params.topic}".

Requirements:
- Difficulty: ${params.difficulty}
- Bloom's Taxonomy Level: ${params.bloomsLevel}
- Question Type: ${params.questionType}
- Educational Level: ${params.syllabus?.class || 'High School'}
- Subject: ${params.syllabus?.subject || 'General'}

${params.userPerformance ? `
User Performance Context:
- Accuracy: ${params.userPerformance.accuracy}%
- Weak Areas: ${params.userPerformance.weakTopics?.join(', ') || 'None identified'}
- Strong Areas: ${params.userPerformance.strongTopics?.join(', ') || 'None identified'}
` : ''}

Format each question as JSON:
{
  "question": "Question text",
  "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
  "correctAnswer": "B) Option 2",
  "explanation": {
    "basic": "Simple explanation",
    "intermediate": "Detailed explanation",
    "advanced": "Comprehensive explanation with connections"
  },
  "hints": ["Hint 1", "Hint 2", "Hint 3"],
  "difficulty": "${params.difficulty}",
  "bloomsLevel": "${params.bloomsLevel}",
  "estimatedTime": 120
}

Return only a JSON array of questions.`;
  }

  buildExplanationPrompt(question, userAnswer, difficulty) {
    const isCorrect = userAnswer === question.content.correctAnswer;
    
    return `Provide a ${difficulty} level explanation for this question:

Question: ${question.content.question}
Correct Answer: ${question.content.correctAnswer}
User's Answer: ${userAnswer}
Result: ${isCorrect ? 'Correct' : 'Incorrect'}

${isCorrect ? 
  'Explain why this answer is correct and provide additional insights.' :
  'Explain why the user\'s answer is incorrect and guide them to the correct answer.'
}

Format as JSON:
{
  "isCorrect": ${isCorrect},
  "explanation": "Detailed explanation",
  "keyPoints": ["Point 1", "Point 2", "Point 3"],
  "relatedConcepts": ["Concept 1", "Concept 2"],
  "nextSteps": "What to study next"
}`;
  }

  buildEvaluationPrompt(question, userAnswer, rubric) {
    return `Evaluate this short answer response:

Question: ${question.content.question}
Student Answer: ${userAnswer}
${rubric ? `Rubric: ${JSON.stringify(rubric)}` : ''}

Provide evaluation as JSON:
{
  "score": 85,
  "maxScore": 100,
  "feedback": "Detailed feedback",
  "strengths": ["Strength 1", "Strength 2"],
  "improvements": ["Area 1", "Area 2"],
  "suggestions": "Specific suggestions for improvement"
}`;
  }

  buildStudyPlanPrompt(userProfile, performanceData, goals) {
    return `Create a personalized study plan:

User Profile:
- Level: ${userProfile.level}
- Subjects: ${userProfile.subjects?.join(', ')}
- Study Time Available: ${userProfile.studyTime} hours/week
- Learning Style: ${userProfile.learningStyle}

Performance Data:
- Overall Accuracy: ${performanceData.accuracy}%
- Weak Topics: ${performanceData.weakTopics?.join(', ')}
- Strong Topics: ${performanceData.strongTopics?.join(', ')}

Goals: ${goals}

Format as JSON:
{
  "duration": "4 weeks",
  "weeklyPlan": [
    {
      "week": 1,
      "focus": "Topic focus",
      "dailyTasks": ["Task 1", "Task 2"],
      "goals": "Week goals"
    }
  ],
  "recommendations": ["Rec 1", "Rec 2"],
  "milestones": ["Milestone 1", "Milestone 2"]
}`;
  }

  buildHintPrompt(question, difficulty) {
    return `Generate 3 progressive hints for this ${difficulty} level question:

Question: ${question.content.question}

Hints should:
1. First hint: General direction without giving away the answer
2. Second hint: More specific guidance
3. Third hint: Almost direct guidance but still requires thinking

Format as JSON array: ["Hint 1", "Hint 2", "Hint 3"]`;
  }

  parseQuestionResponse(content) {
    try {
      const questions = JSON.parse(content);
      return Array.isArray(questions) ? questions : [questions];
    } catch (error) {
      loggingService.logError('Failed to parse question response', error);
      throw new Error('Invalid question format received');
    }
  }

  parseExplanationResponse(content) {
    try {
      return JSON.parse(content);
    } catch (error) {
      return {
        isCorrect: false,
        explanation: content,
        keyPoints: [],
        relatedConcepts: [],
        nextSteps: 'Continue practicing similar problems'
      };
    }
  }

  parseEvaluationResponse(content) {
    try {
      return JSON.parse(content);
    } catch (error) {
      return {
        score: 50,
        maxScore: 100,
        feedback: content,
        strengths: [],
        improvements: [],
        suggestions: 'Please review the topic and try again'
      };
    }
  }

  parseStudyPlanResponse(content) {
    try {
      return JSON.parse(content);
    } catch (error) {
      return {
        duration: '4 weeks',
        weeklyPlan: [],
        recommendations: ['Focus on weak areas', 'Practice regularly'],
        milestones: ['Complete weekly assessments']
      };
    }
  }

  parseHintResponse(content) {
    try {
      const hints = JSON.parse(content);
      return Array.isArray(hints) ? hints : [content];
    } catch (error) {
      return [content];
    }
  }

  isRateLimited(provider) {
    const limit = this.rateLimits.get(provider);
    return limit && Date.now() < limit;
  }

  setRateLimit(provider, duration) {
    this.rateLimits.set(provider, Date.now() + duration);
  }

  updateRateLimit(provider) {
    // Update rate limit tracking for successful requests
    // This could be enhanced with more sophisticated rate limiting
  }

  async getProviderStatus() {
    const status = {};
    
    for (const [provider, client] of Object.entries(this.providers)) {
      status[provider] = {
        available: client !== null,
        rateLimited: this.isRateLimited(provider),
        lastUsed: this.rateLimits.get(`${provider}_last_used`) || null
      };
    }
    
    return status;
  }
}

export default new EnhancedLLMService();

import { loggingService } from './loggingService.js';
import { getDb } from '../firebase.js';
import cacheService from './cacheService.js';

class SyllabusCacheService {
    constructor() {
        this.db = getDb();
        this.loggingService = loggingService;
        this.cacheService = cacheService;
        this.CACHE_PREFIX = 'syllabus:';
        this.CACHE_TTL = 3600; // 1 hour
    }

    /**
     * Get a syllabus by ID, using cache if available
     * @param {string} id - Syllabus ID
     * @returns {Promise<Object>} - Syllabus data
     */
    async getSyllabus(id) {
        const cacheKey = `${this.CACHE_PREFIX}${id}`;
        
        try {
            // Try to get from cache first
            const cached = await this.cacheService.get(cacheKey);
            if (cached) {
                this.loggingService.logInfo('Syllabus cache hit', { id });
                return cached;
            }

            // Get from database
            const doc = await this.db.collection('syllabus').doc(id).get();
            if (!doc.exists) {
                return null;
            }

            const syllabus = doc.data();
            
            // Cache the result
            await this.cacheService.set(cacheKey, syllabus, this.CACHE_TTL);
            this.loggingService.logInfo('Syllabus cached', { id });
            
            return syllabus;
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'getSyllabus',
                id
            });
            throw error;
        }
    }

    /**
     * Get multiple syllabi by IDs, using cache where available
     * @param {string[]} ids - Array of syllabus IDs
     * @returns {Promise<Object[]>} - Array of syllabus data
     */
    async getSyllabi(ids) {
        try {
            const results = await Promise.all(ids.map(id => this.getSyllabus(id)));
            return results.filter(Boolean);
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'getSyllabi',
                ids
            });
            throw error;
        }
    }

    /**
     * Update syllabus in cache
     * @param {string} id - Syllabus ID
     * @param {Object} data - Syllabus data
     */
    async updateCache(id, data) {
        const cacheKey = `${this.CACHE_PREFIX}${id}`;
        try {
            await this.cacheService.set(cacheKey, data, this.CACHE_TTL);
            this.loggingService.logInfo('Syllabus cache updated', { id });
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'updateSyllabusCache',
                id
            });
        }
    }

    /**
     * Invalidate syllabus cache
     * @param {string} id - Syllabus ID
     */
    async invalidateCache(id) {
        const cacheKey = `${this.CACHE_PREFIX}${id}`;
        try {
            await this.cacheService.del(cacheKey);
            this.loggingService.logInfo('Syllabus cache invalidated', { id });
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'invalidateSyllabusCache',
                id
            });
        }
    }

    /**
     * Get context for question generation
     * @param {string} syllabusId - Syllabus ID
     * @param {string} unitId - Unit ID
     * @param {string} topicId - Topic ID
     * @returns {Promise<Object>} - Context data
     */
    async getQuestionContext(syllabusId, unitId, topicId) {
        const cacheKey = `${this.CACHE_PREFIX}context:${syllabusId}:${unitId}:${topicId}`;
        
        try {
            // Try cache first
            const cached = await this.cacheService.get(cacheKey);
            if (cached) {
                this.loggingService.logInfo('Question context cache hit', { syllabusId, unitId, topicId });
                return cached;
            }

            // Get syllabus data
            const syllabus = await this.getSyllabus(syllabusId);
            if (!syllabus) {
                throw new Error('Syllabus not found');
            }

            // Find unit and topic
            const unit = syllabus.units.find(u => u.id === unitId);
            if (!unit) {
                throw new Error('Unit not found');
            }

            const topic = unit.topics.find(t => t.id === topicId);
            if (!topic) {
                throw new Error('Topic not found');
            }

            // Build context
            const context = {
                syllabus: {
                    name: syllabus.name,
                    subject: syllabus.subject,
                    level: syllabus.level
                },
                unit: {
                    name: unit.name,
                    order: unit.order
                },
                topic: {
                    name: topic.name,
                    learning_objectives: topic.learning_objectives,
                    keywords: topic.keywords
                }
            };

            // Cache context
            await this.cacheService.set(cacheKey, context, this.CACHE_TTL);
            this.loggingService.logInfo('Question context cached', { syllabusId, unitId, topicId });

            return context;
        } catch (error) {
            this.loggingService.logError(error, {
                operation: 'getQuestionContext',
                syllabusId,
                unitId,
                topicId
            });
            throw error;
        }
    }
}

const syllabusCacheService = new SyllabusCacheService();
export default syllabusCacheService;

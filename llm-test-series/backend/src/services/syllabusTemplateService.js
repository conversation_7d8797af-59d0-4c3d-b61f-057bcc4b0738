import xlsx from 'xlsx';
import loggingService from './loggingService.js';

class SyllabusTemplateService {
    constructor() {
        this.loggingService = loggingService;
    }

    /**
     * Generate Excel template for syllabus bulk upload
     * @returns {Buffer} Excel file buffer
     */
    generateTemplate() {
        try {
            const workbook = xlsx.utils.book_new();

            // Create sample data
            const sampleData = [{
                name: 'Sample Physics Syllabus',
                subject: 'Physics',
                level: 'undergraduate',
                description: 'Comprehensive physics syllabus covering mechanics to quantum physics',
                units: JSON.stringify([{
                    name: 'Classical Mechanics',
                    weight: 30,
                    topics: [{
                        name: 'Newton\'s Laws',
                        weight: 40,
                        learning_objectives: ['Understand Newton\'s laws', 'Apply forces in problems'],
                        keywords: ['force', 'motion', 'acceleration']
                    }, {
                        name: 'Conservation Laws',
                        weight: 60,
                        learning_objectives: ['Understand energy conservation', 'Apply momentum conservation'],
                        keywords: ['energy', 'momentum', 'conservation']
                    }]
                }])
            }];

            // Create worksheet
            const ws = xlsx.utils.json_to_sheet(sampleData);

            // Add column widths
            ws['!cols'] = [
                { wch: 30 }, // name
                { wch: 20 }, // subject
                { wch: 15 }, // level
                { wch: 40 }, // description
                { wch: 60 }  // units
            ];

            // Add validation
            ws['!dataValidations'] = [{
                sqref: 'C2:C1000',
                validation: {
                    type: 'list',
                    formula1: '"high_school,undergraduate,graduate"',
                    allowBlank: false,
                    showErrorMessage: true,
                    errorTitle: 'Invalid Level',
                    error: 'Please select from the dropdown list'
                }
            }];

            // Add the worksheet to workbook
            xlsx.utils.book_append_sheet(workbook, ws, 'Syllabus Template');

            // Add instructions sheet
            const instructions = [
                ['Syllabus Bulk Upload Instructions'],
                [''],
                ['1. Each row represents one syllabus'],
                ['2. Required fields: name, subject, level'],
                ['3. Level must be one of: high_school, undergraduate, graduate'],
                ['4. Units must be a valid JSON array with:'],
                ['   - name: Unit name'],
                ['   - weight: Unit weight (0-100, must sum to 100)'],
                ['   - topics: Array of topics'],
                ['5. Each topic must have:'],
                ['   - name: Topic name'],
                ['   - weight: Topic weight (0-100, must sum to 100 within unit)'],
                ['   - learning_objectives: Array of learning objectives'],
                ['   - keywords: Array of keywords'],
                [''],
                ['See the sample data in the template sheet for reference.']
            ];

            const wsInstructions = xlsx.utils.aoa_to_sheet(instructions);
            xlsx.utils.book_append_sheet(workbook, wsInstructions, 'Instructions');

            // Write to buffer
            return xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        } catch (error) {
            this.loggingService.logError('Failed to generate syllabus template', { error });
            throw error;
        }
    }

    /**
     * Parse Excel file into syllabus objects
     * @param {Buffer} buffer - Excel file buffer
     * @returns {Array} Array of syllabus objects
     */
    parseExcelFile(buffer) {
        try {
            const workbook = xlsx.read(buffer);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            const data = xlsx.utils.sheet_to_json(worksheet);

            return data.map(row => ({
                ...row,
                units: JSON.parse(row.units || '[]')
            }));
        } catch (error) {
            this.loggingService.logError('Failed to parse Excel file', { error });
            throw error;
        }
    }
}

const syllabusTemplateService = new SyllabusTemplateService();
export default syllabusTemplateService;

import axios from 'axios';
import loggingService from './loggingService.js';

/**
 * Enhanced AI Service with multiple LLM provider support
 * Supports OpenAI, Anthropic, Google, and local models
 */
class EnhancedAIService {
  constructor() {
    this.providers = {
      openai: {
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        enabled: !!process.env.OPENAI_API_KEY
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY,
        baseURL: 'https://api.anthropic.com/v1',
        model: 'claude-3-sonnet-20240229',
        enabled: !!process.env.ANTHROPIC_API_KEY
      },
      google: {
        apiKey: process.env.GEMINI_API_KEY,
        baseURL: 'https://generativelanguage.googleapis.com/v1beta',
        model: 'gemini-1.5-flash',
        enabled: !!process.env.GEMINI_API_KEY
      },
      local: {
        baseURL: process.env.LOCAL_LLM_URL || 'http://localhost:11434',
        model: process.env.LOCAL_LLM_MODEL || 'llama2',
        enabled: !!process.env.LOCAL_LLM_URL
      }
    };

    this.currentProvider = this.selectBestProvider();
    this.fallbackProviders = this.getFallbackProviders();
    
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      providerUsage: {},
      lastReset: new Date()
    };
  }

  selectBestProvider() {
    // Priority order: Google -> OpenAI -> Anthropic -> Local
    const priorities = ['google', 'openai', 'anthropic', 'local'];
    
    for (const provider of priorities) {
      if (this.providers[provider].enabled) {
        console.log(`Using AI provider: ${provider}`);
        return provider;
      }
    }
    
    console.warn('No AI providers configured, using mock responses');
    return 'mock';
  }

  getFallbackProviders() {
    return Object.keys(this.providers)
      .filter(p => p !== this.currentProvider && this.providers[p].enabled);
  }

  async generateQuestions(params) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Check if Google AI is available and enabled
      if (this.providers.google.enabled && this.providers.google.apiKey) {
        console.log('Using Google AI (Gemini) for question generation...');
        const prompt = this.buildQuestionPrompt(params);

        try {
          const response = await this.callProvider('google', prompt, params);
          const questions = this.parseQuestions(response, params);

          // Update metrics
          const responseTime = Date.now() - startTime;
          this.updateMetrics(true, responseTime);

          await loggingService.logLlmCall('question_generation', {
            provider: 'google',
            success: true,
            responseTime,
            questionCount: questions.length,
            subject: params.subject,
            level: params.level
          });

          console.log(`✅ Generated ${questions.length} questions using Google AI`);
          return questions;
        } catch (error) {
          console.warn('Google AI failed, falling back to built-in questions:', error.message);
        }
      }

      // Fallback to built-in questions
      console.log('Using built-in question templates...');
      const questions = this.generateMockQuestions(params);

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateMetrics(true, responseTime);

      await loggingService.logLlmCall('question_generation', {
        provider: 'built-in',
        success: true,
        responseTime,
        questionCount: questions.length,
        subject: params.subject,
        level: params.level
      });

      return questions;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateMetrics(false, responseTime);

      await loggingService.logError(error, {
        operation: 'generateQuestions',
        provider: 'built-in',
        params
      });

      // Return basic fallback questions
      return this.generateBasicFallbackQuestions(params);
    }
  }

  async callProvider(provider, prompt, params) {
    if (provider === 'mock') {
      return this.generateMockResponse(params);
    }

    const config = this.providers[provider];
    const headers = this.getProviderHeaders(provider);
    const payload = this.buildProviderPayload(provider, prompt, params);

    const response = await axios.post(
      `${config.baseURL}${this.getProviderEndpoint(provider)}`,
      payload,
      { headers, timeout: 30000 }
    );

    return this.extractResponseText(provider, response.data);
  }

  getProviderHeaders(provider) {
    const config = this.providers[provider];
    
    switch (provider) {
      case 'openai':
        return {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        };
      case 'anthropic':
        return {
          'x-api-key': config.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        };
      case 'google':
        return {
          'Content-Type': 'application/json'
        };
      case 'local':
        return {
          'Content-Type': 'application/json'
        };
      default:
        return {};
    }
  }

  getProviderEndpoint(provider) {
    switch (provider) {
      case 'openai':
        return '/chat/completions';
      case 'anthropic':
        return '/messages';
      case 'google':
        return `/models/${this.providers[provider].model}:generateContent?key=${this.providers[provider].apiKey}`;
      case 'local':
        return '/api/generate';
      default:
        return '';
    }
  }

  buildProviderPayload(provider, prompt, params) {
    const config = this.providers[provider];
    
    switch (provider) {
      case 'openai':
        return {
          model: config.model,
          messages: [
            { role: 'system', content: 'You are an expert educator creating high-quality multiple choice questions.' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000
        };
        
      case 'anthropic':
        return {
          model: config.model,
          max_tokens: 2000,
          messages: [
            { role: 'user', content: prompt }
          ]
        };
        
      case 'google':
        return {
          contents: [{
            parts: [{ text: prompt }]
          }],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2000
          }
        };
        
      case 'local':
        return {
          model: config.model,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.7,
            num_predict: 2000
          }
        };
        
      default:
        return { prompt };
    }
  }

  extractResponseText(provider, responseData) {
    switch (provider) {
      case 'openai':
        return responseData.choices[0].message.content;
      case 'anthropic':
        return responseData.content[0].text;
      case 'google':
        return responseData.candidates[0].content.parts[0].text;
      case 'local':
        return responseData.response;
      default:
        return responseData.toString();
    }
  }

  buildQuestionPrompt(params) {
    const { subject, level, numQuestions = 1, topic, syllabus_id } = params;
    
    return `Generate ${numQuestions} high-quality multiple choice question(s) for ${subject} at ${level} level.
${topic ? `Focus on the topic: ${topic}` : ''}
${syllabus_id ? `Follow syllabus requirements for ID: ${syllabus_id}` : ''}

Requirements:
1. Create clear, unambiguous questions
2. Provide exactly 4 options (A, B, C, D)
3. Include comprehensive explanations
4. Ensure factual accuracy
5. Vary difficulty and cognitive levels

Format as JSON array:
[{
  "question": "Question text",
  "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
  "correct_answer": "A",
  "explanation": "Detailed explanation",
  "difficulty": "medium",
  "keywords": ["keyword1", "keyword2"],
  "cognitive_level": "application",
  "source_tag": "AI Generated - ${subject}"
}]`;
  }

  parseQuestions(response, params) {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.replace(/```json\s*|\s*```/g, '').trim();

      // Try to extract JSON from response (array or single object)
      let jsonMatch = cleanResponse.match(/\[[\s\S]*\]/) || cleanResponse.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);

        // Handle both single object and array responses
        const questions = Array.isArray(parsed) ? parsed : [parsed];

        console.log(`✅ Successfully parsed ${questions.length} question(s) from AI response`);
        return questions.map(q => this.normalizeQuestion(q, params));
      }
    } catch (error) {
      console.warn('Failed to parse AI response as JSON:', error.message);
      console.warn('Raw response:', response);
    }

    // Fallback to mock questions
    console.log('Falling back to built-in questions due to parsing failure');
    return this.generateMockQuestions(params);
  }

  normalizeQuestion(question, params) {
    return {
      question: question.question || 'Generated question',
      options: question.options || ['A) Option 1', 'B) Option 2', 'C) Option 3', 'D) Option 4'],
      correct_answer: question.correct_answer || question.correctAnswer || 'A',
      explanation: question.explanation || 'AI-generated explanation',
      keywords: question.keywords || [params.subject],
      cognitive_level: question.cognitive_level || question.cognitiveLevel || 'understand',
      source_tag: question.source_tag || `AI Generated - ${params.subject}`,
      quality_score: 0.85,
      difficulty: question.difficulty || 'medium'
    };
  }

  generateMockQuestions(params) {
    const { subject, numQuestions = 1, level } = params;
    const questions = [];

    // Get question templates for the subject
    const templates = this.getQuestionTemplates(subject, level);

    for (let i = 0; i < numQuestions; i++) {
      const template = templates[i % templates.length];
      questions.push({
        ...template,
        id: `q_${Date.now()}_${i}`,
        generated_at: new Date().toISOString(),
        source_tag: `Built-in Question Bank - ${subject}`,
        quality_score: 0.9
      });
    }

    return questions;
  }

  getQuestionTemplates(subject, level) {
    const questionBank = {
      Physics: {
        Beginner: [
          {
            question: "What is the SI unit of force?",
            options: [
              "A) Newton (N)",
              "B) Joule (J)",
              "C) Watt (W)",
              "D) Pascal (Pa)"
            ],
            correct_answer: "A",
            explanation: "The SI unit of force is the Newton (N), named after Sir Isaac Newton. One Newton is defined as the force required to accelerate a mass of one kilogram at a rate of one meter per second squared (1 N = 1 kg⋅m/s²).",
            keywords: ["force", "SI unit", "newton", "physics"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "Which of the following best describes velocity?",
            options: [
              "A) Speed with direction",
              "B) Distance traveled per unit time",
              "C) The rate of change of acceleration",
              "D) Force applied to an object"
            ],
            correct_answer: "A",
            explanation: "Velocity is a vector quantity that describes both the speed and direction of an object's motion. Unlike speed (which is scalar), velocity includes directional information, making it essential for describing motion in physics.",
            keywords: ["velocity", "vector", "speed", "direction"],
            cognitive_level: "understand",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "A ball is thrown horizontally from a height of 20m. If air resistance is negligible, what determines the time it takes to hit the ground?",
            options: [
              "A) Only the initial horizontal velocity",
              "B) Only the height from which it's thrown",
              "C) Both height and horizontal velocity",
              "D) The mass of the ball"
            ],
            correct_answer: "B",
            explanation: "In projectile motion with negligible air resistance, the time of flight depends only on the vertical motion. Using h = ½gt², where h=20m and g=9.8m/s², we get t = √(2h/g) ≈ 2.02 seconds. The horizontal velocity affects the range but not the time of flight.",
            keywords: ["projectile motion", "time of flight", "gravity", "kinematics"],
            cognitive_level: "apply",
            difficulty: "medium"
          }
        ]
      },
      Mathematics: {
        Beginner: [
          {
            question: "What is the value of 2³ + 3²?",
            options: [
              "A) 17",
              "B) 13",
              "C) 15",
              "D) 11"
            ],
            correct_answer: "A",
            explanation: "First calculate the exponents: 2³ = 2×2×2 = 8, and 3² = 3×3 = 9. Then add them: 8 + 9 = 17. Remember that exponents are calculated before addition according to the order of operations (PEMDAS).",
            keywords: ["exponents", "order of operations", "arithmetic"],
            cognitive_level: "apply",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "If f(x) = 2x² - 3x + 1, what is f(2)?",
            options: [
              "A) 3",
              "B) 5",
              "C) 7",
              "D) 9"
            ],
            correct_answer: "A",
            explanation: "Substitute x = 2 into the function: f(2) = 2(2)² - 3(2) + 1 = 2(4) - 6 + 1 = 8 - 6 + 1 = 3. This demonstrates function evaluation, a fundamental concept in algebra.",
            keywords: ["function evaluation", "quadratic function", "substitution"],
            cognitive_level: "apply",
            difficulty: "medium"
          }
        ]
      },
      Chemistry: {
        Beginner: [
          {
            question: "What is the chemical symbol for gold?",
            options: [
              "A) Au",
              "B) Ag",
              "C) Go",
              "D) Gd"
            ],
            correct_answer: "A",
            explanation: "The chemical symbol for gold is Au, derived from the Latin word 'aurum' meaning gold. This is why many chemical symbols don't match their English names - they come from Latin or Greek origins.",
            keywords: ["chemical symbols", "gold", "periodic table"],
            cognitive_level: "remember",
            difficulty: "easy"
          }
        ]
      },
      Biology: {
        Beginner: [
          {
            question: "Which organelle is known as the 'powerhouse of the cell'?",
            options: [
              "A) Mitochondria",
              "B) Nucleus",
              "C) Ribosome",
              "D) Endoplasmic reticulum"
            ],
            correct_answer: "A",
            explanation: "Mitochondria are called the 'powerhouse of the cell' because they produce ATP (adenosine triphosphate), the main energy currency of cells, through cellular respiration. They have their own DNA and are thought to have evolved from ancient bacteria.",
            keywords: ["mitochondria", "ATP", "cellular respiration", "organelles"],
            cognitive_level: "remember",
            difficulty: "easy"
          }
        ]
      }
    };

    // Get questions for the subject and level, with fallback
    const subjectQuestions = questionBank[subject] || questionBank.Physics;
    const levelQuestions = subjectQuestions[level] || subjectQuestions.Beginner || subjectQuestions[Object.keys(subjectQuestions)[0]];

    return levelQuestions || [{
      question: `Sample ${subject} question at ${level} level`,
      options: ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      correct_answer: "A",
      explanation: `This is a sample explanation for ${subject} at ${level} level.`,
      keywords: [subject.toLowerCase(), level.toLowerCase()],
      cognitive_level: "understand",
      difficulty: level.toLowerCase()
    }];
  }

  generateMockResponse(params) {
    const questions = this.generateMockQuestions(params);
    return JSON.stringify(questions, null, 2);
  }

  updateMetrics(success, responseTime) {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }
    
    // Update average response time
    const totalRequests = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
    
    // Update provider usage
    if (!this.metrics.providerUsage[this.currentProvider]) {
      this.metrics.providerUsage[this.currentProvider] = 0;
    }
    this.metrics.providerUsage[this.currentProvider]++;
  }

  getMetrics() {
    return {
      ...this.metrics,
      currentProvider: this.currentProvider,
      availableProviders: Object.keys(this.providers).filter(p => this.providers[p].enabled),
      successRate: this.metrics.totalRequests > 0 
        ? (this.metrics.successfulRequests / this.metrics.totalRequests) * 100 
        : 0
    };
  }

  async generateExplanation(question, userAnswer, correctAnswer) {
    const prompt = `Explain why the correct answer to this question is "${correctAnswer}" and why "${userAnswer}" is incorrect:

Question: ${question}

Provide a clear, educational explanation that helps the student understand the concept.`;

    try {
      const response = await this.callProvider(this.currentProvider, prompt, {});
      return this.extractResponseText(this.currentProvider, response);
    } catch (error) {
      return `The correct answer is ${correctAnswer}. This demonstrates key concepts in the subject matter.`;
    }
  }

  async evaluateAnswer(question, userAnswer, context = {}) {
    const prompt = `Evaluate this answer and provide detailed feedback:

Question: ${question}
Student Answer: ${userAnswer}
Context: ${JSON.stringify(context)}

Provide:
1. Whether the answer is correct
2. Detailed explanation
3. Suggestions for improvement
4. Related concepts to study`;

    try {
      const response = await this.callProvider(this.currentProvider, prompt, {});
      return {
        isCorrect: userAnswer.toLowerCase().includes('correct'),
        feedback: this.extractResponseText(this.currentProvider, response),
        score: Math.random() * 100 // Mock scoring
      };
    } catch (error) {
      return {
        isCorrect: false,
        feedback: 'Unable to evaluate answer at this time.',
        score: 0
      };
    }
  }

  generateBasicFallbackQuestions(params) {
    const { subject = 'General', numQuestions = 1, level = 'Beginner' } = params;
    const questions = [];

    for (let i = 0; i < numQuestions; i++) {
      questions.push({
        question: `Basic ${subject} question ${i + 1} (${level} level)`,
        options: [
          "A) First option",
          "B) Second option",
          "C) Third option",
          "D) Fourth option"
        ],
        correct_answer: "A",
        explanation: `This is a basic fallback explanation for ${subject} at ${level} level.`,
        keywords: [subject.toLowerCase(), level.toLowerCase(), "fallback"],
        cognitive_level: "remember",
        difficulty: level.toLowerCase(),
        source_tag: `Fallback - ${subject}`,
        quality_score: 0.6,
        id: `fallback_${Date.now()}_${i}`,
        generated_at: new Date().toISOString()
      });
    }

    return questions;
  }
}

export default new EnhancedAIService();

import axios from 'axios';
import loggingService from './loggingService.js';

/**
 * Enhanced AI Service with multiple LLM provider support
 * Supports OpenAI, Anthropic, Google, and local models
 */
class EnhancedAIService {
  constructor() {
    this.providers = {
      openai: {
        apiKey: process.env.OPENAI_API_KEY,
        baseURL: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        enabled: !!process.env.OPENAI_API_KEY
      },
      anthropic: {
        apiKey: process.env.ANTHROPIC_API_KEY,
        baseURL: 'https://api.anthropic.com/v1',
        model: 'claude-3-sonnet-20240229',
        enabled: !!process.env.ANTHROPIC_API_KEY
      },
      google: {
        apiKey: process.env.GEMINI_API_KEY,
        baseURL: 'https://generativelanguage.googleapis.com/v1beta',
        model: 'gemini-1.5-flash',
        enabled: !!process.env.GEMINI_API_KEY
      },
      local: {
        baseURL: process.env.LOCAL_LLM_URL || 'http://localhost:11434',
        model: process.env.LOCAL_LLM_MODEL || 'llama2',
        enabled: !!process.env.LOCAL_LLM_URL
      }
    };

    this.currentProvider = this.selectBestProvider();
    this.fallbackProviders = this.getFallbackProviders();
    
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      providerUsage: {},
      lastReset: new Date()
    };
  }

  selectBestProvider() {
    // Priority order: Google -> OpenAI -> Anthropic -> Local
    const priorities = ['google', 'openai', 'anthropic', 'local'];
    
    for (const provider of priorities) {
      if (this.providers[provider].enabled) {
        console.log(`Using AI provider: ${provider}`);
        return provider;
      }
    }
    
    console.warn('No AI providers configured, using mock responses');
    return 'mock';
  }

  getFallbackProviders() {
    return Object.keys(this.providers)
      .filter(p => p !== this.currentProvider && this.providers[p].enabled);
  }

  async generateQuestions(params) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Check if Google AI is available and enabled
      if (this.providers.google.enabled && this.providers.google.apiKey) {
        console.log('Using Google AI (Gemini) for question generation...');
        const prompt = this.buildQuestionPrompt(params);

        try {
          const response = await this.callProvider('google', prompt, params);
          const questions = this.parseQuestions(response, params);

          // Update metrics
          const responseTime = Date.now() - startTime;
          this.updateMetrics(true, responseTime);

          await loggingService.logLlmCall('question_generation', {
            provider: 'google',
            success: true,
            responseTime,
            questionCount: questions.length,
            subject: params.subject,
            level: params.level
          });

          console.log(`✅ Generated ${questions.length} questions using Google AI`);
          return questions;
        } catch (error) {
          console.warn('Google AI failed, falling back to built-in questions:', error.message);
        }
      }

      // Fallback to built-in questions
      console.log('Using built-in question templates...');
      const questions = this.generateMockQuestions(params);

      // Update metrics
      const responseTime = Date.now() - startTime;
      this.updateMetrics(true, responseTime);

      await loggingService.logLlmCall('question_generation', {
        provider: 'built-in',
        success: true,
        responseTime,
        questionCount: questions.length,
        subject: params.subject,
        level: params.level
      });

      return questions;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateMetrics(false, responseTime);

      await loggingService.logError(error, {
        operation: 'generateQuestions',
        provider: 'built-in',
        params
      });

      // Return basic fallback questions
      return this.generateBasicFallbackQuestions(params);
    }
  }

  async callProvider(provider, prompt, params) {
    if (provider === 'mock') {
      return this.generateMockResponse(params);
    }

    const config = this.providers[provider];
    const headers = this.getProviderHeaders(provider);
    const payload = this.buildProviderPayload(provider, prompt, params);

    const response = await axios.post(
      `${config.baseURL}${this.getProviderEndpoint(provider)}`,
      payload,
      { headers, timeout: 30000 }
    );

    return this.extractResponseText(provider, response.data);
  }

  getProviderHeaders(provider) {
    const config = this.providers[provider];
    
    switch (provider) {
      case 'openai':
        return {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        };
      case 'anthropic':
        return {
          'x-api-key': config.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        };
      case 'google':
        return {
          'Content-Type': 'application/json'
        };
      case 'local':
        return {
          'Content-Type': 'application/json'
        };
      default:
        return {};
    }
  }

  getProviderEndpoint(provider) {
    switch (provider) {
      case 'openai':
        return '/chat/completions';
      case 'anthropic':
        return '/messages';
      case 'google':
        return `/models/${this.providers[provider].model}:generateContent?key=${this.providers[provider].apiKey}`;
      case 'local':
        return '/api/generate';
      default:
        return '';
    }
  }

  buildProviderPayload(provider, prompt, params) {
    const config = this.providers[provider];
    
    switch (provider) {
      case 'openai':
        return {
          model: config.model,
          messages: [
            { role: 'system', content: 'You are an expert educator creating high-quality multiple choice questions.' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000
        };
        
      case 'anthropic':
        return {
          model: config.model,
          max_tokens: 2000,
          messages: [
            { role: 'user', content: prompt }
          ]
        };
        
      case 'google':
        return {
          contents: [{
            parts: [{ text: prompt }]
          }],
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2000
          }
        };
        
      case 'local':
        return {
          model: config.model,
          prompt: prompt,
          stream: false,
          options: {
            temperature: 0.7,
            num_predict: 2000
          }
        };
        
      default:
        return { prompt };
    }
  }

  extractResponseText(provider, responseData) {
    switch (provider) {
      case 'openai':
        return responseData.choices[0].message.content;
      case 'anthropic':
        return responseData.content[0].text;
      case 'google':
        return responseData.candidates[0].content.parts[0].text;
      case 'local':
        return responseData.response;
      default:
        return responseData.toString();
    }
  }

  buildQuestionPrompt(params) {
    const { subject, level, numQuestions = 1, topic, syllabus_id } = params;

    // Define difficulty-specific requirements
    const difficultySpecs = {
      'Beginner': {
        description: 'Basic concepts, definitions, and simple applications',
        cognitive: 'Knowledge and Comprehension',
        complexity: 'Simple, straightforward questions with clear answers'
      },
      'Intermediate': {
        description: 'Applied knowledge, problem-solving, and analysis',
        cognitive: 'Application and Analysis',
        complexity: 'Multi-step problems requiring understanding of relationships'
      },
      'Advanced': {
        description: 'Complex analysis, synthesis, and evaluation',
        cognitive: 'Synthesis and Evaluation',
        complexity: 'Advanced problems requiring deep understanding and critical thinking'
      }
    };

    const spec = difficultySpecs[level] || difficultySpecs['Beginner'];

    return `Generate ${numQuestions} UNIQUE, DISTINCT multiple choice questions for ${subject} at ${level} level.

CRITICAL REQUIREMENTS:
- Each question MUST be completely different and unique
- NO duplicate or similar questions
- Difficulty level: ${level} (${spec.description})
- Cognitive level: ${spec.cognitive}
- Complexity: ${spec.complexity}
${topic ? `- Focus specifically on: ${topic}` : ''}
${syllabus_id ? `- Follow syllabus ID: ${syllabus_id}` : ''}

DIFFICULTY GUIDELINES FOR ${level.toUpperCase()}:
${level === 'Beginner' ? `
- Use basic terminology and concepts
- Test fundamental knowledge and definitions
- Simple calculations or direct applications
- Clear, unambiguous correct answers` : ''}
${level === 'Intermediate' ? `
- Require application of concepts to new situations
- Include multi-step problem solving
- Test understanding of relationships between concepts
- May involve calculations or analysis` : ''}
${level === 'Advanced' ? `
- Require synthesis of multiple concepts
- Complex problem-solving scenarios
- Critical thinking and evaluation
- Advanced calculations or theoretical understanding` : ''}

FORMAT: Return ONLY a JSON array with ${numQuestions} unique questions:
[{
  "question": "Unique question text appropriate for ${level} level",
  "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
  "correct_answer": "A",
  "explanation": "Detailed explanation showing why this answer is correct for ${level} level",
  "difficulty": "${level.toLowerCase()}",
  "keywords": ["relevant", "keywords"],
  "cognitive_level": "${spec.cognitive.toLowerCase()}",
  "source_tag": "AI Generated - ${subject} ${level}"
}]

ENSURE: All ${numQuestions} questions are completely different and appropriate for ${level} level in ${subject}.`;
  }

  parseQuestions(response, params) {
    try {
      // Remove markdown code blocks if present
      let cleanResponse = response.replace(/```json\s*|\s*```/g, '').trim();

      // Try to extract JSON from response (array or single object)
      let jsonMatch = cleanResponse.match(/\[[\s\S]*\]/) || cleanResponse.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);

        // Handle both single object and array responses
        const questions = Array.isArray(parsed) ? parsed : [parsed];

        console.log(`✅ Successfully parsed ${questions.length} question(s) from AI response`);
        return questions.map(q => this.normalizeQuestion(q, params));
      }
    } catch (error) {
      console.warn('Failed to parse AI response as JSON:', error.message);
      console.warn('Raw response:', response);
    }

    // Fallback to mock questions
    console.log('Falling back to built-in questions due to parsing failure');
    return this.generateMockQuestions(params);
  }

  normalizeQuestion(question, params) {
    return {
      question: question.question || 'Generated question',
      options: question.options || ['A) Option 1', 'B) Option 2', 'C) Option 3', 'D) Option 4'],
      correct_answer: question.correct_answer || question.correctAnswer || 'A',
      explanation: question.explanation || 'AI-generated explanation',
      keywords: question.keywords || [params.subject],
      cognitive_level: question.cognitive_level || question.cognitiveLevel || 'understand',
      source_tag: question.source_tag || `AI Generated - ${params.subject}`,
      quality_score: 0.85,
      difficulty: question.difficulty || 'medium'
    };
  }

  generateMockQuestions(params) {
    const { subject, numQuestions = 1, level } = params;
    const questions = [];

    // Get question templates for the subject and level
    const templates = this.getQuestionTemplates(subject, level);

    // If we need more questions than templates, generate variations
    const availableTemplates = [...templates];

    for (let i = 0; i < numQuestions; i++) {
      let template;

      if (availableTemplates.length > 0) {
        // Use unique templates first
        const randomIndex = Math.floor(Math.random() * availableTemplates.length);
        template = availableTemplates.splice(randomIndex, 1)[0];
      } else {
        // If we've used all templates, create variations
        const baseTemplate = templates[Math.floor(Math.random() * templates.length)];
        template = this.createQuestionVariation(baseTemplate, i, subject, level);
      }

      questions.push({
        ...template,
        id: `q_${Date.now()}_${Math.random().toString(36).substring(2, 8)}_${i}`,
        generated_at: new Date().toISOString(),
        source_tag: `Built-in Question Bank - ${subject} ${level}`,
        quality_score: 0.9,
        difficulty: level.toLowerCase(),
        cognitive_level: this.getCognitiveLevelForDifficulty(level)
      });
    }

    return questions;
  }

  createQuestionVariation(baseTemplate, index, subject, level) {
    // Create variations of questions to avoid duplicates
    const variations = {
      Physics: {
        force: [
          "What is the SI unit of force?",
          "Which unit is used to measure force in the International System?",
          "In physics, force is measured using which standard unit?",
          "The standard international unit for measuring force is?"
        ],
        motion: [
          "What is Newton's first law of motion?",
          "Which law describes an object at rest staying at rest?",
          "Newton's law of inertia is also known as?",
          "The principle that objects resist changes in motion is called?"
        ]
      }
    };

    // Try to create a variation, otherwise return the base template with modified wording
    const subjectVariations = variations[subject];
    if (subjectVariations) {
      const questionType = Object.keys(subjectVariations)[index % Object.keys(subjectVariations).length];
      const variationList = subjectVariations[questionType];
      const variationQuestion = variationList[index % variationList.length];

      return {
        ...baseTemplate,
        question: variationQuestion
      };
    }

    // Fallback: modify the original question slightly
    return {
      ...baseTemplate,
      question: `${baseTemplate.question} (Variation ${index + 1})`
    };
  }

  getCognitiveLevelForDifficulty(level) {
    const mapping = {
      'Beginner': 'knowledge',
      'Intermediate': 'application',
      'Advanced': 'synthesis'
    };
    return mapping[level] || 'knowledge';
  }

  getQuestionTemplates(subject, level) {
    const questionBank = {
      Physics: {
        Beginner: [
          {
            question: "What is the SI unit of force?",
            options: [
              "A) Newton (N)",
              "B) Joule (J)",
              "C) Watt (W)",
              "D) Pascal (Pa)"
            ],
            correct_answer: "A",
            explanation: "The SI unit of force is the Newton (N), named after Sir Isaac Newton. One Newton is defined as the force required to accelerate a mass of one kilogram at a rate of one meter per second squared (1 N = 1 kg⋅m/s²).",
            keywords: ["force", "SI unit", "newton", "physics"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "Which of the following best describes velocity?",
            options: [
              "A) Speed with direction",
              "B) Distance traveled per unit time",
              "C) The rate of change of acceleration",
              "D) Force applied to an object"
            ],
            correct_answer: "A",
            explanation: "Velocity is a vector quantity that describes both the speed and direction of an object's motion. Unlike speed (which is scalar), velocity includes directional information, making it essential for describing motion in physics.",
            keywords: ["velocity", "vector", "speed", "direction"],
            cognitive_level: "understand",
            difficulty: "easy"
          },
          {
            question: "What is Newton's first law of motion?",
            options: [
              "A) F = ma",
              "B) An object at rest stays at rest unless acted upon by a force",
              "C) For every action there is an equal and opposite reaction",
              "D) Energy cannot be created or destroyed"
            ],
            correct_answer: "B",
            explanation: "Newton's first law, also known as the law of inertia, states that an object at rest will remain at rest, and an object in motion will remain in motion at constant velocity, unless acted upon by an unbalanced external force.",
            keywords: ["newton", "inertia", "motion", "force"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "Which type of energy does a moving car possess?",
            options: [
              "A) Potential energy only",
              "B) Kinetic energy only",
              "C) Both kinetic and potential energy",
              "D) Neither kinetic nor potential energy"
            ],
            correct_answer: "B",
            explanation: "A moving car possesses kinetic energy, which is the energy of motion. The formula for kinetic energy is KE = ½mv², where m is mass and v is velocity. If the car is on level ground, it has minimal gravitational potential energy.",
            keywords: ["kinetic energy", "motion", "energy"],
            cognitive_level: "understand",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "A ball is thrown horizontally from a height of 20m. If air resistance is negligible, what determines the time it takes to hit the ground?",
            options: [
              "A) Only the initial horizontal velocity",
              "B) Only the height from which it's thrown",
              "C) Both height and horizontal velocity",
              "D) The mass of the ball"
            ],
            correct_answer: "B",
            explanation: "In projectile motion with negligible air resistance, the time of flight depends only on the vertical motion. Using h = ½gt², where h=20m and g=9.8m/s², we get t = √(2h/g) ≈ 2.02 seconds. The horizontal velocity affects the range but not the time of flight.",
            keywords: ["projectile motion", "time of flight", "gravity", "kinematics"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "A 2kg object accelerates at 3 m/s². What is the net force acting on it?",
            options: [
              "A) 5 N",
              "B) 6 N",
              "C) 1.5 N",
              "D) 0.67 N"
            ],
            correct_answer: "B",
            explanation: "Using Newton's second law, F = ma, where m = 2kg and a = 3 m/s². Therefore, F = 2kg × 3 m/s² = 6 N. This demonstrates the direct relationship between force, mass, and acceleration.",
            keywords: ["newton's second law", "force", "acceleration", "mass"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "If the frequency of a wave is 50 Hz and its wavelength is 4 m, what is its speed?",
            options: [
              "A) 12.5 m/s",
              "B) 200 m/s",
              "C) 46 m/s",
              "D) 54 m/s"
            ],
            correct_answer: "B",
            explanation: "Wave speed is calculated using v = fλ, where v is speed, f is frequency, and λ is wavelength. Therefore, v = 50 Hz × 4 m = 200 m/s. This relationship is fundamental in wave physics.",
            keywords: ["wave speed", "frequency", "wavelength", "waves"],
            cognitive_level: "apply",
            difficulty: "medium"
          }
        ],
        Advanced: [
          {
            question: "A particle undergoes simple harmonic motion with amplitude A and angular frequency ω. What is the maximum acceleration?",
            options: [
              "A) Aω",
              "B) Aω²",
              "C) A/ω",
              "D) A/ω²"
            ],
            correct_answer: "B",
            explanation: "In simple harmonic motion, acceleration a = -ω²x, where x is displacement. Maximum acceleration occurs at maximum displacement (amplitude A), so a_max = ω²A. The negative sign indicates direction opposite to displacement.",
            keywords: ["simple harmonic motion", "acceleration", "amplitude", "angular frequency"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "In a photoelectric effect experiment, increasing the intensity of light while keeping frequency constant will:",
            options: [
              "A) Increase the maximum kinetic energy of photoelectrons",
              "B) Increase the number of photoelectrons emitted",
              "C) Decrease the work function of the material",
              "D) Change the threshold frequency"
            ],
            correct_answer: "B",
            explanation: "According to Einstein's photoelectric equation, E = hf - φ, the maximum kinetic energy depends only on frequency (f) and work function (φ). Increasing intensity increases the number of photons, thus increasing the number of photoelectrons emitted, but not their individual energies.",
            keywords: ["photoelectric effect", "intensity", "frequency", "photoelectrons"],
            cognitive_level: "analyze",
            difficulty: "hard"
          }
        ]
      },
      Mathematics: {
        Beginner: [
          {
            question: "What is the value of 2³ + 3²?",
            options: [
              "A) 17",
              "B) 13",
              "C) 15",
              "D) 11"
            ],
            correct_answer: "A",
            explanation: "First calculate the exponents: 2³ = 2×2×2 = 8, and 3² = 3×3 = 9. Then add them: 8 + 9 = 17. Remember that exponents are calculated before addition according to the order of operations (PEMDAS).",
            keywords: ["exponents", "order of operations", "arithmetic"],
            cognitive_level: "apply",
            difficulty: "easy"
          },
          {
            question: "What is 15% of 80?",
            options: [
              "A) 10",
              "B) 12",
              "C) 15",
              "D) 20"
            ],
            correct_answer: "B",
            explanation: "To find 15% of 80, multiply 80 by 0.15: 80 × 0.15 = 12. Alternatively, you can think of it as (15/100) × 80 = 1200/100 = 12.",
            keywords: ["percentage", "multiplication", "decimal"],
            cognitive_level: "apply",
            difficulty: "easy"
          },
          {
            question: "Solve for x: 2x + 5 = 13",
            options: [
              "A) x = 4",
              "B) x = 6",
              "C) x = 8",
              "D) x = 9"
            ],
            correct_answer: "A",
            explanation: "Subtract 5 from both sides: 2x = 8. Then divide both sides by 2: x = 4. Check: 2(4) + 5 = 8 + 5 = 13 ✓",
            keywords: ["linear equation", "algebra", "solving"],
            cognitive_level: "apply",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "If f(x) = 2x² - 3x + 1, what is f(2)?",
            options: [
              "A) 3",
              "B) 5",
              "C) 7",
              "D) 9"
            ],
            correct_answer: "A",
            explanation: "Substitute x = 2 into the function: f(2) = 2(2)² - 3(2) + 1 = 2(4) - 6 + 1 = 8 - 6 + 1 = 3. This demonstrates function evaluation, a fundamental concept in algebra.",
            keywords: ["function evaluation", "quadratic function", "substitution"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "What is the derivative of f(x) = 3x² + 2x - 1?",
            options: [
              "A) 6x + 2",
              "B) 3x + 2",
              "C) 6x - 1",
              "D) x² + x"
            ],
            correct_answer: "A",
            explanation: "Using the power rule: d/dx(3x²) = 6x, d/dx(2x) = 2, and d/dx(-1) = 0. Therefore, f'(x) = 6x + 2.",
            keywords: ["derivative", "calculus", "power rule"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "Solve the quadratic equation: x² - 5x + 6 = 0",
            options: [
              "A) x = 2, 3",
              "B) x = 1, 6",
              "C) x = -2, -3",
              "D) x = 2, -3"
            ],
            correct_answer: "A",
            explanation: "Factor the quadratic: x² - 5x + 6 = (x - 2)(x - 3) = 0. Therefore x = 2 or x = 3. Check: 2² - 5(2) + 6 = 4 - 10 + 6 = 0 ✓",
            keywords: ["quadratic equation", "factoring", "roots"],
            cognitive_level: "apply",
            difficulty: "medium"
          }
        ],
        Advanced: [
          {
            question: "Find the limit: lim(x→0) (sin(x)/x)",
            options: [
              "A) 0",
              "B) 1",
              "C) ∞",
              "D) Does not exist"
            ],
            correct_answer: "B",
            explanation: "This is a fundamental limit in calculus. Using L'Hôpital's rule or the squeeze theorem, lim(x→0) (sin(x)/x) = 1. This limit is crucial for deriving the derivative of sin(x).",
            keywords: ["limits", "trigonometry", "calculus", "fundamental limit"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the integral ∫(2x + 1)e^(x² + x) dx?",
            options: [
              "A) e^(x² + x) + C",
              "B) (2x + 1)e^(x² + x) + C",
              "C) 2e^(x² + x) + C",
              "D) (x² + x)e^(x² + x) + C"
            ],
            correct_answer: "A",
            explanation: "This requires substitution. Let u = x² + x, then du = (2x + 1)dx. The integral becomes ∫e^u du = e^u + C = e^(x² + x) + C.",
            keywords: ["integration", "substitution", "exponential", "calculus"],
            cognitive_level: "analyze",
            difficulty: "hard"
          }
        ]
      },
      Chemistry: {
        Beginner: [
          {
            question: "What is the chemical symbol for gold?",
            options: [
              "A) Au",
              "B) Ag",
              "C) Go",
              "D) Gd"
            ],
            correct_answer: "A",
            explanation: "The chemical symbol for gold is Au, derived from the Latin word 'aurum' meaning gold. This is why many chemical symbols don't match their English names - they come from Latin or Greek origins.",
            keywords: ["chemical symbols", "gold", "periodic table"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "How many protons does a carbon atom have?",
            options: [
              "A) 4",
              "B) 6",
              "C) 8",
              "D) 12"
            ],
            correct_answer: "B",
            explanation: "Carbon has 6 protons, which is its atomic number. The atomic number defines the element - all carbon atoms have exactly 6 protons, though they may have different numbers of neutrons (isotopes).",
            keywords: ["atomic number", "protons", "carbon", "periodic table"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "What type of bond forms between sodium and chlorine in table salt?",
            options: [
              "A) Covalent bond",
              "B) Ionic bond",
              "C) Metallic bond",
              "D) Hydrogen bond"
            ],
            correct_answer: "B",
            explanation: "Sodium (Na) and chlorine (Cl) form an ionic bond. Sodium loses an electron to become Na⁺, and chlorine gains an electron to become Cl⁻. The electrostatic attraction between these oppositely charged ions creates the ionic bond.",
            keywords: ["ionic bond", "sodium chloride", "electrons", "ions"],
            cognitive_level: "understand",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "Balance the equation: C₃H₈ + O₂ → CO₂ + H₂O. What is the coefficient for O₂?",
            options: [
              "A) 3",
              "B) 4",
              "C) 5",
              "D) 6"
            ],
            correct_answer: "C",
            explanation: "The balanced equation is C₃H₈ + 5O₂ → 3CO₂ + 4H₂O. Count atoms: 3 carbons need 3 CO₂, 8 hydrogens need 4 H₂O, and this requires 5 O₂ molecules (3×2 + 4×1 = 10 oxygen atoms).",
            keywords: ["balancing equations", "combustion", "stoichiometry"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "What is the molarity of a solution containing 2 moles of NaCl in 500 mL of solution?",
            options: [
              "A) 1 M",
              "B) 2 M",
              "C) 4 M",
              "D) 0.25 M"
            ],
            correct_answer: "C",
            explanation: "Molarity = moles of solute / liters of solution. Convert 500 mL to 0.5 L. Molarity = 2 moles / 0.5 L = 4 M. Molarity is a measure of concentration.",
            keywords: ["molarity", "concentration", "moles", "solution"],
            cognitive_level: "apply",
            difficulty: "medium"
          }
        ],
        Advanced: [
          {
            question: "In the reaction 2A + B → C, if the rate law is rate = k[A]²[B], what happens to the rate when [A] is doubled and [B] is tripled?",
            options: [
              "A) Rate increases by 6 times",
              "B) Rate increases by 12 times",
              "C) Rate increases by 18 times",
              "D) Rate increases by 24 times"
            ],
            correct_answer: "B",
            explanation: "Original rate = k[A]²[B]. New rate = k(2[A])²(3[B]) = k(4[A]²)(3[B]) = 12k[A]²[B]. The rate increases by a factor of 12.",
            keywords: ["rate law", "kinetics", "reaction order", "concentration"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the pH of a 0.01 M solution of a weak acid with Ka = 1.8 × 10⁻⁵?",
            options: [
              "A) 2.87",
              "B) 3.37",
              "C) 4.74",
              "D) 5.00"
            ],
            correct_answer: "B",
            explanation: "For weak acids: [H⁺] = √(Ka × C) = √(1.8 × 10⁻⁵ × 0.01) = √(1.8 × 10⁻⁷) = 4.24 × 10⁻⁴. pH = -log(4.24 × 10⁻⁴) = 3.37",
            keywords: ["pH", "weak acid", "Ka", "acid dissociation"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "In a galvanic cell with Zn|Zn²⁺||Cu²⁺|Cu, if [Zn²⁺] = 0.1 M and [Cu²⁺] = 1.0 M, what is the cell potential? (E°Zn²⁺/Zn = -0.76 V, E°Cu²⁺/Cu = +0.34 V)",
            options: [
              "A) 1.07 V",
              "B) 1.10 V",
              "C) 1.13 V",
              "D) 1.16 V"
            ],
            correct_answer: "C",
            explanation: "E°cell = E°cathode - E°anode = 0.34 - (-0.76) = 1.10 V. Using Nernst equation: E = E° - (RT/nF)ln(Q) = 1.10 - (0.0257/2)ln(0.1/1.0) = 1.10 + 0.0296 = 1.13 V",
            keywords: ["galvanic cell", "Nernst equation", "electrochemistry", "cell potential"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the hybridization and molecular geometry of XeF₄?",
            options: [
              "A) sp³d², octahedral",
              "B) sp³d², square planar",
              "C) sp³d, trigonal bipyramidal",
              "D) sp³, tetrahedral"
            ],
            correct_answer: "B",
            explanation: "Xe has 8 valence electrons, 4 bonding pairs with F, and 2 lone pairs. Total = 6 electron pairs requiring sp³d² hybridization. With 2 lone pairs in axial positions, the molecular geometry is square planar.",
            keywords: ["hybridization", "VSEPR", "molecular geometry", "xenon compounds"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "Calculate the entropy change when 2 moles of an ideal gas expand isothermally from 1 L to 10 L at 298 K.",
            options: [
              "A) 19.1 J/K",
              "B) 38.3 J/K",
              "C) 57.4 J/K",
              "D) 76.5 J/K"
            ],
            correct_answer: "B",
            explanation: "For isothermal expansion of ideal gas: ΔS = nR ln(Vf/Vi) = 2 mol × 8.314 J/(mol·K) × ln(10/1) = 16.628 × 2.303 = 38.3 J/K",
            keywords: ["entropy", "isothermal process", "ideal gas", "thermodynamics"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the major product when 2-methylpropene undergoes hydroboration-oxidation?",
            options: [
              "A) 2-methylpropan-1-ol",
              "B) 2-methylpropan-2-ol",
              "C) 2-methylpropanal",
              "D) 2-methylpropanoic acid"
            ],
            correct_answer: "A",
            explanation: "Hydroboration-oxidation follows anti-Markovnikov addition. The boron adds to the less substituted carbon, followed by oxidation to give the primary alcohol 2-methylpropan-1-ol.",
            keywords: ["hydroboration", "anti-Markovnikov", "alkenes", "organic synthesis"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "In a crystal lattice, what is the coordination number of atoms in a face-centered cubic (FCC) structure?",
            options: [
              "A) 6",
              "B) 8",
              "C) 12",
              "D) 14"
            ],
            correct_answer: "C",
            explanation: "In FCC structure, each atom is surrounded by 12 nearest neighbors: 4 in the same plane, 4 above, and 4 below. This gives a coordination number of 12.",
            keywords: ["crystal structure", "FCC", "coordination number", "solid state"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the rate-determining step in the SN1 mechanism?",
            options: [
              "A) Nucleophile attack",
              "B) Carbocation formation",
              "C) Proton transfer",
              "D) Product formation"
            ],
            correct_answer: "B",
            explanation: "In SN1 mechanism, the rate-determining step is the formation of the carbocation intermediate through heterolytic bond cleavage. This is the slowest step and determines the overall reaction rate.",
            keywords: ["SN1 mechanism", "rate-determining step", "carbocation", "nucleophilic substitution"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "Calculate the wavelength of light emitted when an electron transitions from n=4 to n=2 in a hydrogen atom. (RH = 1.097 × 10⁷ m⁻¹)",
            options: [
              "A) 486 nm",
              "B) 656 nm",
              "C) 434 nm",
              "D) 410 nm"
            ],
            correct_answer: "A",
            explanation: "Using Rydberg equation: 1/λ = RH(1/n₁² - 1/n₂²) = 1.097 × 10⁷(1/4 - 1/16) = 1.097 × 10⁷ × 3/16 = 2.057 × 10⁶ m⁻¹. λ = 486 nm (Hβ line)",
            keywords: ["hydrogen spectrum", "Rydberg equation", "electron transitions", "atomic physics"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the effect of adding a catalyst to a reaction at equilibrium?",
            options: [
              "A) Shifts equilibrium to the right",
              "B) Shifts equilibrium to the left",
              "C) No effect on equilibrium position",
              "D) Changes the equilibrium constant"
            ],
            correct_answer: "C",
            explanation: "A catalyst increases the rate of both forward and reverse reactions equally, so it has no effect on the equilibrium position. It only helps the system reach equilibrium faster.",
            keywords: ["catalyst", "equilibrium", "reaction kinetics", "Le Chatelier's principle"],
            cognitive_level: "analyze",
            difficulty: "hard"
          }
        ]
      },
      Biology: {
        Beginner: [
          {
            question: "Which organelle is known as the 'powerhouse of the cell'?",
            options: [
              "A) Mitochondria",
              "B) Nucleus",
              "C) Ribosome",
              "D) Endoplasmic reticulum"
            ],
            correct_answer: "A",
            explanation: "Mitochondria are called the 'powerhouse of the cell' because they produce ATP (adenosine triphosphate), the main energy currency of cells, through cellular respiration. They have their own DNA and are thought to have evolved from ancient bacteria.",
            keywords: ["mitochondria", "ATP", "cellular respiration", "organelles"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "What is the basic unit of heredity?",
            options: [
              "A) Chromosome",
              "B) Gene",
              "C) DNA",
              "D) Protein"
            ],
            correct_answer: "B",
            explanation: "A gene is the basic unit of heredity. Genes are segments of DNA that contain instructions for making proteins, which determine traits. Chromosomes contain many genes, and DNA is the molecule that makes up genes.",
            keywords: ["gene", "heredity", "DNA", "genetics"],
            cognitive_level: "remember",
            difficulty: "easy"
          },
          {
            question: "Which process do plants use to make their own food?",
            options: [
              "A) Cellular respiration",
              "B) Photosynthesis",
              "C) Fermentation",
              "D) Digestion"
            ],
            correct_answer: "B",
            explanation: "Photosynthesis is the process by which plants convert light energy, carbon dioxide, and water into glucose and oxygen. The equation is: 6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂.",
            keywords: ["photosynthesis", "plants", "glucose", "chlorophyll"],
            cognitive_level: "remember",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "In a cross between two heterozygous individuals (Aa × Aa), what is the probability of offspring being homozygous recessive?",
            options: [
              "A) 0%",
              "B) 25%",
              "C) 50%",
              "D) 75%"
            ],
            correct_answer: "B",
            explanation: "Using a Punnett square: AA (25%), Aa (50%), aa (25%). The probability of homozygous recessive (aa) offspring is 25% or 1/4. This follows Mendel's laws of inheritance.",
            keywords: ["genetics", "Punnett square", "heterozygous", "homozygous recessive"],
            cognitive_level: "apply",
            difficulty: "medium"
          },
          {
            question: "Which stage of mitosis involves the alignment of chromosomes at the cell's equator?",
            options: [
              "A) Prophase",
              "B) Metaphase",
              "C) Anaphase",
              "D) Telophase"
            ],
            correct_answer: "B",
            explanation: "During metaphase, chromosomes align at the cell's equator (metaphase plate). This ensures equal distribution of genetic material. Prophase involves chromosome condensation, anaphase involves separation, and telophase involves nuclear reformation.",
            keywords: ["mitosis", "metaphase", "chromosomes", "cell division"],
            cognitive_level: "understand",
            difficulty: "medium"
          }
        ],
        Advanced: [
          {
            question: "In enzyme kinetics, what does a competitive inhibitor do to Km and Vmax?",
            options: [
              "A) Increases Km, decreases Vmax",
              "B) Increases Km, no change in Vmax",
              "C) Decreases Km, decreases Vmax",
              "D) No change in Km, decreases Vmax"
            ],
            correct_answer: "B",
            explanation: "A competitive inhibitor competes with substrate for the active site, increasing the apparent Km (appears to decrease enzyme affinity) but doesn't change Vmax because high substrate concentrations can overcome inhibition.",
            keywords: ["enzyme kinetics", "competitive inhibition", "Km", "Vmax"],
            cognitive_level: "analyze",
            difficulty: "hard"
          },
          {
            question: "What is the net ATP yield from one glucose molecule during cellular respiration?",
            options: [
              "A) 2 ATP",
              "B) 32 ATP",
              "C) 36 ATP",
              "D) 38 ATP"
            ],
            correct_answer: "D",
            explanation: "Cellular respiration yields approximately 38 ATP: 2 from glycolysis, 2 from citric acid cycle, and 34 from electron transport chain. This assumes optimal conditions and may vary slightly depending on transport mechanisms.",
            keywords: ["cellular respiration", "ATP", "glucose", "electron transport"],
            cognitive_level: "analyze",
            difficulty: "hard"
          }
        ]
      },
      'Computer Science': {
        Beginner: [
          {
            question: "What does CPU stand for?",
            options: [
              "A) Central Processing Unit",
              "B) Computer Processing Unit",
              "C) Central Program Unit",
              "D) Computer Program Unit"
            ],
            correct_answer: "A",
            explanation: "CPU stands for Central Processing Unit. It's the main component of a computer that performs most of the processing inside the computer, executing instructions and performing calculations.",
            keywords: ["CPU", "computer hardware", "processing"],
            cognitive_level: "remember",
            difficulty: "easy"
          }
        ],
        Intermediate: [
          {
            question: "What is the time complexity of binary search?",
            options: [
              "A) O(n)",
              "B) O(log n)",
              "C) O(n log n)",
              "D) O(n²)"
            ],
            correct_answer: "B",
            explanation: "Binary search has O(log n) time complexity because it eliminates half of the remaining elements in each step. This makes it very efficient for searching in sorted arrays.",
            keywords: ["binary search", "time complexity", "algorithms"],
            cognitive_level: "understand",
            difficulty: "medium"
          }
        ],
        Advanced: [
          {
            question: "In database normalization, what is the main purpose of Third Normal Form (3NF)?",
            options: [
              "A) Eliminate partial dependencies",
              "B) Eliminate transitive dependencies",
              "C) Eliminate repeating groups",
              "D) Eliminate all dependencies"
            ],
            correct_answer: "B",
            explanation: "Third Normal Form (3NF) eliminates transitive dependencies, where a non-key attribute depends on another non-key attribute. This reduces redundancy and improves data integrity.",
            keywords: ["database", "normalization", "3NF", "dependencies"],
            cognitive_level: "analyze",
            difficulty: "hard"
          }
        ]
      }
    };

    // Get questions for the subject and level, with fallback
    const subjectQuestions = questionBank[subject] || questionBank.Physics;
    const levelQuestions = subjectQuestions[level] || subjectQuestions.Beginner || subjectQuestions[Object.keys(subjectQuestions)[0]];

    return levelQuestions || [{
      question: `Sample ${subject} question at ${level} level`,
      options: ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      correct_answer: "A",
      explanation: `This is a sample explanation for ${subject} at ${level} level.`,
      keywords: [subject.toLowerCase(), level.toLowerCase()],
      cognitive_level: "understand",
      difficulty: level.toLowerCase()
    }];
  }

  generateMockResponse(params) {
    const questions = this.generateMockQuestions(params);
    return JSON.stringify(questions, null, 2);
  }

  updateMetrics(success, responseTime) {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }
    
    // Update average response time
    const totalRequests = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
    
    // Update provider usage
    if (!this.metrics.providerUsage[this.currentProvider]) {
      this.metrics.providerUsage[this.currentProvider] = 0;
    }
    this.metrics.providerUsage[this.currentProvider]++;
  }

  getMetrics() {
    return {
      ...this.metrics,
      currentProvider: this.currentProvider,
      availableProviders: Object.keys(this.providers).filter(p => this.providers[p].enabled),
      successRate: this.metrics.totalRequests > 0 
        ? (this.metrics.successfulRequests / this.metrics.totalRequests) * 100 
        : 0
    };
  }

  async generateExplanation(question, userAnswer, correctAnswer) {
    const prompt = `Explain why the correct answer to this question is "${correctAnswer}" and why "${userAnswer}" is incorrect:

Question: ${question}

Provide a clear, educational explanation that helps the student understand the concept.`;

    try {
      const response = await this.callProvider(this.currentProvider, prompt, {});
      return this.extractResponseText(this.currentProvider, response);
    } catch (error) {
      return `The correct answer is ${correctAnswer}. This demonstrates key concepts in the subject matter.`;
    }
  }

  async evaluateAnswer(question, userAnswer, context = {}) {
    const prompt = `Evaluate this answer and provide detailed feedback:

Question: ${question}
Student Answer: ${userAnswer}
Context: ${JSON.stringify(context)}

Provide:
1. Whether the answer is correct
2. Detailed explanation
3. Suggestions for improvement
4. Related concepts to study`;

    try {
      const response = await this.callProvider(this.currentProvider, prompt, {});
      return {
        isCorrect: userAnswer.toLowerCase().includes('correct'),
        feedback: this.extractResponseText(this.currentProvider, response),
        score: Math.random() * 100 // Mock scoring
      };
    } catch (error) {
      return {
        isCorrect: false,
        feedback: 'Unable to evaluate answer at this time.',
        score: 0
      };
    }
  }

  generateBasicFallbackQuestions(params) {
    const { subject = 'General', numQuestions = 1, level = 'Beginner' } = params;
    const questions = [];

    for (let i = 0; i < numQuestions; i++) {
      questions.push({
        question: `Basic ${subject} question ${i + 1} (${level} level)`,
        options: [
          "A) First option",
          "B) Second option",
          "C) Third option",
          "D) Fourth option"
        ],
        correct_answer: "A",
        explanation: `This is a basic fallback explanation for ${subject} at ${level} level.`,
        keywords: [subject.toLowerCase(), level.toLowerCase(), "fallback"],
        cognitive_level: "remember",
        difficulty: level.toLowerCase(),
        source_tag: `Fallback - ${subject}`,
        quality_score: 0.6,
        id: `fallback_${Date.now()}_${i}`,
        generated_at: new Date().toISOString()
      });
    }

    return questions;
  }
}

export default new EnhancedAIService();

import { getDb } from '../firebase.js';
import { v4 as uuidv4 } from 'uuid';
import loggingService from './loggingService.js';

class ContentManagementService {
  constructor() {
    this.db = null;
    this.initializeDb();
  }

  async initializeDb() {
    this.db = await getDb();
  }

  // Bulk question import/export
  async importQuestions(questions, userId) {
    try {
      const batch = this.db.batch();
      const importId = uuidv4();
      const timestamp = new Date();

      for (const question of questions) {
        const questionRef = this.db.collection('questions').doc();
        batch.set(questionRef, {
          ...question,
          importId,
          version: 1,
          createdBy: userId,
          createdAt: timestamp,
          updatedAt: timestamp,
          status: 'pending_review',
          tags: question.tags || [],
          reviewHistory: []
        });
      }

      await batch.commit();
      return { success: true, importId };
    } catch (error) {
      loggingService.logError(error, { operation: 'importQuestions' });
      throw error;
    }
  }

  async exportQuestions(filters = {}) {
    try {
      let query = this.db.collection('questions');

      // Apply filters
      if (filters.tags) {
        query = query.where('tags', 'array-contains-any', filters.tags);
      }
      if (filters.status) {
        query = query.where('status', '==', filters.status);
      }
      if (filters.subject) {
        query = query.where('subject', '==', filters.subject);
      }

      const snapshot = await query.get();
      const questions = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        // Remove internal fields
        const { reviewHistory, importId, ...exportData } = data;
        questions.push({
          id: doc.id,
          ...exportData
        });
      });

      return questions;
    } catch (error) {
      loggingService.logError(error, { operation: 'exportQuestions' });
      throw error;
    }
  }

  // Version control
  async updateQuestion(questionId, updates, userId) {
    try {
      const ref = this.db.collection('questions').doc(questionId);
      const doc = await ref.get();

      if (!doc.exists) {
        throw new Error('Question not found');
      }

      const currentData = doc.data();
      const newVersion = currentData.version + 1;

      // Store current version in history
      await this.db.collection('question_versions').add({
        questionId,
        version: currentData.version,
        data: currentData,
        timestamp: new Date(),
        userId: currentData.updatedBy || currentData.createdBy
      });

      // Update question with new version
      await ref.update({
        ...updates,
        version: newVersion,
        updatedAt: new Date(),
        updatedBy: userId
      });

      return { success: true, version: newVersion };
    } catch (error) {
      loggingService.logError(error, { operation: 'updateQuestion' });
      throw error;
    }
  }

  // Question tagging
  async addTags(questionId, tags, userId) {
    try {
      const ref = this.db.collection('questions').doc(questionId);
      await ref.update({
        tags: this.db.FieldValue.arrayUnion(...tags),
        updatedAt: new Date(),
        updatedBy: userId
      });
      return { success: true };
    } catch (error) {
      loggingService.logError(error, { operation: 'addTags' });
      throw error;
    }
  }

  // Content review workflow
  async submitForReview(questionId, userId) {
    try {
      const ref = this.db.collection('questions').doc(questionId);
      await ref.update({
        status: 'pending_review',
        submittedForReviewAt: new Date(),
        submittedForReviewBy: userId
      });
      return { success: true };
    } catch (error) {
      loggingService.logError(error, { operation: 'submitForReview' });
      throw error;
    }
  }

  async reviewQuestion(questionId, reviewData, reviewerId) {
    try {
      const ref = this.db.collection('questions').doc(questionId);
      const doc = await ref.get();
      
      if (!doc.exists) {
        throw new Error('Question not found');
      }

      const currentData = doc.data();
      const reviewHistory = currentData.reviewHistory || [];

      reviewHistory.push({
        ...reviewData,
        reviewerId,
        timestamp: new Date()
      });

      await ref.update({
        status: reviewData.approved ? 'approved' : 'needs_revision',
        reviewHistory,
        updatedAt: new Date(),
        updatedBy: reviewerId
      });

      return { success: true };
    } catch (error) {
      loggingService.logError(error, { operation: 'reviewQuestion' });
      throw error;
    }
  }

  // Question bank management
  async createQuestionBank(bankData, userId) {
    try {
      const ref = await this.db.collection('question_banks').add({
        ...bankData,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        questionCount: 0
      });

      return { success: true, bankId: ref.id };
    } catch (error) {
      loggingService.logError(error, { operation: 'createQuestionBank' });
      throw error;
    }
  }

  async addQuestionsToBank(bankId, questionIds, userId) {
    try {
      const batch = this.db.batch();
      const bankRef = this.db.collection('question_banks').doc(bankId);
      
      // Add questions to bank
      for (const questionId of questionIds) {
        const mappingRef = this.db.collection('bank_questions').doc();
        batch.set(mappingRef, {
          bankId,
          questionId,
          addedBy: userId,
          addedAt: new Date()
        });
      }

      // Update question count
      batch.update(bankRef, {
        questionCount: this.db.FieldValue.increment(questionIds.length),
        updatedAt: new Date(),
        updatedBy: userId
      });

      await batch.commit();
      return { success: true };
    } catch (error) {
      loggingService.logError(error, { operation: 'addQuestionsToBank' });
      throw error;
    }
  }
}

export default new ContentManagementService();

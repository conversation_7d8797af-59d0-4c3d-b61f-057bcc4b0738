import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';
import loggingService from './loggingService.js';

/**
 * Security Audit Service
 * Monitors and audits security events, user behavior, and system integrity
 */
class SecurityAuditService {
  constructor() {
    this.auditLog = [];
    this.securityMetrics = {
      totalRequests: 0,
      blockedRequests: 0,
      suspiciousActivities: 0,
      rateLimitHits: 0,
      authenticationFailures: 0,
      lastAuditTime: new Date()
    };
    this.userBehaviorPatterns = new Map();
    this.threatIntelligence = new Set();
    
    // Start periodic security audits
    this.startPeriodicAudits();
  }

  // Log security events
  logSecurityEvent(eventType, details) {
    const event = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      type: eventType,
      severity: this.calculateSeverity(eventType, details),
      details,
      hash: this.generateEventHash(eventType, details)
    };

    this.auditLog.push(event);
    this.updateMetrics(eventType);
    
    // Keep only last 10000 events in memory
    if (this.auditLog.length > 10000) {
      this.auditLog = this.auditLog.slice(-10000);
    }

    // Log high-severity events immediately
    if (event.severity >= 8) {
      loggingService.logError('High-severity security event', event);
      this.triggerSecurityAlert(event);
    }

    return event.id;
  }

  // Calculate event severity (1-10 scale)
  calculateSeverity(eventType, details) {
    const severityMap = {
      'authentication_failure': 3,
      'rate_limit_exceeded': 4,
      'suspicious_request': 6,
      'sql_injection_attempt': 9,
      'xss_attempt': 8,
      'unauthorized_access': 7,
      'data_breach_attempt': 10,
      'honeypot_triggered': 5,
      'blacklisted_ip': 6,
      'content_theft_attempt': 7
    };

    let baseSeverity = severityMap[eventType] || 5;
    
    // Increase severity based on details
    if (details.repeated) baseSeverity += 2;
    if (details.fromKnownThreat) baseSeverity += 3;
    if (details.targetsSensitiveData) baseSeverity += 2;
    
    return Math.min(baseSeverity, 10);
  }

  // Generate unique hash for event deduplication
  generateEventHash(eventType, details) {
    const hashInput = eventType + JSON.stringify(details);
    return crypto.createHash('sha256').update(hashInput).digest('hex');
  }

  // Update security metrics
  updateMetrics(eventType) {
    this.securityMetrics.totalRequests++;
    
    switch (eventType) {
      case 'rate_limit_exceeded':
        this.securityMetrics.rateLimitHits++;
        this.securityMetrics.blockedRequests++;
        break;
      case 'authentication_failure':
        this.securityMetrics.authenticationFailures++;
        break;
      case 'suspicious_request':
      case 'sql_injection_attempt':
      case 'xss_attempt':
        this.securityMetrics.suspiciousActivities++;
        this.securityMetrics.blockedRequests++;
        break;
    }
  }

  // Analyze user behavior patterns
  analyzeUserBehavior(userId, action, metadata = {}) {
    if (!this.userBehaviorPatterns.has(userId)) {
      this.userBehaviorPatterns.set(userId, {
        actions: [],
        riskScore: 0,
        firstSeen: new Date(),
        lastSeen: new Date()
      });
    }

    const userPattern = this.userBehaviorPatterns.get(userId);
    userPattern.actions.push({
      action,
      timestamp: new Date(),
      metadata
    });
    userPattern.lastSeen = new Date();

    // Keep only last 100 actions per user
    if (userPattern.actions.length > 100) {
      userPattern.actions = userPattern.actions.slice(-100);
    }

    // Calculate risk score
    userPattern.riskScore = this.calculateUserRiskScore(userPattern);

    // Alert on high-risk users
    if (userPattern.riskScore > 80) {
      this.logSecurityEvent('high_risk_user_detected', {
        userId,
        riskScore: userPattern.riskScore,
        recentActions: userPattern.actions.slice(-10)
      });
    }

    return userPattern.riskScore;
  }

  // Calculate user risk score (0-100)
  calculateUserRiskScore(userPattern) {
    let riskScore = 0;
    const actions = userPattern.actions;
    const now = new Date();

    // Rapid successive actions (potential bot)
    const recentActions = actions.filter(a => 
      now - a.timestamp < 60000 // Last minute
    );
    if (recentActions.length > 20) riskScore += 30;

    // Failed authentication attempts
    const failedAuths = actions.filter(a => 
      a.action === 'authentication_failure'
    ).length;
    riskScore += Math.min(failedAuths * 5, 25);

    // Suspicious patterns
    const suspiciousActions = actions.filter(a => 
      a.action.includes('suspicious') || a.action.includes('injection')
    ).length;
    riskScore += Math.min(suspiciousActions * 10, 40);

    // Time-based anomalies (activity at unusual hours)
    const nightActions = actions.filter(a => {
      const hour = a.timestamp.getHours();
      return hour < 6 || hour > 22; // 10 PM to 6 AM
    }).length;
    if (nightActions > actions.length * 0.8) riskScore += 15;

    return Math.min(riskScore, 100);
  }

  // Trigger security alerts
  async triggerSecurityAlert(event) {
    const alert = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      event,
      status: 'active',
      escalated: false
    };

    // Log the alert
    await loggingService.logError('SECURITY ALERT', alert);

    // In production, you would send notifications here
    // - Email to security team
    // - Slack/Discord webhook
    // - SMS for critical alerts
    // - Integration with security tools (SIEM)

    console.log('🚨 SECURITY ALERT:', alert);
    
    return alert.id;
  }

  // Perform security audit
  async performSecurityAudit() {
    const auditResults = {
      timestamp: new Date(),
      metrics: { ...this.securityMetrics },
      findings: [],
      recommendations: []
    };

    // Check for suspicious patterns
    const recentEvents = this.auditLog.filter(event => 
      new Date() - event.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    // Analyze event patterns
    const eventCounts = {};
    recentEvents.forEach(event => {
      eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
    });

    // Check for anomalies
    if (eventCounts.authentication_failure > 50) {
      auditResults.findings.push({
        type: 'high_auth_failures',
        severity: 'high',
        description: `${eventCounts.authentication_failure} authentication failures in 24h`,
        recommendation: 'Implement stronger rate limiting on auth endpoints'
      });
    }

    if (eventCounts.suspicious_request > 20) {
      auditResults.findings.push({
        type: 'suspicious_activity_spike',
        severity: 'medium',
        description: `${eventCounts.suspicious_request} suspicious requests detected`,
        recommendation: 'Review and update security rules'
      });
    }

    // Check user behavior patterns
    const highRiskUsers = Array.from(this.userBehaviorPatterns.entries())
      .filter(([_, pattern]) => pattern.riskScore > 70)
      .length;

    if (highRiskUsers > 0) {
      auditResults.findings.push({
        type: 'high_risk_users',
        severity: 'medium',
        description: `${highRiskUsers} users with high risk scores`,
        recommendation: 'Review high-risk user activities and consider additional verification'
      });
    }

    // Generate recommendations
    auditResults.recommendations = this.generateSecurityRecommendations(auditResults);

    // Save audit results
    await this.saveAuditResults(auditResults);

    return auditResults;
  }

  // Generate security recommendations
  generateSecurityRecommendations(auditResults) {
    const recommendations = [];

    if (this.securityMetrics.blockedRequests / this.securityMetrics.totalRequests > 0.1) {
      recommendations.push({
        priority: 'high',
        category: 'rate_limiting',
        description: 'High percentage of blocked requests - consider adjusting rate limits',
        action: 'Review and optimize rate limiting configuration'
      });
    }

    if (this.securityMetrics.authenticationFailures > 100) {
      recommendations.push({
        priority: 'medium',
        category: 'authentication',
        description: 'High number of authentication failures',
        action: 'Implement CAPTCHA or additional verification steps'
      });
    }

    recommendations.push({
      priority: 'low',
      category: 'monitoring',
      description: 'Regular security monitoring is active',
      action: 'Continue monitoring and review logs weekly'
    });

    return recommendations;
  }

  // Save audit results to file
  async saveAuditResults(auditResults) {
    try {
      const auditDir = path.join(process.cwd(), 'logs', 'security-audits');
      await fs.mkdir(auditDir, { recursive: true });
      
      const filename = `audit-${auditResults.timestamp.toISOString().split('T')[0]}.json`;
      const filepath = path.join(auditDir, filename);
      
      await fs.writeFile(filepath, JSON.stringify(auditResults, null, 2));
      
      loggingService.logInfo('Security audit results saved', { filepath });
    } catch (error) {
      loggingService.logError('Failed to save audit results', error);
    }
  }

  // Start periodic security audits
  startPeriodicAudits() {
    // Run audit every 6 hours
    setInterval(async () => {
      try {
        await this.performSecurityAudit();
      } catch (error) {
        loggingService.logError('Periodic security audit failed', error);
      }
    }, 6 * 60 * 60 * 1000);

    loggingService.logInfo('Periodic security audits started');
  }

  // Get security dashboard data
  getSecurityDashboard() {
    const now = new Date();
    const last24h = new Date(now - 24 * 60 * 60 * 1000);
    
    const recentEvents = this.auditLog.filter(event => 
      event.timestamp > last24h
    );

    return {
      metrics: this.securityMetrics,
      recentEvents: recentEvents.slice(-50),
      highRiskUsers: Array.from(this.userBehaviorPatterns.entries())
        .filter(([_, pattern]) => pattern.riskScore > 70)
        .map(([userId, pattern]) => ({ userId, riskScore: pattern.riskScore })),
      threatLevel: this.calculateOverallThreatLevel(),
      lastAuditTime: this.securityMetrics.lastAuditTime
    };
  }

  // Calculate overall threat level
  calculateOverallThreatLevel() {
    const recentEvents = this.auditLog.filter(event => 
      new Date() - event.timestamp < 60 * 60 * 1000 // Last hour
    );

    const highSeverityEvents = recentEvents.filter(event => event.severity >= 8).length;
    const totalEvents = recentEvents.length;

    if (highSeverityEvents > 5) return 'CRITICAL';
    if (highSeverityEvents > 2) return 'HIGH';
    if (totalEvents > 50) return 'ELEVATED';
    return 'LOW';
  }
}

export default new SecurityAuditService();

class MockFirestore {
  constructor() {
    this.collections = new Map();
  }

  batch() {
    return new MockBatch(this);
  }

  collection(name) {
    if (!this.collections.has(name)) {
      this.collections.set(name, new MockCollection());
    }
    return this.collections.get(name);
  }
}

class MockCollection {
  constructor() {
    this.documents = new Map();
    this.queryFilters = [];
    this.queryOrderBy = null;
    this.queryLimit = null;
  }

  doc(id = Math.random().toString(36).substr(2, 9)) {
    if (!this.documents.has(id)) {
      this.documents.set(id, new MockDocument(id));
    }
    return this.documents.get(id);
  }

  async add(data) {
    const id = Math.random().toString(36).substr(2, 9);
    const doc = this.doc(id);
    await doc.set(data);
    return doc;
  }

  where(field, operator, value) {
    const newCollection = new MockCollection();
    newCollection.documents = this.documents;
    newCollection.queryFilters = [...this.queryFilters, { field, operator, value }];
    newCollection.queryOrderBy = this.queryOrderBy;
    newCollection.queryLimit = this.queryLimit;
    return newCollection;
  }

  orderBy(field, direction = 'asc') {
    const newCollection = new MockCollection();
    newCollection.documents = this.documents;
    newCollection.queryFilters = this.queryFilters;
    newCollection.queryOrderBy = { field, direction };
    newCollection.queryLimit = this.queryLimit;
    return newCollection;
  }

  limit(count) {
    const newCollection = new MockCollection();
    newCollection.documents = this.documents;
    newCollection.queryFilters = this.queryFilters;
    newCollection.queryOrderBy = this.queryOrderBy;
    newCollection.queryLimit = count;
    return newCollection;
  }

  async get() {
    let docs = Array.from(this.documents.values());

    // Apply filters
    for (const filter of this.queryFilters) {
      docs = docs.filter(doc => {
        const data = doc.data();
        const fieldValue = data[filter.field];

        switch (filter.operator) {
          case '==':
            return fieldValue === filter.value;
          case '!=':
            return fieldValue !== filter.value;
          case '>':
            return fieldValue > filter.value;
          case '>=':
            return fieldValue >= filter.value;
          case '<':
            return fieldValue < filter.value;
          case '<=':
            return fieldValue <= filter.value;
          case 'array-contains':
            return Array.isArray(fieldValue) && fieldValue.includes(filter.value);
          case 'in':
            return Array.isArray(filter.value) && filter.value.includes(fieldValue);
          default:
            return true;
        }
      });
    }

    // Apply ordering
    if (this.queryOrderBy) {
      docs.sort((a, b) => {
        const aVal = a.data()[this.queryOrderBy.field];
        const bVal = b.data()[this.queryOrderBy.field];

        if (this.queryOrderBy.direction === 'desc') {
          return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
        } else {
          return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
        }
      });
    }

    // Apply limit
    if (this.queryLimit) {
      docs = docs.slice(0, this.queryLimit);
    }
    return {
      empty: docs.length === 0,
      docs,
      forEach(callback) {
        docs.forEach(callback);
      }
    };
  }
}

class MockDocument {
  constructor(id) {
    this.id = id;
    this.data_ = {};
  }

  async set(data, options = {}) {
    if (options.merge) {
      this.data_ = { ...this.data_, ...data };
    } else {
      this.data_ = data;
    }
    return this;
  }

  async get() {
    return {
      exists: () => Object.keys(this.data_).length > 0,
      data: () => this.data_,
      id: this.id
    };
  }

  data() {
    return this.data_;
  }
}



class MockBatch {
  constructor(firestore) {
    this.firestore = firestore;
    this.operations = [];
  }

  set(docRef, data) {
    this.operations.push(() => docRef.set(data));
    return this;
  }

  async commit() {
    for (const operation of this.operations) {
      await operation();
    }
    this.operations = [];
    return;
  }
}

export { MockFirestore };

const mockDb = new MockFirestore();

export function getDb() {
  return mockDb;
}

// Firebase Firestore compatibility functions
export function collection(db, name) {
  return db.collection(name);
}

export function doc(collectionRef, id) {
  return collectionRef.doc(id);
}

export async function getDoc(docRef) {
  return await docRef.get();
}

export async function setDoc(docRef, data) {
  return await docRef.set(data);
}

export async function updateDoc(docRef, data) {
  return await docRef.set(data, { merge: true });
}

export function serverTimestamp() {
  return new Date();
}

export function query(collectionRef, ...constraints) {
  // Simple mock - just return the collection
  return collectionRef;
}

export function where(field, operator, value) {
  // Mock constraint - not implemented
  return { field, operator, value };
}

export function orderBy(field, direction = 'asc') {
  // Mock constraint - not implemented
  return { field, direction };
}

export function limit(count) {
  // Mock constraint - not implemented
  return { count };
}

export async function getDocs(query) {
  return await query.get();
}

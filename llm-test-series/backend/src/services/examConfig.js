import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';

class ExamConfigService {
    constructor() {
        this.db = getDb();
        this.loggingService = loggingService;
    }

    /**
     * Get all exam configurations
     * @returns {Promise<Array>} Array of exam configurations
     */
    async getAllConfigs() {
        try {
            const snapshot = await this.db.collection('exam_configs').get();
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            this.loggingService.logError(error, { operation: 'getAllConfigs' });
            throw error;
        }
    }

    /**
     * Get a specific exam configuration
     * @param {string} id - Exam configuration ID
     * @returns {Promise<Object>} Exam configuration object
     */
    async getExamConfig(id) {
        try {
            const doc = await this.db.collection('exam_configs').doc(id).get();
            if (!doc.exists) {
                return null;
            }
            return {
                id: doc.id,
                ...doc.data()
            };
        } catch (error) {
            this.loggingService.logError(error, { operation: 'getExamConfig', id });
            throw error;
        }
    }

    /**
     * Create a new exam configuration
     * @param {Object} config - Exam configuration data
     * @returns {Promise<Object>} Created exam configuration
     */
    async createExamConfig(config) {
        try {
            const docRef = await this.db.collection('exam_configs').add({
                ...config,
                created_at: new Date(),
                updated_at: new Date()
            });
            
            const doc = await docRef.get();
            return {
                id: doc.id,
                ...doc.data()
            };
        } catch (error) {
            this.loggingService.logError(error, { operation: 'createExamConfig' });
            throw error;
        }
    }

    /**
     * Update an exam configuration
     * @param {string} id - Exam configuration ID
     * @param {Object} updates - Fields to update
     * @returns {Promise<Object>} Updated exam configuration
     */
    async updateExamConfig(id, updates) {
        try {
            const docRef = this.db.collection('exam_configs').doc(id);
            await docRef.update({
                ...updates,
                updated_at: new Date()
            });
            
            const doc = await docRef.get();
            return {
                id: doc.id,
                ...doc.data()
            };
        } catch (error) {
            this.loggingService.logError(error, { operation: 'updateExamConfig', id });
            throw error;
        }
    }

    /**
     * Delete an exam configuration
     * @param {string} id - Exam configuration ID
     * @returns {Promise<void>}
     */
    async deleteExamConfig(id) {
        try {
            await this.db.collection('exam_configs').doc(id).delete();
        } catch (error) {
            this.loggingService.logError(error, { operation: 'deleteExamConfig', id });
            throw error;
        }
    }
}

export default ExamConfigService;

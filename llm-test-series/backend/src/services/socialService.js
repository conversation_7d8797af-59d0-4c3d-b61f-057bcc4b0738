// Social Service - Friends, Challenges, Study Groups, Social Feed
import { getDb } from '../config/firebase.js';
import loggingService from './loggingService.js';
import redisService from './redisService.js';
import { v4 as uuidv4 } from 'uuid';

class SocialService {
  constructor() {
    this.db = getDb();
  }

  // FRIENDS MANAGEMENT

  async getFriends(userId, status = 'accepted') {
    try {
      const cacheKey = `friends:${userId}:${status}`;
      const cached = await redisService.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      const friendsSnapshot = await this.db
        .collection('friends')
        .where('userId', '==', userId)
        .where('status', '==', status)
        .get();

      const friends = [];
      
      for (const doc of friendsSnapshot.docs) {
        const friendData = doc.data();
        
        // Get friend's profile information
        const friendProfile = await this.getUserProfile(friendData.friendId);
        
        friends.push({
          ...friendData,
          profile: friendProfile
        });
      }

      // Cache for 5 minutes
      await redisService.setex(cacheKey, 300, JSON.stringify(friends));
      
      return friends;
    } catch (error) {
      loggingService.logError('Failed to get friends', error);
      throw error;
    }
  }

  async sendFriendRequest(userId, friendId) {
    try {
      // Check if friendship already exists
      const existingFriendship = await this.checkExistingFriendship(userId, friendId);
      
      if (existingFriendship) {
        throw new Error('Friendship already exists or request pending');
      }

      const friendshipId = uuidv4();
      const timestamp = new Date().toISOString();

      // Create friendship record
      await this.db.collection('friends').doc(friendshipId).set({
        userId,
        friendId,
        status: 'pending',
        requestedBy: userId,
        requestedAt: timestamp,
        createdAt: timestamp,
        updatedAt: timestamp
      });

      // Create reverse record for easier querying
      await this.db.collection('friends').doc(`${friendshipId}_reverse`).set({
        userId: friendId,
        friendId: userId,
        status: 'pending',
        requestedBy: userId,
        requestedAt: timestamp,
        createdAt: timestamp,
        updatedAt: timestamp
      });

      // Clear cache
      await this.clearFriendsCache(userId);
      await this.clearFriendsCache(friendId);

      return {
        friendshipId,
        status: 'pending',
        requestedAt: timestamp
      };
    } catch (error) {
      loggingService.logError('Failed to send friend request', error);
      throw error;
    }
  }

  async respondToFriendRequest(userId, friendId, action) {
    try {
      const friendshipSnapshot = await this.db
        .collection('friends')
        .where('userId', '==', userId)
        .where('friendId', '==', friendId)
        .where('status', '==', 'pending')
        .get();

      if (friendshipSnapshot.empty) {
        throw new Error('Friend request not found');
      }

      const timestamp = new Date().toISOString();
      const newStatus = action === 'accept' ? 'accepted' : 'declined';

      // Update both records
      const batch = this.db.batch();
      
      friendshipSnapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          status: newStatus,
          respondedAt: timestamp,
          updatedAt: timestamp
        });
      });

      // Update reverse record
      const reverseSnapshot = await this.db
        .collection('friends')
        .where('userId', '==', friendId)
        .where('friendId', '==', userId)
        .where('status', '==', 'pending')
        .get();

      reverseSnapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
          status: newStatus,
          respondedAt: timestamp,
          updatedAt: timestamp
        });
      });

      await batch.commit();

      // Clear cache
      await this.clearFriendsCache(userId);
      await this.clearFriendsCache(friendId);

      return {
        status: newStatus,
        respondedAt: timestamp
      };
    } catch (error) {
      loggingService.logError('Failed to respond to friend request', error);
      throw error;
    }
  }

  async removeFriend(userId, friendId) {
    try {
      // Find and delete friendship records
      const friendshipSnapshot = await this.db
        .collection('friends')
        .where('userId', '==', userId)
        .where('friendId', '==', friendId)
        .get();

      const reverseSnapshot = await this.db
        .collection('friends')
        .where('userId', '==', friendId)
        .where('friendId', '==', userId)
        .get();

      const batch = this.db.batch();
      
      friendshipSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      reverseSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      // Clear cache
      await this.clearFriendsCache(userId);
      await this.clearFriendsCache(friendId);

      return { success: true };
    } catch (error) {
      loggingService.logError('Failed to remove friend', error);
      throw error;
    }
  }

  // CHALLENGES

  async getChallenges(userId, filters = {}) {
    try {
      let query = this.db.collection('challenges');
      
      // Filter by user involvement
      query = query.where('participants', 'array-contains', userId);
      
      if (filters.status) {
        query = query.where('status', '==', filters.status);
      }
      
      if (filters.type) {
        query = query.where('type', '==', filters.type);
      }

      query = query.orderBy('createdAt', 'desc').limit(20);

      const snapshot = await query.get();
      const challenges = snapshot.docs.map(doc => ({
        challengeId: doc.id,
        ...doc.data()
      }));

      return challenges;
    } catch (error) {
      loggingService.logError('Failed to get challenges', error);
      throw error;
    }
  }

  async createChallenge(challengerId, challengedId, config) {
    try {
      const challengeId = uuidv4();
      const timestamp = new Date().toISOString();

      const challenge = {
        challengeId,
        challengerId,
        challengedId,
        type: 'direct',
        config,
        status: 'pending',
        participants: [challengerId, challengedId],
        results: null,
        messages: [],
        createdAt: timestamp,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        updatedAt: timestamp
      };

      await this.db.collection('challenges').doc(challengeId).set(challenge);

      return challenge;
    } catch (error) {
      loggingService.logError('Failed to create challenge', error);
      throw error;
    }
  }

  async respondToChallenge(challengeId, userId, action) {
    try {
      const challengeDoc = await this.db.collection('challenges').doc(challengeId).get();
      
      if (!challengeDoc.exists) {
        throw new Error('Challenge not found');
      }

      const challenge = challengeDoc.data();
      
      if (challenge.challengedId !== userId) {
        throw new Error('Not authorized to respond to this challenge');
      }

      if (challenge.status !== 'pending') {
        throw new Error('Challenge is no longer pending');
      }

      const newStatus = action === 'accept' ? 'accepted' : 'declined';
      const timestamp = new Date().toISOString();

      await this.db.collection('challenges').doc(challengeId).update({
        status: newStatus,
        respondedAt: timestamp,
        updatedAt: timestamp
      });

      return {
        challengeId,
        status: newStatus,
        challengerId: challenge.challengerId,
        respondedAt: timestamp
      };
    } catch (error) {
      loggingService.logError('Failed to respond to challenge', error);
      throw error;
    }
  }

  async completeChallenge(challengeId, userId, score, sessionId) {
    try {
      const challengeDoc = await this.db.collection('challenges').doc(challengeId).get();
      
      if (!challengeDoc.exists) {
        throw new Error('Challenge not found');
      }

      const challenge = challengeDoc.data();
      
      if (!challenge.participants.includes(userId)) {
        throw new Error('Not a participant in this challenge');
      }

      // Update participant's completion
      const updatedParticipants = challenge.participants.map(participantId => {
        if (participantId === userId) {
          return {
            userId: participantId,
            status: 'completed',
            score,
            completedAt: new Date().toISOString(),
            sessionId
          };
        }
        return challenge.participantData?.[participantId] || {
          userId: participantId,
          status: 'pending'
        };
      });

      // Check if challenge is fully completed
      const allCompleted = updatedParticipants.every(p => p.status === 'completed');
      
      let results = null;
      if (allCompleted) {
        results = this.calculateChallengeResults(updatedParticipants);
      }

      await this.db.collection('challenges').doc(challengeId).update({
        participantData: updatedParticipants.reduce((acc, p) => {
          acc[p.userId] = p;
          return acc;
        }, {}),
        status: allCompleted ? 'completed' : 'in_progress',
        results,
        updatedAt: new Date().toISOString(),
        ...(allCompleted && { completedAt: new Date().toISOString() })
      });

      return {
        challengeId,
        completed: allCompleted,
        participants: updatedParticipants,
        results
      };
    } catch (error) {
      loggingService.logError('Failed to complete challenge', error);
      throw error;
    }
  }

  // STUDY GROUPS

  async getUserStudyGroups(userId) {
    try {
      const groupsSnapshot = await this.db
        .collection('studyGroups')
        .where('members', 'array-contains', userId)
        .get();

      const groups = groupsSnapshot.docs.map(doc => ({
        groupId: doc.id,
        ...doc.data()
      }));

      return groups;
    } catch (error) {
      loggingService.logError('Failed to get user study groups', error);
      throw error;
    }
  }

  async createStudyGroup(groupData) {
    try {
      const groupId = uuidv4();
      const timestamp = new Date().toISOString();

      const group = {
        groupId,
        ...groupData,
        members: [groupData.createdBy],
        activity: {
          totalSessions: 0,
          totalMessages: 0,
          averageScore: 0,
          activeMembers: 1,
          lastActivity: timestamp
        },
        challenges: [],
        createdAt: timestamp,
        updatedAt: timestamp
      };

      await this.db.collection('studyGroups').doc(groupId).set(group);

      return group;
    } catch (error) {
      loggingService.logError('Failed to create study group', error);
      throw error;
    }
  }

  async joinStudyGroup(groupId, userId) {
    try {
      const groupDoc = await this.db.collection('studyGroups').doc(groupId).get();
      
      if (!groupDoc.exists) {
        throw new Error('Study group not found');
      }

      const group = groupDoc.data();
      
      if (group.members.includes(userId)) {
        throw new Error('Already a member of this group');
      }

      if (group.settings?.maxMembers && group.members.length >= group.settings.maxMembers) {
        throw new Error('Group is full');
      }

      await this.db.collection('studyGroups').doc(groupId).update({
        members: [...group.members, userId],
        'activity.activeMembers': group.members.length + 1,
        updatedAt: new Date().toISOString()
      });

      return { success: true, groupId };
    } catch (error) {
      loggingService.logError('Failed to join study group', error);
      throw error;
    }
  }

  async leaveStudyGroup(groupId, userId) {
    try {
      const groupDoc = await this.db.collection('studyGroups').doc(groupId).get();
      
      if (!groupDoc.exists) {
        throw new Error('Study group not found');
      }

      const group = groupDoc.data();
      
      if (!group.members.includes(userId)) {
        throw new Error('Not a member of this group');
      }

      const updatedMembers = group.members.filter(id => id !== userId);

      await this.db.collection('studyGroups').doc(groupId).update({
        members: updatedMembers,
        'activity.activeMembers': updatedMembers.length,
        updatedAt: new Date().toISOString()
      });

      return { success: true, groupId };
    } catch (error) {
      loggingService.logError('Failed to leave study group', error);
      throw error;
    }
  }

  // SOCIAL FEED

  async getSocialFeed(userId, options = {}) {
    try {
      const { limit = 20, offset = 0 } = options;
      
      // Get user's friends to filter feed
      const friends = await this.getFriends(userId, 'accepted');
      const friendIds = friends.map(f => f.friendId);
      
      // Include user's own posts
      const relevantUserIds = [userId, ...friendIds];
      
      let query = this.db.collection('socialFeed');
      
      // Filter by visibility and user relevance
      query = query.where('userId', 'in', relevantUserIds.slice(0, 10)); // Firestore limit
      query = query.orderBy('createdAt', 'desc');
      query = query.limit(limit);
      
      if (offset > 0) {
        // For pagination, you'd need to implement cursor-based pagination
        // This is a simplified version
      }

      const snapshot = await query.get();
      const posts = snapshot.docs.map(doc => ({
        postId: doc.id,
        ...doc.data()
      }));

      return posts;
    } catch (error) {
      loggingService.logError('Failed to get social feed', error);
      throw error;
    }
  }

  async createSocialPost(postData) {
    try {
      const postId = uuidv4();
      const timestamp = new Date().toISOString();

      const post = {
        postId,
        ...postData,
        interactions: {
          likes: [],
          comments: [],
          shares: []
        },
        createdAt: timestamp,
        updatedAt: timestamp
      };

      await this.db.collection('socialFeed').doc(postId).set(post);

      return post;
    } catch (error) {
      loggingService.logError('Failed to create social post', error);
      throw error;
    }
  }

  async likePost(postId, userId, action) {
    try {
      const postDoc = await this.db.collection('socialFeed').doc(postId).get();
      
      if (!postDoc.exists) {
        throw new Error('Post not found');
      }

      const post = postDoc.data();
      const likes = post.interactions?.likes || [];
      
      let updatedLikes;
      if (action === 'like') {
        if (!likes.some(like => like.userId === userId)) {
          updatedLikes = [...likes, {
            userId,
            timestamp: new Date().toISOString()
          }];
        } else {
          updatedLikes = likes;
        }
      } else {
        updatedLikes = likes.filter(like => like.userId !== userId);
      }

      await this.db.collection('socialFeed').doc(postId).update({
        'interactions.likes': updatedLikes,
        updatedAt: new Date().toISOString()
      });

      return {
        postId,
        likes: updatedLikes,
        likeCount: updatedLikes.length
      };
    } catch (error) {
      loggingService.logError('Failed to like/unlike post', error);
      throw error;
    }
  }

  // HELPER METHODS

  async getUserProfile(userId) {
    try {
      const userDoc = await this.db.collection('users').doc(userId).get();
      
      if (!userDoc.exists) {
        return null;
      }

      const userData = userDoc.data();
      return {
        userId,
        username: userData.profile?.username,
        level: userData.gamification?.level?.current || 1,
        xp: userData.gamification?.level?.xp || 0,
        streak: userData.gamification?.streaks?.current || 0,
        lastActive: userData.profile?.lastActive
      };
    } catch (error) {
      loggingService.logError('Failed to get user profile', error);
      return null;
    }
  }

  async checkExistingFriendship(userId, friendId) {
    try {
      const snapshot = await this.db
        .collection('friends')
        .where('userId', '==', userId)
        .where('friendId', '==', friendId)
        .get();

      return !snapshot.empty;
    } catch (error) {
      loggingService.logError('Failed to check existing friendship', error);
      return false;
    }
  }

  calculateChallengeResults(participants) {
    const sortedParticipants = participants.sort((a, b) => b.score - a.score);
    const winnerId = sortedParticipants[0].userId;
    const winnerScore = sortedParticipants[0].score;
    const isTie = sortedParticipants.length > 1 && sortedParticipants[0].score === sortedParticipants[1].score;

    return {
      winnerId: isTie ? null : winnerId,
      winnerScore,
      margin: isTie ? 0 : winnerScore - sortedParticipants[1].score,
      isTie,
      rankings: sortedParticipants.map((p, index) => ({
        userId: p.userId,
        rank: index + 1,
        score: p.score
      }))
    };
  }

  async clearFriendsCache(userId) {
    const keys = [
      `friends:${userId}:accepted`,
      `friends:${userId}:pending`,
      `friends:${userId}:declined`
    ];
    
    for (const key of keys) {
      await redisService.del(key);
    }
  }
}

export default new SocialService();

import { getDb, collection, doc, getDoc, setDoc, updateDoc, serverTimestamp, query, where, orderBy, limit, getDocs } from '../config/firebase.js';
import loggingService from './loggingService.js';

class GamificationService {
  constructor() {
    this.db = null;
    this.usersCollection = null;
    this.badgesCollection = null;
    this.leaderboardsCollection = null;
    this.achievementsCollection = null;

    // Initialize database connection
    this.initializeDatabase();
  }

  async initializeDatabase() {
    this.db = await getDb();
    this.usersCollection = collection(this.db, 'users');
    this.badgesCollection = collection(this.db, 'badges');
    this.leaderboardsCollection = collection(this.db, 'leaderboards');
    this.achievementsCollection = collection(this.db, 'achievements');

    // Initialize default badges
    this.initializeDefaultBadges();
  }

  async initializeDefaultBadges() {
    const defaultBadges = [
      {
        id: 'first_test',
        name: 'First Steps',
        description: 'Complete your first test',
        icon: '🎯',
        rarity: 'common',
        category: 'milestone',
        requirements: { type: 'test_completed', count: 1 },
        rewards: { xp: 50 }
      },
      {
        id: 'streak_warrior_7',
        name: 'Week Warrior',
        description: 'Maintain a 7-day study streak',
        icon: '🔥',
        rarity: 'rare',
        category: 'consistency',
        requirements: { type: 'streak', count: 7 },
        rewards: { xp: 200, title: 'Consistent Learner' }
      },
      {
        id: 'physics_master',
        name: 'Physics Master',
        description: 'Score 90%+ in 5 Physics tests',
        icon: '⚛️',
        rarity: 'epic',
        category: 'subject_mastery',
        requirements: { type: 'subject_scores', subject: 'physics', minScore: 90, count: 5 },
        rewards: { xp: 500, title: 'Physics Master' }
      },
      {
        id: 'speed_demon',
        name: 'Speed Demon',
        description: 'Answer 10 questions in under 30 seconds each',
        icon: '⚡',
        rarity: 'rare',
        category: 'performance',
        requirements: { type: 'speed_answers', maxTime: 30, count: 10 },
        rewards: { xp: 300 }
      },
      {
        id: 'perfectionist',
        name: 'Perfectionist',
        description: 'Score 100% on any test',
        icon: '💎',
        rarity: 'legendary',
        category: 'achievement',
        requirements: { type: 'perfect_score', score: 100 },
        rewards: { xp: 1000, title: 'Perfectionist' }
      }
    ];

    for (const badge of defaultBadges) {
      try {
        const badgeRef = doc(this.badgesCollection, badge.id);
        const badgeDoc = await getDoc(badgeRef);

        if (!badgeDoc.exists()) {
          await setDoc(badgeRef, {
            ...badge,
            createdAt: serverTimestamp()
          });
        }
      } catch (error) {
        loggingService.logError(error, { badgeId: badge.id });
      }
    }
  }

  async initializeUserGamification(userId, userData = {}) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        const initialData = {
          profile: {
            username: userData.username || `User${userId.slice(-6)}`,
            email: userData.email || '',
            avatar: userData.avatar || '',
            joinDate: serverTimestamp(),
            lastActive: serverTimestamp()
          },
          gamification: {
            level: 1,
            xp: 0,
            totalXp: 0,
            streak: {
              current: 0,
              longest: 0,
              lastStudyDate: null
            },
            badges: [],
            achievements: {},
            dailyGoals: {
              questionsTarget: 20,
              questionsCompleted: 0,
              lastResetDate: new Date().toDateString()
            }
          },
          analytics: {
            totalQuestionsAnswered: 0,
            totalTestsCompleted: 0,
            averageAccuracy: 0,
            totalStudyTime: 0,
            subjectPerformance: {},
            weakTopics: [],
            strongTopics: []
          },
          social: {
            friends: [],
            studyGroups: [],
            challenges: {
              sent: [],
              received: [],
              completed: []
            }
          }
        };

        await setDoc(userRef, initialData);
        loggingService.logInfo('User gamification initialized', { userId });
        return initialData;
      }

      return userDoc.data();
    } catch (error) {
      loggingService.logError(error, { userId });
      throw error;
    }
  }

  async awardXP(userId, amount, source = 'general', metadata = {}) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        await this.initializeUserGamification(userId);
      }

      const currentData = userDoc.data() || {};
      const currentXP = currentData.gamification?.xp || 0;
      const currentTotalXP = currentData.gamification?.totalXp || 0;
      const currentLevel = currentData.gamification?.level || 1;

      const newXP = currentXP + amount;
      const newTotalXP = currentTotalXP + amount;
      const newLevel = this.calculateLevel(newTotalXP);

      const updateData = {
        'gamification.xp': newXP,
        'gamification.totalXp': newTotalXP,
        'gamification.level': newLevel,
        'profile.lastActive': serverTimestamp()
      };

      await updateDoc(userRef, updateData);

      // Check for level up
      const leveledUp = newLevel > currentLevel;
      if (leveledUp) {
        await this.handleLevelUp(userId, newLevel);
      }

      // Check for achievements
      const newAchievements = await this.checkAchievements(userId, 'xp_earned', {
        amount,
        source,
        totalXP: newTotalXP,
        ...metadata
      });

      loggingService.logInfo('XP awarded', { userId, amount, source, newXP, newLevel, leveledUp });

      return {
        xpAwarded: amount,
        newXP,
        newTotalXP,
        newLevel,
        leveledUp,
        newAchievements
      };
    } catch (error) {
      loggingService.logError(error, { userId, amount, source });
      throw error;
    }
  }

  calculateLevel(totalXP) {
    // Level formula: Level = floor(sqrt(totalXP / 100)) + 1
    // This creates a curve where each level requires more XP
    return Math.floor(Math.sqrt(totalXP / 100)) + 1;
  }

  calculateXPForLevel(level) {
    // Inverse of level formula
    return Math.pow(level - 1, 2) * 100;
  }

  async updateStreak(userId) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        await this.initializeUserGamification(userId);
        return { currentStreak: 1, streakMaintained: true, isNewStreak: true };
      }

      const userData = userDoc.data();
      const today = new Date().toDateString();
      const lastStudyDate = userData.gamification?.streak?.lastStudyDate;
      const currentStreak = userData.gamification?.streak?.current || 0;
      const longestStreak = userData.gamification?.streak?.longest || 0;

      let newStreak = currentStreak;
      let streakMaintained = false;
      let isNewStreak = false;

      if (!lastStudyDate) {
        // First time studying
        newStreak = 1;
        streakMaintained = true;
        isNewStreak = true;
      } else {
        const lastDate = new Date(lastStudyDate);
        const todayDate = new Date(today);
        const daysDiff = Math.floor((todayDate - lastDate) / (1000 * 60 * 60 * 24));

        if (daysDiff === 0) {
          // Already studied today
          streakMaintained = true;
        } else if (daysDiff === 1) {
          // Consecutive day
          newStreak = currentStreak + 1;
          streakMaintained = true;
        } else {
          // Streak broken
          newStreak = 1;
          streakMaintained = false;
          isNewStreak = true;
        }
      }

      const newLongestStreak = Math.max(longestStreak, newStreak);

      await updateDoc(userRef, {
        'gamification.streak.current': newStreak,
        'gamification.streak.longest': newLongestStreak,
        'gamification.streak.lastStudyDate': today
      });

      // Award streak bonus XP
      if (streakMaintained && newStreak > 1) {
        const bonusXP = Math.min(newStreak * 5, 100); // Max 100 XP bonus
        await this.awardXP(userId, bonusXP, 'streak_bonus', { streak: newStreak });
      }

      // Check for streak achievements
      await this.checkAchievements(userId, 'streak_updated', {
        currentStreak: newStreak,
        longestStreak: newLongestStreak,
        streakMaintained
      });

      return {
        currentStreak: newStreak,
        longestStreak: newLongestStreak,
        streakMaintained,
        isNewStreak
      };
    } catch (error) {
      loggingService.logError(error, { userId });
      throw error;
    }
  }

  async checkAchievements(userId, action, data) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);
      const userData = userDoc.data();

      const badgesQuery = query(this.badgesCollection);
      const badgesSnapshot = await getDocs(badgesQuery);
      const allBadges = badgesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      const userBadges = userData.gamification?.badges || [];
      const newAchievements = [];

      for (const badge of allBadges) {
        if (userBadges.includes(badge.id)) continue; // Already earned

        const earned = await this.checkBadgeRequirement(userId, badge, action, data, userData);
        if (earned) {
          // Award badge
          await updateDoc(userRef, {
            'gamification.badges': [...userBadges, badge.id],
            [`gamification.achievements.${badge.id}`]: {
              unlockedAt: serverTimestamp(),
              progress: 100
            }
          });

          // Award badge XP
          if (badge.rewards?.xp) {
            await this.awardXP(userId, badge.rewards.xp, 'badge_earned', { badgeId: badge.id });
          }

          newAchievements.push(badge);
          loggingService.logInfo('Badge earned', { userId, badgeId: badge.id });
        }
      }

      return newAchievements;
    } catch (error) {
      loggingService.logError(error, { userId, action });
      return [];
    }
  }

  async checkBadgeRequirement(userId, badge, action, data, userData) {
    const req = badge.requirements;

    switch (req.type) {
      case 'test_completed':
        if (action === 'test_completed') {
          const totalTests = userData.analytics?.totalTestsCompleted || 0;
          return totalTests >= req.count;
        }
        break;

      case 'streak':
        if (action === 'streak_updated') {
          return data.currentStreak >= req.count;
        }
        break;

      case 'subject_scores':
        if (action === 'test_completed' && data.subject === req.subject) {
          const subjectPerf = userData.analytics?.subjectPerformance?.[req.subject];
          if (subjectPerf && data.score >= req.minScore) {
            // Count high scores in this subject
            const highScoreCount = await this.countHighScoresForSubject(userId, req.subject, req.minScore);
            return highScoreCount >= req.count;
          }
        }
        break;

      case 'speed_answers':
        if (action === 'question_answered' && data.timeSpent <= req.maxTime) {
          // This would need to track fast answers over time
          return await this.countFastAnswers(userId, req.maxTime) >= req.count;
        }
        break;

      case 'perfect_score':
        if (action === 'test_completed') {
          return data.score >= req.score;
        }
        break;

      case 'xp_earned':
        if (action === 'xp_earned') {
          return data.totalXP >= req.totalXP;
        }
        break;
    }

    return false;
  }

  async getLeaderboard(type = 'daily', subject = null, limit = 50) {
    try {
      const today = new Date();
      let startDate, endDate;

      switch (type) {
        case 'daily':
          startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          endDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
          break;
        case 'weekly':
          const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
          startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
          endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly':
          startDate = new Date(today.getFullYear(), today.getMonth(), 1);
          endDate = new Date(today.getFullYear(), today.getMonth() + 1, 1);
          break;
        case 'all-time':
        default:
          // For all-time, we'll sort by total XP
          const usersQuery = query(
            this.usersCollection,
            orderBy('gamification.totalXp', 'desc'),
            limit(limit)
          );
          const snapshot = await getDocs(usersQuery);
          
          return snapshot.docs.map((doc, index) => {
            const data = doc.data();
            return {
              rank: index + 1,
              userId: doc.id,
              username: data.profile?.username || 'Anonymous',
              avatar: data.profile?.avatar || '',
              xp: data.gamification?.totalXp || 0,
              level: data.gamification?.level || 1,
              streak: data.gamification?.streak?.current || 0
            };
          });
      }

      // For time-based leaderboards, we'd need to implement session tracking
      // For now, return all-time leaderboard
      return this.getLeaderboard('all-time', subject, limit);

    } catch (error) {
      loggingService.logError(error, { type, subject });
      throw error;
    }
  }

  async getUserStats(userId) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        return null;
      }

      const userData = userDoc.data();
      const gamification = userData.gamification || {};
      const analytics = userData.analytics || {};

      // Calculate rank
      const rank = await this.getUserRank(userId);

      return {
        level: gamification.level || 1,
        xp: gamification.xp || 0,
        totalXp: gamification.totalXp || 0,
        streak: gamification.streak?.current || 0,
        longestStreak: gamification.streak?.longest || 0,
        badges: gamification.badges || [],
        rank: rank,
        totalTests: analytics.totalTestsCompleted || 0,
        averageAccuracy: analytics.averageAccuracy || 0,
        totalStudyTime: analytics.totalStudyTime || 0,
        strongTopics: analytics.strongTopics || [],
        weakTopics: analytics.weakTopics || []
      };
    } catch (error) {
      loggingService.logError(error, { userId });
      throw error;
    }
  }

  async getUserRank(userId) {
    try {
      const userRef = doc(this.usersCollection, userId);
      const userDoc = await getDoc(userRef);
      
      if (!userDoc.exists()) return null;

      const userXP = userDoc.data().gamification?.totalXp || 0;
      
      const higherXPQuery = query(
        this.usersCollection,
        where('gamification.totalXp', '>', userXP)
      );
      
      const higherXPSnapshot = await getDocs(higherXPQuery);
      return higherXPSnapshot.size + 1;
    } catch (error) {
      loggingService.logError(error, { userId });
      return null;
    }
  }

  async handleLevelUp(userId, newLevel) {
    try {
      // Award level up bonus
      const bonusXP = newLevel * 50;
      await this.awardXP(userId, bonusXP, 'level_up', { level: newLevel });

      // Check for level-based achievements
      await this.checkAchievements(userId, 'level_up', { level: newLevel });

      loggingService.logInfo('User leveled up', { userId, newLevel });
    } catch (error) {
      loggingService.logError(error, { userId, newLevel });
    }
  }

  // Helper methods for complex badge requirements
  async countHighScoresForSubject(userId, subject, minScore) {
    // This would require querying test sessions
    // For now, return a placeholder
    return 0;
  }

  async countFastAnswers(userId, maxTime) {
    // This would require querying question responses
    // For now, return a placeholder
    return 0;
  }
}

export default new GamificationService();
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { getDb } from '../config/firebase.js';
import loggingService from '../services/loggingService.js';

// Rate limiting configuration
export const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        loggingService.logWarning('Rate limit exceeded', {
            ip: req.ip,
            path: req.path,
            headers: req.headers
        });
        res.status(429).json({
            error: 'Too many requests, please try again later'
        });
    }
});

// Security headers middleware using helmet
export const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://api.openai.com", "https://*.firebaseio.com"],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: []
        }
    },
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: "same-site" },
    dnsPrefetchControl: true,
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: true,
    ieNoOpen: true,
    noSniff: true,
    referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    xssFilter: true
});

// Anti-scraping middleware
export const antiScraping = async (req, res, next) => {
    try {
        // Skip anti-scraping for authentication routes
        const authRoutes = ['/api/auth/login', '/api/auth/register', '/api/auth/anonymous', '/api/auth/profile'];
        if (authRoutes.includes(req.path)) {
            return next();
        }

        const db = await getDb();
        const requestsRef = db.collection('api_requests');

        // Check for suspicious patterns
        const isSuspicious = checkSuspiciousPatterns(req);
        if (isSuspicious) {
            loggingService.logWarning('Suspicious request pattern detected', {
                ip: req.ip,
                path: req.path,
                headers: req.headers
            });
            return res.status(403).json({ error: 'Access denied' });
        }

        // Track request patterns
        const timestamp = Date.now();
        await requestsRef.add({
            ip: req.ip,
            userAgent: req.headers['user-agent'],
            path: req.path,
            timestamp,
            headers: req.headers
        });

        // Check request frequency
        const recentRequests = await requestsRef
            .where('ip', '==', req.ip)
            .where('timestamp', '>', timestamp - 60000) // Last minute
            .get();

        if (recentRequests.size > 60) { // More than 60 requests per minute
            loggingService.logWarning('High frequency requests detected', {
                ip: req.ip,
                count: recentRequests.size
            });
            return res.status(429).json({ error: 'Too many requests' });
        }

        next();
    } catch (error) {
        loggingService.logError('Error in anti-scraping middleware', { error });
        next(error);
    }
};

// Helper function to check for suspicious patterns
function checkSuspiciousPatterns(req) {
    const userAgent = req.headers['user-agent'] || '';
    const acceptLang = req.headers['accept-language'] || '';
    const acceptEnc = req.headers['accept-encoding'] || '';

    // Bot patterns
    const botPatterns = [
        /bot/i, /spider/i, /crawl/i, /APIs-Google/i,
        /AdsBot/i, /Googlebot/i, /mediapartners/i, /Python/i,
        /curl/i, /wget/i, /scraping/i, /phantomjs/i,
        /selenium/i, /nightmare/i, /jsdom/i, /headless/i
    ];

    if (botPatterns.some(pattern => pattern.test(userAgent))) {
        return true;
    }

    // Check for missing or suspicious headers
    if (!acceptLang || !acceptEnc || !userAgent) {
        return true;
    }

    // Check for automated tool signatures
    const automatedTools = [
        'python', 'ruby', 'node', 'java', 'curl', 'wget',
        'apache', 'php', 'perl', 'scripting', 'automation'
    ];

    if (automatedTools.some(tool => userAgent.toLowerCase().includes(tool))) {
        return true;
    }

    return false;
}

// Export all middleware
export default {
    apiLimiter,
    securityHeaders,
    antiScraping
};

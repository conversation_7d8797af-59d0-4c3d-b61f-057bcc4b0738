import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';
import crypto from 'crypto';
import loggingService from '../services/loggingService.js';

/**
 * Advanced Security Middleware
 * Comprehensive security measures for the LLM Test Series application
 */

// Rate limiting configurations
export const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      loggingService.logWarning('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.path
      });
      res.status(429).json({ error: message });
    }
  });
};

// Different rate limits for different endpoints
export const authRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts
  'Too many authentication attempts, please try again later'
);

export const apiRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests
  'Too many API requests, please try again later'
);

export const questionGenerationRateLimit = createRateLimiter(
  60 * 1000, // 1 minute
  10, // 10 questions per minute
  'Too many question generation requests, please slow down'
);

// CORS configuration
export const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://llm-test-series.vercel.app',
      'https://llm-test-series.netlify.app'
    ];
    
    if (process.env.NODE_ENV === 'development') {
      allowedOrigins.push('http://localhost:3000', 'http://localhost:3001');
    }
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      loggingService.logWarning('CORS blocked request', { origin });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

// Helmet security headers
export const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.openai.com", "https://api.anthropic.com"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Content protection middleware
export const contentProtection = (req, res, next) => {
  // Add headers to prevent content theft
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Prevent caching of sensitive content
  if (req.path.includes('/questions') || req.path.includes('/test-sessions')) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  next();
};

// Request fingerprinting for abuse detection
export const requestFingerprinting = (req, res, next) => {
  const fingerprint = crypto.createHash('sha256')
    .update(req.ip + req.get('User-Agent') + req.get('Accept-Language'))
    .digest('hex');
  
  req.fingerprint = fingerprint;
  next();
};

// Suspicious activity detection
const suspiciousPatterns = [
  /sql\s*injection/i,
  /union\s+select/i,
  /script\s*>/i,
  /<\s*script/i,
  /javascript:/i,
  /vbscript:/i,
  /onload\s*=/i,
  /onerror\s*=/i
];

export const suspiciousActivityDetection = (req, res, next) => {
  const checkString = JSON.stringify(req.body) + req.url + JSON.stringify(req.query);
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(checkString)) {
      loggingService.logError('Suspicious activity detected', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        fingerprint: req.fingerprint,
        pattern: pattern.toString(),
        request: {
          method: req.method,
          url: req.url,
          body: req.body,
          query: req.query
        }
      });
      
      return res.status(400).json({ error: 'Invalid request detected' });
    }
  }
  
  next();
};

// API key validation for external services
export const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (req.path.includes('/admin') || req.path.includes('/internal')) {
    if (!apiKey || apiKey !== process.env.INTERNAL_API_KEY) {
      loggingService.logWarning('Invalid API key attempt', {
        ip: req.ip,
        path: req.path,
        providedKey: apiKey ? 'PROVIDED' : 'MISSING'
      });
      return res.status(401).json({ error: 'Invalid API key' });
    }
  }
  
  next();
};

// Request size limiting
export const requestSizeLimit = (req, res, next) => {
  const contentLength = parseInt(req.get('Content-Length') || '0');
  const maxSize = 1024 * 1024; // 1MB
  
  if (contentLength > maxSize) {
    loggingService.logWarning('Request too large', {
      ip: req.ip,
      contentLength,
      maxSize,
      path: req.path
    });
    return res.status(413).json({ error: 'Request entity too large' });
  }
  
  next();
};

// Honeypot endpoints to catch bots
export const honeypotEndpoints = (app) => {
  const honeypotPaths = [
    '/admin',
    '/wp-admin',
    '/administrator',
    '/phpmyadmin',
    '/.env',
    '/config.php',
    '/wp-config.php'
  ];
  
  honeypotPaths.forEach(path => {
    app.all(path, (req, res) => {
      loggingService.logWarning('Honeypot triggered', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method,
        fingerprint: req.fingerprint
      });
      
      // Return a fake response to waste attacker's time
      setTimeout(() => {
        res.status(404).json({ error: 'Not found' });
      }, 5000);
    });
  });
};

// IP whitelist/blacklist
const blacklistedIPs = new Set();
const whitelistedIPs = new Set(['127.0.0.1', '::1']);

export const ipFiltering = (req, res, next) => {
  const clientIP = req.ip;
  
  if (blacklistedIPs.has(clientIP)) {
    loggingService.logWarning('Blacklisted IP blocked', { ip: clientIP });
    return res.status(403).json({ error: 'Access denied' });
  }
  
  // In production, you might want to implement whitelist-only access for admin endpoints
  if (req.path.includes('/admin') && process.env.NODE_ENV === 'production') {
    if (!whitelistedIPs.has(clientIP)) {
      loggingService.logWarning('Non-whitelisted IP blocked from admin', { ip: clientIP });
      return res.status(403).json({ error: 'Access denied' });
    }
  }
  
  next();
};

// Add IP to blacklist (can be called from other parts of the application)
export const blacklistIP = (ip, reason = 'Suspicious activity') => {
  blacklistedIPs.add(ip);
  loggingService.logWarning('IP blacklisted', { ip, reason });
  
  // Auto-remove after 24 hours
  setTimeout(() => {
    blacklistedIPs.delete(ip);
    loggingService.logInfo('IP removed from blacklist', { ip });
  }, 24 * 60 * 60 * 1000);
};

// Security headers middleware
export const securityHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  res.setHeader('Server', 'LLM-Test-Series');
  
  // Add custom security headers
  res.setHeader('X-API-Version', '1.0');
  res.setHeader('X-Request-ID', crypto.randomUUID());
  
  next();
};

// Export all middleware as a single configuration function
export const configureAdvancedSecurity = (app) => {
  // Apply security middleware in order
  app.use(helmetConfig);
  app.use(cors(corsOptions));
  app.use(requestFingerprinting);
  app.use(contentProtection);
  app.use(securityHeaders);
  app.use(requestSizeLimit);
  app.use(ipFiltering);
  app.use(suspiciousActivityDetection);
  app.use(validateApiKey);
  
  // Apply rate limiting
  app.use('/api/auth', authRateLimit);
  app.use('/api/questions/generate', questionGenerationRateLimit);
  app.use('/api', apiRateLimit);
  
  // Set up honeypot endpoints
  honeypotEndpoints(app);
  
  loggingService.logInfo('Advanced security middleware configured');
};

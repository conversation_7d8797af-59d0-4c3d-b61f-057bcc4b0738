import { getDb } from '../firebase.js';

/**
 * Exam Configuration Service
 * Manages centralized exam configurations in Firestore
 */
class ExamConfigService {
  constructor() {
    this.db = null;
    this.configCache = new Map();
    this.CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  }

  async getDbInstance() {
    if (!this.db) {
      this.db = await getDb();
    }
    return this.db;
  }

  /**
   * Get exam configuration, with caching
   */
  async getExamConfig(examId) {
    // Check cache first
    const cached = this.configCache.get(examId);
    if (cached && cached.timestamp > Date.now() - this.CACHE_TTL) {
      return cached.data;
    }

    const db = await this.getDbInstance();
    // Get from Firestore
    const doc = await db.collection('exam_configs').doc(examId).get();
    if (!doc.exists) {
      throw new Error(`Exam configuration not found: ${examId}`);
    }

    const config = doc.data();
    this.configCache.set(examId, {
      data: config,
      timestamp: Date.now()
    });

    return config;
  }

  /**
   * Update exam configuration
   */
  async updateExamConfig(examId, config) {
    const db = await this.getDbInstance();
    await db.collection('exam_configs').doc(examId).set(config, { merge: true });
    this.configCache.delete(examId); // Invalidate cache
    return config;
  }

  /**
   * Get default exam configuration template
   */
  static getDefaultConfig() {
    return {
      // Basic exam info
      name: '',
      description: '',
      type: '',  // e.g., 'teacher_recruitment', 'civil_services'
      duration: 180,  // minutes
      totalMarks: 100,

      // Question configuration
      questions: {
        defaultCount: 50,
        types: ['multiple_choice', 'fill_in_blanks', 'true_false', 'short_answer'],
        typeDistribution: {
          multiple_choice: 0.7,
          fill_in_blanks: 0.1,
          true_false: 0.1,
          short_answer: 0.1
        },
        difficultyLevels: ['easy', 'medium', 'hard', 'expert'],
        difficultyDistribution: {
          easy: 0.2,
          medium: 0.4,
          hard: 0.3,
          expert: 0.1
        },
        markingScheme: {
          correct: 2,
          incorrect: -0.5,
          unattempted: 0
        }
      },

      // Subjects and topics
      subjects: [],  // Will be populated based on syllabus
      topics: {},    // Will be populated based on syllabus

      // LLM configuration
      llm: {
        defaultModel: 'gpt-4',  // For complex questions
        fallbackModel: 'gpt-3.5-turbo',  // For simpler tasks
        promptTemplates: {
          questionGeneration: '',
          answerValidation: '',
          theoryExplanation: '',
          challengeEvaluation: ''
        },
        costOptimization: {
          cacheTimeout: 24 * 60 * 60 * 1000,  // 24 hours
          batchSize: 10  // Number of questions to generate in one call
        }
      },

      // Performance thresholds
      performance: {
        passingScore: 40,
        gradeRanges: {
          distinction: 75,
          firstClass: 60,
          secondClass: 45,
          pass: 40
        },
        adaptiveDifficulty: {
          promotionThreshold: 0.8,  // 80% correct to increase difficulty
          demotionThreshold: 0.4    // 40% correct to decrease difficulty
        }
      },

      // UI customization
      ui: {
        theme: {
          primary: '#007bff',
          secondary: '#6c757d',
          success: '#28a745',
          error: '#dc3545'
        },
        features: {
          timer: true,
          progressBar: true,
          questionNavigation: true,
          markForReview: true,
          darkMode: true
        },
        questionDisplay: {
          shuffleOptions: true,
          showRemainingTime: true,
          showQuestionNumber: true
        }
      },

      // Analytics configuration
      analytics: {
        trackMetrics: [
          'timePerQuestion',
          'accuracyByTopic',
          'difficultyProgression',
          'commonMistakes',
          'learningTrends'
        ],
        learningAnalytics: {
          weakTopicsThreshold: 0.6,  // Below 60% is a weak topic
          strongTopicsThreshold: 0.8  // Above 80% is a strong topic
        },
        reportGeneration: {
          detailed: true,
          includeGraphs: true,
          includeSuggestions: true
        }
      },

      // System settings
      system: {
        maxConcurrentUsers: 1000,
        maintenanceWindow: {
          day: 'Sunday',
          hour: 2  // 2 AM
        },
        caching: {
          questions: true,
          results: true,
          analytics: true
        },
        backup: {
          frequency: 'daily',
          retention: 30  // days
        }
      }
    };
  }

  static getSampleConfig() {
    return {
      name: 'Sample Exam',
      description: 'A sample exam configuration',
      type: 'teacher_recruitment',
      duration: 45,
      totalMarks: 50,
      questions: {
        defaultCount: 25,
        types: ['multiple_choice'],
        typeDistribution: {
          multiple_choice: 1.0
        },
        difficultyLevels: ['easy', 'medium', 'hard'],
        difficultyDistribution: {
          easy: 0.3,
          medium: 0.4,
          hard: 0.3
        },
        markingScheme: {
          correct: 2,
          incorrect: -0.5,
          unattempted: 0
        }
      },
      subjects: ['Mathematics', 'Science'],
      topics: {
        Mathematics: ['Algebra', 'Geometry'],
        Science: ['Physics', 'Chemistry']
      },
      llm: {
        defaultModel: 'gpt-3.5-turbo',
        fallbackModel: 'gpt-3.5-turbo',
        promptTemplates: {
          questionGeneration: 'Generate a multiple choice question about {topic}',
          answerValidation: 'Validate if the answer {answer} is correct for {question}',
          theoryExplanation: 'Explain the theory behind {topic}',
          challengeEvaluation: 'Evaluate if the challenge {challenge} is valid for {answer}'
        },
        costOptimization: {
          cacheTimeout: 12 * 60 * 60 * 1000,
          batchSize: 5
        }
      },
      performance: {
        passingScore: 40,
        gradeRanges: {
          distinction: 75,
          firstClass: 60,
          secondClass: 45,
          pass: 40
        },
        adaptiveDifficulty: {
          promotionThreshold: 0.7,
          demotionThreshold: 0.3
        }
      },
      ui: {
        theme: {
          primary: '#28a745',
          secondary: '#6c757d',
          success: '#28a745',
          error: '#dc3545'
        },
        features: {
          timer: true,
          progressBar: true,
          questionNavigation: true,
          markForReview: true,
          darkMode: false
        },
        questionDisplay: {
          shuffleOptions: true,
          showRemainingTime: true,
          showQuestionNumber: true
        }
      },
      analytics: {
        trackMetrics: ['timePerQuestion', 'accuracyByTopic'],
        learningAnalytics: {
          weakTopicsThreshold: 0.5,
          strongTopicsThreshold: 0.7
        },
        reportGeneration: {
          detailed: true,
          includeGraphs: true,
          includeSuggestions: true
        }
      },
      system: {
        maxConcurrentUsers: 100,
        maintenanceWindow: {
          day: 'Sunday',
          hour: 3
        },
        caching: {
          questions: true,
          results: true,
          analytics: false
        },
        backup: {
          frequency: 'weekly',
          retention: 14
        }
      }
    };
  }
}

export default ExamConfigService;

// Firebase Configuration - Using Mock Service for Development
import { getDb as getMockDb, MockFirestore } from '../services/mockFirebaseService.js';

let db;
let auth;

// Mock Auth service
class MockAuth {
  async createUser(userData) {
    const uid = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    return {
      uid,
      email: userData.email,
      emailVerified: false,
      disabled: false,
      metadata: {
        creationTime: new Date().toISOString(),
        lastSignInTime: null
      },
      customClaims: {},
      ...userData
    };
  }

  async getUser(uid) {
    return {
      uid,
      email: `user${uid}@example.com`,
      emailVerified: true,
      disabled: false
    };
  }

  async verifyIdToken(token) {
    // Mock token verification
    return {
      uid: 'mock-user-id',
      email: '<EMAIL>',
      aud: 'mock-project',
      iss: 'mock-issuer',
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000)
    };
  }

  async createCustomToken(uid, additionalClaims = {}) {
    return `mock_token_${uid}_${Date.now()}`;
  }
}

export const initializeFirebase = () => {
  try {
    console.log('🔥 Initializing Mock Firebase for development...');

    // Use mock Firebase service for development
    db = getMockDb();
    auth = new MockAuth();

    console.log('✅ Mock Firebase initialized successfully');
    return { db, auth };
  } catch (error) {
    console.error('❌ Firebase configuration failed:', error);
    throw error;
  }
};

export const getDb = () => {
  if (!db) {
    initializeFirebase();
  }
  return db;
};

export const getAuth = () => {
  if (!auth) {
    initializeFirebase();
  }
  return auth;
};

// Re-export Firebase-like functions from mock service
export { collection, doc, getDoc, setDoc, updateDoc, serverTimestamp, query, where, orderBy, limit, getDocs } from '../services/mockFirebaseService.js';

export default { initializeFirebase, getDb, getAuth };

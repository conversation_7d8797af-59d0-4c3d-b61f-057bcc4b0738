{"name": "llm-test-series-backend", "version": "1.0.0", "description": "Backend API for AI-Powered Adaptive Learning Platform", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "type:check": "tsc --noEmit", "deploy": "echo 'Deploy script to be configured'"}, "dependencies": {"@netlify/functions": "^2.8.2", "bcrypt": "^6.0.0", "bull": "^4.12.0", "cloudinary": "^1.41.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "firebase": "^10.7.1", "firebase-admin": "^11.11.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-cache": "^5.1.2", "openai": "^4.20.1", "serverless-http": "^3.2.0", "sharp": "^0.33.1", "socket.io": "^4.7.2", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^18.19.111", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "chai": "^5.2.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^2.8.8", "sinon": "^20.0.0", "supertest": "^6.3.4", "typescript": "^5.8.3"}, "engines": {"node": ">=18.19.0", "npm": ">=8.0.0"}}
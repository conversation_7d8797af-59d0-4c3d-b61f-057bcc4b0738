import winston from 'winston';
import loggingService from '../services/loggingService.js';

interface MetricsConfig {
  requestDuration: boolean;
  errorRate: boolean;
  throughput: boolean;
  statusCodes: boolean;
}

interface LlmMetricsConfig {
  requestCount: boolean;
  tokenUsage: boolean;
  latency: boolean;
  costTracking: boolean;
}

interface SystemMetricsConfig {
  cpuUsage: boolean;
  memoryUsage: boolean;
  diskSpace: boolean;
}

interface CacheMetricsConfig {
  hitRate: boolean;
  missRate: boolean;
  size: boolean;
}

interface AlertsConfig {
  errorRate: number;
  apiLatency: number;
  llmLatency: number;
  systemLoad: number;
  costLimit: number;
}

interface MonitoringConfig {
  metrics: {
    api: MetricsConfig;
    llm: LlmMetricsConfig;
    system: SystemMetricsConfig;
    cache: CacheMetricsConfig;
  };
  alerts: AlertsConfig;
  logging: {
    level: string;
    format: winston.Logform.Format;
    transports: winston.transport[];
  };
}
// Configure monitoring settings
export const monitoringConfig: MonitoringConfig = {
  // Application metrics
  metrics: {
    // API metrics
    api: {
      requestDuration: true,
      errorRate: true,
      throughput: true,
      statusCodes: true
    },
    
    // LLM metrics
    llm: {
      requestCount: true,
      tokenUsage: true,
      latency: true,
      costTracking: true
    },
    
    // System metrics
    system: {
      cpuUsage: true,
      memoryUsage: true,
      diskSpace: true
    },
    
    // Cache metrics
    cache: {
      hitRate: true,
      missRate: true,
      size: true
    }
  },
  
  // Alert thresholds
  alerts: {
    errorRate: 0.05, // 5% error rate threshold
    apiLatency: 2000, // 2 seconds
    llmLatency: 5000, // 5 seconds
    systemLoad: 0.8, // 80% CPU/memory threshold
    costLimit: 100 // $100 daily LLM cost limit
  },
  
  // Logging configuration
  logging: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    transports: [
      loggingService.getFileTransport(),
      loggingService.getConsoleTransport()
    ]
  }
};

// Export monitoring functions
export const startMonitoring = () => {
  // Initialize metrics collection
  initializeMetrics();
  
  // Start periodic monitoring
  startPeriodicMonitoring();
  
  // Set up alert handlers
  setupAlertHandlers();
};

const initializeMetrics = () => {
  // Initialize metric collectors
  loggingService.info('Initializing metrics collectors');
};

const startPeriodicMonitoring = () => {
  // Run monitoring tasks every minute
  setInterval(() => {
    // Collect metrics
    loggingService.info('Collecting system metrics');
    
    // Check alert thresholds
    loggingService.info('Checking alert thresholds');
    
    // Update dashboard
    loggingService.info('Updating monitoring dashboard');
  }, 60000);
};

const setupAlertHandlers = () => {
  // Configure alert notifications
  loggingService.info('Setting up alert handlers');
};

// Export monitoring utilities
export const monitoringUtils = {
  // @ts-ignore - loggingService type will be fixed in a separate PR
  recordApiCall: (endpoint: string, duration: number, status: number): void => {
    // Record API metrics
    loggingService.info('API Call', {
      endpoint,
      duration,
      status,
      timestamp: new Date().toISOString()
    });
  },
  
  recordLlmUsage: (model: string, tokens: number, cost: number): void => {
    // Record LLM usage metrics
    loggingService.info('LLM Usage', {
      model,
      tokens,
      cost,
      timestamp: new Date().toISOString()
    });
  },
  
  recordError: (error: Error, context: Record<string, unknown>): void => {
    // Record error metrics
    loggingService.error('Application Error', {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    });
  }
};

// Unit Tests for Gamification Service
import { expect } from 'chai';
import sinon from 'sinon';
import gamificationService from '../../../src/services/gamificationService.js';
import { getDb } from '../../../src/config/firebase.js';

describe('GamificationService', () => {
  let dbStub;
  let collectionStub;
  let docStub;
  let getStub;
  let setStub;
  let updateStub;

  beforeEach(() => {
    // Create stubs for Firebase operations
    setStub = sinon.stub().resolves();
    updateStub = sinon.stub().resolves();
    getStub = sinon.stub();
    docStub = sinon.stub().returns({
      get: getStub,
      set: setStub,
      update: updateStub
    });
    collectionStub = sinon.stub().returns({
      doc: docStub,
      where: sinon.stub().returnsThis(),
      orderBy: sinon.stub().returnsThis(),
      limit: sinon.stub().returnsThis(),
      get: getStub
    });
    dbStub = sinon.stub().returns({
      collection: collectionStub
    });
    
    // Mock the getDb function
    sinon.stub(gamificationService, 'db').value({
      collection: collectionStub
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('getUserGamificationData', () => {
    it('should return user gamification data', async () => {
      const userId = 'test-user-id';
      const mockData = {
        level: { current: 5, xp: 1250 },
        streaks: { current: 7, longest: 15 },
        badges: ['first_test', 'streak_warrior']
      };

      getStub.resolves({
        exists: true,
        data: () => mockData
      });

      const result = await gamificationService.getUserGamificationData(userId);

      expect(result).to.deep.equal(mockData);
      expect(collectionStub).to.have.been.calledWith('gamification');
      expect(docStub).to.have.been.calledWith(userId);
    });

    it('should return default data for new user', async () => {
      const userId = 'new-user-id';

      getStub.resolves({
        exists: false
      });

      const result = await gamificationService.getUserGamificationData(userId);

      expect(result).to.have.property('level');
      expect(result.level).to.have.property('current', 1);
      expect(result.level).to.have.property('xp', 0);
      expect(result).to.have.property('streaks');
      expect(result).to.have.property('badges');
    });
  });

  describe('awardXP', () => {
    it('should award XP and update user data', async () => {
      const userId = 'test-user-id';
      const amount = 100;
      const source = 'test_completion';
      
      const currentData = {
        level: { current: 1, xp: 50, totalXp: 50 },
        statistics: { totalTests: 5 }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.awardXP(userId, amount, source);

      expect(result).to.have.property('newXP', 150);
      expect(result).to.have.property('totalXP', 150);
      expect(updateStub).to.have.been.called;
    });

    it('should handle level up when XP threshold is reached', async () => {
      const userId = 'test-user-id';
      const amount = 200;
      const source = 'test_completion';
      
      const currentData = {
        level: { current: 1, xp: 950, totalXp: 950 }, // Close to level 2 threshold (1000)
        statistics: { totalTests: 10 }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.awardXP(userId, amount, source);

      expect(result).to.have.property('levelUp', true);
      expect(result).to.have.property('newLevel', 2);
    });

    it('should validate XP amount', async () => {
      const userId = 'test-user-id';
      const invalidAmount = -50;
      const source = 'test_completion';

      try {
        await gamificationService.awardXP(userId, invalidAmount, source);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('Invalid XP amount');
      }
    });
  });

  describe('updateStreak', () => {
    it('should maintain streak for consecutive days', async () => {
      const userId = 'test-user-id';
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const currentData = {
        streaks: {
          current: 5,
          longest: 10,
          lastStudyDate: yesterday.toISOString().split('T')[0]
        }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.updateStreak(userId);

      expect(result).to.have.property('streakMaintained', true);
      expect(result).to.have.property('currentStreak', 6);
    });

    it('should reset streak for non-consecutive days', async () => {
      const userId = 'test-user-id';
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      
      const currentData = {
        streaks: {
          current: 5,
          longest: 10,
          lastStudyDate: threeDaysAgo.toISOString().split('T')[0]
        }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.updateStreak(userId);

      expect(result).to.have.property('streakMaintained', false);
      expect(result).to.have.property('currentStreak', 1);
    });

    it('should update longest streak when current exceeds it', async () => {
      const userId = 'test-user-id';
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const currentData = {
        streaks: {
          current: 15,
          longest: 10,
          lastStudyDate: yesterday.toISOString().split('T')[0]
        }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.updateStreak(userId);

      expect(result).to.have.property('newLongestStreak', true);
      expect(result).to.have.property('longestStreak', 16);
    });
  });

  describe('checkAchievements', () => {
    it('should check and award achievements based on context', async () => {
      const userId = 'test-user-id';
      const eventType = 'test_completed';
      const context = {
        accuracy: 95,
        subject: 'Physics',
        consecutiveCorrect: 10
      };

      const currentData = {
        achievements: {
          accuracy_ace: {
            progress: 80,
            isCompleted: false
          }
        },
        statistics: {
          totalTests: 15,
          averageAccuracy: 88
        }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.checkAchievements(userId, eventType, context);

      expect(result).to.be.an('array');
      // Should check for achievements that could be completed with this context
    });

    it('should not award already completed achievements', async () => {
      const userId = 'test-user-id';
      const eventType = 'test_completed';
      const context = { accuracy: 95 };

      const currentData = {
        achievements: {
          accuracy_ace: {
            progress: 100,
            isCompleted: true,
            completedAt: '2024-01-15T10:30:00Z'
          }
        }
      };

      getStub.resolves({
        exists: true,
        data: () => currentData
      });

      const result = await gamificationService.checkAchievements(userId, eventType, context);

      expect(result).to.be.an('array').that.is.empty;
    });
  });

  describe('getLeaderboard', () => {
    it('should return global leaderboard', async () => {
      const type = 'global';
      const limit = 10;

      const mockLeaderboard = [
        { userId: 'user1', level: 25, xp: 5000, username: 'player1' },
        { userId: 'user2', level: 20, xp: 4000, username: 'player2' },
        { userId: 'user3', level: 18, xp: 3500, username: 'player3' }
      ];

      getStub.resolves({
        docs: mockLeaderboard.map(item => ({
          id: item.userId,
          data: () => item
        }))
      });

      const result = await gamificationService.getLeaderboard(type, null, limit);

      expect(result).to.be.an('array');
      expect(result).to.have.length(3);
      expect(result[0]).to.have.property('rank', 1);
      expect(result[0]).to.have.property('userId', 'user1');
    });

    it('should return subject-specific leaderboard', async () => {
      const type = 'subject';
      const subject = 'Physics';
      const limit = 10;

      getStub.resolves({
        docs: []
      });

      const result = await gamificationService.getLeaderboard(type, subject, limit);

      expect(result).to.be.an('array');
      expect(collectionStub).to.have.been.calledWith('gamification');
    });
  });

  describe('calculateLevel', () => {
    it('should calculate correct level for given XP', () => {
      const testCases = [
        { xp: 0, expectedLevel: 1 },
        { xp: 100, expectedLevel: 2 },
        { xp: 250, expectedLevel: 3 },
        { xp: 450, expectedLevel: 4 },
        { xp: 1000, expectedLevel: 6 }
      ];

      testCases.forEach(({ xp, expectedLevel }) => {
        const level = gamificationService.calculateLevel(xp);
        expect(level).to.equal(expectedLevel);
      });
    });
  });

  describe('calculateXPToNextLevel', () => {
    it('should calculate XP needed for next level', () => {
      const testCases = [
        { currentXP: 50, currentLevel: 1, expected: 50 }, // Need 100 total for level 2
        { currentXP: 200, currentLevel: 2, expected: 50 }, // Need 250 total for level 3
        { currentXP: 400, currentLevel: 3, expected: 50 }  // Need 450 total for level 4
      ];

      testCases.forEach(({ currentXP, currentLevel, expected }) => {
        const xpNeeded = gamificationService.calculateXPToNextLevel(currentXP, currentLevel);
        expect(xpNeeded).to.equal(expected);
      });
    });
  });

  describe('getBadgeDefinitions', () => {
    it('should return all badge definitions', async () => {
      const badges = await gamificationService.getBadgeDefinitions();

      expect(badges).to.be.an('object');
      expect(badges).to.have.property('first_test');
      expect(badges.first_test).to.have.property('name');
      expect(badges.first_test).to.have.property('description');
      expect(badges.first_test).to.have.property('category');
      expect(badges.first_test).to.have.property('rarity');
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      const userId = 'test-user-id';
      
      getStub.rejects(new Error('Database connection failed'));

      try {
        await gamificationService.getUserGamificationData(userId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('Database connection failed');
      }
    });

    it('should handle invalid user ID', async () => {
      const invalidUserId = null;

      try {
        await gamificationService.getUserGamificationData(invalidUserId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('Invalid user ID');
      }
    });
  });
});

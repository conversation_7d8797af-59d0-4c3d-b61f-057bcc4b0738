// Integration Tests for LLM Test Series API
import request from 'supertest';
import { expect } from 'chai';
import app from '../../src/index.js';
import { initializeTestDb, cleanupTestDb } from '../helpers/testDb.js';

describe('LLM Test Series API Integration Tests', () => {
  let authToken;
  let testUserId;
  let testSessionId;

  before(async () => {
    await initializeTestDb();
  });

  after(async () => {
    await cleanupTestDb();
  });

  describe('Authentication', () => {
    it('should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'testpassword123',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('user');
      expect(response.body.data).to.have.property('token');
      
      authToken = response.body.data.token;
      testUserId = response.body.data.user.userId;
    });

    it('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'testpassword123'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('token');
    });

    it('should reject invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).to.have.property('success', false);
    });
  });

  describe('Question Generation', () => {
    it('should generate questions with valid parameters', async () => {
      const questionParams = {
        subject: 'Physics',
        topic: 'Mechanics',
        difficulty: 'intermediate',
        count: 5
      };

      const response = await request(app)
        .post('/api/questions/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(questionParams)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data).to.have.length(5);
      
      response.body.data.forEach(question => {
        expect(question).to.have.property('questionId');
        expect(question).to.have.property('content');
        expect(question.content).to.have.property('question');
        expect(question.content).to.have.property('options');
        expect(question.content).to.have.property('correctAnswer');
      });
    });

    it('should validate question generation parameters', async () => {
      const invalidParams = {
        subject: 'Physics',
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/questions/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidParams)
        .expect(400);

      expect(response.body).to.have.property('success', false);
    });
  });

  describe('Test Sessions', () => {
    it('should create a new test session', async () => {
      const sessionConfig = {
        testType: 'practice',
        subject: 'Physics',
        difficulty: 'intermediate',
        questionCount: 10,
        timeLimit: 1200
      };

      const response = await request(app)
        .post('/api/test-sessions/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(sessionConfig)
        .expect(201);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('session');
      expect(response.body.data).to.have.property('questions');
      
      testSessionId = response.body.data.session.sessionId;
    });

    it('should submit an answer', async () => {
      const answerData = {
        questionId: 'test-question-1',
        userAnswer: 'Option A',
        timeSpent: 45,
        confidence: 4
      };

      const response = await request(app)
        .post(`/api/test-sessions/${testSessionId}/answer`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(answerData)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('isCorrect');
      expect(response.body.data).to.have.property('pointsEarned');
    });

    it('should get test results', async () => {
      const response = await request(app)
        .get(`/api/test-sessions/${testSessionId}/results`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('results');
      expect(response.body.data.results).to.have.property('score');
      expect(response.body.data.results).to.have.property('accuracy');
    });
  });

  describe('Gamification', () => {
    it('should get user gamification data', async () => {
      const response = await request(app)
        .get(`/api/gamification/user/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('level');
      expect(response.body.data).to.have.property('xp');
      expect(response.body.data).to.have.property('badges');
    });

    it('should award XP to user', async () => {
      const xpData = {
        userId: testUserId,
        amount: 50,
        source: 'test_completion',
        metadata: { testType: 'practice' }
      };

      const response = await request(app)
        .post('/api/gamification/award-xp')
        .set('Authorization', `Bearer ${authToken}`)
        .send(xpData)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body).to.have.property('xpAwarded', 50);
    });

    it('should get leaderboard', async () => {
      const response = await request(app)
        .get('/api/gamification/leaderboard/global')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.be.an('array');
    });
  });

  describe('Social Features', () => {
    let secondUserId;
    let secondAuthToken;

    before(async () => {
      // Create a second user for social testing
      const userData = {
        email: '<EMAIL>',
        password: 'testpassword123',
        username: 'testuser2',
        firstName: 'Test2',
        lastName: 'User2'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      secondUserId = response.body.data.user.userId;
      secondAuthToken = response.body.data.token;
    });

    it('should send a friend request', async () => {
      const friendRequest = {
        userId: testUserId,
        friendId: secondUserId
      };

      const response = await request(app)
        .post('/api/social/friends/request')
        .set('Authorization', `Bearer ${authToken}`)
        .send(friendRequest)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('status', 'pending');
    });

    it('should accept friend request', async () => {
      const responseData = {
        userId: secondUserId,
        friendId: testUserId,
        action: 'accept'
      };

      const response = await request(app)
        .post('/api/social/friends/respond')
        .set('Authorization', `Bearer ${secondAuthToken}`)
        .send(responseData)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('status', 'accepted');
    });

    it('should get friends list', async () => {
      const response = await request(app)
        .get(`/api/social/friends/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.be.an('array');
      expect(response.body.data).to.have.length.greaterThan(0);
    });

    it('should create a challenge', async () => {
      const challengeData = {
        challengerId: testUserId,
        challengedId: secondUserId,
        config: {
          subject: 'Physics',
          questionCount: 10,
          timeLimit: 600
        }
      };

      const response = await request(app)
        .post('/api/social/challenges/create')
        .set('Authorization', `Bearer ${authToken}`)
        .send(challengeData)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('challengeId');
      expect(response.body.data).to.have.property('status', 'pending');
    });
  });

  describe('Analytics', () => {
    it('should get user analytics', async () => {
      const response = await request(app)
        .get(`/api/analytics/user/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('overview');
      expect(response.body.data).to.have.property('performance');
    });

    it('should get subject-wise analytics', async () => {
      const response = await request(app)
        .get(`/api/analytics/subject/Physics/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).to.have.property('success', true);
      expect(response.body.data).to.have.property('subjectPerformance');
    });
  });

  describe('Error Handling', () => {
    it('should handle unauthorized requests', async () => {
      const response = await request(app)
        .get(`/api/gamification/user/${testUserId}`)
        .expect(401);

      expect(response.body).to.have.property('success', false);
    });

    it('should handle invalid endpoints', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).to.have.property('success', false);
    });

    it('should handle malformed requests', async () => {
      const response = await request(app)
        .post('/api/questions/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send('invalid json')
        .expect(400);

      expect(response.body).to.have.property('success', false);
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async function() {
      this.timeout(10000); // Increase timeout for this test
      
      const requests = [];
      for (let i = 0; i < 105; i++) { // Exceed the rate limit
        requests.push(
          request(app)
            .get('/api/health')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).to.be.greaterThan(0);
    });
  });

  describe('Performance', () => {
    it('should respond to health check quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      const duration = Date.now() - start;
      
      expect(response.body).to.have.property('status', 'healthy');
      expect(duration).to.be.lessThan(100); // Should respond within 100ms
    });

    it('should handle concurrent requests', async () => {
      const concurrentRequests = 10;
      const requests = [];

      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(
          request(app)
            .get('/api/health')
            .set('Authorization', `Bearer ${authToken}`)
        );
      }

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('status', 'healthy');
      });
    });
  });
});

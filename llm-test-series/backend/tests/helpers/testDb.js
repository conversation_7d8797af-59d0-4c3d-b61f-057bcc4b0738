// Test Database Helper Functions
import { initializeApp, cert } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getAuth } from 'firebase-admin/auth';

let testApp;
let testDb;
let testAuth;

export const initializeTestDb = async () => {
  try {
    // Initialize Firebase Admin for testing
    if (!testApp) {
      testApp = initializeApp({
        credential: cert({
          projectId: process.env.FIREBASE_PROJECT_ID || 'llm-test-series-test',
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
        }),
        projectId: process.env.FIREBASE_PROJECT_ID || 'llm-test-series-test'
      }, 'test-app');
    }

    testDb = getFirestore(testApp);
    testAuth = getAuth(testApp);

    // Set up test collections with initial data
    await setupTestCollections();
    
    console.log('Test database initialized successfully');
    return { db: testDb, auth: testAuth };
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    throw error;
  }
};

export const cleanupTestDb = async () => {
  try {
    if (!testDb) return;

    // Clean up test data
    await cleanupTestCollections();
    
    console.log('Test database cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup test database:', error);
    throw error;
  }
};

const setupTestCollections = async () => {
  // Create test users
  await testDb.collection('users').doc('test-user-1').set({
    userId: 'test-user-1',
    profile: {
      username: 'testuser1',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User1',
      joinDate: new Date().toISOString(),
      lastActive: new Date().toISOString()
    },
    gamification: {
      level: { current: 5, xp: 1250, totalXp: 1250 },
      streaks: { current: 7, longest: 15 },
      badges: ['first_test', 'streak_warrior']
    },
    analytics: {
      totalQuestionsAnswered: 150,
      averageAccuracy: 87.5,
      subjectPerformance: {
        Physics: { accuracy: 89.2, questionsAnswered: 80 },
        Chemistry: { accuracy: 85.8, questionsAnswered: 70 }
      }
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  await testDb.collection('users').doc('test-user-2').set({
    userId: 'test-user-2',
    profile: {
      username: 'testuser2',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User2',
      joinDate: new Date().toISOString(),
      lastActive: new Date().toISOString()
    },
    gamification: {
      level: { current: 3, xp: 650, totalXp: 650 },
      streaks: { current: 3, longest: 8 },
      badges: ['first_test']
    },
    analytics: {
      totalQuestionsAnswered: 75,
      averageAccuracy: 78.3,
      subjectPerformance: {
        Physics: { accuracy: 75.0, questionsAnswered: 40 },
        Chemistry: { accuracy: 81.6, questionsAnswered: 35 }
      }
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // Create test gamification data
  await testDb.collection('gamification').doc('test-user-1').set({
    userId: 'test-user-1',
    level: { current: 5, xp: 1250, totalXp: 1250, xpToNextLevel: 750 },
    streaks: { current: 7, longest: 15, lastStudyDate: new Date().toISOString().split('T')[0] },
    badges: [
      {
        badgeId: 'first_test',
        name: 'First Steps',
        unlockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        badgeId: 'streak_warrior',
        name: 'Streak Warrior',
        unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    achievements: {
      early_bird: { progress: 60, isCompleted: false },
      accuracy_ace: { progress: 100, isCompleted: true }
    },
    statistics: {
      totalTests: 25,
      totalQuestions: 150,
      correctAnswers: 131,
      totalStudyTime: 7200,
      averageAccuracy: 87.3
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // Create test questions
  await testDb.collection('questions').doc('test-question-1').set({
    questionId: 'test-question-1',
    content: {
      question: 'What is the unit of electric charge?',
      options: ['Coulomb', 'Ampere', 'Volt', 'Ohm'],
      correctAnswer: 'Coulomb',
      explanation: {
        basic: 'The SI unit of electric charge is Coulomb (C).',
        intermediate: 'Electric charge is measured in Coulombs, named after Charles-Augustin de Coulomb.',
        advanced: 'One Coulomb is defined as the amount of charge transported by a constant current of one ampere in one second.'
      },
      hints: ['Think about the fundamental units', 'Named after a French physicist', 'Related to current and time']
    },
    metadata: {
      subject: 'Physics',
      topic: 'Electric Charge',
      difficulty: 'beginner',
      estimatedTime: 30,
      sourceTag: 'Manual',
      bloomsLevel: 'remember',
      questionType: 'multiple-choice'
    },
    analytics: {
      totalAttempts: 45,
      correctAttempts: 38,
      averageTime: 25
    },
    validation: {
      isVerified: true,
      verifiedBy: 'admin',
      qualityScore: 90
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // Create test syllabi
  await testDb.collection('syllabi').doc('test-syllabus-1').set({
    syllabusId: 'test-syllabus-1',
    name: 'Test Physics Syllabus',
    subject: 'Physics',
    examType: 'board',
    board: 'Test Board',
    class: '12',
    structure: {
      units: [
        {
          unitId: 'unit-1',
          name: 'Electrostatics',
          chapters: [
            {
              chapterId: 'chapter-1',
              name: 'Electric Charges and Fields',
              topics: [
                {
                  topicId: 'topic-1',
                  name: 'Electric Charge',
                  difficulty: 'beginner'
                }
              ]
            }
          ]
        }
      ],
      totalUnits: 1,
      totalChapters: 1,
      totalTopics: 1
    },
    settings: {
      isActive: true,
      isPublic: true
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  });

  // Create test sessions
  await testDb.collection('testSessions').doc('test-session-1').set({
    sessionId: 'test-session-1',
    userId: 'test-user-1',
    config: {
      testType: 'practice',
      subject: 'Physics',
      difficulty: 'intermediate',
      questionCount: 10,
      timeLimit: 600
    },
    status: 'completed',
    questions: [
      { questionId: 'test-question-1', order: 1, difficulty: 'beginner', points: 5 }
    ],
    answers: [
      {
        questionId: 'test-question-1',
        userAnswer: 'Coulomb',
        isCorrect: true,
        timeSpent: 25,
        confidence: 4,
        pointsEarned: 5
      }
    ],
    results: {
      score: 100,
      totalQuestions: 1,
      correctAnswers: 1,
      accuracy: 100,
      totalTime: 25
    },
    gamification: {
      xpEarned: 15,
      bonusXp: 5
    },
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    completedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 30 * 1000).toISOString()
  });
};

const cleanupTestCollections = async () => {
  const collections = [
    'users',
    'gamification',
    'questions',
    'syllabi',
    'testSessions',
    'challenges',
    'friends',
    'studyGroups',
    'socialFeed'
  ];

  for (const collectionName of collections) {
    const snapshot = await testDb.collection(collectionName).get();
    const batch = testDb.batch();
    
    snapshot.docs.forEach(doc => {
      if (doc.id.startsWith('test-') || doc.id.includes('test')) {
        batch.delete(doc.ref);
      }
    });
    
    await batch.commit();
  }
};

export const createTestUser = async (userData) => {
  const userId = `test-user-${Date.now()}`;
  const user = {
    userId,
    ...userData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await testDb.collection('users').doc(userId).set(user);
  return user;
};

export const createTestSession = async (sessionData) => {
  const sessionId = `test-session-${Date.now()}`;
  const session = {
    sessionId,
    ...sessionData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await testDb.collection('testSessions').doc(sessionId).set(session);
  return session;
};

export const createTestQuestion = async (questionData) => {
  const questionId = `test-question-${Date.now()}`;
  const question = {
    questionId,
    ...questionData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  await testDb.collection('questions').doc(questionId).set(question);
  return question;
};

export const getTestDb = () => testDb;
export const getTestAuth = () => testAuth;

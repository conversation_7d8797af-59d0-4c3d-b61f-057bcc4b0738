const questionGenerationService = require('../services/questionGenerationService');

async function testQuestionGeneration() {
    console.log('Testing Enhanced Question Generation...\n');

    const testCases = [
        {
            name: 'Multiple Choice - Physics',
            params: {
                subject: 'Physics',
                topic: 'Mechanics',
                level: 'medium',
                numQuestions: 2,
                test_type: 'practice',
                questionType: 'multiple_choice'
            }
        },
        {
            name: 'Fill in Blanks - Chemistry',
            params: {
                subject: 'Chemistry',
                topic: 'Organic Chemistry',
                level: 'medium',
                numQuestions: 2,
                test_type: 'practice',
                questionType: 'fill_in_blanks'
            }
        }
    ];

    for (const testCase of testCases) {
        console.log(`\nRunning test case: ${testCase.name}`);
        console.log('Parameters:', JSON.stringify(testCase.params, null, 2));

        try {
            const questions = await questionGenerationService.generateQuestions({
                userId: 'test_user',
                ...testCase.params
            });

            console.log('\nGenerated Questions:');
            questions.forEach((q, i) => {
                console.log(`\nQuestion ${i + 1}:`);
                console.log('Text:', q.question);
                if (q.options) {
                    console.log('Options:', q.options);
                }
                console.log('Correct Answer:', q.correct_answer);
                console.log('Explanation:', q.explanation);
                console.log('Source Tag:', q.source_tag);
                console.log('Quality Score:', q.quality_score);
                console.log('Keywords:', q.keywords);
                console.log('Cognitive Level:', q.cognitive_level);
            });

            console.log('\nTest case passed successfully!');
        } catch (error) {
            console.error(`\nError in test case ${testCase.name}:`, error);
        }
    }
}

// Run the tests
testQuestionGeneration().catch(console.error);

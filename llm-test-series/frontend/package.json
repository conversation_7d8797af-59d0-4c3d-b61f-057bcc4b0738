{"name": "llm-test-series-frontend", "version": "1.0.0", "description": "Frontend React application for AI-Powered Adaptive Learning Platform", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "framer-motion": "^10.16.4", "react-confetti": "^6.1.0", "react-countup": "^6.5.3", "socket.io-client": "^4.7.2", "axios": "^1.6.2", "firebase": "^10.7.1", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "eslint": "^8.56.0", "prettier": "^2.8.8"}, "proxy": "http://localhost:3001"}
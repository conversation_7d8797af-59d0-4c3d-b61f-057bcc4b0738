import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';

// Import components
import Dashboard from './components/Dashboard/Dashboard';
import TestInterface from './components/TestInterface/TestInterface';
import Analytics from './components/Analytics/Analytics';
import Social from './components/Social/Social';
import Gamification from './components/Gamification/Gamification';
import Login from './components/Auth/Login';
import Register from './components/Auth/Register';
import Navigation from './components/Navigation/Navigation';
import LoadingScreen from './components/Common/LoadingScreen';

// Import hooks and services
import { useAuth } from './hooks/useAuth';
import { useTheme } from './hooks/useTheme';
import { initializeFirebase } from './services/firebase';
import { socketService } from './services/socketService';

// Import styles
import './styles/globals.css';
import './styles/animations.css';

function App() {
  const { user, loading: authLoading, login, logout, register } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize Firebase
        await initializeFirebase();
        
        // Initialize socket connection if user is authenticated
        if (user) {
          socketService.connect(user.id);
        }
        
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initializeApp();
  }, [user]);

  useEffect(() => {
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  useEffect(() => {
    // Cleanup socket connection on unmount
    return () => {
      socketService.disconnect();
    };
  }, []);

  if (authLoading || !isInitialized) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-900 to-red-700">
        <div className="text-center text-white p-8">
          <h1 className="text-2xl font-bold mb-4">Application Error</h1>
          <p className="mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-white text-red-900 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className={`app ${theme}`}>
        <AnimatePresence mode="wait">
          {!user ? (
            <Routes key="auth">
              <Route 
                path="/login" 
                element={
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Login onLogin={login} />
                  </motion.div>
                } 
              />
              <Route 
                path="/register" 
                element={
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Register onRegister={register} />
                  </motion.div>
                } 
              />
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          ) : (
            <div className="app-layout">
              <Navigation user={user} onLogout={logout} onThemeToggle={toggleTheme} />
              <main className="main-content">
                <Routes key="main">
                  <Route 
                    path="/dashboard" 
                    element={
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Dashboard user={user} />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/test" 
                    element={
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                      >
                        <TestInterface user={user} />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/analytics" 
                    element={
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Analytics user={user} />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/social" 
                    element={
                      <motion.div
                        initial={{ opacity: 0, rotateY: -10 }}
                        animate={{ opacity: 1, rotateY: 0 }}
                        exit={{ opacity: 0, rotateY: 10 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Social user={user} />
                      </motion.div>
                    } 
                  />
                  <Route 
                    path="/gamification" 
                    element={
                      <motion.div
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Gamification user={user} />
                      </motion.div>
                    } 
                  />
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="*" element={<Navigate to="/dashboard" replace />} />
                </Routes>
              </main>
            </div>
          )}
        </AnimatePresence>
        
        {/* Global toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: theme === 'dark' ? '#1f2937' : '#ffffff',
              color: theme === 'dark' ? '#ffffff' : '#1f2937',
              border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#ffffff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#ffffff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;

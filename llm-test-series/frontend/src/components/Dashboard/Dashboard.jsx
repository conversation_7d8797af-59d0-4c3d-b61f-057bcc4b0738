import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CountUp from 'react-countup';
import {
  Trophy,
  Flame,
  Target,
  Users,
  BookOpen,
  TrendingUp,
  Star,
  Zap,
  Award,
  Clock,
  Lightbulb,
  Play,
  Brain,
  Calendar,
  Settings
} from 'lucide-react';
import Confetti from 'react-confetti';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { apiService } from '../../services/apiService';

// Helper for random tips
const tips = [
  "Reviewing mistakes is key to learning!",
  "Consistency beats intensity in the long run.",
  "Did you know? The mitochondria is the powerhouse of the cell.",
  "Take short breaks every hour to stay focused.",
  "Challenge yourself with a harder topic today!",
  "Practice makes perfect - but perfect practice makes permanent!",
  "Understanding concepts is better than memorizing facts.",
  "Every expert was once a beginner. Keep going!"
];
const Dashboard = ({ user }) => {
  const navigate = useNavigate();

  const initialStats = {
    xp: 2847,
    level: 12,
    streak: 15,
    rank: 47,
    rankChange: 12,
    todayProgress: 80,
    badges: ['physics_master', 'streak_warrior', 'quick_learner']
  };

  const [stats, setStats] = useState({
    xp: 0,
    level: 0,
    streak: 0,
    rank: 0,
    rankChange: 0,
    todayProgress: 0,
    badges: []
  });
  const [dailyMission, setDailyMission] = useState({
    title: "Loading mission...",
    progress: 0,
    total: 1,
    reward: 0
  });
  const [hotTopics, setHotTopics] = useState([]);
  const [recentAchievements, setRecentAchievements] = useState([]);
  const [loading, setLoading] = useState(true); // Keep a single loading state
  const [showConfetti, setShowConfetti] = useState(false);
  const [currentTip, setCurrentTip] = useState("");
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);

      if (!user?.id) { // Use user.id for consistency with useAuth hook
        // Use demo data for non-authenticated users
        setStats(initialStats);
        setDailyMission({ title: "Complete Physics Mock Test", progress: 4, total: 5, reward: 150 });
        setHotTopics([
          { name: "Thermodynamics", students: 234, trend: "up" },
          { name: "Quantum Physics", students: 189, trend: "up" },
          { name: "Electromagnetism", students: 156, trend: "down" }
        ]);
        setRecentAchievements([
          { name: "Physics Master", description: "87% complete", progress: 87 },
          { name: "Streak Warrior", description: "15 day streak!", progress: 100 },
          { name: "Speed Demon", description: "Answer in under 30s", progress: 65 }
        ]);
        setCurrentTip(tips[Math.floor(Math.random() * tips.length)]);
        setLoading(false);
        return;
      }

      try {
        const result = await apiService.getDashboardData(user.id); // Use apiService
        
        if (result.success) {
          const data = result.data;
          setStats(data.stats || initialStats); // Fallback to initialStats if API doesn't provide
          setDailyMission(data.dailyMission || { title: "No mission today", progress: 0, total: 1, reward: 0 });
          setHotTopics(data.hotTopics || []);
          setRecentAchievements(data.recentAchievements || []);
          setCurrentTip(tips[Math.floor(Math.random() * tips.length)]);
        } else {
          throw new Error(result.error || 'Failed to fetch dashboard data');
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        setError(err.message);
        toast.error(`Failed to load dashboard: ${err.message}`);
        // Fallback to initial/dummy data on error to keep UI functional
        setStats(initialStats);
        setDailyMission({ title: "Mission data unavailable", progress: 0, total: 1, reward: 0 });
        setHotTopics([]);
        setCurrentTip(tips[Math.floor(Math.random() * tips.length)]);
        setRecentAchievements([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  useEffect(() => {
    if (dailyMission.progress >= dailyMission.total && dailyMission.total > 0) {
      setShowConfetti(true);
      const timer = setTimeout(() => setShowConfetti(false), 5000); // Confetti for 5 seconds
      return () => clearTimeout(timer);
    }
  }, [dailyMission]);


  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
        <p className="text-xl">Loading Dashboard...</p>
      </div>
    );
  }

  if (error && !stats) { // Show error only if there's no fallback data to display
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
        <p className="text-xl text-red-400">Error loading dashboard: {error}</p>
      </div>
    );
  }

  // Ensure stats and other data are not null before rendering dependent components
  if (!stats || !dailyMission) {
     // This case should ideally be handled by the loading or error state,
     // but as a final fallback:
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
        <p className="text-xl">Preparing dashboard...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      {showConfetti && <Confetti recycle={false} numberOfPieces={300} />}
      {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-lg border-b border-white/10 p-6"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <span className="text-xl font-bold text-black">{user?.username?.[0] || 'U'}</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold">Welcome back, {user?.username || 'Student'}!</h1>
              <div className="flex items-center space-x-2 text-orange-400">
                <Flame className="w-5 h-5" />
                <span className="font-semibold">
                  <CountUp end={stats.streak} duration={1.5} /> day streak
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">
                <CountUp end={stats.xp} duration={2} separator="," />
              </div>
              <div className="text-sm text-gray-300">XP Points</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                #<CountUp end={stats.rank} duration={1.5} />
              </div>
              <div className="text-sm text-green-400">↑{stats.rankChange}</div>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Daily Mission */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-green-600/30 to-emerald-600/30 backdrop-blur-lg border border-green-500/40 rounded-2xl p-6 shadow-2xl hover:shadow-green-500/30 transition-shadow duration-300"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Target className="w-8 h-8 text-green-400" />
              <div>
                <h2 className="text-xl font-bold">Today's Mission</h2>
                <p className="text-green-300">{dailyMission.title}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-yellow-400">
                +<CountUp end={dailyMission.reward} duration={1.5} /> XP
              </div>
              <div className="text-sm text-gray-300">Reward</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex-1 bg-black/30 rounded-full h-3">
              <motion.div 
                initial={{ width: 0 }}
                animate={{ width: `${dailyMission.total > 0 ? (dailyMission.progress / dailyMission.total) * 100 : 0}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="bg-gradient-to-r from-green-400 to-emerald-500 h-full rounded-full"
              />
            </div>
            <span className="text-sm font-medium">
              {dailyMission.progress}/{dailyMission.total}
            </span>
          </div>
        </motion.div>

        {/* Tip of the Day */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-black/20 backdrop-blur-lg border border-yellow-500/30 rounded-2xl p-4"
        >
          <div className="flex items-center space-x-3">
            <Lightbulb className="w-6 h-6 text-yellow-400" />
            <div>
              <h3 className="text-md font-semibold text-yellow-300">Tip of the Day:</h3>
              <p className="text-sm text-gray-200">{currentTip}</p>
            </div>
          </div>
        </motion.div>



        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Stats Grid */}
          <div className="lg:col-span-2 space-y-6">
            {/* Performance Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <StatCard
                icon={<Trophy className="w-6 h-6" />}
                title="Level"
                value={<CountUp end={stats.level} duration={1.5} />}
                color="from-yellow-400 to-orange-500"
                onClick={() => console.log('StatCard clicked: Level')}
              />
              <StatCard
                icon={<Flame className="w-6 h-6" />}
                title="Streak"
                value={<><CountUp end={stats.streak} duration={1.5} /> days</>}
                color="from-red-400 to-pink-500"
                onClick={() => console.log('StatCard clicked: Streak')}
              />
              <StatCard
                icon={<TrendingUp className="w-6 h-6" />}
                title="Rank"
                value={<>#<CountUp end={stats.rank} duration={1.5} /></>}
                color="from-purple-400 to-indigo-500"
                subtitle={`↑${stats.rankChange}`}
                onClick={() => console.log('StatCard clicked: Rank')}
              />
              <StatCard
                icon={<Star className="w-6 h-6" />}
                title="Badges"
                value={<CountUp end={stats.badges.length} duration={1.5} />}
                color="from-blue-400 to-cyan-500"
                onClick={() => console.log('StatCard clicked: Badges')}
              />
            </div>

            {/* Hot Topics */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-black/20 backdrop-blur-lg border border-white/10 rounded-2xl p-6"
            >
              <div className="flex items-center space-x-3 mb-4">
                <Zap className="w-6 h-6 text-yellow-400" />
                <h3 className="text-xl font-bold">🔥 Hot Topics</h3>
              </div>
              
              <div className="space-y-3">
                {hotTopics.map((topic, index) => (
                  <motion.div
                    key={topic.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.45 + 0.1 * index }}
                    className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer" onClick={() => console.log(`Hot topic clicked: ${topic.name}`)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" />
                      <span className="font-medium">{topic.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-300">{topic.students}</span>
                      <TrendingUp className={`w-4 h-4 ${topic.trend === 'up' ? 'text-green-400' : 'text-red-400'}`} />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-black/20 backdrop-blur-lg border border-white/10 rounded-2xl p-6"
            >
              <h3 className="text-lg font-bold mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <ActionButton
                  icon={<BookOpen className="w-5 h-5" />}
                  text="Start Mock Test"
                  color="bg-gradient-to-r from-blue-500 to-purple-600"
                  onClick={() => console.log('ActionButton clicked: Start Mock Test')}
                />
                <ActionButton
                  icon={<Users className="w-5 h-5" />}
                  text="Challenge Friend"
                  color="bg-gradient-to-r from-green-500 to-teal-600"
                  onClick={() => console.log('ActionButton clicked: Challenge Friend')}
                />
                <ActionButton
                  icon={<Trophy className="w-5 h-5" />}
                  text="View Leaderboard"
                  color="bg-gradient-to-r from-yellow-500 to-orange-600"
                  onClick={() => console.log('ActionButton clicked: View Leaderboard')}
                />
              </div>
            </motion.div>

            {/* Recent Achievements */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-black/20 backdrop-blur-lg border border-white/10 rounded-2xl p-6"
            >
              <div className="flex items-center space-x-3 mb-4">
                <Award className="w-6 h-6 text-purple-400" />
                <h3 className="text-lg font-bold">Achievements</h3>
              </div>
              
              <div className="space-y-4">
                {recentAchievements.map((achievement, index) => (
                  <motion.div
                    key={achievement.name}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.55 + 0.1 * index }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{achievement.name}</span>
                      <span className="text-xs text-gray-400">{achievement.progress}%</span>
                    </div>
                    <div className="w-full bg-black/30 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${achievement.progress}%` }}
                        transition={{ duration: 1, ease: "easeOut", delay: 0.6 + 0.1 * index }}
                        className="bg-gradient-to-r from-purple-400 to-pink-500 h-full rounded-full"
                      />
                    </div>
                    <p className="text-xs text-gray-400">{achievement.description}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

const StatCard = ({ icon, title, value, color, subtitle, onClick }) => (
  <motion.div
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.98 }}
    className="bg-black/20 backdrop-blur-lg border border-white/10 rounded-xl p-4 cursor-pointer hover:shadow-lg transition-shadow duration-300" 
    onClick={onClick}
  >
    <div className={`w-10 h-10 bg-gradient-to-r ${color} rounded-lg flex items-center justify-center mb-3`}>
      {icon}
    </div>
    <div className="text-2xl font-bold mb-1">{value}</div>
    <div className="text-sm text-gray-300">{title}</div>
    {subtitle && <div className="text-xs text-green-400 mt-1">{subtitle}</div>}
  </motion.div>
);

const ActionButton = ({ icon, text, color, onClick }) => (
  <motion.button
    whileHover={{ scale: 1.03, y: -2, boxShadow: "0px 5px 15px rgba(0,0,0,0.2)" }}
    whileTap={{ scale: 0.98 }}
    className={`w-full ${color} rounded-lg p-3 flex items-center justify-center space-x-3 font-medium transition-all`}
    onClick={onClick}
  >
    {icon}
    <span>{text}</span>
  </motion.button>
);

export default Dashboard;
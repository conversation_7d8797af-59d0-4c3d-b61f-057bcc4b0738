import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Flame, 
  Award, 
  Target, 
  Zap,
  Crown,
  Medal,
  Gift,
  Calendar,
  TrendingUp,
  Lock,
  CheckCircle
} from 'lucide-react';
import { apiService } from '../../services/apiService';
import { socketService } from '../../services/socketService';
import toast from 'react-hot-toast';
import Confetti from 'react-confetti';

const Gamification = ({ user }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [gamificationData, setGamificationData] = useState(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGamificationData();
    setupSocketListeners();
  }, []);

  const setupSocketListeners = () => {
    socketService.on('levelUp', handleLevelUp);
    socketService.on('achievementUnlocked', handleAchievementUnlocked);
    socketService.on('streakMilestone', handleStreakMilestone);

    return () => {
      socketService.off('levelUp', handleLevelUp);
      socketService.off('achievementUnlocked', handleAchievementUnlocked);
      socketService.off('streakMilestone', handleStreakMilestone);
    };
  };

  const fetchGamificationData = async () => {
    setLoading(true);
    try {
      const result = await apiService.getGamificationData(user.userId);
      
      if (result.success) {
        setGamificationData(result.data);
      } else {
        toast.error('Failed to fetch gamification data');
      }
    } catch (error) {
      toast.error('Failed to fetch gamification data');
    } finally {
      setLoading(false);
    }
  };

  const handleLevelUp = (data) => {
    setShowConfetti(true);
    toast.success(`🎉 Level Up! You're now level ${data.newLevel}!`);
    setTimeout(() => setShowConfetti(false), 5000);
    fetchGamificationData();
  };

  const handleAchievementUnlocked = (data) => {
    toast.success(`🏆 Achievement Unlocked: ${data.name}!`);
    fetchGamificationData();
  };

  const handleStreakMilestone = (data) => {
    toast.success(`🔥 ${data.days} day streak! Keep it up!`);
  };

  // Mock data for demonstration
  

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'common': return 'from-gray-500 to-gray-600';
      case 'rare': return 'from-blue-500 to-blue-600';
      case 'epic': return 'from-purple-500 to-purple-600';
      case 'legendary': return 'from-yellow-500 to-orange-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Trophy },
    { id: 'badges', label: 'Badges', icon: Award },
    { id: 'achievements', label: 'Achievements', icon: Target },
    { id: 'rewards', label: 'Daily Rewards', icon: Gift }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading achievements...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8">
      {showConfetti && <Confetti />}
      
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-4">Achievements & Progress</h1>
          <p className="text-gray-300">Track your learning journey and unlock rewards</p>
        </motion.div>

        {/* Level Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <Crown className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Level {gamificationData.level.current}</h2>
                <p className="text-gray-300">{gamificationData.level.xp.toLocaleString()} / {(gamificationData.level.xp + gamificationData.level.xpToNextLevel).toLocaleString()} XP</p>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-gray-300 text-sm">Next Level</p>
              <p className="text-white font-semibold">{gamificationData.level.xpToNextLevel} XP to go</p>
            </div>
          </div>
          
          <div className="w-full bg-white/20 rounded-full h-4 mb-2">
            <div
              className="bg-gradient-to-r from-yellow-400 to-orange-500 h-4 rounded-full transition-all duration-500"
              style={{ width: `${gamificationData.level.progression}%` }}
            />
          </div>
          <p className="text-gray-300 text-sm">{gamificationData.level.progression.toFixed(1)}% to next level</p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-2 mb-8"
        >
          <div className="flex space-x-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-all ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {/* Current Streak */}
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Flame className="w-8 h-8 text-orange-400" />
                    <div>
                      <h3 className="text-white font-semibold">Current Streak</h3>
                      <p className="text-gray-400 text-sm">Keep it going!</p>
                    </div>
                  </div>
                  <span className="text-3xl font-bold text-orange-400">{mockData.streaks.current}</span>
                </div>
                <div className="text-sm text-gray-300">
                  <p>Longest streak: {mockData.streaks.longest} days</p>
                  <p>Weekly streak: {mockData.streaks.weeklyStreak}/7 days</p>
                </div>
              </div>

              {/* Recent Badges */}
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <h3 className="text-white font-semibold mb-4">Recent Badges</h3>
                <div className="space-y-3">
                  {mockData.badges.slice(0, 3).map((badge) => (
                    <div key={badge.badgeId} className="flex items-center space-x-3">
                      <div className={`w-10 h-10 bg-gradient-to-r ${getRarityColor(badge.rarity)} rounded-full flex items-center justify-center text-lg`}>
                        {badge.icon}
                      </div>
                      <div className="flex-1">
                        <p className="text-white text-sm font-medium">{badge.name}</p>
                        <p className="text-gray-400 text-xs">{badge.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Progress Summary */}
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <h3 className="text-white font-semibold mb-4">Progress Summary</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 text-sm">Badges Earned</span>
                    <span className="text-white font-semibold">{mockData.badges.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 text-sm">Achievements</span>
                    <span className="text-white font-semibold">
                      {Object.values(mockData.achievements).filter(a => a.isCompleted).length}/
                      {Object.values(mockData.achievements).length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300 text-sm">Total XP</span>
                    <span className="text-white font-semibold">{mockData.level.totalXp.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'badges' && (
            <motion.div
              key="badges"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {mockData.badges.map((badge, index) => (
                <motion.div
                  key={badge.badgeId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className={`bg-gradient-to-br ${getRarityColor(badge.rarity)} p-1 rounded-2xl`}
                >
                  <div className="bg-black/20 backdrop-blur-lg rounded-xl p-6 h-full">
                    <div className="text-center">
                      <div className="text-6xl mb-4">{badge.icon}</div>
                      <h3 className="text-white font-bold text-lg mb-2">{badge.name}</h3>
                      <p className="text-gray-200 text-sm mb-3">{badge.description}</p>
                      <div className="flex items-center justify-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          badge.rarity === 'legendary' ? 'bg-yellow-500/20 text-yellow-300' :
                          badge.rarity === 'epic' ? 'bg-purple-500/20 text-purple-300' :
                          badge.rarity === 'rare' ? 'bg-blue-500/20 text-blue-300' :
                          'bg-gray-500/20 text-gray-300'
                        }`}>
                          {badge.rarity.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-gray-400 text-xs mt-2">
                        Earned {new Date(badge.unlockedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'achievements' && (
            <motion.div
              key="achievements"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-6"
            >
              {Object.entries(mockData.achievements).map(([key, achievement], index) => (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 ${
                    achievement.isCompleted ? 'ring-2 ring-green-500/50' : ''
                  }`}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        achievement.isCompleted 
                          ? 'bg-green-500' 
                          : 'bg-gray-500'
                      }`}>
                        {achievement.isCompleted ? (
                          <CheckCircle className="w-6 h-6 text-white" />
                        ) : (
                          <Lock className="w-6 h-6 text-white" />
                        )}
                      </div>
                      <div>
                        <h3 className="text-white font-semibold text-lg">{achievement.name}</h3>
                        <p className="text-gray-300">{achievement.description}</p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="flex items-center space-x-2 mb-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < achievement.tier ? 'text-yellow-400 fill-current' : 'text-gray-400'
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-gray-400 text-sm">Tier {achievement.tier}</p>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-300 text-sm">Progress</span>
                      <span className="text-white font-semibold">{achievement.progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          achievement.isCompleted 
                            ? 'bg-gradient-to-r from-green-400 to-green-600' 
                            : 'bg-gradient-to-r from-blue-400 to-purple-600'
                        }`}
                        style={{ width: `${achievement.progress}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-300">
                      Reward: +{achievement.reward.xp} XP
                      {achievement.reward.badge && ` + ${achievement.reward.badge} badge`}
                    </div>
                    {achievement.isCompleted && (
                      <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm font-semibold">
                        Completed
                      </span>
                    )}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'rewards' && (
            <motion.div
              key="rewards"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-6"
            >
              {/* Daily Reward */}
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-8 h-8 text-blue-400" />
                    <div>
                      <h3 className="text-white font-semibold text-lg">Daily Reward</h3>
                      <p className="text-gray-300">Login daily to claim rewards</p>
                    </div>
                  </div>
                  
                  {mockData.dailyRewards.available ? (
                    <button className="px-6 py-3 bg-gradient-to-r from-green-500 to-teal-600 text-white font-semibold rounded-lg hover:from-green-600 hover:to-teal-700 transition-all">
                      Claim Reward
                    </button>
                  ) : (
                    <span className="px-6 py-3 bg-gray-500/20 text-gray-400 font-semibold rounded-lg">
                      Already Claimed
                    </span>
                  )}
                </div>
                
                <div className="grid grid-cols-7 gap-2">
                  {[...Array(7)].map((_, i) => (
                    <div
                      key={i}
                      className={`aspect-square rounded-lg flex items-center justify-center text-sm font-semibold ${
                        i < mockData.dailyRewards.streak
                          ? 'bg-green-500 text-white'
                          : i === mockData.dailyRewards.streak
                          ? 'bg-blue-500 text-white ring-2 ring-blue-300'
                          : 'bg-white/10 text-gray-400'
                      }`}
                    >
                      {i + 1}
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 text-center">
                  <p className="text-gray-300 text-sm">
                    Current streak: {mockData.dailyRewards.streak} days
                  </p>
                  <p className="text-white font-semibold">
                    Next reward: {mockData.dailyRewards.nextReward.description}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Gamification;

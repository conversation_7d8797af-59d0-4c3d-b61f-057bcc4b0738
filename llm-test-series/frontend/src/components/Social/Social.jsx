import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  UserPlus, 
  MessageCircle, 
  Trophy, 
  Sword, 
  Crown,
  Star,
  Flame,
  Send,
  Search,
  Filter,
  Plus
} from 'lucide-react';
import { apiService } from '../../services/apiService';
import { socketService } from '../../services/socketService';
import toast from 'react-hot-toast';

const Social = ({ user }) => {
  const [activeTab, setActiveTab] = useState('friends');
  const [friends, setFriends] = useState([]);
  const [challenges, setChallenges] = useState([]);
  const [leaderboard, setLeaderboard] = useState([]);
  const [studyGroups, setStudyGroups] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showChallengeModal, setShowChallengeModal] = useState(false);
  const [selectedFriend, setSelectedFriend] = useState(null);

  useEffect(() => {
    fetchSocialData();
    setupSocketListeners();
  }, []);

  const setupSocketListeners = () => {
    socketService.on('challengeReceived', handleChallengeReceived);
    socketService.on('challengeAccepted', handleChallengeAccepted);
    socketService.on('friendRequestReceived', handleFriendRequest);
    socketService.on('leaderboardUpdate', handleLeaderboardUpdate);

    return () => {
      socketService.off('challengeReceived', handleChallengeReceived);
      socketService.off('challengeAccepted', handleChallengeAccepted);
      socketService.off('friendRequestReceived', handleFriendRequest);
      socketService.off('leaderboardUpdate', handleLeaderboardUpdate);
    };
  };

  const fetchSocialData = async () => {
    setLoading(true);
    try {
      const [friendsResult, leaderboardResult] = await Promise.all([
        apiService.getFriends(user.userId),
        apiService.getLeaderboard('global', 20)
      ]);

      if (friendsResult.success) {
        setFriends(friendsResult.data);
      }

      if (leaderboardResult.success) {
        setLeaderboard(leaderboardResult.data);
      }
    } catch (error) {
      toast.error('Failed to fetch social data');
    } finally {
      setLoading(false);
    }
  };

  const handleChallengeReceived = (data) => {
    toast.success(`Challenge received from ${data.challengerName}!`);
    // Refresh challenges
    fetchSocialData();
  };

  const handleChallengeAccepted = (data) => {
    toast.success(`${data.accepterName} accepted your challenge!`);
  };

  const handleFriendRequest = (data) => {
    toast.success(`Friend request from ${data.senderName}!`);
  };

  const handleLeaderboardUpdate = (data) => {
    setLeaderboard(data.leaderboard);
  };

  const sendChallenge = async (friendId, challengeConfig) => {
    try {
      const result = await apiService.sendChallenge({
        challengedId: friendId,
        ...challengeConfig
      });

      if (result.success) {
        toast.success('Challenge sent!');
        setShowChallengeModal(false);
        setSelectedFriend(null);
      } else {
        toast.error(result.error || 'Failed to send challenge');
      }
    } catch (error) {
      toast.error('Failed to send challenge');
    }
  };

  // Mock data for demonstration
  

  const tabs = [
    { id: 'friends', label: 'Friends', icon: Users },
    { id: 'leaderboard', label: 'Leaderboard', icon: Trophy },
    { id: 'challenges', label: 'Challenges', icon: Sword },
    { id: 'groups', label: 'Study Groups', icon: MessageCircle }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading social features...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold text-white mb-4">Social Hub</h1>
          <p className="text-gray-300">Connect, compete, and learn together</p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-2 mb-8"
        >
          <div className="flex space-x-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-all ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'friends' && (
            <motion.div
              key="friends"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-6"
            >
              {/* Search and Add Friends */}
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search friends..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <button className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg text-white font-semibold hover:from-green-600 hover:to-teal-700 transition-all">
                    <UserPlus className="w-5 h-5" />
                    <span>Add Friend</span>
                  </button>
                </div>
              </div>

              {/* Friends List */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {friends.map((friend) => (
                  <motion.div
                    key={friend.userId}
                    whileHover={{ scale: 1.02 }}
                    className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-lg font-bold text-white">
                              {friend.username[0].toUpperCase()}
                            </span>
                          </div>
                          {friend.isOnline && (
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                          )}
                        </div>
                        <div>
                          <h3 className="text-white font-semibold">{friend.username}</h3>
                          <p className="text-gray-400 text-sm">Level {friend.level}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-300">XP</span>
                        <span className="text-white font-medium">{friend.xp.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-300">Streak</span>
                        <div className="flex items-center space-x-1">
                          <Flame className="w-4 h-4 text-orange-400" />
                          <span className="text-white font-medium">{friend.streak}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-300">W/L/T</span>
                        <span className="text-white font-medium">
                          {friend.competitionStats.wins}/{friend.competitionStats.losses}/{friend.competitionStats.ties}
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedFriend(friend);
                          setShowChallengeModal(true);
                        }}
                        className="flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white py-2 px-4 rounded-lg font-semibold hover:from-orange-600 hover:to-red-700 transition-all"
                      >
                        Challenge
                      </button>
                      <button className="flex-1 bg-white/10 hover:bg-white/20 border border-white/20 text-white py-2 px-4 rounded-lg font-semibold transition-all">
                        Message
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === 'leaderboard' && (
            <motion.div
              key="leaderboard"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Global Leaderboard</h2>
                <div className="flex space-x-2">
                  <button className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold">Global</button>
                  <button className="px-4 py-2 bg-white/10 text-gray-300 rounded-lg">Friends</button>
                  <button className="px-4 py-2 bg-white/10 text-gray-300 rounded-lg">Weekly</button>
                </div>
              </div>

              <div className="space-y-4">
                {leaderboard.map((player, index) => (
                  <motion.div
                    key={player.rank}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex items-center justify-between p-4 rounded-lg ${
                      player.rank <= 3 
                        ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30' 
                        : 'bg-white/5 border border-white/10'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8">
                        {player.rank === 1 && <Crown className="w-6 h-6 text-yellow-400" />}
                        {player.rank === 2 && <Star className="w-6 h-6 text-gray-300" />}
                        {player.rank === 3 && <Star className="w-6 h-6 text-orange-400" />}
                        {player.rank > 3 && <span className="text-white font-bold">#{player.rank}</span>}
                      </div>
                      
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-white">
                          {player.username[0].toUpperCase()}
                        </span>
                      </div>
                      
                      <div>
                        <h3 className="text-white font-semibold">{player.username}</h3>
                        <p className="text-gray-400 text-sm">Level {player.level}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <p className="text-gray-300">XP</p>
                        <p className="text-white font-semibold">{player.xp.toLocaleString()}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-300">Streak</p>
                        <div className="flex items-center space-x-1">
                          <Flame className="w-4 h-4 text-orange-400" />
                          <span className="text-white font-semibold">{player.streak}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === 'challenges' && (
            <motion.div
              key="challenges"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-6"
            >
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <h2 className="text-2xl font-bold text-white mb-4">Active Challenges</h2>
                <div className="text-center py-8">
                  <Sword className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-300">No active challenges</p>
                  <p className="text-gray-400 text-sm">Challenge your friends to start competing!</p>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'groups' && (
            <motion.div
              key="groups"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className="space-y-6"
            >
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-bold text-white">Study Groups</h2>
                  <button className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg text-white font-semibold">
                    <Plus className="w-5 h-5" />
                    <span>Create Group</span>
                  </button>
                </div>
                <div className="text-center py-8">
                  <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-300">No study groups yet</p>
                  <p className="text-gray-400 text-sm">Join or create a study group to collaborate with others!</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Challenge Modal */}
        {showChallengeModal && selectedFriend && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 w-full max-w-md"
            >
              <h3 className="text-xl font-bold text-white mb-4">
                Challenge {selectedFriend.username}
              </h3>
              
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">Subject</label>
                  <select className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white">
                    <option value="physics">Physics</option>
                    <option value="chemistry">Chemistry</option>
                    <option value="mathematics">Mathematics</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">Questions</label>
                  <select className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white">
                    <option value={10}>10 Questions</option>
                    <option value={20}>20 Questions</option>
                    <option value={30}>30 Questions</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">Time Limit</label>
                  <select className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white">
                    <option value={600}>10 Minutes</option>
                    <option value={1200}>20 Minutes</option>
                    <option value={1800}>30 Minutes</option>
                  </select>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowChallengeModal(false);
                    setSelectedFriend(null);
                  }}
                  className="flex-1 px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 text-white rounded-lg transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={() => sendChallenge(selectedFriend.userId, {
                    subject: 'physics',
                    questionCount: 10,
                    timeLimit: 600
                  })}
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-lg font-semibold hover:from-orange-600 hover:to-red-700 transition-all"
                >
                  Send Challenge
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Social;

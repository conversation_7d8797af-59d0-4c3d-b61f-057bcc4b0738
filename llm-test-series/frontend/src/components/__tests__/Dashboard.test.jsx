// Dashboard Component Tests
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { expect, vi } from 'vitest';
import Dashboard from '../Dashboard/Dashboard';
import { apiService } from '../../services/apiService';

// Mock the API service
vi.mock('../../services/apiService', () => ({
  apiService: {
    getDashboardData: vi.fn(),
    getRecentSessions: vi.fn(),
    getPerformanceMetrics: vi.fn()
  }
}));

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }) => <p {...props}>{children}</p>
  },
  AnimatePresence: ({ children }) => children
}));

// Mock Chart.js components
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>
}));

const mockUser = {
  userId: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  profile: {
    firstName: 'Test',
    lastName: 'User'
  },
  gamification: {
    level: { current: 5, xp: 1250 },
    streaks: { current: 7, longest: 15 },
    badges: ['first_test', 'streak_warrior']
  }
};

const mockDashboardData = {
  overview: {
    totalTests: 25,
    totalQuestions: 500,
    averageAccuracy: 87.5,
    totalStudyTime: 1800,
    currentStreak: 7,
    rank: 42
  },
  recentActivity: [
    {
      sessionId: 'session-1',
      subject: 'Physics',
      score: 85,
      completedAt: '2024-01-20T10:30:00Z',
      questionsAnswered: 20
    },
    {
      sessionId: 'session-2',
      subject: 'Chemistry',
      score: 92,
      completedAt: '2024-01-19T14:15:00Z',
      questionsAnswered: 15
    }
  ],
  upcomingGoals: [
    {
      id: 'goal-1',
      title: 'Complete 30 Physics questions',
      progress: 75,
      deadline: '2024-01-25'
    }
  ],
  recommendations: [
    {
      type: 'weakness_focus',
      title: 'Focus on Thermodynamics',
      description: 'Your accuracy in this topic is below average',
      priority: 'high'
    }
  ]
};

const renderDashboard = (user = mockUser) => {
  return render(
    <BrowserRouter>
      <Dashboard user={user} />
    </BrowserRouter>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    apiService.getDashboardData.mockResolvedValue({
      success: true,
      data: mockDashboardData
    });
  });

  describe('Rendering', () => {
    it('should render dashboard with user greeting', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText(/Welcome back, Test!/)).toBeInTheDocument();
      });
    });

    it('should display loading state initially', () => {
      renderDashboard();
      
      expect(screen.getByText(/Loading your dashboard/)).toBeInTheDocument();
    });

    it('should render overview statistics', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('25')).toBeInTheDocument(); // Total tests
        expect(screen.getByText('87.5%')).toBeInTheDocument(); // Average accuracy
        expect(screen.getByText('7')).toBeInTheDocument(); // Current streak
        expect(screen.getByText('#42')).toBeInTheDocument(); // Rank
      });
    });

    it('should render recent activity section', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('Recent Activity')).toBeInTheDocument();
        expect(screen.getByText('Physics')).toBeInTheDocument();
        expect(screen.getByText('Chemistry')).toBeInTheDocument();
        expect(screen.getByText('85%')).toBeInTheDocument();
        expect(screen.getByText('92%')).toBeInTheDocument();
      });
    });

    it('should render performance charts', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByTestId('line-chart')).toBeInTheDocument();
        expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
        expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
      });
    });

    it('should render upcoming goals', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('Upcoming Goals')).toBeInTheDocument();
        expect(screen.getByText('Complete 30 Physics questions')).toBeInTheDocument();
        expect(screen.getByText('75%')).toBeInTheDocument();
      });
    });

    it('should render AI recommendations', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('AI Recommendations')).toBeInTheDocument();
        expect(screen.getByText('Focus on Thermodynamics')).toBeInTheDocument();
        expect(screen.getByText(/Your accuracy in this topic is below average/)).toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    it('should handle quick action buttons', async () => {
      renderDashboard();

      await waitFor(() => {
        const startTestButton = screen.getByText('Start Quick Test');
        expect(startTestButton).toBeInTheDocument();
        
        fireEvent.click(startTestButton);
        // Test navigation would be mocked in a real scenario
      });
    });

    it('should handle view all activity button', async () => {
      renderDashboard();

      await waitFor(() => {
        const viewAllButton = screen.getByText('View All');
        expect(viewAllButton).toBeInTheDocument();
        
        fireEvent.click(viewAllButton);
        // Test navigation would be mocked in a real scenario
      });
    });

    it('should handle recommendation actions', async () => {
      renderDashboard();

      await waitFor(() => {
        const startPracticeButton = screen.getByText('Start Practice');
        expect(startPracticeButton).toBeInTheDocument();
        
        fireEvent.click(startPracticeButton);
        // Test that appropriate action is triggered
      });
    });
  });

  describe('Data Loading', () => {
    it('should handle API success', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(apiService.getDashboardData).toHaveBeenCalledWith(mockUser.userId);
        expect(screen.queryByText(/Loading your dashboard/)).not.toBeInTheDocument();
      });
    });

    it('should handle API error', async () => {
      apiService.getDashboardData.mockRejectedValue(new Error('API Error'));
      
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText(/Failed to load dashboard data/)).toBeInTheDocument();
      });
    });

    it('should handle empty data gracefully', async () => {
      apiService.getDashboardData.mockResolvedValue({
        success: true,
        data: {
          overview: {},
          recentActivity: [],
          upcomingGoals: [],
          recommendations: []
        }
      });

      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText(/No recent activity/)).toBeInTheDocument();
        expect(screen.getByText(/No upcoming goals/)).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt to mobile viewport', async () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      renderDashboard();

      await waitFor(() => {
        const container = screen.getByTestId('dashboard-container');
        expect(container).toHaveClass('mobile-layout');
      });
    });

    it('should show desktop layout on larger screens', async () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });

      renderDashboard();

      await waitFor(() => {
        const container = screen.getByTestId('dashboard-container');
        expect(container).toHaveClass('desktop-layout');
      });
    });
  });

  describe('Performance Metrics', () => {
    it('should display performance trends correctly', async () => {
      renderDashboard();

      await waitFor(() => {
        // Check for trend indicators
        expect(screen.getByText(/↗/)).toBeInTheDocument(); // Upward trend
        expect(screen.getByText(/****%/)).toBeInTheDocument(); // Percentage change
      });
    });

    it('should show subject-wise performance', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('Subject Performance')).toBeInTheDocument();
        // Check that chart is rendered
        expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
      });
    });
  });

  describe('Gamification Elements', () => {
    it('should display user level and XP', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('Level 5')).toBeInTheDocument();
        expect(screen.getByText('1,250 XP')).toBeInTheDocument();
      });
    });

    it('should show progress to next level', async () => {
      renderDashboard();

      await waitFor(() => {
        const progressBar = screen.getByRole('progressbar');
        expect(progressBar).toBeInTheDocument();
        expect(progressBar).toHaveAttribute('aria-valuenow', '62.5'); // 1250/2000 * 100
      });
    });

    it('should display recent badges', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByText('Recent Badges')).toBeInTheDocument();
        expect(screen.getByText('Streak Warrior')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      renderDashboard();

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
        expect(screen.getByRole('navigation')).toBeInTheDocument();
        expect(screen.getAllByRole('button')).toHaveLength.greaterThan(0);
      });
    });

    it('should support keyboard navigation', async () => {
      renderDashboard();

      await waitFor(() => {
        const firstButton = screen.getAllByRole('button')[0];
        firstButton.focus();
        expect(document.activeElement).toBe(firstButton);
      });
    });

    it('should have proper heading hierarchy', async () => {
      renderDashboard();

      await waitFor(() => {
        const h1 = screen.getByRole('heading', { level: 1 });
        const h2s = screen.getAllByRole('heading', { level: 2 });
        
        expect(h1).toBeInTheDocument();
        expect(h2s.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should handle real-time XP updates', async () => {
      renderDashboard();

      await waitFor(() => {
        // Simulate real-time XP update
        const xpElement = screen.getByText('1,250 XP');
        expect(xpElement).toBeInTheDocument();
      });

      // Simulate WebSocket update
      // This would be tested with actual WebSocket mocking in a real scenario
    });

    it('should handle streak updates', async () => {
      renderDashboard();

      await waitFor(() => {
        const streakElement = screen.getByText('7');
        expect(streakElement).toBeInTheDocument();
      });
    });
  });
});

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  BookOpen, 
  CheckCircle, 
  XCircle, 
  SkipForward, 
  Lightbulb,
  Flag,
  Pause,
  Play,
  RotateCcw,
  Send
} from 'lucide-react';
import { apiService } from '../../services/apiService';
import { socketService } from '../../services/socketService';
import toast from 'react-hot-toast';

const TestInterface = ({ user }) => {
  const [testSession, setTestSession] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hintsUsed, setHintsUsed] = useState(0);
  const [confidence, setConfidence] = useState(3);
  const [loading, setLoading] = useState(false);
  const [testConfig, setTestConfig] = useState({
    subject: 'Physics',
    difficulty: 'intermediate',
    questionCount: 20,
    timeLimit: 1200, // 20 minutes
    testType: 'practice'
  });

  // Timer effect
  useEffect(() => {
    if (testSession && timeRemaining > 0 && !isPaused) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [testSession, timeRemaining, isPaused]);

  // Socket listeners for real-time features
  useEffect(() => {
    socketService.on('liveTestUpdate', handleLiveTestUpdate);
    socketService.on('challengeUpdate', handleChallengeUpdate);

    return () => {
      socketService.off('liveTestUpdate', handleLiveTestUpdate);
      socketService.off('challengeUpdate', handleChallengeUpdate);
    };
  }, []);

  const handleLiveTestUpdate = (data) => {
    // Handle real-time test updates
    console.log('Live test update:', data);
  };

  const handleChallengeUpdate = (data) => {
    // Handle challenge updates
    toast.success(`Challenge update: ${data.message}`);
  };

  const startTest = async () => {
    setLoading(true);
    try {
      const result = await apiService.createTestSession(testConfig);
      
      if (result.success) {
        setTestSession(result.data.session);
        setCurrentQuestion(result.data.questions[0]);
        setCurrentQuestionIndex(0);
        setTimeRemaining(testConfig.timeLimit);
        
        // Start live test if applicable
        if (testConfig.testType === 'live') {
          socketService.startLiveTest(result.data.session.sessionId);
        }
        
        toast.success('Test started! Good luck!');
      } else {
        toast.error(result.error || 'Failed to start test');
      }
    } catch (error) {
      toast.error('Failed to start test');
    } finally {
      setLoading(false);
    }
  };

  const submitAnswer = async () => {
    if (!selectedAnswer) {
      toast.error('Please select an answer');
      return;
    }

    setLoading(true);
    try {
      const answerData = {
        questionId: currentQuestion.questionId,
        userAnswer: selectedAnswer,
        timeSpent: testConfig.timeLimit - timeRemaining,
        confidence: confidence,
        hintsUsed: hintsUsed
      };

      const result = await apiService.submitAnswer(testSession.sessionId, answerData);
      
      if (result.success) {
        // Show immediate feedback
        const isCorrect = result.data.isCorrect;
        if (isCorrect) {
          toast.success('Correct! +' + result.data.pointsEarned + ' XP');
        } else {
          toast.error('Incorrect. The correct answer was: ' + result.data.correctAnswer);
        }

        // Submit to live test if applicable
        if (testConfig.testType === 'live') {
          socketService.submitLiveAnswer(
            testSession.sessionId,
            currentQuestion.questionId,
            selectedAnswer
          );
        }

        // Move to next question or finish test
        if (currentQuestionIndex < testSession.questions.length - 1) {
          nextQuestion();
        } else {
          finishTest();
        }
      } else {
        toast.error(result.error || 'Failed to submit answer');
      }
    } catch (error) {
      toast.error('Failed to submit answer');
    } finally {
      setLoading(false);
    }
  };

  const nextQuestion = () => {
    const nextIndex = currentQuestionIndex + 1;
    setCurrentQuestionIndex(nextIndex);
    setCurrentQuestion(testSession.questions[nextIndex]);
    setSelectedAnswer('');
    setShowHint(false);
    setHintsUsed(0);
    setConfidence(3);
  };

  const skipQuestion = () => {
    if (currentQuestionIndex < testSession.questions.length - 1) {
      nextQuestion();
      toast.info('Question skipped');
    } else {
      finishTest();
    }
  };

  const finishTest = async () => {
    setLoading(true);
    try {
      const result = await apiService.getTestResults(testSession.sessionId);
      
      if (result.success) {
        // Show results
        toast.success('Test completed!');
        // Navigate to results page or show results modal
        console.log('Test results:', result.data);
      }
    } catch (error) {
      toast.error('Failed to get test results');
    } finally {
      setLoading(false);
    }
  };

  const handleTimeUp = () => {
    toast.error('Time\'s up!');
    finishTest();
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
    toast.info(isPaused ? 'Test resumed' : 'Test paused');
  };

  const requestHint = () => {
    if (hintsUsed < 3 && currentQuestion?.content?.hints) {
      setShowHint(true);
      setHintsUsed(prev => prev + 1);
      toast.info('Hint revealed! (-2 XP)');
    } else {
      toast.error('No more hints available');
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!testSession) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8"
          >
            <h1 className="text-3xl font-bold text-white mb-8 text-center">Start New Test</h1>
            
            {/* Test Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-2">Subject</label>
                <select
                  value={testConfig.subject}
                  onChange={(e) => setTestConfig(prev => ({ ...prev, subject: e.target.value }))}
                  className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Biology">Biology</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-2">Difficulty</label>
                <select
                  value={testConfig.difficulty}
                  onChange={(e) => setTestConfig(prev => ({ ...prev, difficulty: e.target.value }))}
                  className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                  <option value="expert">Expert</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-2">Number of Questions</label>
                <select
                  value={testConfig.questionCount}
                  onChange={(e) => setTestConfig(prev => ({ ...prev, questionCount: parseInt(e.target.value) }))}
                  className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value={10}>10 Questions</option>
                  <option value={20}>20 Questions</option>
                  <option value={50}>50 Questions</option>
                  <option value={100}>100 Questions</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-200 mb-2">Time Limit</label>
                <select
                  value={testConfig.timeLimit}
                  onChange={(e) => setTestConfig(prev => ({ ...prev, timeLimit: parseInt(e.target.value) }))}
                  className="w-full p-3 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value={0}>No Time Limit</option>
                  <option value={600}>10 Minutes</option>
                  <option value={1200}>20 Minutes</option>
                  <option value={1800}>30 Minutes</option>
                  <option value={3600}>1 Hour</option>
                </select>
              </div>
            </div>
            
            <motion.button
              onClick={startTest}
              disabled={loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-gradient-to-r from-green-500 to-teal-600 text-white py-4 px-6 rounded-lg font-semibold flex items-center justify-center space-x-2 hover:from-green-600 hover:to-teal-700 transition-all disabled:opacity-50"
            >
              {loading ? (
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <Play className="w-6 h-6" />
                  <span>Start Test</span>
                </>
              )}
            </motion.button>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4 lg:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Test Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 mb-6"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <BookOpen className="w-6 h-6 text-blue-400" />
                <span className="text-white font-semibold">
                  Question {currentQuestionIndex + 1} of {testSession.questions.length}
                </span>
              </div>
              
              {testConfig.timeLimit > 0 && (
                <div className="flex items-center space-x-2">
                  <Clock className="w-6 h-6 text-orange-400" />
                  <span className={`font-mono text-lg ${timeRemaining < 300 ? 'text-red-400' : 'text-white'}`}>
                    {formatTime(timeRemaining)}
                  </span>
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={togglePause}
                className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
              >
                {isPaused ? <Play className="w-5 h-5 text-white" /> : <Pause className="w-5 h-5 text-white" />}
              </button>
              
              <button
                onClick={requestHint}
                disabled={hintsUsed >= 3 || !currentQuestion?.content?.hints}
                className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50"
              >
                <Lightbulb className="w-5 h-5 text-yellow-400" />
              </button>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4 w-full bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentQuestionIndex + 1) / testSession.questions.length) * 100}%` }}
            />
          </div>
        </motion.div>

        {/* Question Content */}
        <AnimatePresence mode="wait">
          {currentQuestion && (
            <motion.div
              key={currentQuestion.questionId}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-8 mb-6"
            >
              <h2 className="text-2xl font-semibold text-white mb-6">
                {currentQuestion.content.question}
              </h2>
              
              {/* Answer Options */}
              <div className="space-y-4 mb-6">
                {currentQuestion.content.options.map((option, index) => (
                  <motion.button
                    key={index}
                    onClick={() => setSelectedAnswer(option)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`w-full p-4 text-left rounded-lg border-2 transition-all ${
                      selectedAnswer === option
                        ? 'border-blue-500 bg-blue-500/20 text-white'
                        : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40 hover:bg-white/10'
                    }`}
                  >
                    <span className="font-medium">{String.fromCharCode(65 + index)}.</span> {option}
                  </motion.button>
                ))}
              </div>
              
              {/* Hint */}
              {showHint && currentQuestion.content.hints && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6"
                >
                  <div className="flex items-start space-x-2">
                    <Lightbulb className="w-5 h-5 text-yellow-400 mt-0.5" />
                    <div>
                      <p className="text-yellow-200 font-medium">Hint:</p>
                      <p className="text-yellow-100">{currentQuestion.content.hints[hintsUsed - 1]}</p>
                    </div>
                  </div>
                </motion.div>
              )}
              
              {/* Confidence Slider */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-200 mb-2">
                  Confidence Level: {confidence}/5
                </label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={confidence}
                  onChange={(e) => setConfidence(parseInt(e.target.value))}
                  className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Not Sure</span>
                  <span>Very Confident</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <button
            onClick={skipQuestion}
            className="flex items-center space-x-2 px-6 py-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-all"
          >
            <SkipForward className="w-5 h-5" />
            <span>Skip</span>
          </button>
          
          <button
            onClick={submitAnswer}
            disabled={!selectedAnswer || loading}
            className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 rounded-lg text-white font-semibold transition-all disabled:opacity-50"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                <Send className="w-5 h-5" />
                <span>Submit Answer</span>
              </>
            )}
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default TestInterface;

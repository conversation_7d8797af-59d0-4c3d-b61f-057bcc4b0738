import axios from 'axios';
import toast from 'react-hot-toast';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add user ID for analytics
    const userId = localStorage.getItem('userId');
    if (userId) {
      config.headers['user-id'] = userId;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API call failed:', error.response || error.message || error); // Log full error
    const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred';
    const errorStatus = error.response?.status;

    if (errorStatus === 401) {
      toast.error('Unauthorized. Please log in again.');
      localStorage.removeItem('authToken');
      localStorage.removeItem('userId');
      window.location.href = '/login';
    } else if (errorStatus === 429) {
      toast.error('Too many requests. Please slow down.');
    } else if (errorStatus >= 500) {
      toast.error('Server error. Please try again later.');
    } else {
      toast.error(errorMessage); // Generic error for other statuses
    }
    
    return Promise.reject({
      success: false,
      message: errorMessage,
      status: errorStatus,
      details: error.response?.data?.details || null, // Include backend details if available
    });
  }
);

// API Service class
class ApiService {
  // Authentication endpoints
  async login(credentials) {
    try {
      const response = await api.post('/auth/login', credentials);
      const { token, user } = response.data;
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userId', user.id);
      
      return { success: true, user, token };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Login failed' 
      };
    }
  }

  async register(userData) {
    try {
      const response = await api.post('/auth/register', userData);
      const { token, user } = response.data;
      
      localStorage.setItem('authToken', token);
      localStorage.setItem('userId', user.id);
      
      return { success: true, user, token };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Registration failed' 
      };
    }
  }

  async logout() {
    try {
      await api.post('/auth/logout');
      localStorage.removeItem('authToken');
      localStorage.removeItem('userId');
      return { success: true };
    } catch (error) {
      // Even if logout fails on server, clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('userId');
      return { success: true };
    }
  }

  // Dashboard data
  async getDashboardData(userId) {
    try {
      const response = await api.get(`/users/${userId}/dashboard`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch dashboard data' 
      };
    }
  }

  // Gamification endpoints
  async getGamificationData(userId) {
    try {
      const response = await api.get(`/gamification/user/${userId}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch gamification data' 
      };
    }
  }

  async getLeaderboard(type = 'global', limit = 10) {
    try {
      const response = await api.get(`/gamification/leaderboard?type=${type}&limit=${limit}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch leaderboard' 
      };
    }
  }

  // Questions endpoints
  async getQuestions(filters = {}) {
    try {
      const params = new URLSearchParams(filters);
      const response = await api.get(`/questions?${params}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch questions' 
      };
    }
  }

  async generateQuestions(params) {
    try {
      const response = await api.post('/questions/generate', params);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to generate questions' 
      };
    }
  }

  // Test sessions
  async createTestSession(config) {
    try {
      const response = await api.post('/test-sessions', config);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to create test session' 
      };
    }
  }

  async submitAnswer(sessionId, answerData) {
    try {
      const response = await api.post(`/test-sessions/${sessionId}/answer`, answerData);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to submit answer' 
      };
    }
  }

  async getTestResults(sessionId) {
    try {
      const response = await api.get(`/test-sessions/${sessionId}/results`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch test results' 
      };
    }
  }

  // Analytics
  async getAnalytics(userId, timeframe = '30d') {
    try {
      const response = await api.get(`/analytics/user/${userId}?timeframe=${timeframe}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch analytics' 
      };
    }
  }

  // Social features
  async getFriends(userId) {
    try {
      const response = await api.get(`/users/${userId}/friends`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch friends' 
      };
    }
  }

  async sendChallenge(challengeData) {
    try {
      const response = await api.post('/challenge', challengeData);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to send challenge' 
      };
    }
  }

  // Theory and explanations
  async getTheoryExplanation(topic, level = 'intermediate') {
    try {
      const response = await api.post('/theory/explain', { topic, level });
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to get explanation' 
      };
    }
  }

  // Syllabi
  async getSyllabi(filters = {}) {
    try {
      const params = new URLSearchParams(filters);
      const response = await api.get(`/syllabi?${params}`);
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.message || 'Failed to fetch syllabi' 
      };
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await api.get('/health');
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: 'Backend server is not responding' 
      };
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;

import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_FIREBASE_APP_ID,
  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
let app;
let auth;
let db;
let storage;

export const initializeFirebase = () => {
  try {
    if (!app) {
      app = initializeApp(firebaseConfig);
      auth = getAuth(app);
      db = getFirestore(app);
      storage = getStorage(app);

      // Connect to emulators in development
      if (process.env.NODE_ENV === 'development' && process.env.REACT_APP_USE_FIREBASE_EMULATOR === 'true') {
        try {
          connectAuthEmulator(auth, 'http://localhost:9099');
          connectFirestoreEmulator(db, 'localhost', 8080);
          connectStorageEmulator(storage, 'localhost', 9199);
          console.log('Connected to Firebase emulators');
        } catch (error) {
          console.warn('Firebase emulators not available:', error.message);
        }
      }

      console.log('Firebase initialized successfully');
    }
    
    return { app, auth, db, storage };
  } catch (error) {
    console.error('Firebase initialization error:', error);
    throw error;
  }
};

// Export Firebase services
export const getFirebaseAuth = () => {
  return auth;
};

export const getFirebaseDb = () => {
  return db;
};

export const getFirebaseStorage = () => {
  return storage;
};

// Helper functions for common Firebase operations
export const firebaseHelpers = {
  // Check if Firebase is initialized
  isInitialized: () => !!app,
  
  // Get current user
  getCurrentUser: () => {
    const auth = getFirebaseAuth();
    return auth.currentUser;
  },
  
  // Sign out
  signOut: async () => {
    const auth = getFirebaseAuth();
    return auth.signOut();
  },
  
  // Get user token
  getUserToken: async () => {
    const auth = getFirebaseAuth();
    const user = auth.currentUser;
    if (user) {
      return user.getIdToken();
    }
    return null;
  }
};

const firebaseService = {
  initializeFirebase,
  getFirebaseAuth,
  getFirebaseDb,
  getFirebaseStorage,
  firebaseHelpers
};

export default firebaseService;

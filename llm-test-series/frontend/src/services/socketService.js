import io from 'socket.io-client';
import toast from 'react-hot-toast';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.eventListeners = new Map();
  }

  connect(userId) {
    if (this.socket && this.isConnected) {
      return;
    }

    const serverUrl = process.env.REACT_APP_API_URL?.replace('/api', '') || 'http://localhost:3001';
    
    this.socket = io(serverUrl, {
      auth: {
        userId: userId,
        token: localStorage.getItem('authToken')
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventHandlers();
  }

  setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('Socket connected:', this.socket.id);
      
      // Emit user online status
      this.socket.emit('user_online', {
        userId: localStorage.getItem('userId'),
        timestamp: new Date()
      });
    });

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      console.log('Socket disconnected:', reason);
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnect();
      } else {
        toast.error('Unable to connect to real-time services');
      }
    });

    // Real-time notifications
    this.socket.on('notification', (data) => {
      this.handleNotification(data);
    });

    // Challenge notifications
    this.socket.on('challenge_received', (data) => {
      toast.success(`Challenge received from ${data.challengerName}!`);
      this.emit('challengeReceived', data);
    });

    this.socket.on('challenge_accepted', (data) => {
      toast.success(`${data.accepterName} accepted your challenge!`);
      this.emit('challengeAccepted', data);
    });

    // Study group events
    this.socket.on('study_group_message', (data) => {
      this.emit('studyGroupMessage', data);
    });

    this.socket.on('user_joined_group', (data) => {
      this.emit('userJoinedGroup', data);
    });

    this.socket.on('user_left_group', (data) => {
      this.emit('userLeftGroup', data);
    });

    // Live test events
    this.socket.on('live_test_update', (data) => {
      this.emit('liveTestUpdate', data);
    });

    // Leaderboard updates
    this.socket.on('leaderboard_update', (data) => {
      this.emit('leaderboardUpdate', data);
    });

    // Achievement unlocked
    this.socket.on('achievement_unlocked', (data) => {
      toast.success(`🏆 Achievement Unlocked: ${data.name}!`);
      this.emit('achievementUnlocked', data);
    });

    // Level up notification
    this.socket.on('level_up', (data) => {
      toast.success(`🎉 Level Up! You're now level ${data.newLevel}!`);
      this.emit('levelUp', data);
    });

    // Streak milestone
    this.socket.on('streak_milestone', (data) => {
      toast.success(`🔥 ${data.days} day streak! Keep it up!`);
      this.emit('streakMilestone', data);
    });
  }

  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    
    setTimeout(() => {
      if (!this.isConnected && this.socket) {
        console.log(`Reconnection attempt ${this.reconnectAttempts}`);
        this.socket.connect();
      }
    }, delay);
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.eventListeners.clear();
    }
  }

  // Event listener management
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in socket event listener:', error);
        }
      });
    }
  }

  // Socket.IO specific methods
  joinRoom(roomId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_room', roomId);
    }
  }

  leaveRoom(roomId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_room', roomId);
    }
  }

  joinStudyGroup(groupId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-study-group', groupId);
    }
  }

  sendStudyMessage(groupId, message) {
    if (this.socket && this.isConnected) {
      this.socket.emit('study-message', { groupId, message });
    }
  }

  startLiveTest(testId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('start_live_test', { testId });
    }
  }

  submitLiveAnswer(testId, questionId, answer) {
    if (this.socket && this.isConnected) {
      this.socket.emit('live_answer_submit', {
        testId,
        questionId,
        answer,
        timestamp: new Date()
      });
    }
  }

  sendChallenge(challengeData) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_challenge', challengeData);
    }
  }

  acceptChallenge(challengeId) {
    if (this.socket && this.isConnected) {
      this.socket.emit('accept_challenge', { challengeId });
    }
  }

  updateUserStatus(status) {
    if (this.socket && this.isConnected) {
      this.socket.emit('user_status_update', {
        userId: localStorage.getItem('userId'),
        status,
        timestamp: new Date()
      });
    }
  }

  handleNotification(data) {
    const { type, title, message, priority } = data;
    
    switch (priority) {
      case 'high':
        toast.error(`${title}: ${message}`);
        break;
      case 'medium':
        toast.success(`${title}: ${message}`);
        break;
      case 'low':
      default:
        toast(`${title}: ${message}`);
        break;
    }
    
    // Emit to any listeners
    this.emit('notification', data);
  }

  // Utility methods
  isSocketConnected() {
    return this.isConnected && this.socket?.connected;
  }

  getSocketId() {
    return this.socket?.id;
  }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;

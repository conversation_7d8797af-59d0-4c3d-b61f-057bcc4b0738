import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/apiService';
import { socketService } from '../services/socketService';
import toast from 'react-hot-toast';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem('authToken');
        const userId = localStorage.getItem('userId');
        
        if (token && userId) {
          // Verify token is still valid by making a test request
          const result = await apiService.getDashboardData(userId);
          
          if (result.success) {
            setUser({
              userId,
              token,
              ...result.data.user
            });
            
            // Connect to socket service
            socketService.connect(userId);
          } else {
            // Token is invalid, clear storage
            localStorage.removeItem('authToken');
            localStorage.removeItem('userId');
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userId');
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = useCallback(async (credentials) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.login(credentials);
      
      if (result.success) {
        setUser(result.user);
        
        // Connect to socket service
        socketService.connect(result.user.userId);
        
        toast.success(`Welcome back, ${result.user.username || result.user.email}!`);
        return { success: true, user: result.user };
      } else {
        setError(result.error);
        toast.error(result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'Login failed. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  const register = useCallback(async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.register(userData);
      
      if (result.success) {
        setUser(result.user);
        
        // Connect to socket service
        socketService.connect(result.user.userId);
        
        toast.success(`Welcome to LLM Test Series, ${result.user.username || result.user.email}!`);
        return { success: true, user: result.user };
      } else {
        setError(result.error);
        toast.error(result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'Registration failed. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setLoading(true);
      
      // Disconnect socket first
      socketService.disconnect();
      
      // Call logout API
      await apiService.logout();
      
      // Clear user state
      setUser(null);
      setError(null);
      
      toast.success('Logged out successfully');
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      // Even if API call fails, clear local state
      setUser(null);
      setError(null);
      return { success: true };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateUser = useCallback((userData) => {
    setUser(prevUser => ({
      ...prevUser,
      ...userData
    }));
  }, []);

  const refreshUser = useCallback(async () => {
    if (!user?.userId) return;
    
    try {
      const result = await apiService.getDashboardData(user.userId);
      if (result.success && result.data.user) {
        updateUser(result.data.user);
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  }, [user?.userId, updateUser]);

  return {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateUser,
    refreshUser,
    isAuthenticated: !!user,
    isLoading: loading
  };
};

/* Main Styles */
.tab-btn.active {
    color: #4f46e5;
    border-bottom: 2px solid #4f46e5;
}

/* Exam Explorer Styles */
.filter-btn.active {
    background-color: #4f46e5;
    color: white;
}

.exam-card {
    transition: transform 0.2s ease;
}

.exam-card:hover {
    transform: translateY(-5px);
}

/* Mock Test Styles */
.option-label {
    display: block;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.option-label:hover {
    background-color: #f3f4f6;
}

.option-label input[type="radio"] {
    margin-right: 0.5rem;
}

.option-label input[type="radio"]:checked + span {
    font-weight: 600;
    color: #4f46e5;
}

/* Analytics Dashboard Styles */
.loader {
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top: 3px solid #4f46e5;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

.loader.small {
    width: 16px;
    height: 16px;
    border-width: 2px;
}

.loader.large {
    width: 32px;
    height: 32px;
    border-width: 4px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart container styles */
.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
}

/* Topic performance styles */
.topic-performance-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    background-color: #f9fafb;
    transition: background-color 0.2s ease;
}

.topic-performance-item:hover {
    background-color: #f3f4f6;
}

.topic-performance-item .score {
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
}

.score-high {
    background-color: #dcfce7;
    color: #059669;
}

.score-medium {
    background-color: #fef9c3;
    color: #ca8a04;
}

.score-low {
    background-color: #fee2e2;
    color: #dc2626;
}

/* Study plan styles */
.study-plan-section {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.study-plan-section.immediate {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
}

.study-plan-section.short-term {
    background-color: #fef9c3;
    border: 1px solid #fef08a;
}

.study-plan-section.long-term {
    background-color: #dcfce7;
    border: 1px solid #bbf7d0;
}

/* Toast notification styles */
.toast-enter {
    transform: translateY(100%);
}

.toast-enter-active {
    transform: translateY(0%);
    transition: transform 300ms ease-in;
}

.toast-exit {
    transform: translateY(0%);
}

.toast-exit-active {
    transform: translateY(100%);
    transition: transform 300ms ease-in;
}

:root {
    /* Light theme variables */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #333333;
    --text-secondary: #666666;
    --accent-color: #4f46e5;
  --border-color: #dee2e6;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

[data-theme='dark'] {
  /* Dark theme variables */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --accent-color: #4da3ff;
  --border-color: #404040;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Base styles */
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s, color 0.3s;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  background-color: var(--bg-secondary);
  padding: 1rem;
  box-shadow: 0 2px 4px var(--shadow-color);
}

/* Navigation */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

/* Theme switcher */
.theme-switch {
  position: relative;
  width: 60px;
  height: 30px;
  background-color: var(--bg-secondary);
  border-radius: 15px;
  cursor: pointer;
  border: 2px solid var(--border-color);
}

.theme-switch__toggle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 26px;
  height: 26px;
  background-color: var(--accent-color);
  border-radius: 50%;
  transition: transform 0.3s;
}

[data-theme='dark'] .theme-switch__toggle {
  transform: translateX(30px);
}

/* Progress tracker */
.progress-tracker {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.progress-bar {
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar__fill {
  height: 100%;
  background-color: var(--accent-color);
  transition: width 0.3s;
}

/* Collaborative features */
.study-group {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.chat-window {
  height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
}

.chat-message {
  margin: 10px 0;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--bg-primary);
}

.chat-input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Offline support notice */
.offline-notice {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--accent-color);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  display: none;
}

.offline-notice.visible {
  display: block;
}

/* Timer Styles */
.timer-warning {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Question Navigation Styles */
.question-nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    margin: 0.25rem;
    cursor: pointer;
}

.question-nav-btn.current {
    background-color: #4f46e5;
    color: white;
}

.question-nav-btn.answered {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.question-nav-btn.unanswered {
    background-color: #f3f4f6;
    color: #4b5563;
    border: 1px solid #d1d5db;
}

/* Analytics Styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Syllabus Styles */
.syllabus-unit {
    border-left: 3px solid #4f46e5;
    padding-left: 1rem;
    margin-bottom: 1.5rem;
}

.syllabus-topic {
    padding-left: 1.5rem;
    margin-bottom: 0.75rem;
}

/* Loader Styles */
.loader {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .tab-btn {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
    
    .filter-container {
        flex-direction: column;
    }
    
    .filter-group {
        margin-bottom: 0.5rem;
    }
}

/* Statistics Cards */
.stats-card {
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card .card-title {
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
    color: #007bff;
}

.stats-card .card-subtitle {
    font-size: 0.9rem;
}

/* Charts */
.card canvas {
    max-height: 300px;
}

/* Upload Zone */
.upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-zone i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.upload-zone p {
    margin-bottom: 1rem;
    color: #6c757d;
}

/* Progress Bar */
.progress {
    height: 0.5rem;
}

/* Upload Results */
.upload-results {
    margin-top: 1rem;
}

.upload-results .alert {
    margin-bottom: 0.5rem;
}

/* Question Form */
.option-list {
    margin-bottom: 1rem;
}

.option-item {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.option-item .btn {
    flex-shrink: 0;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.3s ease;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    z-index: 1;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }

    .stats-card .card-title {
        font-size: 1.5rem;
    }

    .upload-zone {
        padding: 1rem;
    }

    .upload-zone i {
        font-size: 2rem;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    z-index: 1050;
}

.toast {
    background: white;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 0.5rem;
}

.toast.success {
    border-left: 4px solid #28a745;
}

.toast.error {
    border-left: 4px solid #dc3545;
}

.toast.warning {
    border-left: 4px solid #ffc107;
}

/* Form Validation */
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
}

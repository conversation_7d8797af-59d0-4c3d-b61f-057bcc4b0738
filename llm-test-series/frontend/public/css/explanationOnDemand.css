/* Explanation On-Demand Styles */

/* Modal styles */
.modal-header.bg-primary {
    background-color: #0d6efd !important;
}

.modal-header.bg-info {
    background-color: #0dcaf0 !important;
}

/* Explanation content styles */
.explanation-section {
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 15px;
}

.explanation-section:last-child {
    margin-bottom: 0;
}

.explanation-section h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

/* Concepts content styles */
.concepts-section {
    padding: 15px;
    border-radius: 8px;
    background-color: #f0f9ff;
    margin-bottom: 15px;
}

.concepts-section:last-child {
    margin-bottom: 0;
}

.concepts-section h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

/* Button styles */
.request-explanation-btn,
.view-related-concepts-btn {
    transition: all 0.3s ease;
}

.request-explanation-btn:hover,
.view-related-concepts-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Toast styles */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    min-width: 250px;
    z-index: 1100;
}

/* Loading spinner styles */
.spinner-border-sm {
    margin-right: 5px;
}

/* Code block styles */
.explanation-content pre,
.concepts-content pre {
    background-color: #282c34;
    color: #abb2bf;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
}

/* Formula styles */
.formula {
    font-family: 'Cambria Math', 'Times New Roman', serif;
    background-color: #fff;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    display: inline-block;
    margin: 4px 0;
}

/* Highlight important points */
.important-point {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 10px;
    margin: 10px 0;
}

/* Tips section */
.tips-section {
    background-color: #d1e7dd;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

/* Common mistakes section */
.mistakes-section {
    background-color: #f8d7da;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

/* Summary section */
.summary-section {
    background-color: #e2e3e5;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .explanation-section,
    .concepts-section {
        padding: 10px;
    }
    
    .toast {
        max-width: 90%;
        right: 5%;
    }
}

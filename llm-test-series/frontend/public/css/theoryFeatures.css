/* Theory Features Styles */

/* Modal Base Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 2rem;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close:hover {
    color: #000;
}

/* Theory Content Styles */
.theory-content {
    margin-top: 1.5rem;
}

.theory-section {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.theory-section:last-child {
    border-bottom: none;
}

.theory-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

/* Challenge Form Styles */
.challenge-form {
    margin-top: 1.5rem;
}

.question-text {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.correct-answer {
    color: #28a745;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

#challenge-text {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    transition: border-color 0.2s ease;
}

#challenge-text:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.submit-challenge {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.submit-challenge:hover {
    background-color: #0056b3;
}

/* Challenge Response Styles */
.challenge-response {
    margin-top: 2rem;
}

.evaluation-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.evaluation-section h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

/* Loading and Error States */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.error {
    padding: 1rem;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 1rem 0;
}

/* Feature Buttons */
.understand-theory-btn,
.challenge-answer-btn {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 0.9rem;
    margin-right: 1rem;
    padding: 0.25rem 0;
    text-decoration: underline;
    transition: color 0.2s ease;
}

.understand-theory-btn:hover,
.challenge-answer-btn:hover {
    color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
        padding: 1rem;
    }

    .theory-section {
        margin-bottom: 1.5rem;
    }

    .evaluation-section {
        padding: 0.75rem;
    }
}

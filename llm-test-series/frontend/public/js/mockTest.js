// Mock Test Module
document.addEventListener('DOMContentLoaded', () => {
    const mockTestContainer = document.getElementById('mock-test-container');

    // State variables
    let currentTestQuestions = [];
    let userAnswers = [];
    let currentQuestionIndex = 0;
    let timerInterval;
    let timeLeft = 0;
    const TIME_PER_QUESTION_SECONDS = 90; // 1.5 minutes per question
    let currentTestSessionId = null;

    // DOM Elements (will be populated when UI is rendered)
    let testConfigSection, testActiveArea, testResultsArea, testLoadingIndicator;
    let testSubjectSelector, testLevelSelector, numQuestionsInput, testTypeSelector, syllabusSelectorContainer, syllabusDropdown;
    let startTestBtn, questionDisplay, optionsDisplay, prevQuestionBtn, nextQuestionBtn, submitTestBtn, retakeTestBtn;
    let currentQuestionNumSpan, totalQuestionsNumSpan, timerSpan, scoreDisplay, totalScoreDisplay, detailedResultsDiv;
    let questionNavigationContainer;

    if (mockTestContainer) {
        renderMockTestUI();
        addEventListeners();
        loadSyllabiForDropdown(); // Load syllabi for the dropdown
    }

    function renderMockTestUI() {
        mockTestContainer.innerHTML = `
            <div id="test-config-section" class="bg-white p-6 rounded-lg shadow mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Configure Your Mock Test</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="test-subject-selector" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <select id="test-subject-selector" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="Physics">Physics</option>
                            <option value="Chemistry">Chemistry</option>
                            <option value="Mathematics">Mathematics</option>
                            <option value="Biology">Biology</option>
                            <option value="History">History</option>
                            <option value="Geography">Geography</option>
                            <option value="General Knowledge">General Knowledge</option>
                        </select>
                    </div>
                    <div>
                        <label for="test-level-selector" class="block text-sm font-medium text-gray-700 mb-1">Difficulty Level</label>
                        <select id="test-level-selector" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="Beginner">Beginner</option>
                            <option value="Intermediate">Intermediate</option>
                            <option value="Advanced">Advanced</option>
                        </select>
                    </div>
                    <div>
                        <label for="num-questions-input" class="block text-sm font-medium text-gray-700 mb-1">Number of Questions (1-50)</label>
                        <input type="number" id="num-questions-input" value="10" min="1" max="50" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="test-type-selector" class="block text-sm font-medium text-gray-700 mb-1">Test Type</label>
                        <select id="test-type-selector" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="general">General Subject-Wise</option>
                            <option value="syllabus">Syllabus-Oriented</option>
                        </select>
                    </div>
                    <div id="syllabus-selector-container" class="hidden md:col-span-2">
                        <label for="syllabus-dropdown" class="block text-sm font-medium text-gray-700 mb-1">Select Syllabus</label>
                        <select id="syllabus-dropdown" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>
                <div class="mt-6">
                    <button id="start-test-btn" class="w-full md:w-auto bg-indigo-600 text-white py-2 px-6 rounded-md hover:bg-indigo-700 transition-colors duration-300">Start Test</button>
                </div>
                <div id="test-error-message" class="hidden mt-4 text-red-600"></div>
            </div>

            <div id="test-loading-indicator" class="hidden text-center p-6">
                <div class="loader inline-block"></div>
                <p class="mt-2 text-gray-600">Loading test...</p>
            </div>

            <div id="test-active-area" class="hidden bg-white p-6 rounded-lg shadow">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Mock Test In Progress</h2>
                    <div>
                        <span class="text-sm font-medium text-gray-700">Question: <span id="current-question-num">1</span>/<span id="total-questions-num">10</span></span>
                        <span class="text-sm font-medium text-gray-700 ml-4">Time Left: <span id="timer-span" class="text-indigo-600 font-bold">00:00</span></span>
                    </div>
                </div>
                <div id="question-navigation-container" class="mb-4 flex flex-wrap justify-center">
                    <!-- Question navigation buttons will be rendered here -->
                </div>
                <div id="question-display" class="mb-4 p-4 border border-gray-200 rounded-md bg-gray-50 text-gray-800"></div>
                <div id="options-display" class="space-y-3 mb-6"></div>
                <div class="flex justify-between">
                    <button id="prev-question-btn" class="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors">Previous</button>
                    <button id="next-question-btn" class="bg-indigo-500 text-white py-2 px-4 rounded-md hover:bg-indigo-600 transition-colors">Next</button>
                    <button id="submit-test-btn" class="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors">Submit Test</button>
                </div>
            </div>

            <div id="test-results-area" class="hidden bg-white p-6 rounded-lg shadow">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Test Results</h2>
                <div class="bg-indigo-50 p-4 rounded-md mb-6 text-center">
                    <p class="text-lg text-gray-700">Your Score:</p>
                    <p class="text-4xl font-bold text-indigo-600"><span id="score-display">0</span> / <span id="total-score-display">0</span></p>
                </div>
                <div id="detailed-results-div" class="space-y-4"></div>
                <button id="retake-test-btn" class="mt-6 bg-indigo-600 text-white py-2 px-6 rounded-md hover:bg-indigo-700 transition-colors">Retake Test</button>
            </div>
        `;
        // Assign DOM elements after rendering
        assignDOMElements();
    }

    function assignDOMElements() {
        testConfigSection = document.getElementById('test-config-section');
        testActiveArea = document.getElementById('test-active-area');
        testResultsArea = document.getElementById('test-results-area');
        testLoadingIndicator = document.getElementById('test-loading-indicator');
        
        testSubjectSelector = document.getElementById('test-subject-selector');
        testLevelSelector = document.getElementById('test-level-selector');
        numQuestionsInput = document.getElementById('num-questions-input');
        testTypeSelector = document.getElementById('test-type-selector');
        syllabusSelectorContainer = document.getElementById('syllabus-selector-container');
        syllabusDropdown = document.getElementById('syllabus-dropdown');

        startTestBtn = document.getElementById('start-test-btn');
        questionDisplay = document.getElementById('question-display');
        optionsDisplay = document.getElementById('options-display');
        prevQuestionBtn = document.getElementById('prev-question-btn');
        nextQuestionBtn = document.getElementById('next-question-btn');
        submitTestBtn = document.getElementById('submit-test-btn');
        retakeTestBtn = document.getElementById('retake-test-btn');

        currentQuestionNumSpan = document.getElementById('current-question-num');
        totalQuestionsNumSpan = document.getElementById('total-questions-num');
        timerSpan = document.getElementById('timer-span');
        scoreDisplay = document.getElementById('score-display');
        totalScoreDisplay = document.getElementById('total-score-display');
        detailedResultsDiv = document.getElementById('detailed-results-div');
        questionNavigationContainer = document.getElementById('question-navigation-container');
    }
    
    function addEventListeners() {
        startTestBtn.addEventListener('click', handleStartTest);
        prevQuestionBtn.addEventListener('click', () => navigateQuestion('prev'));
        nextQuestionBtn.addEventListener('click', () => navigateQuestion('next'));
        submitTestBtn.addEventListener('click', handleSubmitTest);
        retakeTestBtn.addEventListener('click', retakeTest);

        testTypeSelector.addEventListener('change', (e) => {
            if (e.target.value === 'syllabus') {
                syllabusSelectorContainer.classList.remove('hidden');
            } else {
                syllabusSelectorContainer.classList.add('hidden');
            }
        });
    }

    async function loadSyllabiForDropdown() {
        try {
            const data = await apiGet('syllabi');
            if (data.syllabi && data.syllabi.length > 0) {
                syllabusDropdown.innerHTML = data.syllabi.map(syllabus => 
                    `<option value="${syllabus.id}">${syllabus.name}</option>`
                ).join('');
            } else {
                syllabusDropdown.innerHTML = '<option value="">No syllabi available</option>';
            }
        } catch (error) {
            console.error('Error loading syllabi:', error);
            syllabusDropdown.innerHTML = '<option value="">Error loading syllabi</option>';
        }
    }

    async function handleStartTest() {
        try {
            // Use adaptive difficulty if not admin and not a specific difficulty level
            if (!isAdmin && !testLevelSelector.value) {
                currentTestQuestions = await adaptiveDifficultyManager.getAdaptiveQuestions({
                    topic: testSubjectSelector.value,
                    subtopic: '',
                    count: parseInt(numQuestionsInput.value) || 10
                });
            } else {
                if (isNaN(numQuestionsInput.value) || numQuestionsInput.value < 1 || numQuestionsInput.value > 50) {
                    testErrorMessage.textContent = 'Please enter a valid number of questions (1-50).';
                    testErrorMessage.classList.remove('hidden');
                    return;
                }
                if (testTypeSelector.value === 'syllabus' && !syllabusDropdown.value) {
                    testErrorMessage.textContent = 'Please select a syllabus for syllabus-oriented test.';
                    testErrorMessage.classList.remove('hidden');
                    return;
                }

                testErrorMessage.classList.add('hidden');
                testConfigSection.classList.add('hidden');
                testLoadingIndicator.classList.remove('hidden');
                startTestBtn.disabled = true;

                try {
                    // 1. Create a new test session
                    const sessionData = await apiPost('test-sessions', {
                        testConfig: {
                            subject: testSubjectSelector.value,
                            level: testLevelSelector.value,
                            numQuestions: parseInt(numQuestionsInput.value),
                            testType: testTypeSelector.value,
                            syllabusId: syllabusDropdown.value
                        }
                    });
                    currentTestSessionId = sessionData.sessionId;

                    // 2. Fetch questions
                    const queryParams = { subject: testSubjectSelector.value, level: testLevelSelector.value, numQuestions: parseInt(numQuestionsInput.value), test_type: testTypeSelector.value };
                    if (syllabusDropdown.value) queryParams.syllabus_id = syllabusDropdown.value;
                    
                    const questionsData = await apiGet('questions/generate', queryParams);
                    currentTestQuestions = questionsData.questions;

                    if (!currentTestQuestions || currentTestQuestions.length === 0) {
                        throw new Error('No questions received from the server.');
                    }
                    if (currentTestQuestions.length < parseInt(numQuestionsInput.value)) {
                        console.warn(`Requested ${numQuestionsInput.value} questions, but received ${currentTestQuestions.length}.`);
                        // Optionally, inform user or adjust numQuestions
                    }

                    // 3. Update session with questions
                    await apiPut(`test-sessions/${currentTestSessionId}/questions`, { questions: currentTestQuestions });

                    currentQuestionIndex = 0;
                    userAnswers = Array(currentTestQuestions.length).fill(null);
                    timeLeft = currentTestQuestions.length * TIME_PER_QUESTION_SECONDS;

                    testLoadingIndicator.classList.add('hidden');
                    testActiveArea.classList.remove('hidden');
                    totalQuestionsNumSpan.textContent = currentTestQuestions.length;
                    
                    renderQuestionNavigation();
                    displayQuestion();
                    startTimer();

                } catch (error) {
                    console.error('Error starting test:', error);
                    testErrorMessage.textContent = `Error: ${error.message}. Could not start test.`;
                    testErrorMessage.classList.remove('hidden');
                    testConfigSection.classList.remove('hidden'); // Show config again
                    testLoadingIndicator.classList.add('hidden');
                } finally {
                    startTestBtn.disabled = false;
                }
            }
        } catch (error) {
            console.error('Error starting test:', error);
            testErrorMessage.textContent = `Error: ${error.message}. Could not start test.`;
            testErrorMessage.classList.remove('hidden');
            testConfigSection.classList.remove('hidden'); // Show config again
            testLoadingIndicator.classList.add('hidden');
        }
    }
    
    function renderQuestionNavigation() {
        questionNavigationContainer.innerHTML = '';
        currentTestQuestions.forEach((_, index) => {
            const navBtn = document.createElement('button');
            navBtn.textContent = index + 1;
            navBtn.classList.add('question-nav-btn');
            if (index === currentQuestionIndex) {
                navBtn.classList.add('current');
            }
            if (userAnswers[index] !== null) {
                navBtn.classList.add('answered');
            } else {
                navBtn.classList.add('unanswered');
            }
            navBtn.addEventListener('click', () => {
                currentQuestionIndex = index;
                displayQuestion();
                updateQuestionNavigation();
            });
            questionNavigationContainer.appendChild(navBtn);
        });
    }

    function updateQuestionNavigation() {
        const navBtns = questionNavigationContainer.querySelectorAll('.question-nav-btn');
        navBtns.forEach((btn, index) => {
            btn.classList.remove('current', 'answered', 'unanswered');
            if (index === currentQuestionIndex) {
                btn.classList.add('current');
            }
            if (userAnswers[index] !== null) {
                btn.classList.add('answered');
            } else {
                btn.classList.add('unanswered');
            }
        });
    }

    function displayQuestion() {
        if (currentQuestionIndex < 0 || currentQuestionIndex >= currentTestQuestions.length) {
            return;
        }
        const question = currentTestQuestions[currentQuestionIndex];
        currentQuestionNumSpan.textContent = currentQuestionIndex + 1;
        questionDisplay.innerHTML = `<p class="text-lg">${currentQuestionIndex + 1}. ${question.question}</p>`;
        optionsDisplay.innerHTML = '';

        question.options.forEach((option, idx) => {
            const optionChar = String.fromCharCode(65 + idx); // A, B, C, D
            const optionId = `q${currentQuestionIndex}_opt${idx}`;
            const isChecked = userAnswers[currentQuestionIndex] === optionChar;

            const optionHtml = `
                <label for="${optionId}" class="option-label block p-3 border border-gray-300 rounded-md hover:bg-gray-100 cursor-pointer has-[:checked]:bg-indigo-100 has-[:checked]:border-indigo-400">
                    <input type="radio" id="${optionId}" name="question-${currentQuestionIndex}" value="${optionChar}" class="mr-2" ${isChecked ? 'checked' : ''}>
                    <span>${optionChar}. ${option}</span>
                </label>
            `;
            optionsDisplay.innerHTML += optionHtml;
        });

        // Add event listeners to new radio buttons
        document.querySelectorAll(`input[name="question-${currentQuestionIndex}"]`).forEach(radio => {
            radio.addEventListener('change', (e) => {
                userAnswers[currentQuestionIndex] = e.target.value;
                apiPut(`test-sessions/${currentTestSessionId}/answer`, {
                    questionIndex: currentQuestionIndex,
                    answer: e.target.value
                }).catch(err => console.error("Failed to save answer:", err));
                updateQuestionNavigation(); // Update nav button style
            });
        });
        updateNavigationButtons();
    }

    function updateNavigationButtons() {
        prevQuestionBtn.disabled = currentQuestionIndex === 0;
        nextQuestionBtn.disabled = currentQuestionIndex === currentTestQuestions.length - 1;
    }

    function navigateQuestion(direction) {
        if (direction === 'prev' && currentQuestionIndex > 0) {
            currentQuestionIndex--;
        } else if (direction === 'next' && currentQuestionIndex < currentTestQuestions.length - 1) {
            currentQuestionIndex++;
        }
        displayQuestion();
        updateQuestionNavigation();
    }

    function startTimer() {
        clearInterval(timerInterval); // Clear any existing timer
        timerSpan.textContent = formatTime(timeLeft);
        timerInterval = setInterval(() => {
            timeLeft--;
            timerSpan.textContent = formatTime(timeLeft);
            if (timeLeft <= 0) {
                clearInterval(timerInterval);
                alert('Time is up!');
                handleSubmitTest(true); // Auto-submit
            }
            if (timeLeft < 60 && !timerSpan.classList.contains('timer-warning')) {
                timerSpan.classList.add('text-red-500', 'timer-warning');
            }
        }, 1000);
    }

    async function handleSubmitTest(isAutoSubmit = false) {
        clearInterval(timerInterval);
        
        // Prepare test results for difficulty adjustment
        const testResults = currentTestQuestions.map((q, idx) => ({
            questionId: q.id,
            topic: q.topic,
            subtopic: q.subtopic,
            isCorrect: userAnswers[idx] === q.correct_answer
        }));
        
        // Update user ratings if not admin
        if (!isAdmin) {
            try {
                await adaptiveDifficultyManager.updateRatings(testResults);
            } catch (error) {
                console.error('Error updating difficulty ratings:', error);
            }
        }
        if (!isAutoSubmit && !confirm('Are you sure you want to submit the test?')) {
            startTimer(); // Resume timer if user cancels
            return;
        }

        testActiveArea.classList.add('hidden');
        testLoadingIndicator.classList.remove('hidden'); // Show loader while submitting

        try {
            const timeTaken = (currentTestQuestions.length * TIME_PER_QUESTION_SECONDS) - timeLeft;
            const resultData = await apiPut(`test-sessions/${currentTestSessionId}/complete`, {
                timeTakenSeconds: timeTaken
            });

            // Display score
            scoreDisplay.textContent = resultData.score.correct;
            totalScoreDisplay.textContent = resultData.score.total;
            detailedResultsDiv.innerHTML = '';

            // Create detailed results
            resultData.results.forEach((result, index) => {
                const resultItem = document.createElement('div');
                const isCorrect = result.isCorrect;
                resultItem.className = `p-4 rounded-md ${isCorrect ? 'bg-green-50' : 'bg-red-50'} border ${isCorrect ? 'border-green-200' : 'border-red-200'} mb-4`;
                resultItem.innerHTML = `
                    <p class="font-semibold text-slate-800 mb-2">${index + 1}. ${result.question}</p>
                    <!-- Original options can be fetched if needed, or stored in currentTestQuestions -->
                    <p class="text-sm">Your Answer: <span class="font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}">${result.userAnswer || 'Not Answered'}</span></p>
                    <p class="text-sm">Correct Answer: <span class="font-medium text-green-700">${result.correctAnswer}</span></p>
                    ${result.explanation ? `<div class="mt-2 p-2 bg-slate-100 rounded-md text-sm text-slate-700"><strong>Explanation:</strong> ${result.explanation}</div>` : ''}
                    <div class="mt-2 flex space-x-4">
                        <button class="request-explanation-btn text-xs text-indigo-600 hover:underline mt-1" 
                            data-question-id="${result.questionId}" 
                            data-topic="${result.topic || ''}" 
                            data-subtopic="${result.subtopic || ''}" 
                            data-difficulty="${result.difficulty || ''}">Get Explanation</button>
                        <button class="challenge-answer-btn text-xs text-blue-600 hover:underline mt-1" 
                            data-question-id="${result.questionId}" 
                            data-question="${result.question.replace(/"/g, '&quot;')}" 
                            data-answer="${result.correctAnswer.replace(/"/g, '&quot;')}" 
                            data-explanation="${(result.explanation || '').replace(/"/g, '&quot;')}">Challenge Answer</button>
                    </div>
                `;
                detailedResultsDiv.appendChild(resultItem);
            });

            // Show test results and notify other modules
            testResultsArea.classList.remove('hidden');
            document.dispatchEvent(new CustomEvent('testResultsDisplayed'));
        } catch (error) {
            console.error('Error submitting test:', error);
            alert(`Failed to submit test: ${error.message}`);
            // Optionally, re-enable the test area or show an error message
            testActiveArea.classList.remove('hidden'); // Or redirect to config
        } finally {
            testLoadingIndicator.classList.add('hidden');
        }
    }
    
    // handleChallengeAnswer has been moved to the challengeAnswer.js module


    function retakeTest() {
        testResultsArea.classList.add('hidden');
        testConfigSection.classList.remove('hidden');
        currentTestQuestions = [];
        userAnswers = [];
        currentQuestionIndex = 0;
        timeLeft = 0;
        currentTestSessionId = null;
        clearInterval(timerInterval);
        timerSpan.textContent = '00:00';
        timerSpan.classList.remove('text-red-500', 'timer-warning');
        questionNavigationContainer.innerHTML = ''; // Clear question navigation
    }
});
/**
 * Admin Panel - Syllabi Listing Module
 * Handles displaying and basic operations for syllabi in admin panel
 */

document.addEventListener('DOMContentLoaded', () => {
  // Only initialize if we're on the admin panel page
  if (!document.getElementById('syllabus-management-section')) return;

  // Initialize the syllabi list component
  initSyllabiList();
});

/**
 * Initialize the syllabi listing component
 */
async function initSyllabiList() {
  const syllabiListContainer = document.getElementById('syllabi-list-container');
  
  if (!syllabiListContainer) return;
  
  // Add the loading state
  syllabiListContainer.innerHTML = `
    <div class="flex justify-center items-center p-8">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      <p class="ml-3 text-indigo-500">Loading syllabi...</p>
    </div>
  `;

  try {
    // Fetch all syllabi
    const data = await apiGet('syllabi');
    
    if (!data || !data.syllabi || data.syllabi.length === 0) {
      syllabiListContainer.innerHTML = `
        <div class="text-center p-8">
          <p class="text-gray-500">No syllabi found. Create your first syllabus!</p>
          <button id="create-first-syllabus-btn" 
            class="mt-4 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors">
            Create Syllabus
          </button>
        </div>
      `;
      
      // Add event listener for the create first syllabus button
      document.getElementById('create-first-syllabus-btn').addEventListener('click', () => {
        // Dispatch event to open create syllabus form
        document.dispatchEvent(new CustomEvent('openCreateSyllabusForm'));
      });
      
      return;
    }
    
    // Render the syllabi list
    renderSyllabiList(data.syllabi);
    
    // Add event listeners
    addSyllabiListEventListeners();
    
  } catch (error) {
    console.error('Error loading syllabi:', error);
    syllabiListContainer.innerHTML = `
      <div class="bg-red-50 border-l-4 border-red-500 p-4">
        <div class="flex items-center">
          <div class="text-red-500">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">
              Error loading syllabi. Please try again later.
            </p>
          </div>
        </div>
        <button id="retry-load-syllabi-btn" class="mt-3 bg-red-100 text-red-800 py-1 px-3 rounded-md hover:bg-red-200 transition-colors">
          Retry
        </button>
      </div>
    `;
    
    // Add retry button event listener
    document.getElementById('retry-load-syllabi-btn').addEventListener('click', () => {
      initSyllabiList();
    });
  }
}

/**
 * Render the syllabi list
 * @param {Array} syllabi - Array of syllabus objects
 */
function renderSyllabiList(syllabi) {
  const syllabiListContainer = document.getElementById('syllabi-list-container');
  
  // Create table to display syllabi
  const syllabusTable = document.createElement('table');
  syllabusTable.className = 'min-w-full divide-y divide-gray-200';
  
  // Create table header
  const tableHeader = document.createElement('thead');
  tableHeader.className = 'bg-gray-50';
  tableHeader.innerHTML = `
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
        Name
      </th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
        Exam Type
      </th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
        Units
      </th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
        Last Updated
      </th>
      <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
        Actions
      </th>
    </tr>
  `;
  
  // Create table body
  const tableBody = document.createElement('tbody');
  tableBody.className = 'bg-white divide-y divide-gray-200';
  
  // Add rows for each syllabus
  syllabi.forEach((syllabus, index) => {
    const row = document.createElement('tr');
    row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
    
    // Format date
    const updatedDate = syllabus.updated_at ? new Date(syllabus.updated_at.seconds * 1000) : new Date();
    const formattedDate = updatedDate.toLocaleDateString();
    
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm font-medium text-gray-900">${syllabus.name}</div>
        <div class="text-sm text-gray-500">${syllabus.description || 'No description'}</div>
      </td>
      <td class="px-6 py-4 whitespace-nowrap">
        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
          ${syllabus.exam_type || 'General'}
        </span>
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        ${syllabus.units ? syllabus.units.length : 0} units
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        ${formattedDate}
      </td>
      <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
        <button class="view-syllabus-btn text-indigo-600 hover:text-indigo-900 mr-3" data-id="${syllabus.id}">
          View
        </button>
        <button class="edit-syllabus-btn text-blue-600 hover:text-blue-900 mr-3" data-id="${syllabus.id}">
          Edit
        </button>
        <button class="delete-syllabus-btn text-red-600 hover:text-red-900" data-id="${syllabus.id}">
          Delete
        </button>
      </td>
    `;
    
    tableBody.appendChild(row);
  });
  
  // Assemble the table
  syllabusTable.appendChild(tableHeader);
  syllabusTable.appendChild(tableBody);
  
  // Add create new syllabus button
  const actionBar = document.createElement('div');
  actionBar.className = 'mt-4 flex justify-between items-center';
  actionBar.innerHTML = `
    <div>
      <span class="text-gray-600">${syllabi.length} Syllabi found</span>
    </div>
    <button id="create-syllabus-btn" class="bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors">
      Create New Syllabus
    </button>
  `;
  
  // Clear and update the container
  syllabiListContainer.innerHTML = '';
  syllabiListContainer.appendChild(actionBar);
  syllabiListContainer.appendChild(syllabusTable);
}

/**
 * Add event listeners to the syllabi list elements
 */
function addSyllabiListEventListeners() {
  // Create syllabus button
  const createSyllabusBtn = document.getElementById('create-syllabus-btn');
  if (createSyllabusBtn) {
    createSyllabusBtn.addEventListener('click', () => {
      // Dispatch event to open create syllabus form
      document.dispatchEvent(new CustomEvent('openCreateSyllabusForm'));
    });
  }
  
  // View syllabus buttons
  document.querySelectorAll('.view-syllabus-btn').forEach(button => {
    button.addEventListener('click', (e) => {
      const syllabusId = e.target.getAttribute('data-id');
      // Dispatch event to view syllabus details
      document.dispatchEvent(new CustomEvent('viewSyllabusDetails', {
        detail: { syllabusId }
      }));
    });
  });
  
  // Edit syllabus buttons
  document.querySelectorAll('.edit-syllabus-btn').forEach(button => {
    button.addEventListener('click', (e) => {
      const syllabusId = e.target.getAttribute('data-id');
      // Dispatch event to edit syllabus
      document.dispatchEvent(new CustomEvent('editSyllabus', {
        detail: { syllabusId }
      }));
    });
  });
  
  // Delete syllabus buttons
  document.querySelectorAll('.delete-syllabus-btn').forEach(button => {
    button.addEventListener('click', (e) => {
      const syllabusId = e.target.getAttribute('data-id');
      handleDeleteSyllabus(syllabusId);
    });
  });
}

/**
 * Handle syllabus deletion
 * @param {string} syllabusId - ID of the syllabus to delete
 */
async function handleDeleteSyllabus(syllabusId) {
  if (!confirm('Are you sure you want to delete this syllabus? This action cannot be undone.')) {
    return;
  }
  
  try {
    await apiDelete(`syllabi/${syllabusId}`);
    
    // Refresh the syllabi list
    initSyllabiList();
    
    // Show success message
    showNotification('Syllabus deleted successfully', 'success');
  } catch (error) {
    console.error('Error deleting syllabus:', error);
    showNotification('Failed to delete syllabus', 'error');
  }
}

/**
 * Show a notification message
 * @param {string} message - Message to display
 * @param {string} type - Type of notification (success, error, info)
 */
function showNotification(message, type = 'info') {
  const notificationContainer = document.getElementById('notification-container') || createNotificationContainer();
  
  const notification = document.createElement('div');
  notification.className = `notification ${type} mb-3 p-3 rounded-md flex items-center`;
  
  // Set background color based on type
  switch (type) {
    case 'success':
      notification.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
      break;
    case 'error':
      notification.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
      break;
    default: // info
      notification.classList.add('bg-blue-100', 'text-blue-800', 'border-l-4', 'border-blue-500');
  }
  
  notification.innerHTML = `
    <div class="mr-3">
      <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        ${type === 'success' ? 
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />' :
          type === 'error' ?
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />' :
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
      </svg>
    </div>
    <div>${message}</div>
  `;
  
  notificationContainer.appendChild(notification);
  
  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.classList.add('fade-out');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

/**
 * Create the notification container if it doesn't exist
 * @returns {HTMLElement} - The notification container element
 */
function createNotificationContainer() {
  const container = document.createElement('div');
  container.id = 'notification-container';
  container.className = 'fixed top-4 right-4 z-50 w-72';
  document.body.appendChild(container);
  return container;
}

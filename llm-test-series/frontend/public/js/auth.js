// Authentication Module
document.addEventListener('DOMContentLoaded', () => {
  const userAuthContainer = document.getElementById('user-auth');

  // Initialize auth state
  initAuth();

  /**
   * Initialize authentication
   */
  function initAuth() {
    // Check for existing token
    const token = localStorage.getItem('authToken');
    if (token) {
      // Verify token and get user profile
      verifyTokenAndSetUser(token);
    } else {
      // No token, show sign in
      currentUser = null;
      userId = null;
      renderSignInButton();
    }
  }

  /**
   * Verify token and set user
   */
  async function verifyTokenAndSetUser(token) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        currentUser = data.user;
        userId = data.user.id;
        renderUserProfile(data.user);

        // Dispatch auth state change event
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { user: currentUser, userId }
        }));
      } else {
        // Token invalid, remove it
        localStorage.removeItem('authToken');
        renderSignInButton();
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      localStorage.removeItem('authToken');
      renderSignInButton();
    }
  }
  
  /**
   * Render user profile when signed in
   */
  function renderUserProfile(user) {
    userAuthContainer.innerHTML = `
      <div class="flex items-center">
        <div class="mr-3">
          <p class="text-sm font-medium text-gray-700">${user.name || 'User'}</p>
          <p class="text-xs text-gray-500">${user.email || 'Anonymous'}</p>
          <p class="text-xs text-indigo-500">Level ${user.profile?.level || 1} • ${user.profile?.xp || 0} XP</p>
        </div>
        <div class="relative">
          <button id="user-menu-btn" class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 text-indigo-600 hover:bg-indigo-200 focus:outline-none">
            ${user.name ? user.name.charAt(0).toUpperCase() : 'U'}
          </button>
          <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
            <a href="#" id="profile-link" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
            <a href="#" id="dashboard-link" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
            <a href="#" id="settings-link" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
            <a href="#" id="sign-out-btn" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
          </div>
        </div>
      </div>
    `;

    // Add event listeners
    document.getElementById('user-menu-btn').addEventListener('click', toggleUserMenu);
    document.getElementById('sign-out-btn').addEventListener('click', signOut);
  }
  
  /**
   * Render sign in button when not signed in
   */
  function renderSignInButton() {
    userAuthContainer.innerHTML = `
      <div class="flex space-x-2">
        <button id="sign-in-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none">
          Sign In
        </button>
        <button id="demo-btn" class="inline-flex items-center px-4 py-2 border border-indigo-600 text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 focus:outline-none">
          Try Demo
        </button>
      </div>
    `;

    // Add event listeners
    document.getElementById('sign-in-btn').addEventListener('click', showSignInModal);
    document.getElementById('demo-btn').addEventListener('click', signInAnonymously);
  }
  
  /**
   * Toggle user dropdown menu
   */
  function toggleUserMenu() {
    const dropdown = document.getElementById('user-dropdown');
    dropdown.classList.toggle('hidden');
    
    // Close menu when clicking outside
    const closeMenu = (e) => {
      if (!e.target.closest('#user-menu-btn') && !e.target.closest('#user-dropdown')) {
        dropdown.classList.add('hidden');
        document.removeEventListener('click', closeMenu);
      }
    };
    
    if (!dropdown.classList.contains('hidden')) {
      setTimeout(() => {
        document.addEventListener('click', closeMenu);
      }, 0);
    }
  }
  
  /**
   * Show sign in modal
   */
  function showSignInModal() {
    const modalContainer = document.getElementById('modal-container');
    const modalContent = document.getElementById('modal-content');

    modalContent.innerHTML = `
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-900">Sign In</h2>
          <button id="close-modal" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-4">
          <!-- Login Form -->
          <div id="login-form">
            <form id="login-form-element" class="space-y-4">
              <div>
                <label for="login-email" class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" id="login-email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              </div>
              <div>
                <label for="login-password" class="block text-sm font-medium text-gray-700">Password</label>
                <input type="password" id="login-password" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              </div>
              <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Sign In
              </button>
            </form>
            <div class="mt-4 text-center">
              <button id="show-register" class="text-indigo-600 hover:text-indigo-500 text-sm">
                Don't have an account? Sign up
              </button>
            </div>
          </div>

          <!-- Register Form -->
          <div id="register-form" class="hidden">
            <form id="register-form-element" class="space-y-4">
              <div>
                <label for="register-name" class="block text-sm font-medium text-gray-700">Full Name</label>
                <input type="text" id="register-name" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              </div>
              <div>
                <label for="register-email" class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" id="register-email" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              </div>
              <div>
                <label for="register-password" class="block text-sm font-medium text-gray-700">Password</label>
                <input type="password" id="register-password" required minlength="6" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
              </div>
              <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Sign Up
              </button>
            </form>
            <div class="mt-4 text-center">
              <button id="show-login" class="text-indigo-600 hover:text-indigo-500 text-sm">
                Already have an account? Sign in
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    modalContainer.classList.remove('hidden');

    // Add event listeners
    document.getElementById('close-modal').addEventListener('click', closeModal);
    document.getElementById('show-register').addEventListener('click', () => {
      document.getElementById('login-form').classList.add('hidden');
      document.getElementById('register-form').classList.remove('hidden');
    });
    document.getElementById('show-login').addEventListener('click', () => {
      document.getElementById('register-form').classList.add('hidden');
      document.getElementById('login-form').classList.remove('hidden');
    });
    document.getElementById('login-form-element').addEventListener('submit', handleLogin);
    document.getElementById('register-form-element').addEventListener('submit', handleRegister);
  }

  /**
   * Close modal
   */
  function closeModal() {
    document.getElementById('modal-container').classList.add('hidden');
  }

  /**
   * Handle login form submission
   */
  async function handleLogin(e) {
    e.preventDefault();
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and set user
        localStorage.setItem('authToken', data.token);
        currentUser = data.user;
        userId = data.user.id;

        renderUserProfile(data.user);
        closeModal();

        // Dispatch auth state change event
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { user: currentUser, userId }
        }));

        console.log('Login successful');
      } else {
        alert(data.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Please try again.');
    }
  }

  /**
   * Handle register form submission
   */
  async function handleRegister(e) {
    e.preventDefault();
    const name = document.getElementById('register-name').value;
    const email = document.getElementById('register-email').value;
    const password = document.getElementById('register-password').value;

    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name, email, password })
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and set user
        localStorage.setItem('authToken', data.token);
        currentUser = data.user;
        userId = data.user.id;

        renderUserProfile(data.user);
        closeModal();

        // Dispatch auth state change event
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { user: currentUser, userId }
        }));

        console.log('Registration successful');
      } else {
        alert(data.error || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      alert('Registration failed. Please try again.');
    }
  }

  /**
   * Sign in anonymously for demo
   */
  async function signInAnonymously() {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/anonymous`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and set user
        localStorage.setItem('authToken', data.token);
        currentUser = data.user;
        userId = data.user.id;

        renderUserProfile(data.user);

        // Dispatch auth state change event
        document.dispatchEvent(new CustomEvent('authStateChanged', {
          detail: { user: currentUser, userId }
        }));

        console.log('Anonymous sign-in successful');
      } else {
        alert(data.error || 'Demo login failed');
      }
    } catch (error) {
      console.error('Anonymous sign-in error:', error);
      alert('Demo login failed. Please try again.');
    }
  }

  /**
   * Sign out
   */
  function signOut() {
    // Remove token and clear user data
    localStorage.removeItem('authToken');
    currentUser = null;
    userId = null;

    renderSignInButton();

    // Dispatch auth state change event
    document.dispatchEvent(new CustomEvent('authStateChanged', {
      detail: { user: null, userId: null }
    }));

    console.log('Sign-out successful');
  }

  // Expose functions globally
  window.isUserSignedIn = () => !!currentUser;
  window.getCurrentUser = () => currentUser;
  window.getUserId = () => userId;
  window.getAuthToken = () => localStorage.getItem('authToken');
});

/**
 * Challenge Answer Module
 * Provides functionality for users to challenge question answers and explanations
 * with AI-powered evaluation of their challenges
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initChallengeAnswerEvents();
});

/**
 * Initialize event listeners for challenge answer feature
 */
function initChallengeAnswerEvents() {
  // Listen for test results displayed event
  document.addEventListener('testResultsDisplayed', () => {
    // Add event listeners to all challenge buttons
    const challengeButtons = document.querySelectorAll('.challenge-answer-btn');
    challengeButtons.forEach(button => {
      button.addEventListener('click', handleChallengeRequest);
    });
  });
}

/**
 * Handle click on challenge answer button
 * @param {Event} e - Click event
 */
function handleChallengeRequest(e) {
  e.preventDefault();
  
  const questionId = e.target.getAttribute('data-question-id');
  const questionText = e.target.getAttribute('data-question');
  const correctAnswer = e.target.getAttribute('data-answer');
  const explanation = e.target.getAttribute('data-explanation');
  
  showChallengeModal(questionId, questionText, correctAnswer, explanation);
}

/**
 * Create and show the challenge modal
 * @param {string} questionId - ID of the question being challenged
 * @param {string} questionText - Text of the question
 * @param {string} correctAnswer - Correct answer
 * @param {string} explanation - Original explanation
 */
function showChallengeModal(questionId, questionText, correctAnswer, explanation) {
  // Create modal if it doesn't exist, otherwise just update it
  let modal = document.getElementById('challenge-answer-modal');
  
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'challenge-answer-modal';
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-90vh overflow-y-auto">
        <div class="flex justify-between items-center p-4 border-b">
          <h3 class="text-lg font-bold">Challenge Answer</h3>
          <button id="close-challenge-modal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div id="challenge-content" class="p-6">
          <div class="mb-4 bg-gray-50 p-4 rounded-md">
            <h4 class="font-semibold mb-2">Question:</h4>
            <p id="challenge-question" class="mb-2"></p>
            <div class="mt-2">
              <p class="font-semibold">Correct Answer:</p>
              <p id="challenge-answer"></p>
            </div>
            <div class="mt-2">
              <p class="font-semibold">Explanation:</p>
              <p id="challenge-explanation"></p>
            </div>
          </div>
          
          <form id="challenge-form">
            <input type="hidden" id="challenge-question-id">
            <div class="mb-4">
              <label for="challenge-reason" class="block font-medium mb-2">
                Why do you think this answer or explanation is incorrect?
              </label>
              <textarea 
                id="challenge-reason" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" 
                rows="5" 
                placeholder="Explain your reasoning..."></textarea>
            </div>
            <div class="flex justify-end">
              <button type="button" id="cancel-challenge" class="px-4 py-2 text-gray-700 mr-2">
                Cancel
              </button>
              <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Submit Challenge
              </button>
            </div>
          </form>
          
          <div id="challenge-result" class="mt-4 hidden"></div>
          <div id="challenge-loading" class="mt-4 hidden text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
            <p class="mt-2">Evaluating your challenge...</p>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add close button handler
    document.getElementById('close-challenge-modal').addEventListener('click', () => {
      modal.classList.add('hidden');
    });
    
    // Add cancel button handler
    document.getElementById('cancel-challenge').addEventListener('click', () => {
      modal.classList.add('hidden');
    });
    
    // Add form submission handler
    document.getElementById('challenge-form').addEventListener('submit', handleChallengeSubmission);
  } else {
    modal.classList.remove('hidden');
  }
  
  // Update modal content with question details
  document.getElementById('challenge-question-id').value = questionId;
  document.getElementById('challenge-question').textContent = questionText;
  document.getElementById('challenge-answer').textContent = correctAnswer;
  document.getElementById('challenge-explanation').textContent = explanation;
  
  // Reset form and results
  document.getElementById('challenge-reason').value = '';
  document.getElementById('challenge-result').classList.add('hidden');
  document.getElementById('challenge-form').classList.remove('hidden');
}

/**
 * Handle submission of challenge form
 * @param {Event} e - Form submission event
 */
async function handleChallengeSubmission(e) {
  e.preventDefault();
  
  const questionId = document.getElementById('challenge-question-id').value;
  const challengeReason = document.getElementById('challenge-reason').value.trim();
  
  if (!challengeReason) {
    alert('Please provide your reasoning for the challenge.');
    return;
  }
  
  // Show loading state
  document.getElementById('challenge-form').classList.add('hidden');
  document.getElementById('challenge-loading').classList.remove('hidden');
  document.getElementById('challenge-result').classList.add('hidden');
  
  try {
    // Submit challenge to API
    const response = await apiPost('enhanced-questions/challenge-answer', {
      questionId,
      challenge: challengeReason
    });
    
    // Hide loading state
    document.getElementById('challenge-loading').classList.add('hidden');
    
    // Show results
    if (response && response.evaluation) {
      showChallengeResult(response.evaluation);
    } else {
      showChallengeError('Failed to evaluate your challenge. Please try again.');
    }
  } catch (error) {
    console.error('Error submitting challenge:', error);
    document.getElementById('challenge-loading').classList.add('hidden');
    showChallengeError('An error occurred while evaluating your challenge. Please try again.');
  }
}

/**
 * Display challenge evaluation result
 * @param {Object} evaluation - Challenge evaluation from API
 */
function showChallengeResult(evaluation) {
  const resultContainer = document.getElementById('challenge-result');
  resultContainer.classList.remove('hidden');
  
  // Determine verdict class and icon
  let verdictClass = 'text-yellow-700';
  let verdictIcon = 'question';
  
  if (evaluation.verdict === 'valid') {
    verdictClass = 'text-green-700';
    verdictIcon = 'check-circle';
  } else if (evaluation.verdict === 'invalid') {
    verdictClass = 'text-red-700';
    verdictIcon = 'x-circle';
  } else if (evaluation.verdict === 'partially-valid') {
    verdictClass = 'text-yellow-700';
    verdictIcon = 'alert-circle';
  }
  
  // Render the evaluation
  resultContainer.innerHTML = `
    <div class="border-t border-gray-200 pt-4">
      <div class="flex items-center mb-4">
        <svg class="w-6 h-6 ${verdictClass} mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="${getIconPath(verdictIcon)}"></path>
        </svg>
        <h4 class="font-bold ${verdictClass}">
          Challenge Evaluation: 
          <span>${evaluation.verdict.charAt(0).toUpperCase() + evaluation.verdict.slice(1)}</span>
        </h4>
      </div>
      
      <div class="mb-4">
        <h5 class="font-semibold mb-1">Evaluation:</h5>
        <div class="bg-gray-50 p-3 rounded-md">
          ${evaluation.reasoning || 'No evaluation provided.'}
        </div>
      </div>
      
      ${evaluation.suggestedCorrection ? `
        <div class="mb-4 border-l-4 border-green-500 pl-3">
          <h5 class="font-semibold mb-1">Suggested Correction:</h5>
          <div class="mt-2">
            ${evaluation.suggestedCorrection.correctAnswer ? `
              <p class="font-medium">Correct Answer:</p>
              <p class="mb-2">${evaluation.suggestedCorrection.correctAnswer}</p>
            ` : ''}
            ${evaluation.suggestedCorrection.explanation ? `
              <p class="font-medium">Explanation:</p>
              <p>${evaluation.suggestedCorrection.explanation}</p>
            ` : ''}
          </div>
        </div>
      ` : ''}
      
      <div class="mt-4 flex justify-end">
        <button id="close-challenge-result" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
          Close
        </button>
      </div>
    </div>
  `;
  
  // Add close button handler
  document.getElementById('close-challenge-result').addEventListener('click', () => {
    document.getElementById('challenge-answer-modal').classList.add('hidden');
  });
}

/**
 * Show error message in challenge modal
 * @param {string} message - Error message to display
 */
function showChallengeError(message) {
  const resultContainer = document.getElementById('challenge-result');
  resultContainer.classList.remove('hidden');
  
  resultContainer.innerHTML = `
    <div class="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
      <p>${message}</p>
    </div>
    <div class="mt-4 flex justify-between">
      <button id="retry-challenge" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
        Try Again
      </button>
      <button id="close-challenge-error" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
        Close
      </button>
    </div>
  `;
  
  // Add retry button handler
  document.getElementById('retry-challenge').addEventListener('click', () => {
    document.getElementById('challenge-result').classList.add('hidden');
    document.getElementById('challenge-form').classList.remove('hidden');
  });
  
  // Add close button handler
  document.getElementById('close-challenge-error').addEventListener('click', () => {
    document.getElementById('challenge-answer-modal').classList.add('hidden');
  });
}

/**
 * Get SVG path for icons
 * @param {string} icon - Icon name
 * @return {string} SVG path definition
 */
function getIconPath(icon) {
  switch(icon) {
    case 'check-circle':
      return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
    case 'x-circle':
      return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';
    case 'alert-circle':
      return 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
    case 'question':
    default:
      return 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }
}

/**
 * Admin Panel - Syllabi Form Module
 * Handles creating and editing syllabi
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Set up event listeners for syllabi form events
  document.addEventListener('openCreateSyllabusForm', handleOpenSyllabusForm);
  document.addEventListener('editSyllabus', (e) => handleOpenSyllabusForm(e, e.detail.syllabusId));
  
  // Initialize close form button if it exists
  const closeFormBtn = document.getElementById('close-syllabus-form-btn');
  if (closeFormBtn) {
    closeFormBtn.addEventListener('click', closeSyllabusForm);
  }
  
  // Initialize form submission
  const syllabusForm = document.getElementById('syllabus-form');
  if (syllabusForm) {
    syllabusForm.addEventListener('submit', handleSyllabusFormSubmit);
  }
});

/**
 * Open the syllabus form for creation or editing
 * @param {Event} event - The event that triggered this handler
 * @param {string} syllabusId - Optional ID of syllabus to edit
 */
async function handleOpenSyllabusForm(event, syllabusId = null) {
  const formContainer = document.getElementById('syllabus-form-container');
  const formTitle = document.getElementById('syllabus-form-title');
  const form = document.getElementById('syllabus-form');
  
  if (!formContainer || !formTitle || !form) return;
  
  // Show the form container
  formContainer.classList.remove('hidden');
  
  // Set form title based on mode (create or edit)
  formTitle.textContent = syllabusId ? 'Edit Syllabus' : 'Create New Syllabus';
  
  // Reset form
  form.reset();
  
  // Set form mode attribute
  form.setAttribute('data-mode', syllabusId ? 'edit' : 'create');
  
  // If editing, load syllabus data
  if (syllabusId) {
    form.setAttribute('data-id', syllabusId);
    
    // Show loading state
    form.innerHTML = `
      <div class="flex justify-center items-center p-8">
        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
        <p class="ml-3 text-indigo-500">Loading syllabus data...</p>
      </div>
    `;
    
    try {
      // Fetch syllabus data
      const response = await fetch(`/api/syllabi/${syllabusId}`);
      if (!response.ok) throw new Error('Failed to fetch syllabus');
      
      const syllabus = await response.json();
      
      // Rebuild the form
      buildSyllabusForm(form);
      
      // Populate form with syllabus data
      document.getElementById('syllabus-name').value = syllabus.name || '';
      document.getElementById('syllabus-description').value = syllabus.description || '';
      document.getElementById('syllabus-exam-type').value = syllabus.exam_type || '';
      
      // Populate units if they exist
      if (syllabus.units && syllabus.units.length > 0) {
        const unitsContainer = document.getElementById('syllabus-units-container');
        
        // Remove any existing unit rows except the template
        Array.from(unitsContainer.children).forEach(child => {
          if (child.id !== 'unit-template') {
            child.remove();
          }
        });
        
        // Add each unit
        syllabus.units.forEach((unit, index) => {
          addUnitRow(unit.name, unit.topics);
        });
      }
      
    } catch (error) {
      console.error('Error loading syllabus data:', error);
      
      // Show error and rebuild form
      buildSyllabusForm(form);
      showFormError('Failed to load syllabus data. Please try again.');
    }
  } else {
    // Build empty form for creation
    buildSyllabusForm(form);
  }
}

/**
 * Build the syllabus form structure
 * @param {HTMLFormElement} form - The form element
 */
function buildSyllabusForm(form) {
  form.innerHTML = `
    <div id="form-error" class="hidden mb-4 p-3 bg-red-100 text-red-700 rounded-md"></div>
    
    <div class="mb-4">
      <label for="syllabus-name" class="block text-sm font-medium text-gray-700">Name</label>
      <input type="text" id="syllabus-name" name="name" required
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
    </div>
    
    <div class="mb-4">
      <label for="syllabus-description" class="block text-sm font-medium text-gray-700">Description</label>
      <textarea id="syllabus-description" name="description" rows="3"
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
    </div>
    
    <div class="mb-4">
      <label for="syllabus-exam-type" class="block text-sm font-medium text-gray-700">Exam Type</label>
      <select id="syllabus-exam-type" name="exam_type"
        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
        <option value="">Select an exam type</option>
        <option value="competitive">Competitive Exam</option>
        <option value="academic">Academic Exam</option>
        <option value="certification">Certification</option>
        <option value="entrance">Entrance Exam</option>
        <option value="other">Other</option>
      </select>
    </div>
    
    <div class="mb-4">
      <div class="flex justify-between items-center">
        <label class="block text-sm font-medium text-gray-700">Units</label>
        <button type="button" id="add-unit-btn" class="text-sm text-indigo-600 hover:text-indigo-900">
          Add Unit
        </button>
      </div>
      
      <div id="syllabus-units-container" class="mt-2 border rounded-md p-3">
        <!-- Unit template - will be cloned when adding new units -->
        <div id="unit-template" class="hidden unit-row border-b pb-3 mb-3">
          <div class="flex items-center justify-between mb-2">
            <input type="text" placeholder="Unit Name" class="unit-name w-3/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            <button type="button" class="remove-unit-btn text-red-600 hover:text-red-900">Remove</button>
          </div>
          <div class="topics-container pl-4">
            <div class="flex items-center justify-between mb-2">
              <input type="text" placeholder="Topic Name" class="topic-name w-3/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
              <button type="button" class="remove-topic-btn text-red-600 hover:text-red-900">Remove</button>
            </div>
          </div>
          <button type="button" class="add-topic-btn text-sm text-indigo-600 hover:text-indigo-900 mt-2">
            Add Topic
          </button>
        </div>
        
        <!-- Units will be added here dynamically -->
        <div class="text-center text-gray-500 py-2">No units added yet. Click "Add Unit" to add syllabus units.</div>
      </div>
    </div>
    
    <div class="mt-6 flex justify-end space-x-3">
      <button type="button" id="close-syllabus-form-btn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
        Cancel
      </button>
      <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Save
      </button>
    </div>
  `;
  
  // Add event listeners for the new form elements
  document.getElementById('add-unit-btn').addEventListener('click', () => addUnitRow());
  
  // Set up the initial event delegation for dynamic elements
  setupFormEventDelegation();
}

/**
 * Add a new unit row to the units container
 * @param {string} unitName - Optional unit name for editing
 * @param {Array} topics - Optional array of topics for editing
 */
function addUnitRow(unitName = '', topics = []) {
  const unitsContainer = document.getElementById('syllabus-units-container');
  const template = document.getElementById('unit-template');
  
  // Remove "no units" message if it exists
  const noUnitsMessage = unitsContainer.querySelector('.text-gray-500');
  if (noUnitsMessage) {
    noUnitsMessage.remove();
  }
  
  // Clone the template
  const unitRow = template.cloneNode(true);
  unitRow.id = ''; // Remove the id
  unitRow.classList.remove('hidden');
  
  // Set unit name if provided
  unitRow.querySelector('.unit-name').value = unitName;
  
  // Clear existing topics except the first one
  const topicsContainer = unitRow.querySelector('.topics-container');
  const firstTopicInput = topicsContainer.querySelector('.topic-name');
  
  if (topics.length > 0) {
    // Set first topic
    firstTopicInput.value = topics[0];
    
    // Add additional topics
    for (let i = 1; i < topics.length; i++) {
      addTopicToUnit(unitRow, topics[i]);
    }
  }
  
  // Append to container
  unitsContainer.appendChild(unitRow);
}

/**
 * Add a new topic to a unit row
 * @param {HTMLElement} unitRow - The unit row element
 * @param {string} topicName - Optional topic name for editing
 */
function addTopicToUnit(unitRow, topicName = '') {
  const topicsContainer = unitRow.querySelector('.topics-container');
  
  // Create new topic row
  const topicRow = document.createElement('div');
  topicRow.className = 'flex items-center justify-between mb-2';
  topicRow.innerHTML = `
    <input type="text" placeholder="Topic Name" value="${topicName}" class="topic-name w-3/4 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
    <button type="button" class="remove-topic-btn text-red-600 hover:text-red-900">Remove</button>
  `;
  
  // Add to topics container
  topicsContainer.appendChild(topicRow);
}

/**
 * Set up event delegation for dynamically added form elements
 */
function setupFormEventDelegation() {
  const form = document.getElementById('syllabus-form');
  
  if (!form) return;
  
  form.addEventListener('click', (e) => {
    // Handle remove unit button
    if (e.target.classList.contains('remove-unit-btn')) {
      const unitRow = e.target.closest('.unit-row');
      if (unitRow) unitRow.remove();
      
      // Check if we need to add the "no units" message back
      const unitsContainer = document.getElementById('syllabus-units-container');
      if (unitsContainer.querySelectorAll('.unit-row:not(#unit-template)').length === 0) {
        unitsContainer.innerHTML += `
          <div class="text-center text-gray-500 py-2">No units added yet. Click "Add Unit" to add syllabus units.</div>
        `;
      }
    }
    
    // Handle remove topic button
    if (e.target.classList.contains('remove-topic-btn')) {
      const topicRow = e.target.closest('div');
      if (topicRow) topicRow.remove();
    }
    
    // Handle add topic button
    if (e.target.classList.contains('add-topic-btn')) {
      const unitRow = e.target.closest('.unit-row');
      if (unitRow) {
        addTopicToUnit(unitRow);
      }
    }
  });
}

/**
 * Close the syllabus form
 */
function closeSyllabusForm() {
  const formContainer = document.getElementById('syllabus-form-container');
  if (formContainer) {
    formContainer.classList.add('hidden');
  }
}

/**
 * Handle syllabus form submission
 * @param {Event} event - The form submission event
 */
async function handleSyllabusFormSubmit(event) {
  event.preventDefault();
  
  const form = event.target;
  const formMode = form.getAttribute('data-mode');
  const syllabusId = form.getAttribute('data-id');
  
  // Collect form data
  const formData = {
    name: document.getElementById('syllabus-name').value,
    description: document.getElementById('syllabus-description').value,
    exam_type: document.getElementById('syllabus-exam-type').value,
    units: []
  };
  
  // Collect units and topics
  const unitRows = document.querySelectorAll('#syllabus-units-container .unit-row:not(#unit-template)');
  unitRows.forEach(unitRow => {
    const unitName = unitRow.querySelector('.unit-name').value;
    if (!unitName) return; // Skip empty units
    
    const topics = [];
    unitRow.querySelectorAll('.topic-name').forEach(topicInput => {
      if (topicInput.value) {
        topics.push(topicInput.value);
      }
    });
    
    formData.units.push({
      name: unitName,
      topics
    });
  });
  
  // Validate form
  if (!formData.name) {
    showFormError('Syllabus name is required');
    return;
  }
  
  if (formData.units.length === 0) {
    showFormError('At least one unit is required');
    return;
  }
  
  try {
    // Show saving state
    const saveButton = form.querySelector('button[type="submit"]');
    const originalButtonText = saveButton.textContent;
    saveButton.disabled = true;
    saveButton.textContent = 'Saving...';
    
    // API endpoint and method based on mode
    const endpoint = formMode === 'edit' 
      ? `/api/syllabi/${syllabusId}` 
      : '/api/syllabi';
    
    const method = formMode === 'edit' ? 'PUT' : 'POST';
    
    // Save to server
    const response = await fetch(endpoint, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to ${formMode} syllabus`);
    }
    
    // Success - close form and reload syllabi
    closeSyllabusForm();
    
    // Trigger syllabus list reload
    document.dispatchEvent(new CustomEvent('syllabusDataChanged'));
    
    // Show success notification
    showNotification(`Syllabus successfully ${formMode === 'edit' ? 'updated' : 'created'}`, 'success');
    
  } catch (error) {
    console.error(`Error ${formMode}ing syllabus:`, error);
    showFormError(`Failed to ${formMode} syllabus. Please try again.`);
    
  } finally {
    // Reset button state
    const saveButton = form.querySelector('button[type="submit"]');
    saveButton.disabled = false;
    saveButton.textContent = 'Save';
  }
}

/**
 * Show error message in the form
 * @param {string} message - Error message to display
 */
function showFormError(message) {
  const errorContainer = document.getElementById('form-error');
  if (!errorContainer) return;
  
  errorContainer.textContent = message;
  errorContainer.classList.remove('hidden');
  
  // Auto-hide after 5 seconds
  setTimeout(() => {
    errorContainer.classList.add('hidden');
  }, 5000);
}

/**
 * Show a notification message
 * @param {string} message - Message to display
 * @param {string} type - Type of notification (success, error, info)
 */
function showNotification(message, type = 'info') {
  const notificationContainer = document.getElementById('notification-container') || createNotificationContainer();
  
  const notification = document.createElement('div');
  notification.className = `notification ${type} mb-3 p-3 rounded-md flex items-center`;
  
  // Set background color based on type
  switch (type) {
    case 'success':
      notification.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
      break;
    case 'error':
      notification.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
      break;
    default: // info
      notification.classList.add('bg-blue-100', 'text-blue-800', 'border-l-4', 'border-blue-500');
  }
  
  notification.innerHTML = `
    <div class="mr-3">
      <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        ${type === 'success' ? 
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />' :
          type === 'error' ?
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />' :
          '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'
        }
      </svg>
    </div>
    <div>${message}</div>
  `;
  
  notificationContainer.appendChild(notification);
  
  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.classList.add('fade-out');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

/**
 * Create the notification container if it doesn't exist
 * @returns {HTMLElement} - The notification container element
 */
function createNotificationContainer() {
  const container = document.createElement('div');
  container.id = 'notification-container';
  container.className = 'fixed top-4 right-4 z-50 w-72';
  document.body.appendChild(container);
  return container;
}

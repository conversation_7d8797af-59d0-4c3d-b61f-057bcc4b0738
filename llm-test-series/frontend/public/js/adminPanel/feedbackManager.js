// Global state
let currentFeedback = null;
let feedbackChart = null;
let currentFilter = 'all';
let reviewModal = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    initializeChart();
    loadReviewQueue();
    loadStatistics();
    loadReviewHistory();
    setupModal();
    startAutoRefresh();
});

// Initialize feedback chart
function initializeChart() {
    const ctx = document.getElementById('feedbackChart').getContext('2d');
    feedbackChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'Approved', 'Rejected'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#ffc107', '#28a745', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Load review queue
async function loadReviewQueue() {
    try {
        const response = await fetch(`/api/admin/feedback/queue?priority=${currentFilter}`);
        if (!response.ok) throw new Error('Failed to load review queue');

        const queue = await response.json();
        const container = document.getElementById('reviewQueue');
        
        container.innerHTML = queue.map(item => `
            <div class="list-group-item ${item.priority === 'high' ? 'high-priority' : ''}" onclick="openReview('${item.feedback_id}')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${item.feedback.feedback_type === 'challenge_answer' ? 'Answer Challenge' : 'Content Correction'}</h6>
                        <small class="text-muted">Submitted ${formatTime(item.created_at)}</small>
                    </div>
                    <div>
                        <span class="badge bg-${item.priority === 'high' ? 'danger' : 'secondary'}">${item.priority}</span>
                    </div>
                </div>
                <p class="mb-1 text-truncate">${item.feedback.notes || 'No notes provided'}</p>
            </div>
        `).join('');

        updateStatistics(queue);
    } catch (error) {
        console.error('Failed to load review queue:', error);
        showToast('Failed to load review queue', 'error');
    }
}

// Load statistics
async function loadStatistics() {
    try {
        // Get counts for different statuses
        const [pending, approved, rejected] = await Promise.all([
            getStatusCount('pending'),
            getStatusCount('approved'),
            getStatusCount('rejected')
        ]);

        // Update statistics display
        document.getElementById('pendingCount').textContent = pending;
        document.getElementById('highPriorityCount').textContent = 
            await getPriorityCount('high');
        document.getElementById('processedToday').textContent = 
            await getProcessedTodayCount();

        // Update chart
        feedbackChart.data.datasets[0].data = [pending, approved, rejected];
        feedbackChart.update();
    } catch (error) {
        console.error('Failed to load statistics:', error);
    }
}

// Load review history
async function loadReviewHistory() {
    try {
        const response = await fetch('/api/admin/feedback?status=reviewed');
        if (!response.ok) throw new Error('Failed to load review history');

        const history = await response.json();
        const container = document.getElementById('reviewHistory');

        container.innerHTML = history.map(item => `
            <tr>
                <td>${formatTime(item.updated_at)}</td>
                <td class="text-truncate" style="max-width: 200px;">${getQuestionPreview(item)}</td>
                <td>${formatFeedbackType(item.feedback_type)}</td>
                <td>
                    <span class="status-badge ${item.status}">${item.status}</span>
                </td>
                <td>${item.reviewer_id || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="openReview('${item.id}')">
                        View
                    </button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Failed to load review history:', error);
    }
}

// Open review modal
async function openReview(feedbackId) {
    try {
        const response = await fetch(`/api/admin/feedback/${feedbackId}`);
        if (!response.ok) throw new Error('Failed to load feedback');

        currentFeedback = await response.json();
        
        // Update modal content
        document.getElementById('originalQuestion').textContent = currentFeedback.question.question_text;
        document.getElementById('originalOptions').innerHTML = currentFeedback.question.options
            .map((option, index) => `
                <div class="option ${option === currentFeedback.question.correct_answer ? 'correct' : ''}">
                    ${String.fromCharCode(65 + index)}) ${option}
                </div>
            `).join('');
        document.getElementById('originalAnswer').textContent = currentFeedback.question.correct_answer;
        document.getElementById('originalExplanation').textContent = currentFeedback.question.explanation;
        
        document.getElementById('feedbackType').textContent = formatFeedbackType(currentFeedback.feedback_type);
        document.getElementById('feedbackNotes').textContent = currentFeedback.notes || 'No notes provided';
        
        // Show suggested changes if present
        const suggestedChanges = document.getElementById('suggestedChanges');
        if (currentFeedback.suggested_correction) {
            suggestedChanges.innerHTML = `
                <h6>Suggested Changes:</h6>
                ${Object.entries(currentFeedback.suggested_correction).map(([key, value]) => `
                    <div class="change-item">
                        <strong>${formatKey(key)}:</strong>
                        <div>${formatValue(value)}</div>
                    </div>
                `).join('')}
            `;
        } else {
            suggestedChanges.innerHTML = '';
        }

        // Show LLM review if present
        const llmReview = document.getElementById('llmReview');
        if (currentFeedback.llm_review) {
            llmReview.innerHTML = `
                <div class="alert ${currentFeedback.llm_review.is_valid || currentFeedback.llm_review.should_update ? 'alert-success' : 'alert-warning'}">
                    ${currentFeedback.llm_review.explanation}
                </div>
            `;
        } else {
            llmReview.innerHTML = '<div class="alert alert-info">AI review pending...</div>';
        }

        // Clear reviewer notes
        document.getElementById('reviewerNotes').value = '';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
        modal.show();
    } catch (error) {
        console.error('Failed to open review:', error);
        showToast('Failed to load feedback details', 'error');
    }
}

// Process reviewer decision
async function processDecision(approved) {
    if (!currentFeedback) return;

    try {
        const notes = document.getElementById('reviewerNotes').value;
        if (!notes.trim()) {
            showToast('Please provide review notes', 'warning');
            return;
        }

        const response = await fetch(`/api/admin/feedback/${currentFeedback.id}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                approved,
                notes
            })
        });

        if (!response.ok) throw new Error('Failed to process review');

        // Close modal and refresh data
        bootstrap.Modal.getInstance(document.getElementById('reviewModal')).hide();
        await Promise.all([
            loadReviewQueue(),
            loadStatistics(),
            loadReviewHistory()
        ]);

        showToast('Review processed successfully', 'success');
    } catch (error) {
        console.error('Failed to process review:', error);
        showToast('Failed to process review', 'error');
    }
}

// Helper functions
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleString();
}

function formatFeedbackType(type) {
    return type.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
}

function formatKey(key) {
    return key.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
}

function formatValue(value) {
    if (Array.isArray(value)) {
        return value.map((item, index) => 
            `${String.fromCharCode(65 + index)}) ${item}`
        ).join('<br>');
    }
    return value;
}

function getQuestionPreview(item) {
    return item.question?.question_text?.slice(0, 50) + '...' || 'Question not found';
}

// Status counts
async function getStatusCount(status) {
    try {
        const response = await fetch(`/api/admin/feedback/count?status=${status}`);
        if (!response.ok) throw new Error('Failed to get count');
        const data = await response.json();
        return data.count;
    } catch (error) {
        console.error(`Failed to get ${status} count:`, error);
        return 0;
    }
}

async function getPriorityCount(priority) {
    try {
        const response = await fetch(`/api/admin/feedback/count?priority=${priority}`);
        if (!response.ok) throw new Error('Failed to get count');
        const data = await response.json();
        return data.count;
    } catch (error) {
        console.error(`Failed to get ${priority} priority count:`, error);
        return 0;
    }
}

async function getProcessedTodayCount() {
    try {
        const response = await fetch('/api/admin/feedback/count?processed=today');
        if (!response.ok) throw new Error('Failed to get count');
        const data = await response.json();
        return data.count;
    } catch (error) {
        console.error('Failed to get processed today count:', error);
        return 0;
    }
}

// Filter queue
function filterQueue(filter) {
    currentFilter = filter;
    loadReviewQueue();

    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.textContent.toLowerCase().includes(filter)) {
            btn.classList.add('active');
        }
    });
}

// Refresh data
function refreshQueue() {
    const button = document.querySelector('button[onclick="refreshQueue()"]');
    button.classList.add('loading');
    
    Promise.all([
        loadReviewQueue(),
        loadStatistics(),
        loadReviewHistory()
    ]).finally(() => {
        button.classList.remove('loading');
    });
}

// Auto-refresh
function startAutoRefresh() {
    // Refresh every 5 minutes
    setInterval(refreshQueue, 300000);
}

// Toast notifications
function showToast(message, type = 'info') {
    // You can implement this using Bootstrap's toast component
    // or any other notification library
    alert(message);
}

// Approve/Reject shortcuts
function approveFeedback() {
    processDecision(true);
}

function rejectFeedback() {
    processDecision(false);
}

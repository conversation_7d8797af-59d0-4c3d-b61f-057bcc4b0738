/**
 * Monitoring Dashboard Module
 * Displays API and LLM usage statistics
 */

class MonitoringDashboard {
  constructor() {
    this.container = document.getElementById('llm-monitor-section');
    this.init();
  }

  /**
   * Initialize dashboard
   */
  async init() {
    this.createDateRangeControls();
    await this.loadStats();
    this.startAutoRefresh();
  }

  /**
   * Create date range controls
   */
  createDateRangeControls() {
    const controls = document.createElement('div');
    controls.className = 'mb-6 flex items-center space-x-4';
    controls.innerHTML = `
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium">From:</label>
        <input type="date" id="stats-from-date" class="border rounded-md p-1">
      </div>
      <div class="flex items-center space-x-2">
        <label class="text-sm font-medium">To:</label>
        <input type="date" id="stats-to-date" class="border rounded-md p-1">
      </div>
      <button id="refresh-stats" class="bg-indigo-500 text-white px-4 py-1 rounded-md hover:bg-indigo-600">
        Refresh
      </button>
    `;

    // Set default date range (last 7 days)
    const today = new Date();
    const weekAgo = new Date(today);
    weekAgo.setDate(weekAgo.getDate() - 7);

    controls.querySelector('#stats-from-date').value = this.formatDate(weekAgo);
    controls.querySelector('#stats-to-date').value = this.formatDate(today);

    // Add event listener
    controls.querySelector('#refresh-stats').onclick = () => this.loadStats();

    this.container.insertBefore(controls, this.container.firstChild);
  }

  /**
   * Load statistics
   */
  async loadStats() {
    const fromDate = new Date(document.getElementById('stats-from-date').value);
    const toDate = new Date(document.getElementById('stats-to-date').value);
    toDate.setHours(23, 59, 59); // Include full day

    try {
      const [apiStats, llmStats] = await Promise.all([
        this.fetchApiStats(fromDate, toDate),
        this.fetchLlmStats(fromDate, toDate)
      ]);

      this.renderStats(apiStats, llmStats);

    } catch (error) {
      console.error('Error loading stats:', error);
      this.showError('Failed to load statistics. Please try again.');
    }
  }

  /**
   * Fetch API statistics
   */
  async fetchApiStats(fromDate, toDate) {
    const response = await fetch('/api/admin/stats/api', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fromDate, toDate })
    });

    if (!response.ok) throw new Error('Failed to fetch API stats');
    return response.json();
  }

  /**
   * Fetch LLM statistics
   */
  async fetchLlmStats(fromDate, toDate) {
    const response = await fetch('/api/admin/stats/llm', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fromDate, toDate })
    });

    if (!response.ok) throw new Error('Failed to fetch LLM stats');
    return response.json();
  }

  /**
   * Render statistics
   */
  renderStats(apiStats, llmStats) {
    const statsContainer = document.createElement('div');
    statsContainer.className = 'grid grid-cols-1 md:grid-cols-2 gap-6';

    // API Stats Card
    const apiCard = document.createElement('div');
    apiCard.className = 'bg-white rounded-lg shadow-md p-6';
    apiCard.innerHTML = `
      <h3 class="text-lg font-semibold mb-4">API Usage Statistics</h3>
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-600">Total Calls</p>
            <p class="text-2xl font-semibold">${apiStats.totalCalls.toLocaleString()}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">Error Rate</p>
            <p class="text-2xl font-semibold ${apiStats.errorRate > 5 ? 'text-red-600' : ''}">
              ${apiStats.errorRate.toFixed(1)}%
            </p>
          </div>
        </div>
        
        <div>
          <p class="text-sm text-gray-600 mb-2">Top Endpoints</p>
          <div class="space-y-2">
            ${Object.entries(apiStats.pathCounts)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 5)
              .map(([path, count]) => `
                <div class="flex justify-between items-center">
                  <span class="text-sm font-mono">${path}</span>
                  <span class="text-sm">${count.toLocaleString()}</span>
                </div>
              `).join('')}
          </div>
        </div>
        
        ${apiStats.errors.length ? `
          <div>
            <p class="text-sm text-gray-600 mb-2">Recent Errors</p>
            <div class="space-y-2">
              ${apiStats.errors.slice(0, 3).map(error => `
                <div class="text-sm text-red-600">
                  ${error.method} ${error.path}: ${error.error}
                </div>
              `).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;

    // LLM Stats Card
    const llmCard = document.createElement('div');
    llmCard.className = 'bg-white rounded-lg shadow-md p-6';
    llmCard.innerHTML = `
      <h3 class="text-lg font-semibold mb-4">LLM Usage Statistics</h3>
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-600">Total Calls</p>
            <p class="text-2xl font-semibold">${llmStats.totalCalls.toLocaleString()}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600">Total Cost</p>
            <p class="text-2xl font-semibold">$${llmStats.totalCost.toFixed(2)}</p>
          </div>
        </div>
        
        <div>
          <p class="text-sm text-gray-600 mb-2">Usage by Type</p>
          <div class="space-y-2">
            ${Object.entries(llmStats.typeBreakdown)
              .sort(([,a], [,b]) => b - a)
              .map(([type, count]) => `
                <div class="flex justify-between items-center">
                  <span class="text-sm">${type}</span>
                  <span class="text-sm">${count.toLocaleString()}</span>
                </div>
              `).join('')}
          </div>
        </div>
        
        ${llmStats.errors.length ? `
          <div>
            <p class="text-sm text-gray-600 mb-2">Recent Errors</p>
            <div class="space-y-2">
              ${llmStats.errors.slice(0, 3).map(error => `
                <div class="text-sm text-red-600">
                  ${error.type}: ${error.error}
                </div>
              `).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    `;

    statsContainer.appendChild(apiCard);
    statsContainer.appendChild(llmCard);

    // Replace existing stats
    const existingStats = this.container.querySelector('.stats-container');
    if (existingStats) {
      existingStats.replaceWith(statsContainer);
    } else {
      this.container.appendChild(statsContainer);
    }
    statsContainer.classList.add('stats-container');
  }

  /**
   * Start auto-refresh
   */
  startAutoRefresh() {
    // Refresh every 5 minutes
    setInterval(() => this.loadStats(), 5 * 60 * 1000);
  }

  /**
   * Format date for input
   */
  formatDate(date) {
    return date.toISOString().split('T')[0];
  }

  /**
   * Show error message
   */
  showError(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
  }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.monitoringDashboard = new MonitoringDashboard();
});

// Global state
let yearChart = null;
let difficultyChart = null;
let addQuestionModal = null;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    initializeCharts();
    loadStatistics();
    setupModal();
});

// Initialize charts
function initializeCharts() {
    // Year chart
    const yearCtx = document.getElementById('yearChart').getContext('2d');
    yearChart = new Chart(yearCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Questions',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Difficulty chart
    const difficultyCtx = document.getElementById('difficultyChart').getContext('2d');
    difficultyChart = new Chart(difficultyCtx, {
        type: 'doughnut',
        data: {
            labels: ['Easy', 'Medium', 'Hard', 'Expert'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#fd7e14',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Load statistics
async function loadStatistics() {
    try {
        const response = await fetch('/api/admin/past-year-questions/statistics');
        if (!response.ok) throw new Error('Failed to load statistics');

        const stats = await response.json();

        // Update stat cards
        document.getElementById('totalQuestions').textContent = stats.total;
        document.getElementById('verifiedQuestions').textContent = stats.verifiedCount;
        document.getElementById('averageUsage').textContent = stats.averageUsage.toFixed(1);
        document.getElementById('yearsCovered').textContent = Object.keys(stats.byYear).length;

        // Update year chart
        const years = Object.keys(stats.byYear).sort();
        const questionCounts = years.map(year => stats.byYear[year]);
        yearChart.data.labels = years;
        yearChart.data.datasets[0].data = questionCounts;
        yearChart.update();

        // Update difficulty chart
        difficultyChart.data.datasets[0].data = [
            stats.byDifficulty.easy,
            stats.byDifficulty.medium,
            stats.byDifficulty.hard,
            stats.byDifficulty.expert
        ];
        difficultyChart.update();
    } catch (error) {
        console.error('Failed to load statistics:', error);
        showToast('Failed to load statistics', 'error');
    }
}

// Modal handling
function setupModal() {
    addQuestionModal = new bootstrap.Modal(document.getElementById('addQuestionModal'));
}

function openAddModal() {
    document.getElementById('questionForm').reset();
    addQuestionModal.show();
}

// Option handling
function addOption() {
    const optionList = document.querySelector('.option-list');
    const newOption = document.createElement('div');
    newOption.className = 'option-item slide-in';
    newOption.innerHTML = `
        <input type="text" class="form-control" name="options[]" required>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeOption(this)">
            <i class="bi bi-trash"></i>
        </button>
    `;
    optionList.appendChild(newOption);
}

function removeOption(button) {
    const optionItem = button.parentElement;
    optionItem.remove();
}

// Form submission
async function submitQuestion() {
    const form = document.getElementById('questionForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const formData = new FormData(form);
    const questionData = {
        question_text: formData.get('question_text'),
        options: Array.from(formData.getAll('options[]')),
        correct_answer: formData.get('correct_answer'),
        explanation: formData.get('explanation'),
        year: parseInt(formData.get('year')),
        exam_name: formData.get('exam_name'),
        source_url: formData.get('source_url'),
        topics: formData.get('topics').split(',').map(t => t.trim()).filter(t => t),
        difficulty: formData.get('difficulty'),
        verified: formData.get('verified') === 'on'
    };

    try {
        const response = await fetch('/api/admin/past-year-questions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(questionData)
        });

        if (!response.ok) throw new Error('Failed to add question');

        showToast('Question added successfully', 'success');
        addQuestionModal.hide();
        loadStatistics();
    } catch (error) {
        console.error('Failed to add question:', error);
        showToast('Failed to add question', 'error');
    }
}

// File handling
function handleDragOver(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.add('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    event.stopPropagation();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleFile(file);
    }
}

async function handleFile(file) {
    if (file.type !== 'text/csv') {
        showToast('Please upload a CSV file', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    const progress = document.getElementById('uploadProgress');
    const progressBar = progress.querySelector('.progress-bar');
    progress.style.display = 'block';
    progressBar.style.width = '0%';

    try {
        const response = await fetch('/api/admin/past-year-questions/bulk', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) throw new Error('Upload failed');

        const results = await response.json();
        showUploadResults(results);
        loadStatistics();
    } catch (error) {
        console.error('Upload failed:', error);
        showToast('Upload failed', 'error');
    } finally {
        progress.style.display = 'none';
    }
}

function showUploadResults(results) {
    const container = document.getElementById('uploadResults');
    container.innerHTML = `
        <div class="alert ${results.failed > 0 ? 'alert-warning' : 'alert-success'}">
            <h5>Upload Results</h5>
            <p>Successfully uploaded: ${results.success} questions</p>
            ${results.failed > 0 ? `<p>Failed to upload: ${results.failed} questions</p>` : ''}
        </div>
        ${results.errors.length > 0 ? `
            <div class="alert alert-danger">
                <h6>Errors:</h6>
                <ul>
                    ${results.errors.map(error => `
                        <li>${error.question}: ${error.error}</li>
                    `).join('')}
                </ul>
            </div>
        ` : ''}
    `;
}

function downloadTemplate() {
    const template = `question_text,options,correct_answer,explanation,year,exam_name,source_url,topics,difficulty,verified
"What is the capital of France?","Paris|London|Berlin|Madrid","Paris","Paris is the capital and largest city of France.",2023,"General Knowledge","https://example.com","Geography|Capitals","easy","true"
`;

    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'past_year_questions_template.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// Toast notifications
function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast ${type} show`;
    toast.innerHTML = `
        <div class="toast-header">
            <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
        <div class="toast-body">${message}</div>
    `;
    toastContainer.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

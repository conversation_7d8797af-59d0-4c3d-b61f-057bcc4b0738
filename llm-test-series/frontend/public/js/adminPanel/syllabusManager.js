// Syllabus Manager JavaScript

let currentSyllabus = null;
let syllabusMap = new Map();

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    checkAuth();
    loadSyllabi();
    initializeFormHandlers();
    initializeDragAndDrop();
});

// Load all syllabi
async function loadSyllabi() {
    try {
        const response = await fetch('/api/admin/syllabus');
        const syllabi = await response.json();
        
        const listElement = document.getElementById('syllabusList');
        listElement.innerHTML = '';
        syllabusMap.clear();

        syllabi.forEach(syllabus => {
            syllabusMap.set(syllabus.id, syllabus);
            const item = createSyllabusListItem(syllabus);
            listElement.appendChild(item);
        });
    } catch (error) {
        showToast('Error loading syllabi', 'error');
        console.error('Failed to load syllabi:', error);
    }
}

// Create syllabus list item
function createSyllabusListItem(syllabus) {
    const item = document.createElement('div');
    item.className = 'list-group-item d-flex justify-content-between align-items-center';
    item.innerHTML = `
        <div class="d-flex align-items-center" style="cursor: pointer" onclick="selectSyllabus('${syllabus.id}')">
            <div class="ms-2 me-auto">
                <div class="fw-bold">${syllabus.name}</div>
                <small class="text-muted">${syllabus.subject} - ${syllabus.level}</small>
            </div>
            <span class="badge bg-${getStatusBadgeClass(syllabus.status)} ms-2">${syllabus.status}</span>
        </div>
        <div class="ms-3">
            <button class="btn btn-sm btn-outline-success me-2" onclick="showPreComputationModal('${syllabus.id}')">
                <i class="bi bi-lightning-charge"></i> Generate Questions
            </button>
            <button class="btn btn-sm btn-outline-primary me-2" onclick="editSyllabus('${syllabus.id}')">Edit</button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteSyllabus('${syllabus.id}')">Delete</button>
        </div>
    `;
    return item;
}

function renderSyllabusList(syllabi) {
    const listContainer = document.getElementById('syllabusList');
    listContainer.innerHTML = '';
    syllabi.forEach(syllabus => {
        listContainer.appendChild(createSyllabusListItem(syllabus));
        syllabusMap.set(syllabus.id, syllabus);
    });
}

// Get badge class based on status
function getStatusBadgeClass(status) {
    switch (status) {
        case 'active': return 'success';
        case 'draft': return 'warning';
        case 'archived': return 'secondary';
        default: return 'primary';
    }
}

// Select a syllabus for editing
function selectSyllabus(id) {
    const syllabus = syllabusMap.get(id);
    if (!syllabus) return;

    currentSyllabus = syllabus;
    document.querySelectorAll('#syllabusList .list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`#syllabusList [data-id="${id}"]`)?.classList.add('active');

    loadSyllabusData(syllabus);
}

// Load syllabus data into form
function loadSyllabusData(syllabus) {
    document.getElementById('syllabusId').value = syllabus.id;
    document.getElementById('syllabusName').value = syllabus.name;
    document.getElementById('syllabusSubject').value = syllabus.subject;
    document.getElementById('syllabusLevel').value = syllabus.level;
    document.getElementById('syllabusDescription').value = syllabus.description || '';

    // Clear existing units
    document.getElementById('unitsList').innerHTML = '';

    // Load units
    syllabus.units.forEach(unit => {
        addUnit(unit);
    });

    // Load distribution settings
    loadDistributionSettings(syllabus);
}

// Load distribution settings
function loadDistributionSettings(syllabus) {
    const table = document.getElementById('distributionTable').getElementsByTagName('tbody')[0];
    table.innerHTML = '';

    syllabus.units.forEach(unit => {
        // Add unit row
        const unitRow = table.insertRow();
        unitRow.innerHTML = `
            <td><strong>${unit.name}</strong></td>
            <td>
                <input type="number" class="form-control form-control-sm unit-weight" 
                    value="${unit.weight || 0}" min="0" max="100" 
                    data-unit-id="${unit.id}" onchange="validateUnitWeights()">
            </td>
            <td>
                <div class="input-group input-group-sm">
                    <input type="number" class="form-control difficulty-dist" placeholder="Easy" 
                        value="${unit.difficulty_distribution?.easy || 25}" min="0" max="100">
                    <input type="number" class="form-control difficulty-dist" placeholder="Medium" 
                        value="${unit.difficulty_distribution?.medium || 40}" min="0" max="100">
                    <input type="number" class="form-control difficulty-dist" placeholder="Hard" 
                        value="${unit.difficulty_distribution?.hard || 25}" min="0" max="100">
                    <input type="number" class="form-control difficulty-dist" placeholder="Expert" 
                        value="${unit.difficulty_distribution?.expert || 10}" min="0" max="100">
                </div>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                    value="${unit.min_questions || 0}" min="0">
            </td>
        `;

        // Add topic rows
        unit.topics.forEach(topic => {
            const topicRow = table.insertRow();
            topicRow.innerHTML = `
                <td class="ps-4">${topic.name}</td>
                <td>
                    <input type="number" class="form-control form-control-sm topic-weight" 
                        value="${topic.weight || 0}" min="0" max="100" 
                        data-unit-id="${unit.id}" data-topic-id="${topic.id}" 
                        onchange="validateTopicWeights('${unit.id}')">
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control difficulty-dist" placeholder="Easy" 
                            value="${topic.difficulty_distribution?.easy || 25}" min="0" max="100">
                        <input type="number" class="form-control difficulty-dist" placeholder="Medium" 
                            value="${topic.difficulty_distribution?.medium || 40}" min="0" max="100">
                        <input type="number" class="form-control difficulty-dist" placeholder="Hard" 
                            value="${topic.difficulty_distribution?.hard || 25}" min="0" max="100">
                        <input type="number" class="form-control difficulty-dist" placeholder="Expert" 
                            value="${topic.difficulty_distribution?.expert || 10}" min="0" max="100">
                    </div>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" 
                        value="${topic.min_questions || 0}" min="0">
                </td>
            `;
        });
    });
}

// Validate unit weights sum to 100%
function validateUnitWeights() {
    const unitWeights = document.querySelectorAll('.unit-weight');
    let total = 0;
    unitWeights.forEach(input => {
        total += Number(input.value) || 0;
    });

    if (total !== 100) {
        unitWeights.forEach(input => {
            input.classList.add('is-invalid');
        });
        showToast('Unit weights must sum to 100%', 'error');
        return false;
    }

    unitWeights.forEach(input => {
        input.classList.remove('is-invalid');
    });
    return true;
}

// Validate topic weights within a unit sum to 100%
function validateTopicWeights(unitId) {
    const topicWeights = document.querySelectorAll(`.topic-weight[data-unit-id="${unitId}"]`);
    let total = 0;
    topicWeights.forEach(input => {
        total += Number(input.value) || 0;
    });

    if (total !== 100) {
        topicWeights.forEach(input => {
            input.classList.add('is-invalid');
        });
        showToast(`Topic weights for unit must sum to 100%`, 'error');
        return false;
    }

    topicWeights.forEach(input => {
        input.classList.remove('is-invalid');
    });
    return true;
}

// Validate difficulty distributions sum to 100%
function validateDifficultyDistributions() {
    const rows = document.getElementById('distributionTable').getElementsByTagName('tr');
    for (const row of rows) {
        const inputs = row.querySelectorAll('.difficulty-dist');
        if (inputs.length === 0) continue;

        let total = 0;
        inputs.forEach(input => {
            total += Number(input.value) || 0;
        });

        if (total !== 100) {
            inputs.forEach(input => {
                input.classList.add('is-invalid');
            });
            showToast('Difficulty distributions must sum to 100%', 'error');
            return false;
        }

        inputs.forEach(input => {
            input.classList.remove('is-invalid');
        });
    }
    return true;
}

// Get distribution settings from form
function getDistributionSettings() {
    const units = [];
    const rows = document.getElementById('distributionTable').getElementsByTagName('tr');
    let currentUnit = null;

    for (const row of rows) {
        const unitWeight = row.querySelector('.unit-weight');
        if (unitWeight) {
            // Unit row
            currentUnit = {
                id: unitWeight.dataset.unitId,
                weight: Number(unitWeight.value),
                difficulty_distribution: getDifficultyDistribution(row),
                min_questions: Number(row.querySelector('input:last-child').value),
                topics: []
            };
            units.push(currentUnit);
        } else {
            // Topic row
            const topicWeight = row.querySelector('.topic-weight');
            if (topicWeight && currentUnit) {
                currentUnit.topics.push({
                    id: topicWeight.dataset.topicId,
                    weight: Number(topicWeight.value),
                    difficulty_distribution: getDifficultyDistribution(row),
                    min_questions: Number(row.querySelector('input:last-child').value)
                });
            }
        }
    }

    return units;
}

// Get difficulty distribution from a row
function getDifficultyDistribution(row) {
    const inputs = row.querySelectorAll('.difficulty-dist');
    if (inputs.length !== 4) return null;

    return {
        easy: Number(inputs[0].value),
        medium: Number(inputs[1].value),
        hard: Number(inputs[2].value),
        expert: Number(inputs[3].value)
    };
}

// Create unit element
function createUnitElement(unit, index) {
    const unitElement = document.createElement('div');
    unitElement.className = 'unit-card';
    unitElement.setAttribute('data-unit-id', unit.id);
    unitElement.innerHTML = `
        <div class="unit-header">
            <div class="d-flex align-items-center">
                <i class="bi bi-grip-vertical drag-handle"></i>
                <h6 class="mb-0">Unit ${index + 1}: ${unit.name}</h6>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="editUnit('${unit.id}')">
                    <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteUnit('${unit.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
        <div class="unit-content">
            <p class="text-muted small mb-2">${unit.description}</p>
            <div class="topic-list">
                ${unit.topics?.map(topic => createTopicElement(topic)).join('') || ''}
            </div>
            <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addTopic('${unit.id}')">
                <i class="bi bi-plus"></i> Add Topic
            </button>
        </div>
    `;
    return unitElement;
}

// Create topic element
function createTopicElement(topic) {
    return `
        <div class="topic-item" data-topic-id="${topic.id}">
            <div class="topic-header">
                <div class="d-flex align-items-center">
                    <i class="bi bi-grip-vertical drag-handle"></i>
                    <span>${topic.name}</span>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editTopic('${topic.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteTopic('${topic.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            ${topic.subtopics?.length ? `
                <div class="subtopic-list">
                    ${topic.subtopics.map(subtopic => createSubtopicElement(subtopic)).join('')}
                </div>
            ` : ''}
            <button type="button" class="btn btn-outline-secondary btn-sm mt-2" onclick="addSubtopic('${topic.id}')">
                <i class="bi bi-plus"></i> Add Subtopic
            </button>
        </div>
    `;
}

// Create subtopic element
function createSubtopicElement(subtopic) {
    return `
        <div class="subtopic-item" data-subtopic-id="${subtopic.id}">
            <div class="d-flex justify-content-between align-items-center">
                <span>${subtopic.name}</span>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="editSubtopic('${subtopic.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteSubtopic('${subtopic.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Initialize form handlers
function initializeFormHandlers() {
    document.getElementById('syllabusForm').onsubmit = async (e) => {
        e.preventDefault();
        await saveSyllabus();
    };
}

// Save syllabus
async function saveSyllabus(event) {
    event.preventDefault();

    // Validate distribution settings
    if (!validateUnitWeights() || !validateDifficultyDistributions()) {
        return;
    }

    const data = {
        name: document.getElementById('syllabusName').value,
        subject: document.getElementById('syllabusSubject').value,
        level: document.getElementById('syllabusLevel').value,
        description: document.getElementById('syllabusDescription').value,
        units: getUnits()
    };

    // Add distribution settings
    const distributionSettings = getDistributionSettings();
    data.units = data.units.map(unit => {
        const distribution = distributionSettings.find(d => d.id === unit.id);
        if (distribution) {
            unit.weight = distribution.weight;
            unit.difficulty_distribution = distribution.difficulty_distribution;
            unit.min_questions = distribution.min_questions;
            unit.topics = unit.topics.map(topic => {
                const topicDist = distribution.topics.find(t => t.id === topic.id);
                if (topicDist) {
                    topic.weight = topicDist.weight;
                    topic.difficulty_distribution = topicDist.difficulty_distribution;
                    topic.min_questions = topicDist.min_questions;
                }
                return topic;
            });
        }
        return unit;
    });

    try {
        const response = await fetch(`/api/admin/syllabus/${currentSyllabus.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) throw new Error('Failed to update syllabus');

        showToast('Syllabus updated successfully', 'success');
        await loadSyllabi();
    } catch (error) {
        showToast('Error updating syllabus', 'error');
        console.error('Failed to update syllabus:', error);
    }
}

// Show create syllabus modal
function showCreateSyllabusModal() {
    const modal = new bootstrap.Modal(document.getElementById('createSyllabusModal'));
    modal.show();
}

function showBulkUploadModal() {
    const modal = new bootstrap.Modal(document.getElementById('bulkUploadModal'));
    resetBulkUploadForm();
    modal.show();
    
    // Initialize drag and drop
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('syllabusFile');

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            // Trigger validation immediately
            validateAndUpload();
        }
    });

    // File input change handler
    fileInput.addEventListener('change', () => {
        validateAndUpload();
    });
    
    // Add template download button handler
    document.getElementById('downloadTemplate').onclick = async () => {
        try {
            const response = await fetch('/api/admin/syllabus/template');
            if (!response.ok) throw new Error('Failed to download template');
            
            // Create blob and download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'syllabus_template.xlsx';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showToast('Template downloaded successfully', 'success');
        } catch (error) {
            showToast('Failed to download template: ' + error.message, 'error');
        }
    };
}

// Reset bulk upload form
function resetBulkUploadForm() {
    document.getElementById('bulkUploadForm').reset();
    document.getElementById('bulkUploadValidation').classList.add('d-none');
    document.getElementById('bulkUploadProgress').classList.add('d-none');
    document.querySelector('#bulkUploadProgress .progress-bar').style.width = '0%';
    document.querySelector('#bulkUploadProgress .progress-bar').classList.remove('bg-danger');
}

// Validate and upload file
async function validateAndUpload() {
    const fileInput = document.getElementById('syllabusFile');
    const file = fileInput.files[0];
    
    if (!file) {
        showToast('Please select a file', 'error');
        return;
    }

    try {
        const syllabi = await readUploadedFile(file);
        const validationResults = validateSyllabi(syllabi);
        displayValidationResults(validationResults);

        if (validationResults.valid) {
            await processBulkUpload(syllabi);
        }
    } catch (error) {
        showToast('Error processing file: ' + error.message, 'error');
        console.error('Bulk upload error:', error);
    }
}

// Read uploaded file
async function readUploadedFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async (e) => {
            try {
                if (file.type === 'application/json') {
                    const content = JSON.parse(e.target.result);
                    resolve(Array.isArray(content) ? content : [content]);
                } else if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    const response = await fetch('/api/admin/syllabus/bulk/parse', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!response.ok) {
                        throw new Error('Failed to parse Excel file');
                    }
                    
                    const data = await response.json();
                    resolve(data);
                } else {
                    reject(new Error('Unsupported file type. Please use JSON or Excel files.'));
                }
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('Error reading file'));
        
        if (file.type === 'application/json') {
            reader.readAsText(file);
        } else {
            reader.readAsArrayBuffer(file);
        }
    });
}

// Validate syllabi
function validateSyllabi(syllabi) {
    const results = {
        valid: true,
        errors: [],
        warnings: [],
        syllabi: []
    };

    if (!Array.isArray(syllabi)) {
        results.valid = false;
        results.errors.push('Invalid file format: expected array of syllabi');
        return results;
    }

    syllabi.forEach((syllabus, index) => {
        const syllabusResults = validateSyllabus(syllabus, index);
        if (syllabusResults.errors.length > 0) {
            results.valid = false;
        }
        results.errors.push(...syllabusResults.errors);
        results.warnings.push(...syllabusResults.warnings);
        results.syllabi.push(syllabusResults);
    });

    return results;
}

function validateSyllabus(syllabus, index) {
    const results = {
        name: syllabus.name || `Syllabus ${index + 1}`,
        errors: [],
        warnings: []
    };

    // Required fields
    if (!syllabus.name?.trim()) {
        results.errors.push('Missing name');
    }
    if (!syllabus.subject?.trim()) {
        results.errors.push('Missing subject');
    }
    if (!syllabus.level || !['high_school', 'undergraduate', 'graduate'].includes(syllabus.level)) {
        results.errors.push('Invalid level');
    }

    // Units validation
    if (!Array.isArray(syllabus.units) || syllabus.units.length === 0) {
        results.errors.push('Missing or empty units array');
        return results;
    }

    let totalWeight = 0;
    syllabus.units.forEach((unit, unitIndex) => {
        if (!unit.name?.trim()) {
            results.errors.push(`Unit ${unitIndex + 1}: Missing name`);
        }
        if (typeof unit.weight !== 'number' || unit.weight <= 0) {
            results.errors.push(`Unit ${unitIndex + 1}: Invalid weight`);
        } else {
            totalWeight += unit.weight;
        }

        // Topics validation
        if (!Array.isArray(unit.topics) || unit.topics.length === 0) {
            results.errors.push(`Unit ${unitIndex + 1}: Missing or empty topics array`);
            return;
        }

        let unitTopicWeight = 0;
        unit.topics.forEach((topic, topicIndex) => {
            if (!topic.name?.trim()) {
                results.errors.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Missing name`);
            }
            if (typeof topic.weight !== 'number' || topic.weight <= 0) {
                results.errors.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Invalid weight`);
            } else {
                unitTopicWeight += topic.weight;
            }

            // Learning objectives validation
            if (!Array.isArray(topic.learning_objectives) || topic.learning_objectives.length === 0) {
                results.warnings.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: No learning objectives`);
            }

            // Keywords validation
            if (!Array.isArray(topic.keywords) || topic.keywords.length === 0) {
                results.warnings.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: No keywords`);
            }
        });

        if (Math.abs(unitTopicWeight - 100) > 0.1) {
            results.errors.push(`Unit ${unitIndex + 1}: Topic weights sum to ${unitTopicWeight}%, expected 100%`);
        }
    });

    if (Math.abs(totalWeight - 100) > 0.1) {
        results.errors.push(`Unit weights sum to ${totalWeight}%, expected 100%`);
    }

    return results;
}

// Display validation results
function displayValidationResults(results) {
    const validationDiv = document.getElementById('bulkUploadValidation');
    const resultsDiv = document.getElementById('validationResults');
    validationDiv.classList.remove('d-none');

    let html = '<div class="validation-results">';
    if (results.valid) {
        validationDiv.classList.remove('alert-danger', 'alert-info');
        validationDiv.classList.add('alert-success');
        html += `
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-check-circle-fill text-success me-2"></i>
                <h6 class="mb-0">Validation Passed</h6>
            </div>
            <p class="text-muted">All syllabi are valid and ready to upload.</p>
        `;
    } else {
        validationDiv.classList.remove('alert-success', 'alert-info');
        validationDiv.classList.add('alert-danger');
        html += `
            <div class="d-flex align-items-center mb-3">
                <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                <h6 class="mb-0">Validation Failed</h6>
            </div>
        `;
    }

    // Group errors by syllabus
    const syllabusErrors = new Map();
    results.syllabi?.forEach(syllabus => {
        if (syllabus.errors.length > 0 || syllabus.warnings.length > 0) {
            syllabusErrors.set(syllabus.name, {
                errors: syllabus.errors,
                warnings: syllabus.warnings
            });
        }
    });

    if (syllabusErrors.size > 0) {
        html += '<div class="accordion" id="validationAccordion">';
        let index = 0;
        syllabusErrors.forEach((validation, syllabusName) => {
            const hasErrors = validation.errors.length > 0;
            const hasWarnings = validation.warnings.length > 0;
            html += `
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button ${hasErrors ? 'text-danger' : 'text-warning'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                            ${syllabusName} (${hasErrors ? validation.errors.length + ' errors' : ''} ${hasWarnings ? (hasErrors ? ', ' : '') + validation.warnings.length + ' warnings' : ''})
                        </button>
                    </h2>
                    <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#validationAccordion">
                        <div class="accordion-body">
                            ${hasErrors ? `
                                <div class="mb-3">
                                    <strong class="text-danger">Errors:</strong>
                                    <ul class="list-unstyled mb-0 mt-2">
                                        ${validation.errors.map(error => `<li class="error-item"><i class="bi bi-x-circle me-2"></i>${error}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            ${hasWarnings ? `
                                <div>
                                    <strong class="text-warning">Warnings:</strong>
                                    <ul class="list-unstyled mb-0 mt-2">
                                        ${validation.warnings.map(warning => `<li class="warning-item"><i class="bi bi-exclamation-circle me-2"></i>${warning}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
            index++;
        });
        html += '</div>';
    }

    html += '</div>';
    resultsDiv.innerHTML = html;

    // Initialize all tooltips
    const tooltips = resultsDiv.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
}

// Pre-computation handling
let currentPreComputationJob = null;
let preComputationStatusInterval = null;

async function showPreComputationModal(syllabusId) {
    const modal = new bootstrap.Modal(document.getElementById('preComputationModal'));
    modal.show();

    // Reset UI
    document.getElementById('preComputationStatus').classList.add('d-none');
    document.getElementById('preComputationError').classList.add('d-none');
    document.getElementById('startPreComputationBtn').disabled = false;

    // Add event listener
    document.getElementById('startPreComputationBtn').onclick = () => startPreComputation(syllabusId);
}

async function startPreComputation(syllabusId) {
    try {
        document.getElementById('startPreComputationBtn').disabled = true;
        document.getElementById('preComputationStatus').classList.remove('d-none');
        document.getElementById('preComputationError').classList.add('d-none');

        const response = await fetch(`/api/admin/precomputation/start/${syllabusId}`, {
            method: 'POST'
        });

        if (!response.ok) {
            throw new Error('Failed to start pre-computation');
        }

        const { jobId } = await response.json();
        currentPreComputationJob = jobId;

        // Start polling for status
        preComputationStatusInterval = setInterval(() => checkPreComputationStatus(jobId), 5000);
    } catch (error) {
        showPreComputationError(error.message);
    }
}

async function checkPreComputationStatus(jobId) {
    try {
        const response = await fetch(`/api/admin/precomputation/status/${jobId}`);
        if (!response.ok) {
            throw new Error('Failed to get job status');
        }

        const status = await response.json();
        updatePreComputationUI(status);

        // Stop polling if job is completed or failed
        if (status.status === 'completed' || status.status === 'failed') {
            clearInterval(preComputationStatusInterval);
            currentPreComputationJob = null;
        }
    } catch (error) {
        showPreComputationError(error.message);
        clearInterval(preComputationStatusInterval);
    }
}

function updatePreComputationUI(status) {
    const progressBar = document.querySelector('#preComputationStatus .progress-bar');
    const progressText = document.getElementById('preComputationProgress');
    const statsText = document.getElementById('preComputationStats');
    const detailsDiv = document.getElementById('preComputationDetails');

    // Update progress
    progressBar.style.width = `${status.progress}%`;
    progressText.textContent = `${Math.round(status.progress)}%`;

    // Update stats
    statsText.textContent = `${status.completedTopics}/${status.totalTopics} topics`;

    // Update details
    let details = `Generated ${status.questionsGenerated} questions so far`;
    if (status.errors && status.errors.length > 0) {
        details += `<br><span class="text-warning">${status.errors.length} errors encountered</span>`;
    }
    detailsDiv.innerHTML = details;

    // Show completion message
    if (status.status === 'completed') {
        showToast(status.completionMessage, 'success');
        document.getElementById('startPreComputationBtn').disabled = false;
    }
}

function showPreComputationError(message) {
    const errorDiv = document.getElementById('preComputationError');
    const errorMessage = document.getElementById('preComputationErrorMessage');
    errorDiv.classList.remove('d-none');
    errorMessage.textContent = message;
    document.getElementById('startPreComputationBtn').disabled = false;
}

// Process bulk upload
async function processBulkUpload(syllabi) {
    const progressBar = document.querySelector('#bulkUploadProgress .progress-bar');
    const progressDiv = document.getElementById('bulkUploadProgress');
    const progressText = document.getElementById('progressText');
    progressDiv.classList.remove('d-none');
    progressBar.classList.remove('bg-danger');
    progressBar.style.width = '0%';

    const results = {
        total: syllabi.length,
        successful: 0,
        failed: 0,
        details: []
    };

    try {
        for (let i = 0; i < syllabi.length; i++) {
            const syllabus = syllabi[i];
            const progress = ((i + 1) / syllabi.length) * 100;
            progressBar.style.width = `${progress}%`;
            progressText.textContent = `Processing ${i + 1} of ${syllabi.length}: ${syllabus.name}`;

            try {
                const response = await fetch('/api/admin/syllabus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(syllabus)
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || `Failed to upload syllabus '${syllabus.name}'`);
                }

                results.successful++;
                results.details.push({
                    name: syllabus.name,
                    status: 'success'
                });
            } catch (error) {
                results.failed++;
                results.details.push({
                    name: syllabus.name,
                    status: 'error',
                    error: error.message
                });
                showToast(`Error uploading syllabus '${syllabus.name}': ${error.message}`, 'error');
            }
        }

        // Show final results
        if (results.failed === 0) {
            showToast(`Successfully uploaded all ${results.total} syllabi`, 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkUploadModal'));
            modal.hide();
            await loadSyllabi(); // Refresh the list
        } else {
            progressBar.classList.add('bg-warning');
            progressText.innerHTML = `
                <strong>Upload Complete:</strong> 
                <span class="text-success">${results.successful} successful</span>, 
                <span class="text-danger">${results.failed} failed</span>
            `;
            
            // Show detailed results in validation area
            const validationDiv = document.getElementById('bulkUploadValidation');
            const resultsDiv = document.getElementById('validationResults');
            validationDiv.classList.remove('d-none', 'alert-info', 'alert-success');
            validationDiv.classList.add('alert-warning');

            let html = '<div class="validation-results">';
            html += '<h6>Upload Results</h6>';
            html += '<div class="list-group">';
            results.details.forEach(detail => {
                html += `
                    <div class="list-group-item list-group-item-${detail.status === 'success' ? 'success' : 'danger'} d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-${detail.status === 'success' ? 'check-circle' : 'x-circle'} me-2"></i>
                            ${detail.name}
                        </div>
                        ${detail.error ? `<small class="text-danger">${detail.error}</small>` : ''}
                    </div>
                `;
            });
            html += '</div></div>';
            resultsDiv.innerHTML = html;
        }
    } catch (error) {
        console.error('Bulk upload failed:', error);
        progressBar.classList.add('bg-danger');
        progressText.innerHTML = `<span class="text-danger">Upload failed: ${error.message}</span>`;
    }
}

// Create new syllabus
async function createSyllabus() {
    const data = {
        name: document.getElementById('newSyllabusName').value,
        subject: document.getElementById('newSyllabusSubject').value,
        level: document.getElementById('newSyllabusLevel').value,
        units: []
    };

    try {
        const response = await fetch('/api/admin/syllabus', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) throw new Error('Failed to create syllabus');

        const modal = bootstrap.Modal.getInstance(document.getElementById('createSyllabusModal'));
        modal.hide();
        
        showToast('Syllabus created successfully', 'success');
        await loadSyllabi();
    } catch (error) {
        showToast('Error creating syllabus', 'error');
        console.error('Failed to create syllabus:', error);
    }
}

// Initialize drag and drop functionality
function initializeDragAndDrop() {
    const containers = document.querySelectorAll('.topic-list, .subtopic-list');
    containers.forEach(container => {
        new Sortable(container, {
            handle: '.drag-handle',
            animation: 150,
            onEnd: async (evt) => {
                const itemId = evt.item.getAttribute('data-topic-id') || evt.item.getAttribute('data-subtopic-id');
                const newIndex = evt.newIndex;
                await updateItemOrder(itemId, newIndex);
            }
        });
    });
}

// Update item order after drag and drop
async function updateItemOrder(itemId, newIndex) {
    if (!currentSyllabus) return;

    try {
        const response = await fetch(`/api/admin/syllabus/${currentSyllabus.id}/reorder`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ itemId, newIndex })
        });

        if (!response.ok) throw new Error('Failed to update order');
    } catch (error) {
        showToast('Error updating order', 'error');
        console.error('Failed to update order:', error);
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast bg-${type === 'error' ? 'danger' : type} text-white`;
    toast.innerHTML = `
        <div class="toast-body">
            ${message}
        </div>
    `;
    toastContainer.appendChild(toast);
    new bootstrap.Toast(toast).show();
}

// Create toast container if it doesn't exist
function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

// Utility functions for unit/topic/subtopic management
function addUnit() {
    // Implementation for adding a unit
}

function editUnit(unitId) {
    // Implementation for editing a unit
}

function deleteUnit(unitId) {
    // Implementation for deleting a unit
}

function addTopic(unitId) {
    // Implementation for adding a topic
}

function editTopic(topicId) {
    // Implementation for editing a topic
}

function deleteTopic(topicId) {
    // Implementation for deleting a topic
}

function addSubtopic(topicId) {
    // Implementation for adding a subtopic
}

function editSubtopic(subtopicId) {
    // Implementation for editing a subtopic
}

function deleteSubtopic(subtopicId) {
    // Implementation for deleting a subtopic
}

// Logout function
function logout() {
    localStorage.removeItem('adminToken');
    window.location.href = '/admin/login.html';
}

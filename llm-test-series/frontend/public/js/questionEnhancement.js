/**
 * Question Enhancement Module
 * Handles theory explanations and answer challenges
 */

// Cache for theory explanations to reduce LLM calls
const theoryExplanationCache = new Map();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Set up event listeners for theory explanation and challenge buttons
  document.addEventListener('testResultsDisplayed', attachEnhancementButtons);
});

/**
 * Attach enhancement buttons to questions in test results
 */
function attachEnhancementButtons() {
  const questionContainers = document.querySelectorAll('.question-container');
  
  questionContainers.forEach(container => {
    const questionId = container.getAttribute('data-question-id');
    const questionText = container.querySelector('.question-text').textContent;
    const options = Array.from(container.querySelectorAll('.option')).map(opt => opt.textContent);
    const correctAnswer = container.querySelector('.correct-answer').textContent;
    const explanation = container.querySelector('.explanation').textContent;
    
    // Add Theory Explanation button
    const theoryBtn = document.createElement('button');
    theoryBtn.className = 'theory-btn bg-blue-500 text-white px-3 py-1 rounded-md text-sm mr-2 hover:bg-blue-600 transition-colors';
    theoryBtn.textContent = 'Understand Theory';
    theoryBtn.onclick = () => handleTheoryExplanation(questionId, questionText);
    
    // Add Challenge Answer button
    const challengeBtn = document.createElement('button');
    challengeBtn.className = 'challenge-btn bg-orange-500 text-white px-3 py-1 rounded-md text-sm hover:bg-orange-600 transition-colors';
    challengeBtn.textContent = 'Challenge Answer';
    challengeBtn.onclick = () => handleChallengeAnswer(questionId, questionText, options, correctAnswer, explanation);
    
    // Add buttons container
    const btnContainer = document.createElement('div');
    btnContainer.className = 'enhancement-buttons mt-3 flex items-center';
    btnContainer.appendChild(theoryBtn);
    btnContainer.appendChild(challengeBtn);
    
    container.appendChild(btnContainer);
  });
}

/**
 * Handle theory explanation request
 * @param {string} questionId - Question ID
 * @param {string} questionText - Question text
 */
async function handleTheoryExplanation(questionId, questionText) {
  // Check cache first
  if (theoryExplanationCache.has(questionId)) {
    showTheoryModal(theoryExplanationCache.get(questionId));
    return;
  }
  
  // Show loading state
  const loadingModal = showLoadingModal('Generating theory explanation...');
  
  try {
    // Call backend API
    const response = await fetch('/api/learning/theory', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ questionId, questionText })
    });
    
    if (!response.ok) throw new Error('Failed to fetch theory explanation');
    
    const data = await response.json();
    
    // Cache the explanation
    theoryExplanationCache.set(questionId, data.explanation);
    
    // Hide loading and show theory
    loadingModal.remove();
    showTheoryModal(data.explanation);
    
  } catch (error) {
    console.error('Error fetching theory explanation:', error);
    loadingModal.remove();
    showErrorModal('Failed to generate theory explanation. Please try again.');
  }
}

/**
 * Handle answer challenge submission
 * @param {string} questionId - Question ID
 * @param {string} questionText - Question text
 * @param {Array} options - Answer options
 * @param {string} correctAnswer - Correct answer
 * @param {string} explanation - Original explanation
 */
async function handleChallengeAnswer(questionId, questionText, options, correctAnswer, explanation) {
  // Show challenge input modal
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4';
  modal.innerHTML = `
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
      <h3 class="text-lg font-semibold mb-4">Challenge Answer</h3>
      <div class="mb-4">
        <p class="font-medium">Question:</p>
        <p class="mb-2">${questionText}</p>
        <p class="font-medium">Correct Answer:</p>
        <p class="mb-2">${correctAnswer}</p>
        <p class="font-medium">Original Explanation:</p>
        <p class="mb-4">${explanation}</p>
      </div>
      <div class="mb-4">
        <label for="challenge-text" class="block font-medium mb-2">Your Challenge:</label>
        <textarea id="challenge-text" rows="4" 
          class="w-full border rounded-md p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
          placeholder="Explain why you think this answer or explanation is incorrect or incomplete..."></textarea>
      </div>
      <div class="flex justify-end space-x-3">
        <button class="cancel-btn px-4 py-2 border rounded-md hover:bg-gray-50">Cancel</button>
        <button class="submit-btn px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
          Submit Challenge
        </button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // Handle modal interactions
  const cancelBtn = modal.querySelector('.cancel-btn');
  const submitBtn = modal.querySelector('.submit-btn');
  const challengeText = modal.querySelector('#challenge-text');
  
  cancelBtn.onclick = () => modal.remove();
  
  submitBtn.onclick = async () => {
    const challenge = challengeText.value.trim();
    if (!challenge) {
      alert('Please enter your challenge explanation');
      return;
    }
    
    // Replace modal content with loading state
    modal.querySelector('.bg-white').innerHTML = `
      <div class="text-center p-6">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p class="mt-4 text-gray-600">Evaluating your challenge...</p>
      </div>
    `;
    
    try {
      // Submit challenge to backend
      const response = await fetch('/api/questions/challenge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          questionId,
          questionText,
          options,
          correctAnswer,
          originalExplanation: explanation,
          challengeText: challenge
        })
      });
      
      if (!response.ok) throw new Error('Failed to evaluate challenge');
      
      const data = await response.json();
      
      // Show evaluation result
      modal.querySelector('.bg-white').innerHTML = `
        <div class="p-6">
          <h3 class="text-lg font-semibold mb-4">Challenge Evaluation</h3>
          <div class="mb-4">
            <p class="font-medium">Verdict:</p>
            <p class="mb-2 ${data.verdict === 'valid' ? 'text-green-600' : 
              data.verdict === 'partially-valid' ? 'text-orange-600' : 'text-red-600'}">
              ${data.verdict.charAt(0).toUpperCase() + data.verdict.slice(1)}
            </p>
          </div>
          <div class="mb-4">
            <p class="font-medium">Reasoning:</p>
            <p class="mb-4">${data.reasoning}</p>
          </div>
          ${data.suggestedCorrection ? `
            <div class="mb-4">
              <p class="font-medium">Suggested Correction:</p>
              <p>${data.suggestedCorrection.explanation || ''}</p>
            </div>
          ` : ''}
          <div class="flex justify-end">
            <button class="close-btn px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
              Close
            </button>
          </div>
        </div>
      `;
      
      modal.querySelector('.close-btn').onclick = () => modal.remove();
      
    } catch (error) {
      console.error('Error evaluating challenge:', error);
      showErrorModal('Failed to evaluate challenge. Please try again.');
      modal.remove();
    }
  };
}

/**
 * Show theory explanation in a modal
 * @param {string} theory - Theory explanation text
 */
function showTheoryModal(theory) {
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4';
  modal.innerHTML = `
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full">
      <h3 class="text-lg font-semibold mb-4">Theory Explanation</h3>
      <div class="theory-content prose max-w-none mb-6">
        ${theory}
      </div>
      <div class="flex justify-end">
        <button class="close-btn px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
          Close
        </button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  modal.querySelector('.close-btn').onclick = () => modal.remove();
}

/**
 * Show loading modal
 * @param {string} message - Loading message to display
 * @returns {HTMLElement} The modal element
 */
function showLoadingModal(message) {
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4';
  modal.innerHTML = `
    <div class="bg-white rounded-lg shadow-xl p-6">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        <p class="ml-3">${message}</p>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  return modal;
}

/**
 * Show error modal
 * @param {string} message - Error message to display
 */
function showErrorModal(message) {
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4';
  modal.innerHTML = `
    <div class="bg-white rounded-lg shadow-xl p-6">
      <div class="flex items-center text-red-600">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="ml-3">${message}</p>
      </div>
      <div class="mt-4 flex justify-end">
        <button class="close-btn px-4 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200">
          Close
        </button>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  modal.querySelector('.close-btn').onclick = () => modal.remove();
}

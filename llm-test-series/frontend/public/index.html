<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced AI-powered mock test system with real-time collaboration">
    <title>AI Mock Test System</title>

    <!-- Security meta tags -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://www.gstatic.com https://cdn.socket.io; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com; connect-src 'self' http://localhost:5000 ws://localhost:5000">
    <meta name="referrer" content="same-origin">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">

    <!-- PWA support -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#007bff">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <style>
        body { font-family: 'Inter', sans-serif; }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .question-card { transition: all 0.3s ease; }
        .question-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .tab-btn.active { border-bottom: 2px solid #4f46e5; color: #4f46e5; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .exam-card { transition: all 0.3s ease; cursor: pointer; }
        .exam-card:hover { transform: translateY(-4px); box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
        .subject-btn { transition: all 0.3s ease; }
        .subject-btn:hover { transform: scale(1.05); }
        .nav-link { transition: all 0.3s ease; }
        .nav-link:hover { color: #4f46e5; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container">
        <header class="header bg-white shadow-sm">
            <nav class="nav">
                <div class="nav-brand">
                    <h1 class="text-2xl font-bold text-indigo-600">🤖 AI-Powered Mock Test System</h1>
                </div>

                <div class="nav-links hidden-mobile">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/tests" class="nav-link">Tests</a>
                    <a href="/groups" class="nav-link">Study Groups</a>
                    <a href="/profile" class="nav-link">Profile</a>
                </div>

                <div class="nav-controls">
                    <div id="user-info" class="text-sm text-gray-600 mr-4">
                        <span id="user-status">Not logged in</span>
                    </div>
                    <button id="login-btn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors mr-4">
                        Login as Anonymous
                    </button>
                    <div class="theme-switch" role="button" aria-label="Toggle dark mode">
                        <div class="theme-switch__toggle"></div>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Tab Navigation -->
        <div class="tab-navigation bg-white border-b">
            <button id="tab-exam-explorer" class="tab-btn px-4 py-2 font-medium text-sm text-indigo-600 border-b-2 border-indigo-600 active">Exam Explorer</button>
            <button id="tab-mock-test" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Mock Test</button>
            <button id="tab-analytics" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Analytics</button>
            <button id="tab-syllabus" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Syllabus</button>
            <button id="tab-study-groups" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Study Groups</button>
        </div>

        <!-- Tab content containers -->
        <div id="exam-explorer-container" class="tab-content active">
            <!-- Exam Explorer content will be loaded here -->
        </div>

        <div id="mock-test-container" class="tab-content">
            <!-- Mock Test content will be loaded here -->
        </div>

        <div id="analytics-container" class="tab-content">
            <!-- Analytics content will be loaded here -->
        </div>

        <div id="syllabus-container" class="tab-content">
            <!-- Syllabus content will be loaded here -->
        </div>

        <div id="study-groups-container" class="tab-content">
            <!-- Study Groups content will be loaded here -->
        </div>

        <!-- Status Messages -->
        <div id="status-messages" class="fixed bottom-4 right-4 space-y-2 z-50">
            <!-- Status messages will appear here -->
        </div>
    </main>

    <!-- Modal container -->
    <div id="modal-container" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div id="modal-content" class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <!-- Modal content will be dynamically inserted here -->
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Scripts -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        // Comprehensive AI Mock Test System Application
        const API_BASE = 'http://localhost:5000/api';
        let authToken = null;
        let currentUser = null;
        let socket = null;

        // DOM elements
        const loginBtn = document.getElementById('login-btn');
        const userStatus = document.getElementById('user-status');
        const statusMessages = document.getElementById('status-messages');

        // Tab elements
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        // Utility functions
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            const bgColor = type === 'error' ? 'bg-red-500' : type === 'success' ? 'bg-green-500' : 'bg-blue-500';
            messageDiv.className = `${bgColor} text-white px-4 py-2 rounded-md mb-2 shadow-lg`;
            messageDiv.textContent = message;
            statusMessages.appendChild(messageDiv);
            setTimeout(() => messageDiv.remove(), 5000);
        }

        function showLoader(container) {
            container.innerHTML = '<div class="flex justify-center p-8"><div class="loader"></div></div>';
        }

        // Tab Management
        function switchTab(tabId) {
            // Update tab buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'text-indigo-600', 'border-b-2', 'border-indigo-600');
                btn.classList.add('text-gray-500');
            });

            // Update tab contents
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Activate selected tab
            const activeBtn = document.getElementById(`tab-${tabId}`);
            const activeContent = document.getElementById(`${tabId}-container`);

            if (activeBtn && activeContent) {
                activeBtn.classList.add('active', 'text-indigo-600', 'border-b-2', 'border-indigo-600');
                activeBtn.classList.remove('text-gray-500');
                activeContent.classList.add('active');

                // Load content for the tab
                loadTabContent(tabId);
            }
        }

        // Content Loading Functions
        function loadTabContent(tabId) {
            const container = document.getElementById(`${tabId}-container`);

            switch(tabId) {
                case 'exam-explorer':
                    loadExamExplorer(container);
                    break;
                case 'mock-test':
                    loadMockTest(container);
                    break;
                case 'analytics':
                    loadAnalytics(container);
                    break;
                case 'syllabus':
                    loadSyllabus(container);
                    break;
                case 'study-groups':
                    loadStudyGroups(container);
                    break;
            }
        }

        // Authentication
        async function loginAsAnonymous() {
            try {
                showMessage('Logging in as anonymous user...');
                const response = await fetch(`${API_BASE}/auth/anonymous`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error('Login failed');

                const data = await response.json();
                authToken = data.token;
                currentUser = data.user;

                userStatus.textContent = `Logged in as ${currentUser.name}`;
                loginBtn.textContent = 'Logged In ✓';
                loginBtn.disabled = true;
                loginBtn.className = 'bg-green-600 text-white px-4 py-2 rounded-md cursor-not-allowed';

                showMessage('Successfully logged in!', 'success');

                // Initialize socket connection
                initializeSocket();

                // Load initial content
                loadTabContent('exam-explorer');
            } catch (error) {
                showMessage('Login failed: ' + error.message, 'error');
            }
        }

        // Socket initialization
        function initializeSocket() {
            socket = io('http://localhost:5000');
            socket.on('connect', () => {
                console.log('Connected to server');
            });
        }

        // Exam Explorer Content
        function loadExamExplorer(container) {
            container.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">🎯 Exam Explorer</h2>
                    <p class="text-gray-600 mb-6">Discover and explore different exam types and subjects available in our system.</p>

                    <!-- Exam Types -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <div class="exam-card bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-lg shadow-lg" onclick="selectExamType('competitive')">
                            <h3 class="text-xl font-bold mb-2">🏆 Competitive Exams</h3>
                            <p class="text-blue-100">JEE, NEET, CAT, GATE, and more</p>
                        </div>
                        <div class="exam-card bg-gradient-to-br from-green-500 to-green-600 text-white p-6 rounded-lg shadow-lg" onclick="selectExamType('academic')">
                            <h3 class="text-xl font-bold mb-2">📚 Academic Tests</h3>
                            <p class="text-green-100">School and college level assessments</p>
                        </div>
                        <div class="exam-card bg-gradient-to-br from-purple-500 to-purple-600 text-white p-6 rounded-lg shadow-lg" onclick="selectExamType('certification')">
                            <h3 class="text-xl font-bold mb-2">🎓 Certifications</h3>
                            <p class="text-purple-100">Professional certification exams</p>
                        </div>
                    </div>

                    <!-- Subject Selection -->
                    <div class="mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">📖 Select Subject</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            ${['Physics', 'Mathematics', 'Chemistry', 'Biology', 'Computer Science', 'English'].map(subject => `
                                <button class="subject-btn bg-white border-2 border-gray-200 hover:border-indigo-500 p-4 rounded-lg text-center transition-all" onclick="selectSubject('${subject}')">
                                    <div class="text-2xl mb-2">${getSubjectIcon(subject)}</div>
                                    <div class="font-medium">${subject}</div>
                                </button>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Level Selection -->
                    <div class="mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">🎯 Select Difficulty Level</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button class="level-btn bg-green-50 border-2 border-green-200 hover:border-green-500 p-4 rounded-lg text-center transition-all" onclick="selectLevel('Beginner')">
                                <div class="text-2xl mb-2">🌱</div>
                                <div class="font-medium text-green-700">Beginner</div>
                                <div class="text-sm text-green-600">Basic concepts</div>
                            </button>
                            <button class="level-btn bg-yellow-50 border-2 border-yellow-200 hover:border-yellow-500 p-4 rounded-lg text-center transition-all" onclick="selectLevel('Intermediate')">
                                <div class="text-2xl mb-2">🚀</div>
                                <div class="font-medium text-yellow-700">Intermediate</div>
                                <div class="text-sm text-yellow-600">Applied knowledge</div>
                            </button>
                            <button class="level-btn bg-red-50 border-2 border-red-200 hover:border-red-500 p-4 rounded-lg text-center transition-all" onclick="selectLevel('Advanced')">
                                <div class="text-2xl mb-2">🔥</div>
                                <div class="font-medium text-red-700">Advanced</div>
                                <div class="text-sm text-red-600">Expert level</div>
                            </button>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button onclick="quickGenerateQuestions()" class="bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <div class="text-lg font-semibold">🚀 Quick Generate</div>
                            <div class="text-sm opacity-90">Generate sample questions</div>
                        </button>
                        <button onclick="switchTab('mock-test')" class="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors">
                            <div class="text-lg font-semibold">📝 Start Test</div>
                            <div class="text-sm opacity-90">Begin a mock test</div>
                        </button>
                        <button onclick="switchTab('analytics')" class="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors">
                            <div class="text-lg font-semibold">📊 View Analytics</div>
                            <div class="text-sm opacity-90">Check your progress</div>
                        </button>
                    </div>
                </div>

                <!-- Generated Questions Container -->
                <div id="generated-questions" class="space-y-4">
                    <!-- Questions will appear here -->
                </div>
            `;
        }

        function getSubjectIcon(subject) {
            const icons = {
                'Physics': '⚛️',
                'Mathematics': '🔢',
                'Chemistry': '🧪',
                'Biology': '🧬',
                'Computer Science': '💻',
                'English': '📝'
            };
            return icons[subject] || '📚';
        }

        // Global variables for selected subject and level
        let selectedSubject = 'Physics';
        let selectedLevel = 'Beginner';

        // Question Generation Functions
        async function quickGenerateQuestions() {
            if (!authToken) {
                showMessage('Please login first', 'error');
                return;
            }

            try {
                showMessage(`Generating ${selectedSubject} questions at ${selectedLevel} level...`);
                const container = document.getElementById('generated-questions');
                showLoader(container);

                // Add timestamp to prevent caching
                const timestamp = Date.now();
                const response = await fetch(`${API_BASE}/questions/generate?subject=${selectedSubject}&level=${selectedLevel}&numQuestions=2&t=${timestamp}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (!response.ok) throw new Error('Failed to generate questions');

                const data = await response.json();
                displayQuestions(data.questions, container);
                showMessage(`Generated ${data.questions.length} ${selectedSubject} questions successfully!`, 'success');
            } catch (error) {
                document.getElementById('generated-questions').innerHTML = '<p class="text-red-600 p-4">Failed to generate questions: ' + error.message + '</p>';
                showMessage('Generation failed: ' + error.message, 'error');
            }
        }

        function selectSubject(subject) {
            selectedSubject = subject;
            showMessage(`Selected subject: ${subject}`, 'success');

            // Update UI to show selection
            document.querySelectorAll('.subject-btn').forEach(btn => {
                btn.classList.remove('border-indigo-500', 'bg-indigo-50');
                btn.classList.add('border-gray-200');
            });

            // Highlight selected subject
            event.target.classList.add('border-indigo-500', 'bg-indigo-50');
            event.target.classList.remove('border-gray-200');
        }

        function selectLevel(level) {
            selectedLevel = level;
            showMessage(`Selected difficulty level: ${level}`, 'success');

            // Update UI to show selection
            document.querySelectorAll('.level-btn').forEach(btn => {
                btn.classList.remove('ring-2', 'ring-offset-2');
                if (btn.textContent.includes('Beginner')) {
                    btn.classList.remove('ring-green-500');
                } else if (btn.textContent.includes('Intermediate')) {
                    btn.classList.remove('ring-yellow-500');
                } else {
                    btn.classList.remove('ring-red-500');
                }
            });

            // Highlight selected level
            if (level === 'Beginner') {
                event.target.classList.add('ring-2', 'ring-offset-2', 'ring-green-500');
            } else if (level === 'Intermediate') {
                event.target.classList.add('ring-2', 'ring-offset-2', 'ring-yellow-500');
            } else {
                event.target.classList.add('ring-2', 'ring-offset-2', 'ring-red-500');
            }
        }

        function selectExamType(type) {
            // Set difficulty level based on exam type
            if (type === 'competitive') {
                selectedLevel = 'Advanced';
            } else if (type === 'academic') {
                selectedLevel = 'Intermediate';
            } else {
                selectedLevel = 'Beginner';
            }

            showMessage(`Selected exam type: ${type} (Level: ${selectedLevel})`, 'success');

            // Update UI to show selection
            document.querySelectorAll('.exam-card').forEach(card => {
                card.style.transform = 'translateY(0px)';
                card.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
            });

            // Highlight selected exam type
            event.target.style.transform = 'translateY(-8px)';
            event.target.style.boxShadow = '0 25px 50px rgba(0,0,0,0.2)';
        }

        function displayQuestions(questions, container) {
            container.innerHTML = '';

            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-card bg-white p-6 rounded-lg shadow-sm border mb-4';
                questionDiv.innerHTML = `
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Question ${index + 1}</h3>
                        <p class="text-gray-700 mb-4">${q.question}</p>

                        <div class="space-y-2 mb-4">
                            ${q.options.map(option => `
                                <div class="p-2 border rounded ${option.startsWith(q.correct_answer) ? 'bg-green-50 border-green-200' : 'bg-gray-50'}">
                                    ${option}
                                </div>
                            `).join('')}
                        </div>

                        <div class="bg-blue-50 p-4 rounded-md">
                            <h4 class="font-medium text-blue-900 mb-2">Explanation:</h4>
                            <p class="text-blue-800">${q.explanation}</p>
                        </div>

                        <div class="mt-4 flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-gray-200 text-gray-700 rounded text-sm">Difficulty: ${q.difficulty}</span>
                            <span class="px-2 py-1 bg-gray-200 text-gray-700 rounded text-sm">Level: ${q.cognitive_level}</span>
                            ${q.keywords.map(keyword => `
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">${keyword}</span>
                            `).join('')}
                        </div>
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        // Other Tab Content Functions
        function loadMockTest(container) {
            container.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">📝 Mock Test</h2>
                    <p class="text-gray-600 mb-6">Take comprehensive mock tests to evaluate your knowledge and skills.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                            <h3 class="text-lg font-semibold mb-2">🚀 Quick Test</h3>
                            <p class="text-gray-600 mb-4">5 questions, 10 minutes</p>
                            <p class="text-sm text-gray-500 mb-4">Subject: ${selectedSubject} | Level: ${selectedLevel}</p>
                            <button onclick="startMockTest(5)" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">Start Quick Test</button>
                        </div>
                        <div class="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                            <h3 class="text-lg font-semibold mb-2">📚 Full Test</h3>
                            <p class="text-gray-600 mb-4">20 questions, 30 minutes</p>
                            <p class="text-sm text-gray-500 mb-4">Subject: ${selectedSubject} | Level: ${selectedLevel}</p>
                            <button onclick="startMockTest(20)" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">Start Full Test</button>
                        </div>
                    </div>

                    <!-- Test Results Area -->
                    <div id="test-area" class="hidden">
                        <div id="test-questions" class="space-y-4">
                            <!-- Test questions will appear here -->
                        </div>
                        <div id="test-controls" class="mt-6 flex justify-between">
                            <button onclick="cancelTest()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel Test</button>
                            <button onclick="submitTest()" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Submit Test</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadAnalytics(container) {
            container.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">📊 Analytics Dashboard</h2>
                    <p class="text-gray-600 mb-6">Track your progress and performance across different subjects and tests.</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h3 class="text-lg font-semibold text-blue-900">Tests Taken</h3>
                            <p class="text-2xl font-bold text-blue-600">12</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h3 class="text-lg font-semibold text-green-900">Average Score</h3>
                            <p class="text-2xl font-bold text-green-600">78%</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h3 class="text-lg font-semibold text-purple-900">Study Time</h3>
                            <p class="text-2xl font-bold text-purple-600">24h</p>
                        </div>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-2">Performance Chart</h3>
                        <p class="text-gray-600">Chart visualization would go here</p>
                    </div>
                </div>
            `;
        }

        function loadSyllabus(container) {
            container.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">📚 Syllabus Management</h2>
                    <p class="text-gray-600 mb-6">Manage and track your syllabus coverage across different subjects.</p>

                    <div class="space-y-4">
                        <div class="border rounded-lg p-4">
                            <h3 class="text-lg font-semibold mb-2">Physics</h3>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">65% completed</p>
                        </div>
                        <div class="border rounded-lg p-4">
                            <h3 class="text-lg font-semibold mb-2">Mathematics</h3>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">80% completed</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadStudyGroups(container) {
            container.innerHTML = `
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">👥 Study Groups</h2>
                    <p class="text-gray-600 mb-6">Join study groups and collaborate with other students.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="border rounded-lg p-4">
                            <h3 class="text-lg font-semibold mb-2">Physics Study Group</h3>
                            <p class="text-gray-600 mb-2">15 members</p>
                            <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Join Group</button>
                        </div>
                        <div class="border rounded-lg p-4">
                            <h3 class="text-lg font-semibold mb-2">Math Problem Solvers</h3>
                            <p class="text-gray-600 mb-2">23 members</p>
                            <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Join Group</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Mock Test Functions
        let currentTest = null;
        let testStartTime = null;

        async function startMockTest(numQuestions) {
            if (!authToken) {
                showMessage('Please login first', 'error');
                return;
            }

            try {
                showMessage(`Starting ${numQuestions}-question test in ${selectedSubject}...`);

                // Generate test questions
                const timestamp = Date.now();
                const response = await fetch(`${API_BASE}/questions/generate?subject=${selectedSubject}&level=${selectedLevel}&numQuestions=${numQuestions}&t=${timestamp}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (!response.ok) throw new Error('Failed to generate test questions');

                const data = await response.json();
                currentTest = {
                    questions: data.questions,
                    answers: {},
                    startTime: Date.now()
                };

                displayTestQuestions(data.questions);
                showMessage(`Test started! ${numQuestions} questions loaded.`, 'success');
            } catch (error) {
                showMessage('Failed to start test: ' + error.message, 'error');
            }
        }

        function displayTestQuestions(questions) {
            const testArea = document.getElementById('test-area');
            const questionsContainer = document.getElementById('test-questions');

            testArea.classList.remove('hidden');

            questionsContainer.innerHTML = questions.map((q, index) => `
                <div class="bg-gray-50 p-6 rounded-lg border">
                    <h3 class="text-lg font-semibold mb-4">Question ${index + 1}</h3>
                    <p class="text-gray-800 mb-4">${q.question}</p>
                    <div class="space-y-2">
                        ${q.options.map(option => `
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="radio" name="question_${index}" value="${option}" onchange="recordAnswer(${index}, this.value)" class="text-indigo-600">
                                <span>${option}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        function recordAnswer(questionIndex, answer) {
            if (currentTest) {
                currentTest.answers[questionIndex] = answer;
            }
        }

        function submitTest() {
            if (!currentTest) return;

            const totalQuestions = currentTest.questions.length;
            const answeredQuestions = Object.keys(currentTest.answers).length;

            if (answeredQuestions < totalQuestions) {
                if (!confirm(`You have only answered ${answeredQuestions} out of ${totalQuestions} questions. Submit anyway?`)) {
                    return;
                }
            }

            // Calculate score
            let correct = 0;
            currentTest.questions.forEach((q, index) => {
                const userAnswer = currentTest.answers[index];
                if (userAnswer && userAnswer.startsWith(q.correct_answer)) {
                    correct++;
                }
            });

            const score = Math.round((correct / totalQuestions) * 100);
            const timeTaken = Math.round((Date.now() - currentTest.startTime) / 1000);

            showTestResults(score, correct, totalQuestions, timeTaken);
        }

        function showTestResults(score, correct, total, timeTaken) {
            const testArea = document.getElementById('test-area');
            testArea.innerHTML = `
                <div class="bg-white p-6 rounded-lg border text-center">
                    <h3 class="text-2xl font-bold mb-4">🎉 Test Completed!</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded">
                            <div class="text-2xl font-bold text-blue-600">${score}%</div>
                            <div class="text-blue-800">Score</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded">
                            <div class="text-2xl font-bold text-green-600">${correct}/${total}</div>
                            <div class="text-green-800">Correct</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded">
                            <div class="text-2xl font-bold text-purple-600">${timeTaken}s</div>
                            <div class="text-purple-800">Time</div>
                        </div>
                    </div>
                    <button onclick="cancelTest()" class="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700">Take Another Test</button>
                </div>
            `;

            showMessage(`Test completed! Score: ${score}% (${correct}/${total})`, 'success');
        }

        function cancelTest() {
            currentTest = null;
            const testArea = document.getElementById('test-area');
            testArea.classList.add('hidden');
            loadTabContent('mock-test'); // Reload the mock test interface
        }

        // Event listeners
        loginBtn.addEventListener('click', loginAsAnonymous);

        // Tab event listeners
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabId = e.target.id.replace('tab-', '');
                switchTab(tabId);
            });
        });

        // Initialize
        showMessage('Welcome! Click "Login as Anonymous" to get started.');
        loadTabContent('exam-explorer'); // Load initial content
    </script>
</body>
</html>

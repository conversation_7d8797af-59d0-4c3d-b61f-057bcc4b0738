<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Mock Test System</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .question-card { transition: all 0.3s ease; }
        .question-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-indigo-600">🤖 AI Mock Test System</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="user-info" class="text-sm text-gray-600">
                        <span id="user-status">Not logged in</span>
                    </div>
                    <button id="login-btn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">
                        Login as Anonymous
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Welcome to AI Mock Test System</h2>
            <p class="text-gray-600 mb-6">Generate AI-powered questions, take mock tests, and track your progress with our advanced learning platform.</p>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <button id="demo-btn" class="bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors">
                    <div class="text-lg font-semibold">🚀 Try Demo</div>
                    <div class="text-sm opacity-90">Generate sample questions</div>
                </button>
                <button id="start-test-btn" class="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors">
                    <div class="text-lg font-semibold">📝 Start Test</div>
                    <div class="text-sm opacity-90">Begin a mock test</div>
                </button>
                <button id="view-analytics-btn" class="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors">
                    <div class="text-lg font-semibold">📊 Analytics</div>
                    <div class="text-sm opacity-90">View your progress</div>
                </button>
            </div>
        </div>

        <!-- Demo Section -->
        <div id="demo-section" class="bg-white rounded-lg shadow-sm p-6 mb-8 hidden">
            <h3 class="text-xl font-bold text-gray-900 mb-4">🎯 Question Generation Demo</h3>

            <!-- Controls -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <select id="subject-select" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="Physics">Physics</option>
                        <option value="Mathematics">Mathematics</option>
                        <option value="Chemistry">Chemistry</option>
                        <option value="Biology">Biology</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Level</label>
                    <select id="level-select" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="Beginner">Beginner</option>
                        <option value="Intermediate">Intermediate</option>
                        <option value="Advanced">Advanced</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Questions</label>
                    <select id="num-questions-select" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="2">2 Questions</option>
                        <option value="5">5 Questions</option>
                        <option value="10">10 Questions</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button id="generate-btn" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">
                        Generate Questions
                    </button>
                </div>
            </div>

            <!-- Results -->
            <div id="questions-container" class="space-y-4">
                <!-- Generated questions will appear here -->
            </div>
        </div>

        <!-- Status Messages -->
        <div id="status-messages" class="space-y-2">
            <!-- Status messages will appear here -->
        </div>
    </main>

    <!-- Modal container -->
    <div id="modal-container" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div id="modal-content" class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <!-- Modal content will be dynamically inserted here -->
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Scripts -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="/js/protection.js"></script>
    <script src="/js/security.js"></script>
    <script src="/js/config.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/utils.js"></script>
    <script src="/js/app.js"></script>
    <script src="/js/examExplorer.js"></script>
    <script src="/js/mockTest.js"></script>
    <script src="/js/analytics.js"></script>
    <script src="/js/syllabus.js"></script>
    <script src="/js/theoryExplanation.js"></script>
    <script src="/js/challengeAnswer.js"></script>
    <script src="/js/explanationOnDemand.js"></script>
    <script src="/js/questionEnhancement.js"></script>
</body>
</html>

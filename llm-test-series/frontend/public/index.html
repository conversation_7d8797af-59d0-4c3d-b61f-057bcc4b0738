<!DOCTYPE html>
<html lang="en" data-theme="light" oncontextmenu="return false" onselectstart="return false" ondragstart="return false">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced AI-powered mock test system with real-time collaboration">
    <title>AI Mock Test System</title>
    
    <!-- Security meta tags -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com">
    <meta name="referrer" content="same-origin">
    <meta name="robots" content="noindex, nofollow">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()">
    
    <!-- Disable caching for dynamic content -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- PWA support -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#007bff">
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png">
    
    <!-- Additional security headers -->
    <meta http-equiv="X-DNS-Prefetch-Control" content="off">
    <meta http-equiv="Window-Target" content="_value">
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="date=no">
    <meta name="format-detection" content="address=no">
    <meta name="format-detection" content="email=no">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/explanationOnDemand.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container">
        <header class="header bg-white shadow-sm">
            <nav class="nav">
                <div class="nav-brand">
                    <h1 class="text-2xl font-bold text-indigo-600">AI-Powered Mock Test System</h1>
                </div>
                
                <div class="nav-links hidden-mobile">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/tests" class="nav-link">Tests</a>
                    <a href="/groups" class="nav-link">Study Groups</a>
                    <a href="/profile" class="nav-link">Profile</a>
                </div>

                <div class="nav-controls">
                    <div class="theme-switch" role="button" aria-label="Toggle dark mode">
                        <div class="theme-switch__toggle"></div>
                    </div>
                </div>
                <button id="tab-mock-test" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Mock Test</button>
                <button id="tab-analytics" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Analytics</button>
                <button id="tab-syllabus" class="tab-btn px-4 py-2 font-medium text-sm text-gray-500">Syllabus</button>
            </div>
        </div>

        <!-- Tab content containers -->
        <div id="exam-explorer-container" class="tab-content">
            <!-- Exam Explorer content will be loaded here -->
        </div>

        <div id="mock-test-container" class="tab-content hidden">
            <!-- Mock Test content will be loaded here -->
        </div>

        <div id="analytics-container" class="tab-content hidden">
            <!-- Analytics content will be loaded here -->
        </div>

        <div id="syllabus-container" class="tab-content hidden">
            <!-- Syllabus content will be loaded here -->
        </div>
    </main>

    <!-- Modal container -->
    <div id="modal-container" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div id="modal-content" class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <!-- Modal content will be dynamically inserted here -->
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>

    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="/js/protection.js"></script>
    <script src="/js/app.js" type="module"></script>
    <script src="/js/examExplorer.js"></script>
    <script src="/js/explanationOnDemand.js" type="module"></script>
    <script src="js/mockTest.js"></script>
    <script src="js/analytics.js"></script>
    <script src="js/syllabus.js"></script>
    <script src="js/theoryExplanation.js"></script>
    <script src="js/challengeAnswer.js"></script>
    <!-- Security Module - Load this before other scripts -->
    <script src="js/security.js"></script>
    
    <script src="js/app.js"></script>
    <!-- Question Enhancement Module -->
    <script src="/js/questionEnhancement.js"></script>
</body>
</html>

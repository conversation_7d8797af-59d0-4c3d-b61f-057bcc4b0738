<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Configuration - Admin Panel</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/adminPanel/examConfig.css">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1>Exam Configuration</h1>
            <button id="create-exam-btn" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create New Exam
            </button>
        </div>

        <!-- Alert Container -->
        <div id="alert-container"></div>

        <!-- Exam Configs List -->
        <div id="exam-configs-list" class="exam-configs-list"></div>

        <!-- Exam Config Modal -->
        <div id="exam-config-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Exam Configuration</h2>
                    <span class="close">&times;</span>
                </div>
                <form id="exam-config-form" class="form-grid">
                    <!-- Basic Info -->
                    <div class="form-group">
                        <label for="name">Exam Name</label>
                        <input type="text" id="name" name="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="type">Exam Type</label>
                        <select id="type" name="type" class="form-control" required>
                            <option value="teacher_recruitment">Teacher Recruitment</option>
                            <option value="civil_services">Civil Services</option>
                            <option value="banking">Banking</option>
                            <option value="engineering">Engineering</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="duration">Duration (minutes)</label>
                        <input type="number" id="duration" name="duration" class="form-control" required min="15" max="360">
                    </div>
                    <div class="form-group">
                        <label for="totalMarks">Total Marks</label>
                        <input type="number" id="totalMarks" name="totalMarks" class="form-control" required min="10" max="1000">
                    </div>

                    <!-- Question Configuration -->
                    <div class="form-group">
                        <label for="questions.defaultCount">Default Questions Count</label>
                        <input type="number" id="questions.defaultCount" name="questions.defaultCount" class="form-control" required min="5" max="200">
                    </div>
                    <div class="form-group">
                        <label>Question Types</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="type-mcq" name="questions.types" value="multiple_choice">
                                <label for="type-mcq">Multiple Choice</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type-fib" name="questions.types" value="fill_in_blanks">
                                <label for="type-fib">Fill in Blanks</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type-tf" name="questions.types" value="true_false">
                                <label for="type-tf">True/False</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="type-sa" name="questions.types" value="short_answer">
                                <label for="type-sa">Short Answer</label>
                            </div>
                        </div>
                    </div>

                    <!-- Marking Scheme -->
                    <div class="form-group">
                        <label for="questions.markingScheme.correct">Marks per Correct Answer</label>
                        <input type="number" id="questions.markingScheme.correct" name="questions.markingScheme.correct" class="form-control" required step="0.5">
                    </div>
                    <div class="form-group">
                        <label for="questions.markingScheme.incorrect">Negative Marks per Wrong Answer</label>
                        <input type="number" id="questions.markingScheme.incorrect" name="questions.markingScheme.incorrect" class="form-control" required step="0.5">
                    </div>

                    <!-- Performance Settings -->
                    <div class="form-group">
                        <label for="performance.passingScore">Passing Score (%)</label>
                        <input type="number" id="performance.passingScore" name="performance.passingScore" class="form-control" required min="0" max="100">
                    </div>
                    <div class="form-group">
                        <label>Grade Ranges (%)</label>
                        <div class="form-grid" style="gap: 0.5rem">
                            <input type="number" name="performance.gradeRanges.distinction" placeholder="Distinction" class="form-control" min="0" max="100">
                            <input type="number" name="performance.gradeRanges.firstClass" placeholder="First Class" class="form-control" min="0" max="100">
                            <input type="number" name="performance.gradeRanges.secondClass" placeholder="Second Class" class="form-control" min="0" max="100">
                            <input type="number" name="performance.gradeRanges.pass" placeholder="Pass" class="form-control" min="0" max="100">
                        </div>
                    </div>

                    <!-- UI Features -->
                    <div class="form-group">
                        <label>UI Features</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="feature-timer" name="ui.features.timer">
                                <label for="feature-timer">Timer</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="feature-progress" name="ui.features.progressBar">
                                <label for="feature-progress">Progress Bar</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="feature-navigation" name="ui.features.questionNavigation">
                                <label for="feature-navigation">Question Navigation</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="feature-review" name="ui.features.markForReview">
                                <label for="feature-review">Mark for Review</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="feature-dark" name="ui.features.darkMode">
                                <label for="feature-dark">Dark Mode</label>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Settings -->
                    <div class="form-group">
                        <label>Analytics Features</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="analytics-detailed" name="analytics.reportGeneration.detailed">
                                <label for="analytics-detailed">Detailed Reports</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="analytics-graphs" name="analytics.reportGeneration.includeGraphs">
                                <label for="analytics-graphs">Include Graphs</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="analytics-suggestions" name="analytics.reportGeneration.includeSuggestions">
                                <label for="analytics-suggestions">Include Suggestions</label>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="form-group" style="grid-column: span 2;">
                        <button type="button" id="save-exam-config" class="btn btn-primary">Save Configuration</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/adminPanel/examConfig.js"></script>
</body>
</html>

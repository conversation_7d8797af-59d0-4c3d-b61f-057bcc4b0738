<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Distribution Configuration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/adminPanel/questionDistribution.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/syllabusManager.html">Syllabus Manager</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/learningObjectives.html">Learning Objectives</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/questionDistribution.html">Question Distribution</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Syllabus Selection -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Syllabi</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" id="syllabusList">
                            <!-- Syllabi will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Distribution Configuration -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Question Distribution Configuration</h5>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="calculateDistribution()">
                                    <i class="bi bi-calculator"></i> Calculate
                                </button>
                                <button class="btn btn-primary" onclick="saveConfiguration()">
                                    <i class="bi bi-save"></i> Save
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Global Settings -->
                        <div class="mb-4">
                            <h6>Global Settings</h6>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Total Questions</label>
                                    <input type="number" class="form-control" id="totalQuestions" min="1" value="100">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Default Min Questions</label>
                                    <input type="number" class="form-control" id="defaultMinQuestions" min="0" value="1">
                                </div>
                            </div>
                        </div>

                        <!-- Units and Topics -->
                        <div id="unitsContainer">
                            <!-- Units and topics will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Distribution Preview -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Distribution Preview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Unit Distribution Chart -->
                            <div class="col-md-6">
                                <canvas id="unitDistributionChart"></canvas>
                            </div>
                            <!-- Difficulty Distribution Chart -->
                            <div class="col-md-6">
                                <canvas id="difficultyDistributionChart"></canvas>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h6>Detailed Distribution</h6>
                            <div id="detailedDistribution" class="table-responsive">
                                <!-- Distribution table will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/adminPanel/questionDistribution.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Past Year Questions Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/adminPanel/common.css" rel="stylesheet">
    <link href="/css/adminPanel/pastYearQuestions.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">Admin Panel</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/syllabusManager.html">Syllabus</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/pastYearQuestions.html">Past Year Questions</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/feedbackManager.html">Feedback</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/monitoring.html">Monitoring</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Questions</h6>
                        <h2 class="card-title" id="totalQuestions">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Verified Questions</h6>
                        <h2 class="card-title" id="verifiedQuestions">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Average Usage</h6>
                        <h2 class="card-title" id="averageUsage">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Years Covered</h6>
                        <h2 class="card-title" id="yearsCovered">0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Questions by Year</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="yearChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Questions by Difficulty</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="difficultyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Add Questions</h5>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="downloadTemplate()">
                                <i class="bi bi-download"></i> Download Template
                            </button>
                            <button class="btn btn-primary" onclick="openAddModal()">
                                <i class="bi bi-plus"></i> Add Question
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="upload-zone" ondrop="handleDrop(event)" ondragover="handleDragOver(event)">
                            <i class="bi bi-cloud-upload"></i>
                            <p>Drag and drop CSV file here or</p>
                            <input type="file" id="fileUpload" accept=".csv" style="display: none" onchange="handleFileSelect(event)">
                            <button class="btn btn-outline-primary" onclick="document.getElementById('fileUpload').click()">
                                Browse Files
                            </button>
                        </div>
                        <div id="uploadProgress" class="progress mt-3" style="display: none">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="uploadResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Question Modal -->
    <div class="modal fade" id="addQuestionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Past Year Question</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="questionForm">
                        <div class="mb-3">
                            <label class="form-label">Question Text</label>
                            <textarea class="form-control" name="question_text" rows="3" required></textarea>
                        </div>
                        <div class="mb-3" id="optionsContainer">
                            <label class="form-label">Options</label>
                            <div class="option-list">
                                <div class="option-item">
                                    <input type="text" class="form-control" name="options[]" required>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeOption(this)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addOption()">
                                Add Option
                            </button>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Correct Answer</label>
                            <input type="text" class="form-control" name="correct_answer" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Explanation</label>
                            <textarea class="form-control" name="explanation" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Year</label>
                                    <input type="number" class="form-control" name="year" required min="1900" max="2100">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Exam Name</label>
                                    <input type="text" class="form-control" name="exam_name" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Source URL</label>
                            <input type="url" class="form-control" name="source_url">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Topics</label>
                            <input type="text" class="form-control" name="topics" placeholder="Comma-separated topics">
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Difficulty</label>
                                    <select class="form-select" name="difficulty">
                                        <option value="easy">Easy</option>
                                        <option value="medium">Medium</option>
                                        <option value="hard">Hard</option>
                                        <option value="expert">Expert</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Verified</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="verified">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitQuestion()">Add Question</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/js/adminPanel/pastYearQuestions.js"></script>
</body>
</html>

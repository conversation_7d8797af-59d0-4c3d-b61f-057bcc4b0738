#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Setting up LLM Test Series Platform...\n');

// Helper function to run commands
function runCommand(command, cwd = __dirname) {
  try {
    console.log(`📦 Running: ${command}`);
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    return true;
  } catch (error) {
    console.error(`❌ Error running command: ${command}`);
    console.error(error.message);
    return false;
  }
}

// Helper function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Helper function to copy file
function copyFile(src, dest) {
  try {
    fs.copyFileSync(src, dest);
    console.log(`✅ Copied ${src} to ${dest}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to copy ${src} to ${dest}:`, error.message);
    return false;
  }
}

// Step 1: Install root dependencies
console.log('1️⃣ Installing root dependencies...');
if (!runCommand('npm install')) {
  console.error('❌ Failed to install root dependencies');
  process.exit(1);
}

// Step 2: Install backend dependencies
console.log('\n2️⃣ Installing backend dependencies...');
if (!runCommand('npm install', path.join(__dirname, 'backend'))) {
  console.error('❌ Failed to install backend dependencies');
  process.exit(1);
}

// Step 3: Install frontend dependencies
console.log('\n3️⃣ Installing frontend dependencies...');
if (!runCommand('npm install', path.join(__dirname, 'frontend'))) {
  console.error('❌ Failed to install frontend dependencies');
  process.exit(1);
}

// Step 4: Setup environment files
console.log('\n4️⃣ Setting up environment files...');

const backendEnvExample = path.join(__dirname, 'backend', '.env.example');
const backendEnv = path.join(__dirname, 'backend', '.env');

if (fileExists(backendEnvExample) && !fileExists(backendEnv)) {
  copyFile(backendEnvExample, backendEnv);
  console.log('⚠️  Please update backend/.env with your actual configuration values');
} else if (fileExists(backendEnv)) {
  console.log('✅ Backend .env file already exists');
} else {
  console.log('⚠️  Backend .env.example not found, skipping environment setup');
}

// Step 5: Create necessary directories
console.log('\n5️⃣ Creating necessary directories...');
const directories = [
  'backend/logs',
  'backend/uploads',
  'frontend/build',
  'docs/api',
  'database/backups'
];

directories.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  } else {
    console.log(`✅ Directory already exists: ${dir}`);
  }
});

// Step 6: Test backend server
console.log('\n6️⃣ Testing backend server...');
console.log('Starting test server to verify setup...');

try {
  // Start test server in background
  const testServerPath = path.join(__dirname, 'backend', 'src', 'test-server.js');
  if (fileExists(testServerPath)) {
    console.log('✅ Test server file found');
    console.log('🧪 You can test the backend by running: npm run test:server');
  } else {
    console.log('⚠️  Test server file not found');
  }
} catch (error) {
  console.log('⚠️  Could not test server automatically:', error.message);
}

// Step 7: Setup complete
console.log('\n🎉 Setup Complete!\n');

console.log('📋 Next Steps:');
console.log('1. Update backend/.env with your Firebase and OpenAI credentials');
console.log('2. Start Redis server (if using Redis caching)');
console.log('3. Run the development servers:');
console.log('   - Backend: cd backend && npm run dev');
console.log('   - Frontend: cd frontend && npm start');
console.log('   - Or both: npm run dev (from root)');
console.log('\n🔗 Useful URLs:');
console.log('   - Frontend: http://localhost:3000');
console.log('   - Backend API: http://localhost:3001');
console.log('   - Health Check: http://localhost:3001/health');
console.log('   - Test Endpoint: http://localhost:3001/test');

console.log('\n📚 Documentation:');
console.log('   - README.md - Project overview');
console.log('   - docs/PROJECT_STATUS.md - Current implementation status');
console.log('   - backend/.env.example - Environment configuration guide');

console.log('\n🐛 Troubleshooting:');
console.log('   - Check logs in backend/logs/ directory');
console.log('   - Verify Firebase credentials in backend/.env');
console.log('   - Ensure Redis is running (if enabled)');
console.log('   - Check port availability (3000, 3001)');

console.log('\n✨ Happy coding! The LLM Test Series Platform is ready for development.');

export default true;

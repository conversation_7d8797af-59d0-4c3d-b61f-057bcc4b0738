{"name": "ai-powered-mock-test-system", "version": "1.0.0", "description": "AI-powered mock test system with real-time collaboration", "main": "server/index.js", "type": "module", "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "build": "webpack --mode production", "build:staging": "webpack --mode production --env staging", "test": "jest", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type:check": "tsc --noEmit", "prepare": "husky install", "deploy:staging": "netlify deploy", "deploy:prod": "netlify deploy --prod", "build:functions": "netlify-lambda build netlify/functions", "start:lambda": "netlify-lambda serve netlify/functions"}, "keywords": ["education", "mock-test", "ai", "real-time"], "author": "", "license": "ISC", "engines": {"node": ">=18.19.0", "npm": ">=8.0.0"}, "dependencies": {"@netlify/functions": "^2.8.2", "bcrypt": "^6.0.0", "bull": "^4.12.0", "cloudinary": "^1.41.1", "cluster": "^0.7.7", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "firebase": "^10.7.1", "firebase-admin": "^11.11.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "netlify-lambda": "^2.0.16", "node-cache": "^5.1.2", "pm2": "^5.3.1", "react-countup": "^6.5.3", "serverless-http": "^3.2.0", "sharp": "^0.33.1", "socket.io": "^4.7.2", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^18.19.111", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.16", "chai": "^5.2.0", "css-loader": "^6.8.1", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.2", "mocha": "^11.5.0", "nodemon": "^3.0.1", "nyc": "^17.1.0", "postcss": "^8.4.31", "prettier": "^2.8.8", "sinon": "^20.0.0", "style-loader": "^3.3.3", "supertest": "^6.3.4", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "lint-staged": {"server/**/*.js": ["eslint --fix", "jest --findRelatedTests --passWithNoTests"], "**/*.{ts,tsx}": ["eslint --fix", "tsc --noEmit", "jest --findRelatedTests --passWithNoTests"]}}
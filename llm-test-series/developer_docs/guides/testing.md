# 🧪 Testing Strategy & Guidelines

## 📋 Testing Philosophy

Our testing strategy follows the **Testing Pyramid** principle with emphasis on fast, reliable, and maintainable tests that provide confidence in our codebase.

```mermaid
graph TB
    subgraph "Testing Pyramid"
        E2E[🎭 E2E Tests<br/>Few, High-Value<br/>~5% of tests]
        INTEGRATION[🔗 Integration Tests<br/>Some, Key Flows<br/>~15% of tests]
        UNIT[🧪 Unit Tests<br/>Many, Fast<br/>~80% of tests]
    end
    
    E2E --> INTEGRATION
    INTEGRATION --> UNIT
    
    subgraph "Test Types"
        UNIT --> PURE[Pure Functions]
        UNIT --> SERVICES[Service Logic]
        UNIT --> UTILS[Utilities]
        
        INTEGRATION --> API[API Endpoints]
        INTEGRATION --> DB[Database Operations]
        INTEGRATION --> AI[AI Service Integration]
        
        E2E --> USER[User Workflows]
        E2E --> CRITICAL[Critical Paths]
    end
```

## 🎯 Testing Levels

### 1. Unit Tests (80%)

**Purpose**: Test individual functions and components in isolation

**Characteristics**:
- Fast execution (<1ms per test)
- No external dependencies
- High code coverage
- Easy to debug

**Example**:
```javascript
// test/unit/services/questionService.test.js
describe('QuestionService', () => {
  describe('normalizeQuestion', () => {
    it('should format question with correct structure', () => {
      const input = {
        question: 'What is 2+2?',
        options: ['A) 3', 'B) 4', 'C) 5'],
        correct_answer: 'B'
      };
      
      const result = questionService.normalizeQuestion(input);
      
      expect(result).toEqual({
        id: expect.any(String),
        question: 'What is 2+2?',
        options: ['A) 3', 'B) 4', 'C) 5'],
        correct_answer: 'B',
        difficulty: 'Beginner',
        cognitive_level: 'Knowledge',
        keywords: expect.any(Array),
        estimated_time: expect.any(Number),
        points: expect.any(Number)
      });
    });
    
    it('should handle missing optional fields', () => {
      const input = {
        question: 'Test question?',
        options: ['A) 1', 'B) 2'],
        correct_answer: 'A'
      };
      
      const result = questionService.normalizeQuestion(input);
      
      expect(result.difficulty).toBe('Beginner');
      expect(result.keywords).toEqual([]);
      expect(result.estimated_time).toBeGreaterThan(0);
    });
  });
});
```

### 2. Integration Tests (15%)

**Purpose**: Test interactions between components and external services

**Characteristics**:
- Test real API endpoints
- Use test database
- Mock external services
- Validate data flow

**Example**:
```javascript
// test/integration/api/questions.test.js
describe('Questions API Integration', () => {
  let authToken;
  
  beforeAll(async () => {
    // Setup test environment
    await setupTestDatabase();
    const authResponse = await request(app)
      .post('/api/auth/anonymous')
      .expect(200);
    authToken = authResponse.body.token;
  });
  
  afterAll(async () => {
    await cleanupTestDatabase();
  });
  
  describe('POST /api/questions/generate', () => {
    it('should generate questions with valid parameters', async () => {
      const response = await request(app)
        .get('/api/questions/generate')
        .query({
          subject: 'Physics',
          level: 'Beginner',
          numQuestions: 2
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.questions).toHaveLength(2);
      expect(response.body.questions[0]).toMatchObject({
        question: expect.any(String),
        options: expect.any(Array),
        correct_answer: expect.any(String),
        explanation: expect.any(String)
      });
    });
    
    it('should return 401 without authentication', async () => {
      await request(app)
        .get('/api/questions/generate')
        .query({ subject: 'Physics', level: 'Beginner', numQuestions: 1 })
        .expect(401);
    });
    
    it('should validate input parameters', async () => {
      const response = await request(app)
        .get('/api/questions/generate')
        .query({
          subject: 'InvalidSubject',
          level: 'Beginner',
          numQuestions: 1
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid subject');
    });
  });
});
```

### 3. End-to-End Tests (5%)

**Purpose**: Test complete user workflows from frontend to backend

**Characteristics**:
- Test critical user journeys
- Use real browser automation
- Test across different environments
- Validate business requirements

**Example**:
```javascript
// test/e2e/userWorkflow.test.js
describe('Complete User Workflow', () => {
  let page;
  
  beforeAll(async () => {
    page = await browser.newPage();
    await page.goto('http://localhost:3000');
  });
  
  afterAll(async () => {
    await page.close();
  });
  
  it('should complete full question generation and test workflow', async () => {
    // Step 1: Login
    await page.click('#login-btn');
    await page.waitForSelector('#user-status');
    const userStatus = await page.textContent('#user-status');
    expect(userStatus).toContain('Logged in as');
    
    // Step 2: Select subject
    await page.click('button[onclick="selectSubject(\'Physics\')"]');
    await page.waitForSelector('.subject-btn.border-indigo-500');
    
    // Step 3: Generate questions
    await page.click('button[onclick="quickGenerateQuestions()"]');
    await page.waitForSelector('#generated-questions .question-card');
    
    const questions = await page.$$('#generated-questions .question-card');
    expect(questions.length).toBeGreaterThan(0);
    
    // Step 4: Start mock test
    await page.click('#tab-mock-test');
    await page.waitForSelector('#mock-test-container.active');
    
    await page.click('button[onclick="startMockTest(5)"]');
    await page.waitForSelector('#test-area:not(.hidden)');
    
    // Step 5: Answer questions
    const firstQuestion = await page.$('input[name="question_0"]');
    await firstQuestion.click();
    
    // Step 6: Submit test
    await page.click('button[onclick="submitTest()"]');
    await page.waitForSelector('.test-results');
    
    const score = await page.textContent('.test-results .score');
    expect(score).toMatch(/\d+%/);
  });
});
```

## 🔧 Testing Tools & Setup

### Testing Stack
```javascript
// package.json
{
  "devDependencies": {
    "jest": "^29.0.0",           // Test runner
    "supertest": "^6.3.0",      // API testing
    "playwright": "^1.28.0",    // E2E testing
    "@testing-library/jest-dom": "^5.16.0", // DOM assertions
    "nock": "^13.2.0",          // HTTP mocking
    "sinon": "^14.0.0",         // Spies and stubs
    "faker": "^6.6.6"           // Test data generation
  }
}
```

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  testMatch: [
    '<rootDir>/test/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/index.js',
    '!src/**/*.config.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 10000
};
```

### Test Setup
```javascript
// test/setup.js
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

beforeAll(async () => {
  // Setup in-memory database for testing
  mongoServer = await MongoMemoryServer.create();
  process.env.DATABASE_URL = mongoServer.getUri();
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-secret';
});

afterAll(async () => {
  if (mongoServer) {
    await mongoServer.stop();
  }
});

// Global test utilities
global.createTestUser = () => ({
  id: 'test_user_123',
  name: 'Test User',
  type: 'anonymous'
});

global.createTestQuestion = () => ({
  question: 'What is 2+2?',
  options: ['A) 3', 'B) 4', 'C) 5', 'D) 6'],
  correct_answer: 'B',
  explanation: '2+2 equals 4'
});
```

## 🎯 Testing Patterns

### 1. AAA Pattern (Arrange, Act, Assert)
```javascript
describe('calculateScore', () => {
  it('should calculate correct percentage score', () => {
    // Arrange
    const answers = ['A', 'B', 'C', 'D'];
    const correctAnswers = ['A', 'B', 'X', 'D'];
    
    // Act
    const score = calculateScore(answers, correctAnswers);
    
    // Assert
    expect(score).toBe(75); // 3 out of 4 correct = 75%
  });
});
```

### 2. Test Data Builders
```javascript
class QuestionBuilder {
  constructor() {
    this.question = {
      question: 'Default question?',
      options: ['A) Option 1', 'B) Option 2'],
      correct_answer: 'A',
      subject: 'Physics',
      level: 'Beginner'
    };
  }
  
  withSubject(subject) {
    this.question.subject = subject;
    return this;
  }
  
  withLevel(level) {
    this.question.level = level;
    return this;
  }
  
  withOptions(options) {
    this.question.options = options;
    return this;
  }
  
  build() {
    return { ...this.question };
  }
}

// Usage
const question = new QuestionBuilder()
  .withSubject('Mathematics')
  .withLevel('Advanced')
  .build();
```

### 3. Mock Strategies
```javascript
// Mock external AI service
jest.mock('../src/services/aiService', () => ({
  generateQuestions: jest.fn().mockResolvedValue([
    {
      question: 'Mocked question?',
      options: ['A) Mock 1', 'B) Mock 2'],
      correct_answer: 'A'
    }
  ])
}));

// Mock with different behaviors
const mockAIService = require('../src/services/aiService');

describe('Question Generation with AI failures', () => {
  it('should fallback to templates when AI fails', async () => {
    // Setup mock to fail
    mockAIService.generateQuestions.mockRejectedValueOnce(
      new Error('AI service unavailable')
    );
    
    const result = await questionService.generateQuestions({
      subject: 'Physics',
      level: 'Beginner',
      numQuestions: 1
    });
    
    expect(result.questions).toHaveLength(1);
    expect(result.source).toBe('template'); // Fallback used
  });
});
```

## 📊 Test Coverage & Quality

### Coverage Requirements
```javascript
// Minimum coverage thresholds
const coverageThresholds = {
  statements: 80,
  branches: 75,
  functions: 80,
  lines: 80
};

// Critical paths require 100% coverage
const criticalPaths = [
  'src/services/enhancedAIService.js',
  'src/routes/questions.js',
  'src/middleware/auth.js'
];
```

### Quality Metrics
```javascript
// Test quality indicators
const qualityMetrics = {
  testToCodeRatio: 1.2,        // 1.2 lines of test per line of code
  averageTestTime: '<100ms',    // Fast test execution
  flakyTestRate: '<1%',        // Reliable tests
  mutationScore: '>85%'        // Mutation testing score
};
```

## 🚀 Running Tests

### Development Workflow
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- questions.test.js

# Run tests with coverage
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run tests for specific component
npm test -- --grep "QuestionService"
```

### CI/CD Pipeline
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 🔍 Debugging Tests

### Debug Configuration
```javascript
// Debug specific test
npm test -- --detectOpenHandles --forceExit questions.test.js

// Run with verbose output
npm test -- --verbose

// Debug with Node.js debugger
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Common Issues & Solutions
```javascript
// Issue: Async operations not completing
// Solution: Proper async/await usage
it('should handle async operations', async () => {
  const result = await asyncFunction();
  expect(result).toBeDefined();
});

// Issue: Tests affecting each other
// Solution: Proper cleanup
afterEach(async () => {
  await cleanupDatabase();
  jest.clearAllMocks();
});

// Issue: Timeouts in CI
// Solution: Increase timeout for slow operations
it('should handle slow operations', async () => {
  // ... test code
}, 30000); // 30 second timeout
```

## 📈 Performance Testing

### Load Testing
```javascript
// test/performance/load.test.js
describe('API Performance', () => {
  it('should handle concurrent question generation', async () => {
    const concurrentRequests = 50;
    const promises = Array(concurrentRequests).fill().map(() =>
      request(app)
        .get('/api/questions/generate')
        .query({ subject: 'Physics', level: 'Beginner', numQuestions: 1 })
        .set('Authorization', `Bearer ${authToken}`)
    );
    
    const startTime = Date.now();
    const responses = await Promise.all(promises);
    const endTime = Date.now();
    
    // All requests should succeed
    responses.forEach(response => {
      expect(response.status).toBe(200);
    });
    
    // Should complete within reasonable time
    const totalTime = endTime - startTime;
    expect(totalTime).toBeLessThan(10000); // 10 seconds
  });
});
```

## 🎯 Best Practices

### 1. Test Naming
```javascript
// ✅ Good: Descriptive test names
describe('QuestionService.generateQuestions', () => {
  it('should return 5 questions when numQuestions is 5', () => {});
  it('should throw error when subject is invalid', () => {});
  it('should use fallback when AI service fails', () => {});
});

// ❌ Bad: Vague test names
describe('QuestionService', () => {
  it('should work', () => {});
  it('test generation', () => {});
});
```

### 2. Test Independence
```javascript
// ✅ Good: Independent tests
describe('User authentication', () => {
  beforeEach(() => {
    // Fresh setup for each test
    user = createTestUser();
    database.clear();
  });
});

// ❌ Bad: Dependent tests
describe('User workflow', () => {
  it('should create user', () => {
    user = createUser(); // Other tests depend on this
  });
  
  it('should login user', () => {
    login(user); // Depends on previous test
  });
});
```

### 3. Meaningful Assertions
```javascript
// ✅ Good: Specific assertions
expect(response.body).toEqual({
  success: true,
  questions: expect.arrayContaining([
    expect.objectContaining({
      question: expect.any(String),
      options: expect.any(Array),
      correct_answer: expect.stringMatching(/^[A-D]$/)
    })
  ])
});

// ❌ Bad: Vague assertions
expect(response.body).toBeTruthy();
expect(response.body.questions.length).toBeGreaterThan(0);
```

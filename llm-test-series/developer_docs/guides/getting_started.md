# 🚀 Getting Started - Developer Guide

## 📋 Prerequisites

### **System Requirements**
- **Node.js**: 18.0.0 or higher
- **npm**: 8.0.0 or higher
- **Git**: Latest version
- **OS**: Linux, macOS, or Windows with WSL

### **Development Tools**
- **Code Editor**: VS Code (recommended)
- **API Testing**: Postman or curl
- **Database**: Firebase account (optional)
- **AI Service**: Google AI API key

## ⚡ Quick Setup (5 Minutes)

### **1. Clone Repository**
```bash
git clone <repository-url>
cd llm-test-series
```

### **2. Install Dependencies**
```bash
# Backend dependencies
cd backend
npm install

# Frontend dependencies  
cd ../frontend
npm install

# Return to root
cd ..
```

### **3. Environment Configuration**
```bash
# Create backend environment file
cd backend
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Environment Variables:**
```bash
# AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Server Configuration
PORT=5000
NODE_ENV=development

# Security
JWT_SECRET=your_super_secret_jwt_key_here
```

### **4. Start Development Servers**
```bash
# Terminal 1: Start Backend
cd backend
npm start

# Terminal 2: Start Frontend
cd frontend
node static-server.js
```

### **5. Verify Installation**
```bash
# Test backend
curl http://localhost:5000/api/health

# Test frontend
curl http://localhost:3000
```

## 🏗️ Project Structure Deep Dive

```
llm-test-series/
├── 📁 backend/                 # Node.js API Server
│   ├── 📁 src/
│   │   ├── 📄 index.js         # Entry point & clustering
│   │   ├── 📁 routes/          # API route handlers
│   │   │   ├── 📄 questions.js # Question generation API
│   │   │   ├── 📄 userAuth.js  # Authentication API
│   │   │   ├── 📄 testSessions.js # Test management API
│   │   │   └── 📄 analytics.js # Analytics API
│   │   ├── 📁 services/        # Business logic services
│   │   │   ├── 📄 enhancedAIService.js # AI integration
│   │   │   ├── 📄 questionGenerationService.js # Question logic
│   │   │   ├── 📄 testSessionService.js # Test management
│   │   │   ├── 📄 analyticsService.js # Analytics logic
│   │   │   └── 📄 loggingService.js # Logging utilities
│   │   ├── 📁 middleware/      # Express middleware
│   │   ├── 📁 utils/          # Utility functions
│   │   └── 📄 firebase.js     # Database configuration
│   ├── 📄 package.json        # Dependencies & scripts
│   └── 📄 .env.example       # Environment template
├── 📁 frontend/               # Client-side application
│   ├── 📁 public/
│   │   ├── 📄 index.html      # Main application
│   │   ├── 📁 css/           # Stylesheets
│   │   ├── 📁 js/            # JavaScript modules
│   │   └── 📁 assets/        # Images, icons, etc.
│   ├── 📄 static-server.js   # Development server
│   └── 📄 package.json       # Frontend dependencies
├── 📁 developer_docs/        # Developer documentation
├── 📁 docs/                  # User documentation
├── 📄 test-integration.js    # Integration tests
├── 📄 README.md             # User-facing documentation
└── 📄 package.json          # Root package configuration
```

## 🔧 Development Workflow

### **Branch Strategy**
```mermaid
gitgraph
    commit id: "main"
    branch development
    checkout development
    commit id: "dev-base"
    branch feature/your-feature
    checkout feature/your-feature
    commit id: "implement"
    commit id: "test"
    checkout development
    merge feature/your-feature
    commit id: "integration"
    checkout main
    merge development
    commit id: "release"
```

### **Daily Development Process**
```bash
# 1. Start your day
git checkout development
git pull origin development

# 2. Create feature branch
git checkout -b feature/your-feature-name

# 3. Make changes and commit
git add .
git commit -m "feat: add new feature"

# 4. Push and create PR
git push origin feature/your-feature-name
# Create Pull Request on GitHub

# 5. After review and merge
git checkout development
git pull origin development
git branch -d feature/your-feature-name
```

## 🧪 Testing Your Changes

### **Unit Tests**
```bash
# Run all tests
npm test

# Run specific test file
npm test -- --grep "QuestionService"

# Run tests with coverage
npm run test:coverage
```

### **Integration Tests**
```bash
# Full integration test
node test-integration.js

# Test specific API endpoint
curl -X POST http://localhost:5000/api/auth/anonymous \
  -H "Content-Type: application/json" | \
  jq -r '.token' | \
  xargs -I {} curl -X GET \
  "http://localhost:5000/api/questions/generate?subject=Physics&level=Beginner&numQuestions=1" \
  -H "Authorization: Bearer {}"
```

### **Manual Testing Checklist**
- [ ] Backend starts without errors
- [ ] Frontend loads correctly
- [ ] Authentication works
- [ ] Question generation works
- [ ] Mock tests function properly
- [ ] All tabs navigate correctly

## 🔍 Debugging Guide

### **Backend Debugging**
```bash
# Enable debug logging
DEBUG=* npm start

# Check logs
tail -f logs/app.log

# Monitor performance
node --inspect src/index.js
```

### **Frontend Debugging**
```javascript
// Enable verbose logging in browser console
localStorage.setItem('debug', 'true');

// Check API calls in Network tab
// Use React DevTools for component inspection
```

### **Common Issues & Solutions**

#### **Issue: "GEMINI_API_KEY not found"**
```bash
# Solution: Check environment variables
echo $GEMINI_API_KEY

# If empty, export it:
export GEMINI_API_KEY=your_api_key_here

# Or add to .env file
echo "GEMINI_API_KEY=your_api_key_here" >> backend/.env
```

#### **Issue: "Port 5000 already in use"**
```bash
# Solution: Kill process on port 5000
lsof -ti:5000 | xargs kill -9

# Or use different port
PORT=5001 npm start
```

#### **Issue: "Firebase connection failed"**
```bash
# Solution: This is normal in development
# The app uses mock database as fallback
# Check logs for "falling back to mock DB"
```

## 📊 Performance Monitoring

### **Key Metrics to Watch**
```javascript
// Response times
console.time('question-generation');
const questions = await generateQuestions(params);
console.timeEnd('question-generation');

// Memory usage
console.log('Memory usage:', process.memoryUsage());

// Cache performance
console.log('Cache hit rate:', cache.getHitRate());
```

### **Performance Benchmarks**
- **Question Generation**: < 4 seconds
- **API Response**: < 100ms
- **Page Load**: < 2 seconds
- **Memory Usage**: < 50MB per worker

## 🔒 Security Considerations

### **Development Security**
```bash
# Never commit sensitive data
echo "*.env" >> .gitignore
echo "*.key" >> .gitignore

# Use environment variables
export GEMINI_API_KEY=your_key
export JWT_SECRET=your_secret

# Validate all inputs
const { error } = schema.validate(userInput);
if (error) throw new Error('Invalid input');
```

### **API Security Testing**
```bash
# Test rate limiting
for i in {1..20}; do
  curl http://localhost:5000/api/questions/generate
done

# Test authentication
curl http://localhost:5000/api/questions/generate
# Should return 401 Unauthorized

# Test input validation
curl -X GET "http://localhost:5000/api/questions/generate?subject=<script>alert('xss')</script>"
# Should return 400 Bad Request
```

## 🎯 Code Quality Standards

### **ESLint Configuration**
```bash
# Install ESLint
npm install -g eslint

# Run linting
eslint src/

# Fix auto-fixable issues
eslint src/ --fix
```

### **Code Formatting**
```bash
# Install Prettier
npm install -g prettier

# Format code
prettier --write "src/**/*.js"
```

### **Pre-commit Hooks**
```bash
# Install husky
npm install --save-dev husky

# Add pre-commit hook
npx husky add .husky/pre-commit "npm test && npm run lint"
```

## 📚 Learning Resources

### **Essential Reading**
1. [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)
2. [Express.js Guide](https://expressjs.com/en/guide/)
3. [JavaScript ES6+ Features](https://github.com/lukehoban/es6features)
4. [API Design Guidelines](https://github.com/microsoft/api-guidelines)

### **Project-Specific Docs**
- [System Architecture](../architecture/system_architecture.md)
- [API Documentation](../api/questions.md)
- [Code Philosophy](./code_philosophy.md)
- [Testing Strategy](./testing.md)

## 🆘 Getting Help

### **Internal Resources**
- **Code Reviews**: Create PR for feedback
- **Architecture Questions**: Check architecture docs
- **API Issues**: Refer to API documentation
- **Performance**: Check performance monitoring

### **External Resources**
- **Node.js Issues**: [Node.js Documentation](https://nodejs.org/docs/)
- **Express Issues**: [Express.js Documentation](https://expressjs.com/)
- **Google AI**: [Gemini API Documentation](https://ai.google.dev/docs)

### **Troubleshooting Steps**
1. **Check logs** for error messages
2. **Verify environment** variables are set
3. **Test API endpoints** individually
4. **Check network connectivity**
5. **Restart services** if needed
6. **Ask team** for help if stuck

## 🎉 You're Ready!

Congratulations! You now have:
- ✅ Working development environment
- ✅ Understanding of project structure
- ✅ Knowledge of development workflow
- ✅ Testing and debugging tools
- ✅ Code quality standards

**Next Steps:**
1. Pick a task from the project board
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

**Happy coding! 🚀**

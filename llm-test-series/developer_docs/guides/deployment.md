# 🚀 Deployment Guide

## 📋 Overview

This guide covers deployment strategies for the AI Mock Test System across different environments, from development to production.

## 🏗️ Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        LB[🔄 Load Balancer<br/>Nginx/HAProxy]
        
        subgraph "Application Tier"
            APP1[🚀 Node.js Instance 1]
            APP2[🚀 Node.js Instance 2]
            APP3[🚀 Node.js Instance N]
        end
        
        subgraph "Static Assets"
            CDN[🌍 CDN<br/>CloudFlare/AWS CloudFront]
            STATIC[📁 Static Files]
        end
        
        subgraph "Data Tier"
            DB[🔥 Firebase Firestore]
            CACHE[📦 Redis Cluster]
        end
        
        subgraph "External Services"
            AI[🧠 Google Gemini API]
            MONITOR[📊 Monitoring<br/>DataDog/New Relic]
        end
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> DB
    APP2 --> DB
    APP3 --> DB
    
    APP1 --> CACHE
    APP2 --> CACHE
    APP3 --> CACHE
    
    APP1 --> AI
    APP2 --> AI
    APP3 --> AI
    
    CDN --> STATIC
    
    APP1 --> MONITOR
    APP2 --> MONITOR
    APP3 --> M<PERSON>ITOR
```

## 🌍 Environment Configurations

### Development Environment
```bash
# .env.development
NODE_ENV=development
PORT=5000
GEMINI_API_KEY=your_dev_api_key
JWT_SECRET=dev_jwt_secret
LOG_LEVEL=debug
ENABLE_REDIS=false
FIREBASE_PROJECT_ID=dev-project
```

### Staging Environment
```bash
# .env.staging
NODE_ENV=staging
PORT=5000
GEMINI_API_KEY=your_staging_api_key
JWT_SECRET=staging_jwt_secret_longer_and_secure
LOG_LEVEL=info
ENABLE_REDIS=true
REDIS_URL=redis://staging-redis:6379
FIREBASE_PROJECT_ID=staging-project
```

### Production Environment
```bash
# .env.production
NODE_ENV=production
PORT=5000
GEMINI_API_KEY=your_production_api_key
JWT_SECRET=super_secure_production_jwt_secret_64_chars_minimum
LOG_LEVEL=warn
ENABLE_REDIS=true
REDIS_URL=redis://prod-redis-cluster:6379
FIREBASE_PROJECT_ID=production-project
FIREBASE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----...
FIREBASE_CLIENT_EMAIL=<EMAIL>
```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
# Multi-stage build for optimization
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build frontend assets
RUN cd frontend && npm run build

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

WORKDIR /app

# Copy built application
COPY --from=builder --chown=nodejs:nodejs /app .

# Switch to non-root user
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

EXPOSE 5000

CMD ["npm", "start"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data:
```

### Nginx Configuration
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app_servers {
        server app:5000 max_fails=3 fail_timeout=30s;
        # Add more servers for load balancing
        # server app2:5000 max_fails=3 fail_timeout=30s;
        # server app3:5000 max_fails=3 fail_timeout=30s;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    server {
        listen 80;
        server_name your-domain.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://app_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Auth routes with stricter rate limiting
        location /api/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://app_servers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files
        location / {
            root /var/www/html;
            try_files $uri $uri/ /index.html;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }

        # WebSocket support
        location /socket.io/ {
            proxy_pass http://app_servers;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
    }
}
```

## ☁️ Cloud Deployment

### AWS Deployment
```yaml
# aws-deployment.yml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'AI Mock Test System Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]

Resources:
  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub '${Environment}-ai-mock-test'

  # Application Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub '${Environment}-alb'
      Scheme: internet-facing
      Type: application
      Subnets: 
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2

  # ECS Service
  ECSService:
    Type: AWS::ECS::Service
    Properties:
      Cluster: !Ref ECSCluster
      TaskDefinition: !Ref TaskDefinition
      DesiredCount: 3
      LaunchType: FARGATE
      NetworkConfiguration:
        AwsvpcConfiguration:
          SecurityGroups: [!Ref SecurityGroup]
          Subnets: [!Ref PrivateSubnet1, !Ref PrivateSubnet2]

  # Task Definition
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: !Sub '${Environment}-ai-mock-test'
      NetworkMode: awsvpc
      RequiresCompatibilities: [FARGATE]
      Cpu: 512
      Memory: 1024
      ExecutionRoleArn: !Ref ExecutionRole
      ContainerDefinitions:
        - Name: app
          Image: your-account.dkr.ecr.region.amazonaws.com/ai-mock-test:latest
          PortMappings:
            - ContainerPort: 5000
          Environment:
            - Name: NODE_ENV
              Value: !Ref Environment
            - Name: REDIS_URL
              Value: !Sub '${RedisCluster.RedisEndpoint.Address}:6379'
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref LogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: ecs

  # Redis Cluster
  RedisCluster:
    Type: AWS::ElastiCache::CacheCluster
    Properties:
      CacheNodeType: cache.t3.micro
      Engine: redis
      NumCacheNodes: 1
```

### Kubernetes Deployment
```yaml
# k8s-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-mock-test
  labels:
    app: ai-mock-test
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-mock-test
  template:
    metadata:
      labels:
        app: ai-mock-test
    spec:
      containers:
      - name: app
        image: ai-mock-test:latest
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          value: "production"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: redis-url
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: gemini-api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: ai-mock-test-service
spec:
  selector:
    app: ai-mock-test
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: LoadBalancer

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-mock-test-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: ai-mock-test-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-mock-test-service
            port:
              number: 80
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ai-mock-test
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to ECS
        run: |
          aws ecs update-service \
            --cluster production-ai-mock-test \
            --service ai-mock-test-service \
            --force-new-deployment
```

## 📊 Monitoring & Observability

### Health Checks
```javascript
// src/routes/health.js
app.get('/api/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version,
    environment: process.env.NODE_ENV,
    checks: {}
  };

  try {
    // Database check
    health.checks.database = await checkDatabase();
    
    // Redis check
    health.checks.redis = await checkRedis();
    
    // AI service check
    health.checks.ai_service = await checkAIService();
    
    // Memory check
    const memUsage = process.memoryUsage();
    health.checks.memory = {
      status: memUsage.heapUsed < 500 * 1024 * 1024 ? 'healthy' : 'warning',
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    };

    const allHealthy = Object.values(health.checks)
      .every(check => check.status === 'healthy');
    
    health.status = allHealthy ? 'healthy' : 'degraded';
    
    res.status(allHealthy ? 200 : 503).json(health);
  } catch (error) {
    health.status = 'unhealthy';
    health.error = error.message;
    res.status(503).json(health);
  }
});
```

### Logging Configuration
```javascript
// src/utils/logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'ai-mock-test',
    version: process.env.npm_package_version,
    environment: process.env.NODE_ENV
  },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

module.exports = logger;
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] **Environment Variables**: All secrets in environment variables
- [ ] **HTTPS**: SSL/TLS certificates configured
- [ ] **Rate Limiting**: API rate limits implemented
- [ ] **Input Validation**: All inputs validated and sanitized
- [ ] **Authentication**: JWT tokens with proper expiration
- [ ] **CORS**: Proper CORS configuration
- [ ] **Security Headers**: All security headers set
- [ ] **Dependencies**: All dependencies updated and scanned
- [ ] **Logging**: No sensitive data in logs
- [ ] **Monitoring**: Security monitoring in place

### Security Configuration
```javascript
// src/middleware/security.js
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://generativelanguage.googleapis.com"]
    }
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

app.use('/api/', limiter);
```

## 📈 Performance Optimization

### Production Optimizations
```javascript
// src/index.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster && process.env.NODE_ENV === 'production') {
  console.log(`Master ${process.pid} is running`);

  // Fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork(); // Restart worker
  });
} else {
  // Worker process
  const app = require('./app');
  const PORT = process.env.PORT || 5000;
  
  app.listen(PORT, () => {
    console.log(`Worker ${process.pid} running on port ${PORT}`);
  });
}
```

## 🚨 Troubleshooting

### Common Deployment Issues
```bash
# Check container logs
docker logs container_name

# Check resource usage
docker stats

# Check health endpoint
curl http://localhost:5000/api/health

# Check environment variables
docker exec container_name env

# Check network connectivity
docker exec container_name ping external_service
```

### Rollback Strategy
```bash
# Quick rollback using Docker
docker tag ai-mock-test:previous ai-mock-test:latest
docker-compose up -d

# Rollback using Kubernetes
kubectl rollout undo deployment/ai-mock-test

# Rollback using AWS ECS
aws ecs update-service --cluster prod --service ai-mock-test --task-definition previous-task-def
```

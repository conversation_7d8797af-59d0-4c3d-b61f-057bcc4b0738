# 🔗 Component Interaction Diagrams

## 🏗️ System Component Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[🎨 User Interface]
        TABS[📑 Tab System]
        FORMS[📝 Forms & Controls]
        DISPLAY[📺 Display Components]
    end
    
    subgraph "Communication Layer"
        HTTP[🌐 HTTP Client]
        WS[📡 WebSocket Client]
        AUTH[🔐 Auth Manager]
        STATE[📊 State Manager]
    end
    
    subgraph "Backend API Layer"
        ROUTER[🛣️ Express Router]
        MIDDLEWARE[🔧 Middleware Stack]
        CONTROLLERS[🎮 Controllers]
        VALIDATORS[✅ Validators]
    end
    
    subgraph "Service Layer"
        AI_SVC[🤖 Enhanced AI Service]
        QUESTION_SVC[❓ Question Service]
        TEST_SVC[📝 Test Service]
        ANALYTICS_SVC[📊 Analytics Service]
        USER_SVC[👤 User Service]
    end
    
    subgraph "Data Layer"
        CACHE[💾 Cache Manager]
        DB[🗄️ Database Service]
        LOGGER[📝 Logging Service]
        METRICS[📈 Metrics Collector]
    end
    
    subgraph "External Services"
        GEMINI[🧠 Google Gemini]
        FIREBASE[🔥 Firebase]
        CDN[🌍 CDN]
    end
    
    UI --> TABS
    TABS --> FORMS
    FORMS --> DISPLAY
    
    UI --> HTTP
    UI --> WS
    HTTP --> AUTH
    WS --> STATE
    
    HTTP --> ROUTER
    ROUTER --> MIDDLEWARE
    MIDDLEWARE --> CONTROLLERS
    CONTROLLERS --> VALIDATORS
    
    VALIDATORS --> AI_SVC
    VALIDATORS --> QUESTION_SVC
    VALIDATORS --> TEST_SVC
    VALIDATORS --> ANALYTICS_SVC
    VALIDATORS --> USER_SVC
    
    AI_SVC --> CACHE
    QUESTION_SVC --> DB
    TEST_SVC --> LOGGER
    ANALYTICS_SVC --> METRICS
    
    AI_SVC --> GEMINI
    DB --> FIREBASE
    UI --> CDN
```

## 🎯 Question Generation Component Flow

```mermaid
sequenceDiagram
    participant UI as 🎨 Frontend UI
    participant HTTP as 🌐 HTTP Client
    participant AUTH as 🔐 Auth Manager
    participant ROUTER as 🛣️ Express Router
    participant MIDDLEWARE as 🔧 Middleware
    participant CONTROLLER as 🎮 Question Controller
    participant VALIDATOR as ✅ Input Validator
    participant AI_SVC as 🤖 Enhanced AI Service
    participant CACHE as 💾 Cache Manager
    participant GEMINI as 🧠 Google Gemini
    participant LOGGER as 📝 Logger
    participant METRICS as 📈 Metrics
    
    UI->>HTTP: Generate Questions Request
    HTTP->>AUTH: Get Auth Token
    AUTH->>HTTP: Return Token
    HTTP->>ROUTER: POST /api/questions/generate
    
    ROUTER->>MIDDLEWARE: Security Check
    MIDDLEWARE->>MIDDLEWARE: Rate Limiting
    MIDDLEWARE->>MIDDLEWARE: CORS Check
    MIDDLEWARE->>CONTROLLER: Route to Controller
    
    CONTROLLER->>VALIDATOR: Validate Input
    VALIDATOR->>CONTROLLER: Validation Result
    CONTROLLER->>AI_SVC: generateQuestions(params)
    
    AI_SVC->>CACHE: Check Cache
    CACHE->>AI_SVC: Cache Miss
    AI_SVC->>GEMINI: Generate Questions
    GEMINI->>AI_SVC: AI Response
    AI_SVC->>AI_SVC: Parse & Normalize
    AI_SVC->>CACHE: Store Questions
    AI_SVC->>LOGGER: Log Generation Event
    AI_SVC->>METRICS: Update Metrics
    AI_SVC->>CONTROLLER: Return Questions
    
    CONTROLLER->>ROUTER: JSON Response
    ROUTER->>HTTP: HTTP Response
    HTTP->>UI: Display Questions
```

## 🧪 Test Session Component Interaction

```mermaid
graph LR
    subgraph "Test Initiation"
        START[▶️ Start Button]
        CONFIG[⚙️ Test Config]
        SESSION[🆕 Session Creator]
    end
    
    subgraph "Question Management"
        LOADER[📥 Question Loader]
        CACHE_MGR[💾 Cache Manager]
        DISPLAY_MGR[📺 Display Manager]
        PROGRESS[📈 Progress Tracker]
    end
    
    subgraph "User Interaction"
        ANSWER_CAP[✅ Answer Capture]
        TIME_TRACK[⏱️ Timer]
        AUTO_SAVE[💾 Auto-saver]
        VALIDATION[✅ Validator]
    end
    
    subgraph "Submission & Results"
        SUBMIT[📤 Submitter]
        SCORER[🧮 Score Calculator]
        RESULT_GEN[📊 Result Generator]
        ANALYTICS[📈 Analytics Updater]
    end
    
    START --> CONFIG
    CONFIG --> SESSION
    SESSION --> LOADER
    
    LOADER --> CACHE_MGR
    CACHE_MGR --> DISPLAY_MGR
    DISPLAY_MGR --> PROGRESS
    
    PROGRESS --> ANSWER_CAP
    ANSWER_CAP --> TIME_TRACK
    TIME_TRACK --> AUTO_SAVE
    AUTO_SAVE --> VALIDATION
    
    VALIDATION --> SUBMIT
    SUBMIT --> SCORER
    SCORER --> RESULT_GEN
    RESULT_GEN --> ANALYTICS
```

## 🔐 Authentication Component Flow

```mermaid
flowchart TD
    subgraph "Frontend Auth Components"
        LOGIN_BTN[🔑 Login Button]
        TOKEN_MGR[🎫 Token Manager]
        AUTH_STATE[📊 Auth State]
        INTERCEPTOR[🔍 HTTP Interceptor]
    end
    
    subgraph "Backend Auth Components"
        AUTH_ROUTE[🛣️ Auth Routes]
        JWT_SVC[🎫 JWT Service]
        USER_SVC[👤 User Service]
        AUTH_MIDDLEWARE[🔧 Auth Middleware]
    end
    
    subgraph "Security Components"
        RATE_LIMITER[🚦 Rate Limiter]
        VALIDATOR[✅ Input Validator]
        SANITIZER[🧹 Data Sanitizer]
        LOGGER[📝 Security Logger]
    end
    
    LOGIN_BTN --> AUTH_ROUTE
    AUTH_ROUTE --> RATE_LIMITER
    RATE_LIMITER --> VALIDATOR
    VALIDATOR --> SANITIZER
    SANITIZER --> JWT_SVC
    JWT_SVC --> USER_SVC
    USER_SVC --> TOKEN_MGR
    
    TOKEN_MGR --> AUTH_STATE
    AUTH_STATE --> INTERCEPTOR
    INTERCEPTOR --> AUTH_MIDDLEWARE
    AUTH_MIDDLEWARE --> LOGGER
```

## 📊 Analytics Component Architecture

```mermaid
graph TB
    subgraph "Data Collection Layer"
        EVENT_CAP[📥 Event Capturer]
        METRIC_COL[📊 Metrics Collector]
        PERF_MON[⚡ Performance Monitor]
        ERROR_TRACK[❌ Error Tracker]
    end
    
    subgraph "Processing Layer"
        DATA_PROC[🔧 Data Processor]
        AGGREGATOR[📈 Aggregator]
        ENRICHER[🔧 Data Enricher]
        VALIDATOR[✅ Data Validator]
    end
    
    subgraph "Storage Layer"
        CACHE_STORE[💾 Cache Store]
        DB_STORE[🗄️ Database Store]
        METRICS_STORE[📊 Metrics Store]
        LOG_STORE[📝 Log Store]
    end
    
    subgraph "Analysis Layer"
        TREND_ANALYZER[📈 Trend Analyzer]
        INSIGHT_GEN[💡 Insight Generator]
        ALERT_SYS[🚨 Alert System]
        REPORT_GEN[📋 Report Generator]
    end
    
    subgraph "Visualization Layer"
        DASHBOARD[📊 Dashboard]
        CHARTS[📈 Chart Components]
        TABLES[📋 Data Tables]
        EXPORTS[📤 Export Tools]
    end
    
    EVENT_CAP --> DATA_PROC
    METRIC_COL --> AGGREGATOR
    PERF_MON --> ENRICHER
    ERROR_TRACK --> VALIDATOR
    
    DATA_PROC --> CACHE_STORE
    AGGREGATOR --> DB_STORE
    ENRICHER --> METRICS_STORE
    VALIDATOR --> LOG_STORE
    
    CACHE_STORE --> TREND_ANALYZER
    DB_STORE --> INSIGHT_GEN
    METRICS_STORE --> ALERT_SYS
    LOG_STORE --> REPORT_GEN
    
    TREND_ANALYZER --> DASHBOARD
    INSIGHT_GEN --> CHARTS
    ALERT_SYS --> TABLES
    REPORT_GEN --> EXPORTS
```

## 🔄 Real-time Communication Components

```mermaid
sequenceDiagram
    participant CLIENT as 🖥️ Client
    participant WS_CLIENT as 📡 WebSocket Client
    participant WS_SERVER as 📡 WebSocket Server
    participant EVENT_MGR as 🎭 Event Manager
    participant ROOM_MGR as 🏠 Room Manager
    participant BROADCAST as 📢 Broadcaster
    participant PERSISTENCE as 💾 Persistence Layer
    
    CLIENT->>WS_CLIENT: Connect to Server
    WS_CLIENT->>WS_SERVER: WebSocket Handshake
    WS_SERVER->>EVENT_MGR: Register Client
    EVENT_MGR->>ROOM_MGR: Join Room
    
    Note over CLIENT,PERSISTENCE: Real-time Question Generation
    
    CLIENT->>WS_CLIENT: Request Questions
    WS_CLIENT->>WS_SERVER: Emit 'generate-questions'
    WS_SERVER->>EVENT_MGR: Handle Event
    EVENT_MGR->>BROADCAST: Notify Room
    BROADCAST->>PERSISTENCE: Log Event
    BROADCAST->>WS_SERVER: Send Response
    WS_SERVER->>WS_CLIENT: Emit 'questions-ready'
    WS_CLIENT->>CLIENT: Update UI
    
    Note over CLIENT,PERSISTENCE: Live Test Session
    
    CLIENT->>WS_CLIENT: Submit Answer
    WS_CLIENT->>WS_SERVER: Emit 'answer-submitted'
    WS_SERVER->>EVENT_MGR: Process Answer
    EVENT_MGR->>PERSISTENCE: Save Answer
    EVENT_MGR->>BROADCAST: Update Progress
    BROADCAST->>WS_SERVER: Emit 'progress-update'
    WS_SERVER->>WS_CLIENT: Real-time Feedback
    WS_CLIENT->>CLIENT: Update Progress Bar
```

## 🔧 Middleware Component Stack

```mermaid
flowchart TD
    subgraph "Request Processing Pipeline"
        REQUEST[📥 Incoming Request]
        HELMET[🛡️ Security Headers]
        CORS[🌐 CORS Handler]
        RATE_LIMIT[🚦 Rate Limiter]
        BODY_PARSER[📦 Body Parser]
        AUTH[🔐 Authentication]
        VALIDATION[✅ Input Validation]
        LOGGING[📝 Request Logger]
        CONTROLLER[🎮 Route Controller]
    end
    
    subgraph "Response Processing Pipeline"
        RESPONSE[📤 Response Handler]
        ERROR_HANDLER[❌ Error Handler]
        COMPRESSION[🗜️ Response Compression]
        CACHE_HEADERS[💾 Cache Headers]
        METRICS[📊 Metrics Collection]
        FINAL_LOG[📝 Response Logger]
    end
    
    REQUEST --> HELMET
    HELMET --> CORS
    CORS --> RATE_LIMIT
    RATE_LIMIT --> BODY_PARSER
    BODY_PARSER --> AUTH
    AUTH --> VALIDATION
    VALIDATION --> LOGGING
    LOGGING --> CONTROLLER
    
    CONTROLLER --> RESPONSE
    RESPONSE --> ERROR_HANDLER
    ERROR_HANDLER --> COMPRESSION
    COMPRESSION --> CACHE_HEADERS
    CACHE_HEADERS --> METRICS
    METRICS --> FINAL_LOG
```

## 🎨 Frontend Component Hierarchy

```mermaid
graph TB
    subgraph "Application Root"
        APP[🏠 App Component]
        ROUTER[🛣️ Router]
        LAYOUT[📐 Layout]
    end
    
    subgraph "Navigation Components"
        HEADER[📋 Header]
        NAV[🧭 Navigation]
        TABS[📑 Tab System]
        BREADCRUMB[🍞 Breadcrumbs]
    end
    
    subgraph "Page Components"
        EXAM_EXPLORER[🎯 Exam Explorer]
        MOCK_TEST[📝 Mock Test]
        ANALYTICS[📊 Analytics]
        SYLLABUS[📚 Syllabus]
        STUDY_GROUPS[👥 Study Groups]
    end
    
    subgraph "Shared Components"
        QUESTION_CARD[❓ Question Card]
        ANSWER_OPTION[✅ Answer Option]
        PROGRESS_BAR[📈 Progress Bar]
        LOADING[⏳ Loading Spinner]
        MODAL[🪟 Modal Dialog]
        TOAST[🍞 Toast Notification]
    end
    
    subgraph "Form Components"
        INPUT[📝 Input Field]
        SELECT[📋 Select Dropdown]
        BUTTON[🔘 Button]
        CHECKBOX[☑️ Checkbox]
        RADIO[🔘 Radio Button]
    end
    
    APP --> ROUTER
    ROUTER --> LAYOUT
    LAYOUT --> HEADER
    HEADER --> NAV
    NAV --> TABS
    TABS --> BREADCRUMB
    
    TABS --> EXAM_EXPLORER
    TABS --> MOCK_TEST
    TABS --> ANALYTICS
    TABS --> SYLLABUS
    TABS --> STUDY_GROUPS
    
    EXAM_EXPLORER --> QUESTION_CARD
    MOCK_TEST --> ANSWER_OPTION
    ANALYTICS --> PROGRESS_BAR
    
    QUESTION_CARD --> INPUT
    ANSWER_OPTION --> SELECT
    PROGRESS_BAR --> BUTTON
    
    APP --> LOADING
    APP --> MODAL
    APP --> TOAST
```

## 🔄 State Management Flow

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Unauthenticated : App Loaded
    Unauthenticated --> Authenticating : Login Clicked
    Authenticating --> Authenticated : Login Success
    Authenticating --> Unauthenticated : Login Failed
    
    Authenticated --> QuestionGeneration : Generate Clicked
    QuestionGeneration --> QuestionsReady : Generation Success
    QuestionGeneration --> QuestionError : Generation Failed
    QuestionsReady --> TestSession : Start Test
    
    TestSession --> AnswerSubmission : Answer Selected
    AnswerSubmission --> TestSession : Continue Test
    AnswerSubmission --> TestComplete : Submit Test
    TestComplete --> Results : Calculate Score
    Results --> Authenticated : Return to Dashboard
    
    QuestionError --> Authenticated : Retry
    Authenticated --> [*] : Logout
```

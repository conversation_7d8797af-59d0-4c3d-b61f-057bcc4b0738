# 📊 Data Flow Diagrams

## 🔄 Complete System Data Flow

```mermaid
flowchart TD
    subgraph "User Interface Layer"
        USER[👤 User]
        BROWSER[🌐 Browser]
        UI[🎨 Frontend UI]
    end
    
    subgraph "API Gateway Layer"
        NGINX[⚡ Load Balancer]
        CORS[🔒 CORS Handler]
        RATE[🚦 Rate Limiter]
        AUTH[🔑 JWT Auth]
    end
    
    subgraph "Application Layer"
        CLUSTER[🔄 Node.js Cluster]
        EXPRESS[🚀 Express Server]
        SOCKET[📡 Socket.io]
        MIDDLEWARE[🔧 Middleware Stack]
    end
    
    subgraph "Business Logic Layer"
        ROUTES[🛣️ Route Handlers]
        SERVICES[⚙️ Business Services]
        AI_SERVICE[🤖 Enhanced AI Service]
        VALIDATION[✅ Data Validation]
    end
    
    subgraph "Data Access Layer"
        CACHE[💾 Cache Layer]
        DB_SERVICE[🗄️ Database Service]
        LOGGING[📝 Logging Service]
    end
    
    subgraph "External Services"
        GEMINI[🧠 Google Gemini]
        FIREBASE[🔥 Firebase]
        CDN[🌍 CDN]
    end
    
    subgraph "Storage Layer"
        REDIS[📦 Redis Cache]
        MOCK_DB[🗃️ Mock Database]
        LOGS[📋 Log Files]
    end
    
    USER --> BROWSER
    BROWSER --> UI
    UI --> NGINX
    NGINX --> CORS
    CORS --> RATE
    RATE --> AUTH
    AUTH --> CLUSTER
    CLUSTER --> EXPRESS
    EXPRESS --> SOCKET
    EXPRESS --> MIDDLEWARE
    MIDDLEWARE --> ROUTES
    ROUTES --> SERVICES
    SERVICES --> AI_SERVICE
    SERVICES --> VALIDATION
    VALIDATION --> CACHE
    CACHE --> DB_SERVICE
    DB_SERVICE --> LOGGING
    
    AI_SERVICE --> GEMINI
    DB_SERVICE --> FIREBASE
    DB_SERVICE --> MOCK_DB
    CACHE --> REDIS
    LOGGING --> LOGS
    UI --> CDN
```

## 🎯 Question Generation Data Flow

```mermaid
flowchart LR
    subgraph "Frontend"
        SELECT[📚 Subject Selection]
        LEVEL[🎯 Level Selection]
        GENERATE[🚀 Generate Button]
        DISPLAY[📋 Question Display]
    end
    
    subgraph "API Processing"
        VALIDATE[✅ Validate Request]
        EXTRACT[📤 Extract Parameters]
        ROUTE[🛣️ Route to Service]
    end
    
    subgraph "AI Service Processing"
        CHECK_PROVIDER[🔍 Check AI Provider]
        BUILD_PROMPT[📝 Build Prompt]
        CALL_AI[🤖 Call Gemini API]
        PARSE_RESPONSE[🔧 Parse Response]
        NORMALIZE[⚖️ Normalize Data]
        FALLBACK[🔄 Fallback Logic]
    end
    
    subgraph "Data Management"
        CACHE_CHECK[💾 Check Cache]
        CACHE_STORE[💾 Store in Cache]
        LOG_EVENT[📝 Log Generation]
        METRICS[📊 Update Metrics]
    end
    
    SELECT --> GENERATE
    LEVEL --> GENERATE
    GENERATE --> VALIDATE
    VALIDATE --> EXTRACT
    EXTRACT --> ROUTE
    ROUTE --> CHECK_PROVIDER
    CHECK_PROVIDER --> BUILD_PROMPT
    BUILD_PROMPT --> CALL_AI
    CALL_AI --> PARSE_RESPONSE
    PARSE_RESPONSE --> NORMALIZE
    NORMALIZE --> CACHE_STORE
    CACHE_STORE --> LOG_EVENT
    LOG_EVENT --> METRICS
    METRICS --> DISPLAY
    
    CALL_AI -.->|On Failure| FALLBACK
    FALLBACK --> NORMALIZE
    
    CHECK_PROVIDER --> CACHE_CHECK
    CACHE_CHECK -.->|Cache Hit| DISPLAY
```

## 🧪 Test Session Data Flow

```mermaid
flowchart TD
    subgraph "Test Initiation"
        START_BTN[▶️ Start Test Button]
        TEST_CONFIG[⚙️ Test Configuration]
        SESSION_CREATE[🆕 Create Session]
    end
    
    subgraph "Question Loading"
        LOAD_QUESTIONS[📥 Load Questions]
        QUESTION_CACHE[💾 Question Cache]
        QUESTION_DISPLAY[📺 Display Questions]
    end
    
    subgraph "User Interaction"
        ANSWER_SELECT[✅ Answer Selection]
        PROGRESS_TRACK[📈 Progress Tracking]
        TIME_TRACK[⏱️ Time Tracking]
        AUTO_SAVE[💾 Auto-save Answers]
    end
    
    subgraph "Test Submission"
        SUBMIT_BTN[📤 Submit Button]
        VALIDATE_ANSWERS[✅ Validate Answers]
        CALCULATE_SCORE[🧮 Calculate Score]
        GENERATE_REPORT[📊 Generate Report]
    end
    
    subgraph "Results Processing"
        SAVE_RESULTS[💾 Save Results]
        UPDATE_ANALYTICS[📈 Update Analytics]
        DISPLAY_RESULTS[📋 Display Results]
        SEND_FEEDBACK[📧 Send Feedback]
    end
    
    START_BTN --> TEST_CONFIG
    TEST_CONFIG --> SESSION_CREATE
    SESSION_CREATE --> LOAD_QUESTIONS
    LOAD_QUESTIONS --> QUESTION_CACHE
    QUESTION_CACHE --> QUESTION_DISPLAY
    
    QUESTION_DISPLAY --> ANSWER_SELECT
    ANSWER_SELECT --> PROGRESS_TRACK
    PROGRESS_TRACK --> TIME_TRACK
    TIME_TRACK --> AUTO_SAVE
    
    AUTO_SAVE --> SUBMIT_BTN
    SUBMIT_BTN --> VALIDATE_ANSWERS
    VALIDATE_ANSWERS --> CALCULATE_SCORE
    CALCULATE_SCORE --> GENERATE_REPORT
    
    GENERATE_REPORT --> SAVE_RESULTS
    SAVE_RESULTS --> UPDATE_ANALYTICS
    UPDATE_ANALYTICS --> DISPLAY_RESULTS
    DISPLAY_RESULTS --> SEND_FEEDBACK
```

## 🔐 Authentication Data Flow

```mermaid
flowchart LR
    subgraph "Client Side"
        LOGIN_BTN[🔑 Login Button]
        TOKEN_STORE[💾 Token Storage]
        API_CALLS[📡 API Calls]
        TOKEN_REFRESH[🔄 Token Refresh]
    end
    
    subgraph "Server Side"
        AUTH_ENDPOINT[🛡️ Auth Endpoint]
        USER_CREATE[👤 Create User]
        JWT_GENERATE[🎫 Generate JWT]
        TOKEN_VALIDATE[✅ Validate Token]
        PROTECTED_ROUTE[🔒 Protected Route]
    end
    
    subgraph "Security Layer"
        RATE_LIMIT[🚦 Rate Limiting]
        CORS_CHECK[🌐 CORS Check]
        HEADER_VALIDATE[📋 Header Validation]
        PERMISSION_CHECK[🔍 Permission Check]
    end
    
    LOGIN_BTN --> AUTH_ENDPOINT
    AUTH_ENDPOINT --> RATE_LIMIT
    RATE_LIMIT --> USER_CREATE
    USER_CREATE --> JWT_GENERATE
    JWT_GENERATE --> TOKEN_STORE
    
    TOKEN_STORE --> API_CALLS
    API_CALLS --> CORS_CHECK
    CORS_CHECK --> HEADER_VALIDATE
    HEADER_VALIDATE --> TOKEN_VALIDATE
    TOKEN_VALIDATE --> PERMISSION_CHECK
    PERMISSION_CHECK --> PROTECTED_ROUTE
    
    TOKEN_VALIDATE -.->|Expired| TOKEN_REFRESH
    TOKEN_REFRESH --> JWT_GENERATE
```

## 📊 Analytics Data Flow

```mermaid
flowchart TB
    subgraph "Data Collection"
        USER_ACTION[👆 User Actions]
        API_METRICS[📊 API Metrics]
        PERFORMANCE[⚡ Performance Data]
        ERROR_EVENTS[❌ Error Events]
    end
    
    subgraph "Data Processing"
        EVENT_CAPTURE[📥 Event Capture]
        DATA_VALIDATION[✅ Data Validation]
        DATA_ENRICHMENT[🔧 Data Enrichment]
        AGGREGATION[📈 Data Aggregation]
    end
    
    subgraph "Storage & Caching"
        REAL_TIME_CACHE[⚡ Real-time Cache]
        BATCH_STORAGE[📦 Batch Storage]
        HISTORICAL_DATA[📚 Historical Data]
        METRICS_STORE[📊 Metrics Store]
    end
    
    subgraph "Analytics Engine"
        TREND_ANALYSIS[📈 Trend Analysis]
        PERFORMANCE_CALC[🧮 Performance Calculation]
        INSIGHT_GEN[💡 Insight Generation]
        ALERT_SYSTEM[🚨 Alert System]
    end
    
    subgraph "Visualization"
        DASHBOARD[📊 Dashboard]
        REPORTS[📋 Reports]
        CHARTS[📈 Charts]
        EXPORT[📤 Data Export]
    end
    
    USER_ACTION --> EVENT_CAPTURE
    API_METRICS --> EVENT_CAPTURE
    PERFORMANCE --> EVENT_CAPTURE
    ERROR_EVENTS --> EVENT_CAPTURE
    
    EVENT_CAPTURE --> DATA_VALIDATION
    DATA_VALIDATION --> DATA_ENRICHMENT
    DATA_ENRICHMENT --> AGGREGATION
    
    AGGREGATION --> REAL_TIME_CACHE
    AGGREGATION --> BATCH_STORAGE
    BATCH_STORAGE --> HISTORICAL_DATA
    REAL_TIME_CACHE --> METRICS_STORE
    
    METRICS_STORE --> TREND_ANALYSIS
    HISTORICAL_DATA --> PERFORMANCE_CALC
    TREND_ANALYSIS --> INSIGHT_GEN
    PERFORMANCE_CALC --> ALERT_SYSTEM
    
    INSIGHT_GEN --> DASHBOARD
    ALERT_SYSTEM --> REPORTS
    DASHBOARD --> CHARTS
    REPORTS --> EXPORT
```

## 🔄 Real-time Communication Flow

```mermaid
sequenceDiagram
    participant Client as 🖥️ Client
    participant Server as 🖥️ Server
    participant Socket as 📡 Socket.io
    participant Service as ⚙️ Service
    participant DB as 🗄️ Database
    
    Client->>Server: HTTP Request (Upgrade to WebSocket)
    Server->>Socket: Initialize Socket Connection
    Socket->>Client: Connection Established
    
    Note over Client,DB: Real-time Question Generation
    
    Client->>Socket: Request Questions
    Socket->>Service: Generate Questions
    Service->>DB: Log Request
    Service->>Socket: Questions Ready
    Socket->>Client: Stream Questions
    
    Note over Client,DB: Live Test Session
    
    Client->>Socket: Start Test Session
    Socket->>Service: Create Session
    Service->>DB: Save Session
    Socket->>Client: Session Started
    
    loop During Test
        Client->>Socket: Answer Submitted
        Socket->>Service: Process Answer
        Service->>DB: Save Answer
        Socket->>Client: Answer Confirmed
    end
    
    Client->>Socket: Submit Test
    Socket->>Service: Calculate Results
    Service->>DB: Save Results
    Socket->>Client: Results Available
```

## 🔧 Error Handling Data Flow

```mermaid
flowchart TD
    subgraph "Error Sources"
        CLIENT_ERROR[🖥️ Client Errors]
        SERVER_ERROR[🖥️ Server Errors]
        AI_ERROR[🤖 AI Service Errors]
        DB_ERROR[🗄️ Database Errors]
        NETWORK_ERROR[🌐 Network Errors]
    end
    
    subgraph "Error Detection"
        TRY_CATCH[🎯 Try-Catch Blocks]
        ERROR_MIDDLEWARE[🔧 Error Middleware]
        HEALTH_CHECKS[❤️ Health Checks]
        MONITORING[👁️ Monitoring]
    end
    
    subgraph "Error Processing"
        ERROR_CLASSIFY[🏷️ Classify Error]
        ERROR_LOG[📝 Log Error]
        ERROR_NOTIFY[📢 Notify Team]
        FALLBACK_TRIGGER[🔄 Trigger Fallback]
    end
    
    subgraph "Recovery Actions"
        RETRY_LOGIC[🔄 Retry Logic]
        FALLBACK_SERVICE[🛡️ Fallback Service]
        GRACEFUL_DEGRADE[📉 Graceful Degradation]
        USER_FEEDBACK[💬 User Feedback]
    end
    
    CLIENT_ERROR --> TRY_CATCH
    SERVER_ERROR --> ERROR_MIDDLEWARE
    AI_ERROR --> HEALTH_CHECKS
    DB_ERROR --> MONITORING
    NETWORK_ERROR --> MONITORING
    
    TRY_CATCH --> ERROR_CLASSIFY
    ERROR_MIDDLEWARE --> ERROR_CLASSIFY
    HEALTH_CHECKS --> ERROR_CLASSIFY
    MONITORING --> ERROR_CLASSIFY
    
    ERROR_CLASSIFY --> ERROR_LOG
    ERROR_LOG --> ERROR_NOTIFY
    ERROR_NOTIFY --> FALLBACK_TRIGGER
    
    FALLBACK_TRIGGER --> RETRY_LOGIC
    RETRY_LOGIC --> FALLBACK_SERVICE
    FALLBACK_SERVICE --> GRACEFUL_DEGRADE
    GRACEFUL_DEGRADE --> USER_FEEDBACK
```

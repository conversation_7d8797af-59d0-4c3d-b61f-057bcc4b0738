# 🔐 Authentication API Documentation

## 📋 Overview

The Authentication API provides secure user authentication and authorization using JWT tokens. It supports both anonymous and registered user authentication with comprehensive security features.

## 🔗 Base URL
```
http://localhost:5000/api/auth
```

## 🎯 Authentication Flow

```mermaid
sequenceDiagram
    participant Client as 🖥️ Client
    participant API as 🚀 Auth API
    participant JWT as 🎫 JWT Service
    participant User as 👤 User Service
    participant DB as 🗄️ Database
    
    Client->>API: POST /auth/anonymous
    API->>User: createAnonymousUser()
    User->>DB: Save User
    DB->>User: User Created
    User->>JWT: generateToken(user)
    JWT->>API: JWT Token
    API->>Client: {token, user}
    
    Note over Client,DB: Subsequent API Calls
    
    Client->>API: API Request + Bearer Token
    API->>JWT: verifyToken(token)
    JWT->>API: Token Valid + User Data
    API->>Client: Protected Resource
```

## 📊 API Endpoints

### 1. Anonymous Authentication

**Endpoint:** `POST /anonymous`

**Description:** Creates an anonymous user session and returns a JWT token

**Request:**
```bash
curl -X POST http://localhost:5000/api/auth/anonymous \
  -H "Content-Type: application/json"
```

**Response Schema:**
```javascript
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "anon_1672531200_abc123",
    "name": "Anonymous User",
    "type": "anonymous",
    "created_at": "2023-01-01T00:00:00.000Z",
    "permissions": ["take_tests", "view_results"]
  },
  "expires_in": 86400
}
```

**Error Responses:**
```javascript
// 429 Too Many Requests
{
  "success": false,
  "error": "Too many authentication attempts",
  "code": "RATE_LIMIT_EXCEEDED",
  "retry_after": 60
}

// 500 Internal Server Error
{
  "success": false,
  "error": "Authentication service unavailable",
  "code": "AUTH_SERVICE_ERROR"
}
```

### 2. Token Validation

**Endpoint:** `GET /validate`

**Description:** Validates a JWT token and returns user information

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request:**
```bash
curl -X GET http://localhost:5000/api/auth/validate \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response Schema:**
```javascript
{
  "success": true,
  "valid": true,
  "user": {
    "id": "anon_1672531200_abc123",
    "name": "Anonymous User",
    "type": "anonymous",
    "created_at": "2023-01-01T00:00:00.000Z",
    "permissions": ["take_tests", "view_results"]
  },
  "token_info": {
    "issued_at": "2023-01-01T00:00:00.000Z",
    "expires_at": "2023-01-02T00:00:00.000Z",
    "time_remaining": 86400
  }
}
```

### 3. Token Refresh

**Endpoint:** `POST /refresh`

**Description:** Refreshes an existing JWT token

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request:**
```bash
curl -X POST http://localhost:5000/api/auth/refresh \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response Schema:**
```javascript
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "anon_1672531200_abc123",
    "name": "Anonymous User",
    "type": "anonymous"
  },
  "expires_in": 86400
}
```

### 4. Logout

**Endpoint:** `POST /logout`

**Description:** Invalidates the current JWT token

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request:**
```bash
curl -X POST http://localhost:5000/api/auth/logout \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response Schema:**
```javascript
{
  "success": true,
  "message": "Successfully logged out"
}
```

## 🔒 Security Features

### JWT Token Structure
```javascript
// Header
{
  "alg": "HS256",
  "typ": "JWT"
}

// Payload
{
  "sub": "anon_1672531200_abc123",
  "name": "Anonymous User",
  "type": "anonymous",
  "permissions": ["take_tests", "view_results"],
  "iat": 1672531200,
  "exp": 1672617600
}

// Signature
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret
)
```

### Rate Limiting
```javascript
const rateLimits = {
  '/anonymous': { requests: 5, window: 300 },    // 5 requests per 5 minutes
  '/validate': { requests: 100, window: 60 },    // 100 requests per minute
  '/refresh': { requests: 10, window: 60 },      // 10 requests per minute
  '/logout': { requests: 20, window: 60 }        // 20 requests per minute
};
```

### Security Headers
```javascript
// Applied to all auth endpoints
{
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
  "Content-Security-Policy": "default-src 'self'"
}
```

## 🔧 Implementation Details

### Anonymous User Generation
```javascript
function generateAnonymousUser() {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 8);
  
  return {
    id: `anon_${timestamp}_${randomId}`,
    name: 'Anonymous User',
    type: 'anonymous',
    created_at: new Date().toISOString(),
    permissions: ['take_tests', 'view_results'],
    session_data: {
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      created_at: timestamp
    }
  };
}
```

### JWT Token Generation
```javascript
function generateJWT(user) {
  const payload = {
    sub: user.id,
    name: user.name,
    type: user.type,
    permissions: user.permissions,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, {
    algorithm: 'HS256'
  });
}
```

### Token Validation Middleware
```javascript
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Access token required',
      code: 'NO_TOKEN'
    });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }
    
    req.user = user;
    next();
  });
}
```

## 🚨 Error Handling

### Common Error Codes
```javascript
const AUTH_ERRORS = {
  NO_TOKEN: 'Access token required',
  INVALID_TOKEN: 'Invalid or expired token',
  EXPIRED_TOKEN: 'Token has expired',
  MALFORMED_TOKEN: 'Token format is invalid',
  RATE_LIMIT_EXCEEDED: 'Too many requests',
  AUTH_SERVICE_ERROR: 'Authentication service unavailable',
  INVALID_CREDENTIALS: 'Invalid username or password',
  USER_NOT_FOUND: 'User not found',
  PERMISSION_DENIED: 'Insufficient permissions'
};
```

### Error Response Format
```javascript
{
  "success": false,
  "error": "Human-readable error message",
  "code": "MACHINE_READABLE_CODE",
  "details": {
    "field": "specific field that caused error",
    "value": "invalid value",
    "expected": "expected format or value"
  },
  "timestamp": "2023-01-01T00:00:00.000Z",
  "request_id": "req_1672531200_abc123"
}
```

## 📊 Usage Examples

### Frontend Integration
```javascript
class AuthManager {
  constructor() {
    this.token = localStorage.getItem('auth_token');
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
  }
  
  async loginAnonymous() {
    try {
      const response = await fetch('/api/auth/anonymous', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (data.success) {
        this.token = data.token;
        this.user = data.user;
        localStorage.setItem('auth_token', this.token);
        localStorage.setItem('user', JSON.stringify(this.user));
        return true;
      }
      
      throw new Error(data.error);
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  }
  
  async makeAuthenticatedRequest(url, options = {}) {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`,
      ...options.headers
    };
    
    const response = await fetch(url, { ...options, headers });
    
    if (response.status === 401) {
      // Token expired, try to refresh
      const refreshed = await this.refreshToken();
      if (refreshed) {
        // Retry with new token
        headers['Authorization'] = `Bearer ${this.token}`;
        return fetch(url, { ...options, headers });
      } else {
        // Refresh failed, redirect to login
        this.logout();
        throw new Error('Authentication required');
      }
    }
    
    return response;
  }
  
  async refreshToken() {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${this.token}` }
      });
      
      const data = await response.json();
      
      if (data.success) {
        this.token = data.token;
        localStorage.setItem('auth_token', this.token);
        return true;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }
  
  logout() {
    this.token = null;
    this.user = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }
}
```

## 📈 Performance Metrics

### Response Times
- **Anonymous Login**: <50ms
- **Token Validation**: <10ms
- **Token Refresh**: <30ms
- **Logout**: <20ms

### Security Metrics
- **Token Expiry**: 24 hours
- **Rate Limiting**: Per endpoint limits
- **Failed Attempts**: Logged and monitored
- **Token Rotation**: Automatic on refresh

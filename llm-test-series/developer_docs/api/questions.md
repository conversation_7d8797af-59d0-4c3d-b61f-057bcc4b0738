# 🎯 Questions API Documentation

## 📋 Overview

The Questions API handles all question generation, retrieval, and management operations. It integrates with the Enhanced AI Service to provide high-quality, dynamic question generation.

## 🔗 Base URL
```
http://localhost:5000/api/questions
```

## 🔐 Authentication
All endpoints require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt_token>
```

## 📊 API Endpoints

### 1. Generate Questions

**Endpoint:** `GET /generate`

**Description:** Generates questions using AI or fallback templates

**Parameters:**
```javascript
{
  subject: string,      // Required: Physics, Mathematics, Chemistry, Biology, etc.
  level: string,        // Required: Beginner, Intermediate, Advanced
  numQuestions: number, // Required: 1-50
  t: number            // Optional: Timestamp for cache busting
}
```

**Request Example:**
```bash
curl -X GET "http://localhost:5000/api/questions/generate?subject=Physics&level=Advanced&numQuestions=2&t=1672531200" \
  -H "Authorization: Bearer eyJhb<PERSON>ciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response Schema:**
```javascript
{
  "success": true,
  "questions": [
    {
      "id": "q_1672531200_001",
      "question": "A particle moves in a circular path...",
      "options": [
        "A) 10 m/s²",
        "B) 15 m/s²", 
        "C) 20 m/s²",
        "D) 25 m/s²"
      ],
      "correct_answer": "C",
      "explanation": "The centripetal acceleration is calculated using...",
      "difficulty": "Advanced",
      "cognitive_level": "Application",
      "keywords": ["circular motion", "acceleration", "physics"],
      "subject": "Physics",
      "level": "Advanced",
      "estimated_time": 120,
      "points": 4
    }
  ],
  "metadata": {
    "generated_at": "2023-01-01T00:00:00.000Z",
    "provider": "google",
    "generation_time": 3.2,
    "cache_hit": false
  }
}
```

**Error Responses:**
```javascript
// 400 Bad Request
{
  "success": false,
  "error": "Invalid subject parameter",
  "code": "INVALID_SUBJECT"
}

// 401 Unauthorized
{
  "success": false,
  "error": "Invalid or expired token",
  "code": "UNAUTHORIZED"
}

// 429 Too Many Requests
{
  "success": false,
  "error": "Rate limit exceeded",
  "code": "RATE_LIMIT_EXCEEDED",
  "retry_after": 60
}

// 500 Internal Server Error
{
  "success": false,
  "error": "Question generation failed",
  "code": "GENERATION_FAILED"
}
```

### 2. Get Question by ID

**Endpoint:** `GET /:questionId`

**Description:** Retrieves a specific question by its ID

**Parameters:**
- `questionId` (path): Unique question identifier

**Request Example:**
```bash
curl -X GET "http://localhost:5000/api/questions/q_1672531200_001" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**Response Schema:**
```javascript
{
  "success": true,
  "question": {
    "id": "q_1672531200_001",
    "question": "A particle moves in a circular path...",
    "options": ["A) 10 m/s²", "B) 15 m/s²", "C) 20 m/s²", "D) 25 m/s²"],
    "correct_answer": "C",
    "explanation": "The centripetal acceleration is calculated using...",
    "difficulty": "Advanced",
    "cognitive_level": "Application",
    "keywords": ["circular motion", "acceleration", "physics"],
    "subject": "Physics",
    "level": "Advanced",
    "estimated_time": 120,
    "points": 4,
    "created_at": "2023-01-01T00:00:00.000Z"
  }
}
```

### 3. Validate Answer

**Endpoint:** `POST /:questionId/validate`

**Description:** Validates a user's answer for a specific question

**Request Body:**
```javascript
{
  "answer": "C",
  "time_taken": 45
}
```

**Response Schema:**
```javascript
{
  "success": true,
  "result": {
    "correct": true,
    "user_answer": "C",
    "correct_answer": "C",
    "explanation": "The centripetal acceleration is calculated using...",
    "points_earned": 4,
    "time_taken": 45,
    "performance": "excellent"
  }
}
```

### 4. Get Question Statistics

**Endpoint:** `GET /:questionId/stats`

**Description:** Retrieves statistics for a specific question

**Response Schema:**
```javascript
{
  "success": true,
  "statistics": {
    "question_id": "q_1672531200_001",
    "total_attempts": 150,
    "correct_attempts": 89,
    "accuracy_rate": 59.3,
    "average_time": 67.5,
    "difficulty_rating": 3.8,
    "common_wrong_answers": [
      {"answer": "A", "count": 35},
      {"answer": "B", "count": 26}
    ]
  }
}
```

## 🔄 Data Flow Diagram

```mermaid
sequenceDiagram
    participant Client as 🖥️ Client
    participant API as 🚀 API Server
    participant Auth as 🔐 Auth Service
    participant AI as 🤖 Enhanced AI Service
    participant Cache as 💾 Cache
    participant DB as 🗄️ Database
    participant Gemini as 🧠 Google Gemini
    
    Client->>API: GET /questions/generate
    API->>Auth: Validate JWT Token
    Auth->>API: Token Valid
    
    API->>Cache: Check for cached questions
    Cache->>API: Cache miss
    
    API->>AI: generateQuestions(params)
    AI->>Gemini: Generate questions
    Gemini->>AI: AI response
    AI->>AI: Parse and normalize
    AI->>Cache: Store questions
    AI->>DB: Log generation event
    AI->>API: Return questions
    
    API->>Client: JSON response with questions
```

## 🎯 Question Generation Logic

```mermaid
flowchart TD
    START[📥 Request Received] --> VALIDATE[✅ Validate Parameters]
    VALIDATE --> CHECK_CACHE[💾 Check Cache]
    CHECK_CACHE -->|Hit| RETURN_CACHED[📤 Return Cached]
    CHECK_CACHE -->|Miss| CHECK_AI[🤖 Check AI Provider]
    
    CHECK_AI -->|Available| BUILD_PROMPT[📝 Build AI Prompt]
    CHECK_AI -->|Unavailable| FALLBACK[🔄 Use Fallback]
    
    BUILD_PROMPT --> CALL_AI[🧠 Call Gemini API]
    CALL_AI -->|Success| PARSE[🔧 Parse Response]
    CALL_AI -->|Failure| FALLBACK
    
    PARSE --> NORMALIZE[⚖️ Normalize Data]
    NORMALIZE --> CACHE_STORE[💾 Store in Cache]
    CACHE_STORE --> LOG_EVENT[📝 Log Event]
    
    FALLBACK --> TEMPLATE[📋 Use Templates]
    TEMPLATE --> CACHE_STORE
    
    LOG_EVENT --> RETURN[📤 Return Questions]
    RETURN_CACHED --> RETURN
    RETURN --> END[✅ Complete]
```

## 🔧 Implementation Details

### **Enhanced AI Service Integration**
```javascript
// Service call example
const questions = await enhancedAIService.generateQuestions({
  userId: req.user.id,
  subject: 'Physics',
  level: 'Advanced',
  numQuestions: 5,
  questionType: 'multiple_choice',
  previousQuestions: []
});
```

### **Caching Strategy**
```javascript
// Cache key format
const cacheKey = `questions:${subject}:${level}:${numQuestions}:${hash}`;

// Cache TTL
const CACHE_TTL = 3600; // 1 hour
```

### **Rate Limiting**
```javascript
// Rate limits per endpoint
const rateLimits = {
  '/generate': { requests: 10, window: 60 }, // 10 requests per minute
  '/:id': { requests: 100, window: 60 },     // 100 requests per minute
  '/:id/validate': { requests: 50, window: 60 } // 50 requests per minute
};
```

## 🚨 Error Handling

### **Common Error Scenarios**
1. **Invalid Parameters**: Missing or invalid subject/level
2. **AI Service Failure**: Gemini API unavailable
3. **Rate Limiting**: Too many requests
4. **Authentication**: Invalid or expired token
5. **Database Errors**: Connection or query failures

### **Fallback Mechanisms**
1. **Primary**: Google Gemini AI
2. **Secondary**: Built-in question templates
3. **Tertiary**: Cached questions from previous sessions

## 📊 Performance Metrics

### **Response Times**
- **Cache Hit**: <10ms
- **AI Generation**: 2-4 seconds
- **Template Fallback**: <100ms

### **Success Rates**
- **AI Generation**: 95%+
- **Overall Success**: 99.9%+

### **Throughput**
- **Questions/Hour**: 10,000+
- **Concurrent Requests**: 100+

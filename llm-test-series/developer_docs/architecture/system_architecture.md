# 🏗️ System Architecture

## 📊 High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        UI[Frontend UI<br/>Vanilla JS + TailwindCSS]
        PWA[Progressive Web App<br/>Offline Support]
    end
    
    subgraph "API Gateway"
        LB[Load Balancer<br/>Nginx/Cluster]
        CORS[CORS Handler]
        RATE[Rate Limiter]
        AUTH[JWT Auth]
    end
    
    subgraph "Application Layer"
        API[Express API Server<br/>Node.js Cluster]
        WS[WebSocket Server<br/>Socket.io]
        CACHE[Redis Cache<br/>Optional]
    end
    
    subgraph "Service Layer"
        QS[Question Service]
        AS[Analytics Service]
        TS[Test Service]
        US[User Service]
        AIS[Enhanced AI Service]
    end
    
    subgraph "External Services"
        GEMINI[Google Gemini<br/>AI Question Generation]
        FIREBASE[Firebase<br/>Database & Auth]
        CDN[CDN<br/>Static Assets]
    end
    
    subgraph "Data Layer"
        MOCK[Mock Database<br/>Development]
        REDIS[Redis<br/>Caching]
        LOGS[Logging Service]
    end
    
    UI --> LB
    PWA --> LB
    LB --> CORS
    CORS --> RATE
    RATE --> AUTH
    AUTH --> API
    API --> WS
    API --> CACHE
    
    API --> QS
    API --> AS
    API --> TS
    API --> US
    
    QS --> AIS
    AIS --> GEMINI
    
    QS --> FIREBASE
    AS --> FIREBASE
    TS --> FIREBASE
    US --> FIREBASE
    
    QS --> MOCK
    AS --> MOCK
    TS --> MOCK
    US --> MOCK
    
    CACHE --> REDIS
    API --> LOGS
    
    UI --> CDN
```

## 🔧 Component Breakdown

### **Frontend Architecture**
```mermaid
graph LR
    subgraph "Frontend Components"
        INDEX[index.html<br/>Main Entry Point]
        TABS[Tab System<br/>Navigation]
        EXAM[Exam Explorer]
        TEST[Mock Test Interface]
        ANALYTICS[Analytics Dashboard]
        SYLLABUS[Syllabus Tracker]
        GROUPS[Study Groups]
    end
    
    subgraph "Frontend Services"
        API_CLIENT[API Client<br/>Fetch Wrapper]
        AUTH_CLIENT[Auth Manager]
        SOCKET_CLIENT[Socket.io Client]
        STATE[State Management]
    end
    
    INDEX --> TABS
    TABS --> EXAM
    TABS --> TEST
    TABS --> ANALYTICS
    TABS --> SYLLABUS
    TABS --> GROUPS
    
    EXAM --> API_CLIENT
    TEST --> API_CLIENT
    ANALYTICS --> API_CLIENT
    
    API_CLIENT --> AUTH_CLIENT
    API_CLIENT --> SOCKET_CLIENT
    API_CLIENT --> STATE
```

### **Backend Architecture**
```mermaid
graph TB
    subgraph "Entry Point"
        MAIN[src/index.js<br/>Cluster Master]
        WORKER[Worker Processes<br/>Express Servers]
    end
    
    subgraph "Middleware Stack"
        HELMET[Security Headers]
        CORS_MW[CORS Middleware]
        RATE_MW[Rate Limiting]
        AUTH_MW[JWT Authentication]
        LOG_MW[Request Logging]
    end
    
    subgraph "Route Handlers"
        QUEST_ROUTE[/api/questions]
        AUTH_ROUTE[/api/auth]
        TEST_ROUTE[/api/test-sessions]
        ANALYTICS_ROUTE[/api/analytics]
        USER_ROUTE[/api/users]
    end
    
    subgraph "Business Logic"
        ENHANCED_AI[Enhanced AI Service]
        QUESTION_GEN[Question Generation]
        TEST_MGMT[Test Management]
        ANALYTICS_SVC[Analytics Service]
        USER_MGMT[User Management]
    end
    
    MAIN --> WORKER
    WORKER --> HELMET
    HELMET --> CORS_MW
    CORS_MW --> RATE_MW
    RATE_MW --> AUTH_MW
    AUTH_MW --> LOG_MW
    
    LOG_MW --> QUEST_ROUTE
    LOG_MW --> AUTH_ROUTE
    LOG_MW --> TEST_ROUTE
    LOG_MW --> ANALYTICS_ROUTE
    LOG_MW --> USER_ROUTE
    
    QUEST_ROUTE --> ENHANCED_AI
    QUEST_ROUTE --> QUESTION_GEN
    TEST_ROUTE --> TEST_MGMT
    ANALYTICS_ROUTE --> ANALYTICS_SVC
    USER_ROUTE --> USER_MGMT
```

## 🔄 Data Flow Architecture

### **Question Generation Flow**
```mermaid
sequenceDiagram
    participant UI as Frontend UI
    participant API as Express API
    participant AI as Enhanced AI Service
    participant GEMINI as Google Gemini
    participant CACHE as Cache Layer
    participant DB as Database
    
    UI->>API: POST /api/questions/generate
    API->>API: Validate JWT Token
    API->>AI: generateQuestions(params)
    
    alt Google AI Available
        AI->>GEMINI: Generate Questions
        GEMINI->>AI: AI Response
        AI->>AI: Parse & Normalize
    else Fallback
        AI->>AI: Use Built-in Templates
    end
    
    AI->>CACHE: Store Questions
    AI->>DB: Log Generation Event
    AI->>API: Return Questions
    API->>UI: JSON Response
```

### **Test Session Flow**
```mermaid
sequenceDiagram
    participant UI as Frontend UI
    participant API as Express API
    participant TEST as Test Service
    participant QUEST as Question Service
    participant DB as Database
    participant ANALYTICS as Analytics Service
    
    UI->>API: POST /api/test-sessions/start
    API->>TEST: createTestSession()
    TEST->>QUEST: generateQuestions()
    QUEST->>TEST: Return Questions
    TEST->>DB: Save Test Session
    TEST->>API: Return Session ID
    API->>UI: Test Session Created
    
    Note over UI: User takes test
    
    UI->>API: POST /api/test-sessions/submit
    API->>TEST: submitAnswers()
    TEST->>TEST: Calculate Score
    TEST->>DB: Save Results
    TEST->>ANALYTICS: Update Statistics
    TEST->>API: Return Results
    API->>UI: Test Results
```

## 🔒 Security Architecture

### **Authentication Flow**
```mermaid
graph LR
    subgraph "Client"
        LOGIN[Login Request]
        TOKEN[JWT Token]
        REQUESTS[API Requests]
    end

    subgraph "API Gateway"
        AUTH_EP[Auth Endpoint]
        VERIFY[Token Verification]
        PROTECTED[Protected Routes]
    end

    subgraph "Security Services"
        JWT_SVC[JWT Service]
        USER_SVC[User Service]
        RATE_LIMIT[Rate Limiter]
    end

    LOGIN --> AUTH_EP
    AUTH_EP --> JWT_SVC
    JWT_SVC --> USER_SVC
    USER_SVC --> TOKEN

    TOKEN --> VERIFY
    VERIFY --> RATE_LIMIT
    RATE_LIMIT --> PROTECTED
    PROTECTED --> REQUESTS
```

## 📈 Scalability Design

### **Horizontal Scaling**
```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx/HAProxy]
    end
    
    subgraph "Application Tier"
        APP1[Node.js Instance 1]
        APP2[Node.js Instance 2]
        APP3[Node.js Instance N]
    end
    
    subgraph "Caching Tier"
        REDIS_CLUSTER[Redis Cluster]
    end
    
    subgraph "Database Tier"
        DB_PRIMARY[Primary Database]
        DB_REPLICA[Read Replicas]
    end
    
    subgraph "External Services"
        AI_POOL[AI Service Pool]
    end
    
    LB --> APP1
    LB --> APP2
    LB --> APP3
    
    APP1 --> REDIS_CLUSTER
    APP2 --> REDIS_CLUSTER
    APP3 --> REDIS_CLUSTER
    
    APP1 --> DB_PRIMARY
    APP2 --> DB_REPLICA
    APP3 --> DB_REPLICA
    
    APP1 --> AI_POOL
    APP2 --> AI_POOL
    APP3 --> AI_POOL
```

## 🔧 Technology Stack

### **Frontend Stack**
- **Core**: Vanilla JavaScript (ES6+)
- **Styling**: TailwindCSS
- **Build**: Static serving
- **Communication**: Fetch API, Socket.io
- **PWA**: Service Workers, Manifest

### **Backend Stack**
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Clustering**: Node.js Cluster
- **Authentication**: JWT
- **WebSockets**: Socket.io
- **Security**: Helmet, CORS, Rate Limiting

### **AI Integration**
- **Primary**: Google Gemini 1.5 Flash
- **Fallback**: Built-in question templates
- **Processing**: Custom parsing and normalization

### **Database & Caching**
- **Production**: Firebase Firestore
- **Development**: Mock database
- **Caching**: Redis (optional), In-memory
- **Logging**: Custom logging service

## 📊 Performance Characteristics

### **Response Times**
- **API Endpoints**: <100ms
- **Question Generation**: 2-4 seconds
- **Database Queries**: <50ms
- **Cache Hits**: <10ms

### **Throughput**
- **Concurrent Users**: 1000+
- **Requests/Second**: 500+
- **Questions/Hour**: 10,000+

### **Resource Usage**
- **Memory**: ~20MB per worker
- **CPU**: <30% under normal load
- **Network**: Optimized payloads

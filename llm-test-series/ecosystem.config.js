module.exports = {
  apps: [{
    name: 'mock-test-system',
    script: './server/cluster.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true,
    merge_logs: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    node_args: '--max-old-space-size=4096',
    exp_backoff_restart_delay: 100
  }],

  deploy: {
    production: {
      user: 'ubuntu',
      host: process.env.PROD_HOST,
      ref: 'origin/main',
      repo: '**************:yourusername/mock-test-system.git',
      path: '/var/www/mock-test-system',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      env: {
        NODE_ENV: 'production'
      }
    }
  }
};

{"name": "llm-test-series", "version": "1.0.0", "description": "AI-Powered Adaptive Learning & Test Preparation Platform", "private": true, "workspaces": ["frontend", "backend", "ai-services/*"], "scripts": {"setup": "node setup.js", "install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && npm run dev", "test:server": "cd backend && npm run test:server", "test:server:dev": "cd backend && npm run test:server:dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "deploy": "npm run build && npm run deploy:backend", "deploy:backend": "cd backend && npm run deploy"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.19.0", "npm": ">=8.0.0"}, "keywords": ["education", "ai", "adaptive-learning", "test-preparation", "gamification", "personalization"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0"}}
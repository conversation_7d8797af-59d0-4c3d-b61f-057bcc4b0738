{"compilerOptions": {"target": "es2020", "module": "es2020", "moduleResolution": "node", "esModuleInterop": true, "allowJs": true, "checkJs": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["server/*"], "@public/*": ["public/*"], "@tests/*": ["tests/*"]}, "outDir": "dist", "rootDir": "."}, "include": ["server/**/*", "public/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "coverage"]}
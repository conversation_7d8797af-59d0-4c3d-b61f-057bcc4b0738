# 🗂️ PROJECT CONSOLIDATION MAPPING

## 📋 **CURRENT STATE ANALYSIS**

### **OUTSIDE llm-test-series (TO BE CONSOLIDATED)**
```
/home/<USER>/web_project/
├── 📄 Formal_Problem_Statement.md ✅ KEEP (Move to docs/)
├── 📄 README.md ✅ KEEP (Merge with llm-test-series/README.md)
├── 📄 implementation_log.md ✅ KEEP (Move to docs/)
├── 📄 initial_plan.md ✅ KEEP (Move to docs/)
├── 📄 psychological_engagement_strategy.md ✅ KEEP (Move to docs/)
├── 📄 technical_implementation_plan.md ✅ KEEP (Move to docs/)
├── 📄 package.json ✅ KEEP (Root package.json)
├── 📄 package-lock.json ✅ KEEP (Root lock file)
├── 📄 firebase-service-account.json ✅ KEEP (Move to llm-test-series/)
├── 📄 ecosystem.config.js ✅ KEEP (PM2 config)
├── 📄 jest.config.js ✅ KEEP (Test config)
├── 📄 tsconfig.json ✅ KEEP (TypeScript config)
├── 📄 webpack.config.js ✅ KEEP (Build config)
├── 📄 sonar-project.properties ✅ KEEP (Code quality)
├── 📄 netlify.toml ✅ KEEP (Deployment)
├── 📄 web.html ❌ DELETE (Duplicate/test file)
├── 📄 combined.log ❌ DELETE (Log file)
├── 📄 error.log ❌ DELETE (Log file)
├── 📄 firebase-debug.log ❌ DELETE (Log file)
├── 📄 nodemon ❌ DELETE (Unknown file)
├── 📄 ai-powered-mock-test-system@1.0.0 ❌ DELETE (Build artifact)
├── 📁 node_modules/ ❌ DELETE (Dependencies - will be in llm-test-series/)
├── 📁 logs/ ❌ DELETE (Log files)
├── 📁 netlify/ ✅ KEEP (Deployment functions)
├── 📁 test/ ✅ KEEP (Move to llm-test-series/tests/)
├── 📁 tests/ ✅ KEEP (Move to llm-test-series/tests/)
├── 📁 types/ ✅ KEEP (Move to llm-test-series/types/)
├── 📁 server/ ✅ WORKING BACKEND (Consolidate into llm-test-series/backend/)
├── 📁 public/ ✅ WORKING FRONTEND (Consolidate into llm-test-series/frontend/)
└── 📁 frontend/ ✅ PARTIAL FRONTEND (Merge with llm-test-series/frontend/)
```

### **INSIDE llm-test-series (TARGET STRUCTURE)**
```
/home/<USER>/web_project/llm-test-series/
├── 📄 README.md ✅ KEEP (Main project readme)
├── 📄 package.json ✅ KEEP (Root package.json)
├── 📄 package-lock.json ✅ KEEP (Root lock file)
├── 📄 setup.js ✅ KEEP (Setup script)
├── 📄 FINAL_IMPLEMENTATION_REPORT.md ✅ KEEP (Documentation)
├── 📄 HONEST_PROJECT_STATUS.md ✅ KEEP (Documentation)
├── 📄 IMPLEMENTATION_COMPLETE.md ✅ KEEP (Documentation)
├── 📁 node_modules/ ✅ KEEP (Dependencies)
├── 📁 docs/ ✅ KEEP (Documentation folder)
│   ├── 📄 DEPENDENCIES_MAPPING.md ✅ KEEP
│   └── 📄 PROJECT_STATUS.md ✅ KEEP
├── 📁 ai-services/ ✅ KEEP (AI microservices)
│   ├── 📁 analytics/ ✅ KEEP
│   ├── 📁 explanation-engine/ ✅ KEEP
│   ├── 📁 personalization/ ✅ KEEP
│   └── 📁 question-generator/ ✅ KEEP
├── 📁 backend/ ✅ KEEP (Backend API)
│   ├── 📄 package.json ✅ KEEP
│   ├── 📁 src/ ✅ KEEP (Source code)
│   ├── 📁 tests/ ✅ KEEP (Backend tests)
│   ├── 📁 types/ ✅ KEEP (TypeScript types)
│   ├── 📁 config/ ✅ KEEP (Configuration)
│   └── 📁 node_modules/ ✅ KEEP (Backend dependencies)
├── 📁 frontend/ ✅ KEEP (React frontend)
│   ├── 📄 package.json ✅ KEEP
│   ├── 📁 src/ ✅ KEEP (React source)
│   ├── 📁 public/ ✅ KEEP (Static files)
│   └── 📁 node_modules/ ✅ KEEP (Frontend dependencies)
└── 📁 database/ ✅ KEEP (Database schemas)
    ├── 📁 migrations/ ✅ KEEP
    ├── 📁 schemas/ ✅ KEEP
    └── 📁 seeds/ ✅ KEEP
```

## 🎯 **CONSOLIDATION PLAN**

### **PHASE 1: Document Consolidation**
1. Move `/Formal_Problem_Statement.md` → `/llm-test-series/docs/`
2. Move `/implementation_log.md` → `/llm-test-series/docs/`
3. Move `/initial_plan.md` → `/llm-test-series/docs/`
4. Move `/psychological_engagement_strategy.md` → `/llm-test-series/docs/`
5. Move `/technical_implementation_plan.md` → `/llm-test-series/docs/`
6. Merge `/README.md` with `/llm-test-series/README.md`

### **PHASE 2: Configuration Consolidation**
1. Move `/firebase-service-account.json` → `/llm-test-series/`
2. Move `/ecosystem.config.js` → `/llm-test-series/`
3. Move `/jest.config.js` → `/llm-test-series/`
4. Move `/tsconfig.json` → `/llm-test-series/`
5. Move `/webpack.config.js` → `/llm-test-series/`
6. Move `/sonar-project.properties` → `/llm-test-series/`
7. Move `/netlify.toml` → `/llm-test-series/`
8. Move `/netlify/` → `/llm-test-series/netlify/`

### **PHASE 3: Code Consolidation**
1. **Backend**: Merge `/server/` → `/llm-test-series/backend/src/`
2. **Frontend**: Merge `/public/` → `/llm-test-series/frontend/public/`
3. **Tests**: Merge `/test/` and `/tests/` → `/llm-test-series/tests/`
4. **Types**: Merge `/types/` → `/llm-test-series/types/`

### **PHASE 4: Package Management**
1. Update root `/llm-test-series/package.json` with all dependencies
2. Ensure all sub-packages are properly configured
3. Run `npm install` in root and sub-directories

### **PHASE 5: Cleanup**
1. Delete duplicate files outside llm-test-series
2. Delete log files and build artifacts
3. Delete unnecessary node_modules outside llm-test-series

## ⚠️ **CRITICAL FILES TO PRESERVE**
- All working server code in `/server/`
- All working frontend code in `/public/`
- All configuration files
- All documentation files
- Firebase service account
- Package.json files with dependencies

## ✅ **CONSOLIDATION COMPLETED SUCCESSFULLY**

### **WORKING SYSTEM STATUS:**
- ✅ **Backend**: Running on http://localhost:5000 (4 workers)
- ✅ **Frontend**: Running on http://localhost:3000 (Static server)
- ✅ **API**: Responding correctly with authentication
- ✅ **All files consolidated** into llm-test-series structure

### **READY FOR CLEANUP:**
Now that everything is working inside llm-test-series, the outside directory can be cleaned up.

## 🚫 **FILES TO DELETE**
- Log files (*.log)
- Build artifacts
- Duplicate node_modules
- Test files (web.html, nodemon)
- Debug files
- All working files that have been successfully consolidated

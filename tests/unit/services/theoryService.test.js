import theoryService from '../../../server/services/theoryService.js';
import redisService from '../../../server/services/redisService.js';
import loggingService from '../../../server/services/loggingService.js';
import { getDb } from '../../../server/firebase.js';

jest.mock('../../../server/services/redisService.js');
jest.mock('../../../server/services/loggingService.js');

describe('TheoryService', () => {
    const mockDb = {
        collection: jest.fn(() => ({
            doc: jest.fn(),
            where: jest.fn(),
            get: jest.fn(),
            add: jest.fn()
        }))
    };

    beforeAll(() => {
        // Mock Firestore
        jest.spyOn(getDb, 'getDb').mockReturnValue(mockDb);
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getTheoryExplanation', () => {
        it('should return cached explanation if available', async () => {
            const topic = 'algebra';
            const cachedExplanation = {
                content: 'Cached algebra explanation',
                timestamp: Date.now()
            };

            redisService.get.mockResolvedValue(JSON.stringify(cachedExplanation));

            const result = await theoryService.getTheoryExplanation(topic);

            expect(result).toEqual(cachedExplanation.content);
            expect(redisService.get).toHaveBeenCalledWith(`theory:${topic}`);
            expect(loggingService.logInfo).toHaveBeenCalledWith('Theory explanation served from cache', {
                topic,
                source: 'cache'
            });
        });

        it('should generate new explanation if cache is expired', async () => {
            const topic = 'algebra';
            const oldExplanation = {
                content: 'Old algebra explanation',
                timestamp: Date.now() - (25 * 60 * 60 * 1000) // 25 hours old
            };

            redisService.get.mockResolvedValue(JSON.stringify(oldExplanation));
            
            const newExplanation = 'New algebra explanation';
            mockDb.collection().add.mockResolvedValue({ id: 'new-explanation' });
            jest.spyOn(theoryService, 'generateExplanation').mockResolvedValue(newExplanation);

            const result = await theoryService.getTheoryExplanation(topic);

            expect(result).toEqual(newExplanation);
            expect(redisService.set).toHaveBeenCalledWith(
                `theory:${topic}`,
                JSON.stringify({
                    content: newExplanation,
                    timestamp: expect.any(Number)
                }),
                24 * 60 * 60 // 24 hours
            );
        });
    });

    describe('getRelatedConcepts', () => {
        it('should return related concepts from database', async () => {
            const concept = 'quadratic_equations';
            const mockRelatedConcepts = [
                { concept: 'polynomial_functions', score: 0.9 },
                { concept: 'factorization', score: 0.8 }
            ];

            mockDb.collection().where().get.mockResolvedValue({
                docs: mockRelatedConcepts.map(rc => ({
                    data: () => rc
                }))
            });

            const result = await theoryService.getRelatedConcepts(concept);

            expect(result).toEqual(mockRelatedConcepts);
            expect(mockDb.collection).toHaveBeenCalledWith('related_concepts');
            expect(loggingService.logInfo).toHaveBeenCalledWith('Retrieved related concepts', {
                concept,
                count: mockRelatedConcepts.length
            });
        });
    });

    describe('generateExplanation', () => {
        it('should generate explanation with appropriate difficulty level', async () => {
            const topic = 'calculus';
            const userLevel = 'advanced';
            const mockLLMResponse = 'Advanced calculus explanation with complex concepts';

            jest.spyOn(theoryService, 'callLLM').mockResolvedValue(mockLLMResponse);

            const result = await theoryService.generateExplanation(topic, userLevel);

            expect(result).toEqual(mockLLMResponse);
            expect(theoryService.callLLM).toHaveBeenCalledWith(
                expect.stringContaining(topic),
                expect.objectContaining({ difficulty: userLevel })
            );
            expect(loggingService.logInfo).toHaveBeenCalledWith('Generated new explanation', {
                topic,
                difficulty: userLevel
            });
        });

        it('should handle LLM errors gracefully', async () => {
            const topic = 'physics';
            const error = new Error('LLM API error');

            jest.spyOn(theoryService, 'callLLM').mockRejectedValue(error);

            await expect(theoryService.generateExplanation(topic)).rejects.toThrow('Failed to generate explanation');
            expect(loggingService.logError).toHaveBeenCalledWith('LLM explanation generation failed', {
                topic,
                error
            });
        });
    });
});

import { getDb } from '../firebase.js';
import { syllabusSchema } from '../models/syllabusSchema.js';
import Ajv from 'ajv';
import loggingService from './loggingService.js';
import questionGenerationService from './questionGenerationService.js';

class SyllabusService {
    constructor() {
        this.ajv = new Ajv();
        this.validateSyllabus = this.ajv.compile(syllabusSchema);
    }

    /**
     * Validate and ingest a new syllabus
     * @param {Object} syllabusData The syllabus data to ingest
     * @returns {Promise<string>} The ID of the ingested syllabus
     */
    async ingestSyllabus(syllabusData) {
        try {
            const db = await getDb();
            // Validate syllabus data against schema
            const valid = this.validateSyllabus(syllabusData);
            if (!valid) {
                throw new Error(`Invalid syllabus data: ${JSON.stringify(this.validateSyllabus.errors)}`);
            }

            // Check if syllabus already exists
            const existingSyllabus = await db.collection('syllabi')
                .where('syllabus_id', '==', syllabusData.syllabus_id)
                .get();

            if (!existingSyllabus.empty) {
                throw new Error('Syllabus with this ID already exists');
            }

            // Save to Firestore
            await db.collection('syllabi').add(syllabusData);

            // Trigger question pre-generation for each topic
            this.preGenerateQuestions(syllabusData);

            return syllabusData.syllabus_id;
        } catch (error) {
            loggingService.logError('Failed to ingest syllabus', { error });
            throw error;
        }
    }

    /**
     * Pre-generate questions for each topic in the syllabus
     * @param {Object} syllabus The syllabus data
     */
    async preGenerateQuestions(syllabus) {
        try {
            for (const unit of syllabus.units) {
                for (const topic of unit.topics) {
                    // Generate questions in batches to avoid overwhelming the LLM API
                    const batchSize = 10;
                    const totalQuestions = 100;
                    const batches = Math.ceil(totalQuestions / batchSize);

                    for (let i = 0; i < batches; i++) {
                        await questionGenerationService.generateSyllabusBasedQuestions({
                            syllabusId: syllabus.syllabus_id,
                            unitId: unit.unit_id,
                            topicId: topic.topic_id,
                            syllabusName: syllabus.name,
                            unitName: unit.unit_name,
                            topicName: topic.topic_name,
                            numQuestions: Math.min(batchSize, totalQuestions - (i * batchSize))
                        });

                        // Add a delay between batches to respect rate limits
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }
        } catch (error) {
            loggingService.logError('Failed to pre-generate questions', { error });
            // Don't throw - we want to continue even if some questions fail to generate
        }
    }

    /**
     * Get all syllabi
     * @returns {Promise<Array>} List of all syllabi
     */
    async getAllSyllabi() {
        try {
            const db = await getDb();
            const snapshot = await db.collection('syllabi').get();
            return snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        } catch (error) {
            loggingService.logError('Failed to get syllabi', { error });
            throw error;
        }
    }

    /**
     * Get a specific syllabus by ID
     * @param {string} syllabusId The syllabus ID
     * @returns {Promise<Object>} The syllabus data
     */
    async getSyllabus(syllabusId) {
        try {
            const db = await getDb();
            const snapshot = await db.collection('syllabi')
                .where('syllabus_id', '==', syllabusId)
                .get();

            if (snapshot.empty) {
                throw new Error('Syllabus not found');
            }

            return {
                id: snapshot.docs[0].id,
                ...snapshot.docs[0].data()
            };
        } catch (error) {
            loggingService.logError('Failed to get syllabus', { error });
            throw error;
        }
    }

    /**
     * Update a syllabus
     * @param {string} syllabusId The syllabus ID
     * @param {Object} updates The updates to apply
     * @returns {Promise<void>}
     */
    async updateSyllabus(syllabusId, updates) {
        try {
            const db = await getDb();
            const snapshot = await db.collection('syllabi')
                .where('syllabus_id', '==', syllabusId)
                .get();

            if (snapshot.empty) {
                throw new Error('Syllabus not found');
            }

            // Validate updated data
            const updatedData = { ...snapshot.docs[0].data(), ...updates };
            const valid = this.validateSyllabus(updatedData);
            if (!valid) {
                throw new Error(`Invalid syllabus data: ${JSON.stringify(this.validateSyllabus.errors)}`);
            }

            await snapshot.docs[0].ref.update(updates);
        } catch (error) {
            loggingService.logError('Failed to update syllabus', { error });
            throw error;
        }
    }

    /**
     * Delete a syllabus
     * @param {string} syllabusId The syllabus ID
     * @returns {Promise<void>}
     */
    async deleteSyllabus(syllabusId) {
        try {
            const db = await getDb();
            const snapshot = await db.collection('syllabi')
                .where('syllabus_id', '==', syllabusId)
                .get();

            if (snapshot.empty) {
                throw new Error('Syllabus not found');
            }

            await snapshot.docs[0].ref.delete();
        } catch (error) {
            loggingService.logError('Failed to delete syllabus', { error });
            throw error;
        }
    }
}

const syllabusService = new SyllabusService();
export default syllabusService;

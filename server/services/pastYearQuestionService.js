import { db } from '../config/firebase.js';
import { pastYearQuestionSchema } from '../models/pastYearQuestionSchema.js';
import Ajv from 'ajv';
import loggingService from './loggingService.js';
import { v4 as uuidv4 } from 'uuid';

class PastYearQuestionService {
    constructor() {
        this.ajv = new Ajv();
        this.validateQuestion = this.ajv.compile(pastYearQuestionSchema);
    }

    /**
     * Add a single past year question
     * @param {Object} questionData Question data
     * @returns {Promise<string>} Question ID
     */
    async addQuestion(questionData) {
        try {
            // Add timestamps and ID
            questionData.created_at = new Date().toISOString();
            questionData.updated_at = questionData.created_at;
            questionData.question_id = uuidv4();
            questionData.usage_count = 0;

            // Validate question data
            const valid = this.validateQuestion(questionData);
            if (!valid) {
                throw new Error(`Invalid question data: ${JSON.stringify(this.validateQuestion.errors)}`);
            }

            // Save to Firestore
            await db.collection('past_year_questions').doc(questionData.question_id).set(questionData);

            // Index topics for faster retrieval
            if (questionData.topics && questionData.topics.length > 0) {
                await this.indexQuestionTopics(questionData.question_id, questionData.topics);
            }

            return questionData.question_id;
        } catch (error) {
            loggingService.logError('Failed to add past year question', { error });
            throw error;
        }
    }

    /**
     * Bulk upload past year questions
     * @param {Array} questions Array of question data
     * @returns {Promise<Object>} Upload results
     */
    async bulkUpload(questions) {
        const results = {
            success: 0,
            failed: 0,
            errors: []
        };

        const batch = db.batch();
        const topicsToIndex = [];

        try {
            for (const question of questions) {
                try {
                    // Add timestamps and ID
                    question.created_at = new Date().toISOString();
                    question.updated_at = question.created_at;
                    question.question_id = uuidv4();
                    question.usage_count = 0;

                    // Validate question
                    const valid = this.validateQuestion(question);
                    if (!valid) {
                        throw new Error(`Invalid question data: ${JSON.stringify(this.validateQuestion.errors)}`);
                    }

                    // Add to batch
                    const docRef = db.collection('past_year_questions').doc(question.question_id);
                    batch.set(docRef, question);

                    // Track topics for indexing
                    if (question.topics && question.topics.length > 0) {
                        topicsToIndex.push({
                            questionId: question.question_id,
                            topics: question.topics
                        });
                    }

                    results.success++;
                } catch (error) {
                    results.failed++;
                    results.errors.push({
                        question: question.question_text?.substring(0, 50) + '...',
                        error: error.message
                    });
                }
            }

            // Commit the batch
            await batch.commit();

            // Index topics in batches
            for (const item of topicsToIndex) {
                await this.indexQuestionTopics(item.questionId, item.topics);
            }

            return results;
        } catch (error) {
            loggingService.logError('Failed to bulk upload past year questions', { error });
            throw error;
        }
    }

    /**
     * Index question topics for faster retrieval
     * @param {string} questionId Question ID
     * @param {Array} topics Array of topic IDs
     */
    async indexQuestionTopics(questionId, topics) {
        const batch = db.batch();

        try {
            for (const topic of topics) {
                const indexRef = db.collection('topic_question_index')
                    .doc(`${topic}_${questionId}`);
                
                batch.set(indexRef, {
                    topic_id: topic,
                    question_id: questionId,
                    question_type: 'past_year',
                    created_at: new Date().toISOString()
                });
            }

            await batch.commit();
        } catch (error) {
            loggingService.logError('Failed to index question topics', { error });
            throw error;
        }
    }

    /**
     * Get questions by topic with smart prioritization
     * @param {string} topicId Topic ID
     * @param {Object} options Query options
     * @returns {Promise<Array>} Prioritized questions
     */
    async getQuestionsByTopic(topicId, options = {}) {
        try {
            // Get question IDs from index
            const indexSnapshot = await db.collection('topic_question_index')
                .where('topic_id', '==', topicId)
                .where('question_type', '==', 'past_year')
                .get();

            const questionIds = indexSnapshot.docs.map(doc => doc.data().question_id);

            if (questionIds.length === 0) {
                return [];
            }

            // Get full question data
            const questions = await Promise.all(
                questionIds.map(id => 
                    db.collection('past_year_questions')
                        .doc(id)
                        .get()
                        .then(doc => ({
                            id: doc.id,
                            ...doc.data()
                        }))
                )
            );

            // Apply prioritization
            return this.prioritizeQuestions(questions, options);
        } catch (error) {
            loggingService.logError('Failed to get questions by topic', { error });
            throw error;
        }
    }

    /**
     * Smart prioritization of questions
     * @param {Array} questions Array of questions
     * @param {Object} options Prioritization options
     * @returns {Array} Prioritized questions
     */
    prioritizeQuestions(questions, options = {}) {
        const {
            preferredDifficulty,
            recentYearsWeight = 0.4,
            unusedWeight = 0.3,
            difficultyWeight = 0.3,
            maxAge = 30 // days
        } = options;

        const currentYear = new Date().getFullYear();
        const now = new Date();

        return questions
            .map(question => {
                let score = 0;

                // Recent years score (0-1)
                const yearDiff = currentYear - question.year;
                const yearScore = Math.max(0, 1 - (yearDiff / 10)); // Last 10 years get higher scores
                score += yearScore * recentYearsWeight;

                // Usage score (0-1)
                const lastUsed = question.last_used ? new Date(question.last_used) : null;
                const daysSinceUsed = lastUsed ? 
                    Math.floor((now - lastUsed) / (1000 * 60 * 60 * 24)) : maxAge;
                const usageScore = Math.min(1, daysSinceUsed / maxAge) * (1 - (question.usage_count / 10));
                score += usageScore * unusedWeight;

                // Difficulty score (0-1)
                if (preferredDifficulty && question.difficulty) {
                    const difficultyMap = { easy: 0, medium: 1, hard: 2, expert: 3 };
                    const diffDiff = Math.abs(
                        difficultyMap[preferredDifficulty] - difficultyMap[question.difficulty]
                    );
                    const difficultyScore = 1 - (diffDiff / 3);
                    score += difficultyScore * difficultyWeight;
                }

                return { ...question, priorityScore: score };
            })
            .sort((a, b) => b.priorityScore - a.priorityScore);
    }

    /**
     * Update question usage statistics
     * @param {string} questionId Question ID
     */
    async updateUsageStats(questionId) {
        try {
            const docRef = db.collection('past_year_questions').doc(questionId);
            await docRef.update({
                usage_count: db.FieldValue.increment(1),
                last_used: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });
        } catch (error) {
            loggingService.logError('Failed to update question usage stats', { error });
            throw error;
        }
    }

    /**
     * Get question statistics
     * @returns {Promise<Object>} Statistics
     */
    async getStatistics() {
        try {
            const snapshot = await db.collection('past_year_questions').get();
            const questions = snapshot.docs.map(doc => doc.data());

            const stats = {
                total: questions.length,
                byYear: {},
                byDifficulty: {
                    easy: 0,
                    medium: 0,
                    hard: 0,
                    expert: 0
                },
                verifiedCount: 0,
                averageUsage: 0
            };

            let totalUsage = 0;
            questions.forEach(q => {
                // Count by year
                stats.byYear[q.year] = (stats.byYear[q.year] || 0) + 1;

                // Count by difficulty
                if (q.difficulty) {
                    stats.byDifficulty[q.difficulty]++;
                }

                // Count verified
                if (q.verified) {
                    stats.verifiedCount++;
                }

                // Sum usage
                totalUsage += q.usage_count || 0;
            });

            stats.averageUsage = totalUsage / questions.length;

            return stats;
        } catch (error) {
            loggingService.logError('Failed to get question statistics', { error });
            throw error;
        }
    }
}

const pastYearQuestionService = new PastYearQuestionService();
export default pastYearQuestionService;

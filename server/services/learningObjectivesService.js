import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';
import cacheService from './cacheService.js';

class LearningObjectivesService {
  constructor() {
    this.db = getDb();
    this.objectivesCache = new Map();
  }

  // Create a new learning objective
  async createObjective(objective) {
    try {
      const objectiveRef = this.db.collection('learningObjectives').doc();
      await objectiveRef.set({
        ...objective,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Clear cache for this syllabus
      cacheService.delete(`objectives_${objective.syllabusId}`);

      return objectiveRef.id;
    } catch (error) {
      loggingService.logError('Failed to create learning objective', { error });
      throw error;
    }
  }

  // Get all learning objectives for a syllabus
  async getObjectivesForSyllabus(syllabusId) {
    try {
      // Check cache first
      const cached = cacheService.get(`objectives_${syllabusId}`);
      if (cached) return cached;

      const snapshot = await this.db.collection('learningObjectives')
        .where('syllabusId', '==', syllabusId)
        .get();

      const objectives = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Cache the results
      cacheService.set(`objectives_${syllabusId}`, objectives);

      return objectives;
    } catch (error) {
      loggingService.logError('Failed to get learning objectives', { error });
      throw error;
    }
  }

  // Update a learning objective
  async updateObjective(objectiveId, updates) {
    try {
      const objectiveRef = this.db.collection('learningObjectives').doc(objectiveId);
      const objective = await objectiveRef.get();

      if (!objective.exists) {
        throw new Error('Learning objective not found');
      }

      await objectiveRef.update({
        ...updates,
        updatedAt: new Date()
      });

      // Clear cache for this syllabus
      const objectiveData = objective.data();
      cacheService.delete(`objectives_${objectiveData.syllabusId}`);

      return true;
    } catch (error) {
      loggingService.logError('Failed to update learning objective', { error });
      throw error;
    }
  }

  // Delete a learning objective
  async deleteObjective(objectiveId) {
    try {
      const objectiveRef = this.db.collection('learningObjectives').doc(objectiveId);
      const objective = await objectiveRef.get();

      if (!objective.exists) {
        throw new Error('Learning objective not found');
      }

      // Clear cache before deletion
      const objectiveData = objective.data();
      cacheService.delete(`objectives_${objectiveData.syllabusId}`);

      await objectiveRef.delete();
      return true;
    } catch (error) {
      loggingService.logError('Failed to delete learning objective', { error });
      throw error;
    }
  }

  // Map learning objectives to topics
  async mapObjectivesToTopic(topicId, objectiveIds) {
    try {
      const batch = this.db.batch();
      const topicRef = this.db.collection('syllabusTopics').doc(topicId);

      // Get current topic data
      const topicDoc = await topicRef.get();
      if (!topicDoc.exists) {
        throw new Error('Topic not found');
      }

      // Update topic with objectives
      batch.update(topicRef, {
        learningObjectiveIds: objectiveIds,
        updatedAt: new Date()
      });

      // Update each objective with this topic
      for (const objectiveId of objectiveIds) {
        const objectiveRef = this.db.collection('learningObjectives').doc(objectiveId);
        batch.update(objectiveRef, {
          topicIds: this.db.FieldValue.arrayUnion(topicId),
          updatedAt: new Date()
        });
      }

      await batch.commit();

      // Clear caches
      const topicData = topicDoc.data();
      cacheService.delete(`objectives_${topicData.syllabusId}`);
      cacheService.delete(`topic_${topicId}`);

      return true;
    } catch (error) {
      loggingService.logError('Failed to map objectives to topic', { error });
      throw error;
    }
  }

  // Track progress for a learning objective
  async trackProgress(userId, objectiveId, progress) {
    try {
      const progressRef = this.db.collection('objectiveProgress').doc();
      await progressRef.set({
        userId,
        objectiveId,
        progress, // 0-100
        timestamp: new Date()
      });

      // Update user's overall progress
      await this.updateUserProgress(userId, objectiveId, progress);

      return true;
    } catch (error) {
      loggingService.logError('Failed to track objective progress', { error });
      throw error;
    }
  }

  // Get progress for all objectives for a user
  async getUserProgress(userId, syllabusId) {
    try {
      const objectives = await this.getObjectivesForSyllabus(syllabusId);
      const progressPromises = objectives.map(async objective => {
        const snapshot = await this.db.collection('objectiveProgress')
          .where('userId', '==', userId)
          .where('objectiveId', '==', objective.id)
          .orderBy('timestamp', 'desc')
          .limit(1)
          .get();

        const latestProgress = snapshot.empty ? 0 : snapshot.docs[0].data().progress;

        return {
          objectiveId: objective.id,
          name: objective.name,
          description: objective.description,
          progress: latestProgress
        };
      });

      return Promise.all(progressPromises);
    } catch (error) {
      loggingService.logError('Failed to get user progress', { error });
      throw error;
    }
  }

  // Private: Update user's overall progress
  async updateUserProgress(userId, objectiveId, progress) {
    try {
      const userProgressRef = this.db.collection('userProgress')
        .doc(`${userId}_${objectiveId}`);

      await userProgressRef.set({
        userId,
        objectiveId,
        latestProgress: progress,
        updatedAt: new Date()
      }, { merge: true });
    } catch (error) {
      loggingService.logError('Failed to update user progress', { error });
      // Don't throw here as this is a background update
    }
  }
}

export default new LearningObjectivesService();

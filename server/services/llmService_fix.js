import dotenv from 'dotenv';
import loggingService from './loggingService.js';

dotenv.config();

class LLMService {
  constructor() {
    this.apiKey = process.env.LLM_API_KEY;
    this.defaultModel = 'gpt-4';
    this.fallbackModel = 'gemini-pro';
  }

  async generateQuestions(params) {
    try {
      const { subject, level, numQuestions = 5, testType = 'general', syllabusId = null } = params;
      
      // For now, return mock questions to get the server running
      return this.getMockQuestions(subject, level, numQuestions);
    } catch (error) {
      loggingService.logError(error, { operation: 'generateQuestions' });
      return this.getMockQuestions('General', 'medium', 5);
    }
  }

  getMockQuestions(subject, level, numQuestions) {
    const mockQuestions = [];
    
    for (let i = 1; i <= numQuestions; i++) {
      mockQuestions.push({
        id: `mock_${i}`,
        question: `Sample ${subject} question ${i} at ${level} level?`,
        options: [
          `A) Option A for question ${i}`,
          `B) Option B for question ${i}`,
          `C) Option C for question ${i}`,
          `D) Option D for question ${i}`
        ],
        correctAnswer: 'A',
        explanation: `This is a sample explanation for question ${i} about ${subject}.`,
        difficulty: level,
        subject: subject,
        keywords: [subject.toLowerCase(), level],
        sourceTag: `Mock Generated - ${subject}`,
        cognitiveLevel: 'application',
        lastUpdated: new Date().toISOString()
      });
    }
    
    return mockQuestions;
  }

  async generateTheoryExplanation(topic, questionDetails = {}, syllabusId = null) {
    try {
      // Return mock theory explanation
      return {
        content: `This is a comprehensive explanation about ${topic}. It covers the fundamental concepts and principles related to the topic.`,
        references: ['Mock Reference 1', 'Mock Reference 2'],
        keyPoints: [
          `Key concept 1 about ${topic}`,
          `Key concept 2 about ${topic}`,
          `Key concept 3 about ${topic}`
        ]
      };
    } catch (error) {
      loggingService.logError(error, { operation: 'generateTheoryExplanation' });
      return {
        content: 'Theory explanation not available at the moment.',
        references: [],
        keyPoints: []
      };
    }
  }

  async evaluateShortAnswer(question, userAnswer, correctAnswer) {
    try {
      // Simple mock evaluation
      const similarity = userAnswer.toLowerCase().includes(correctAnswer.toLowerCase()) ? 0.8 : 0.3;
      
      return {
        score: similarity,
        feedback: similarity > 0.7 ? 'Good answer!' : 'Your answer could be improved.',
        suggestions: ['Consider adding more detail', 'Review the key concepts']
      };
    } catch (error) {
      loggingService.logError(error, { operation: 'evaluateShortAnswer' });
      return {
        score: 0.5,
        feedback: 'Unable to evaluate answer at the moment.',
        suggestions: []
      };
    }
  }
}

export default new LLMService();
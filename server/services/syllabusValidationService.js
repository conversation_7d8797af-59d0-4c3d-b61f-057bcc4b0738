import loggingService from './loggingService.js';

class SyllabusValidationService {
    constructor() {
        this.loggingService = loggingService;
    }

    /**
     * Validate a syllabus structure
     * @param {Object} syllabus - The syllabus to validate
     * @returns {Object} - Validation results with errors and warnings
     */
    validateSyllabus(syllabus) {
        const results = {
            errors: [],
            warnings: []
        };

        // Required fields
        if (!syllabus.name) {
            results.errors.push('Name is required');
        }
        if (!syllabus.subject) {
            results.errors.push('Subject is required');
        }
        if (!syllabus.level || !['high_school', 'undergraduate', 'graduate'].includes(syllabus.level)) {
            results.errors.push('Invalid level');
        }

        // Units validation
        if (!Array.isArray(syllabus.units)) {
            results.errors.push('Units must be an array');
        } else {
            syllabus.units.forEach((unit, index) => {
                this.validateUnit(unit, index, results);
            });
        }

        return results;
    }

    /**
     * Validate a unit structure
     * @param {Object} unit - The unit to validate
     * @param {number} index - Unit index for error messages
     * @param {Object} results - Results object to append to
     */
    validateUnit(unit, index, results) {
        if (!unit.name) {
            results.errors.push(`Unit ${index + 1}: Name is required`);
        }

        if (!Array.isArray(unit.topics)) {
            results.errors.push(`Unit ${index + 1}: Topics must be an array`);
        } else {
            unit.topics.forEach((topic, topicIndex) => {
                this.validateTopic(topic, index, topicIndex, results);
            });
        }
    }

    /**
     * Validate a topic structure
     * @param {Object} topic - The topic to validate
     * @param {number} unitIndex - Unit index for error messages
     * @param {number} topicIndex - Topic index for error messages
     * @param {Object} results - Results object to append to
     */
    validateTopic(topic, unitIndex, topicIndex, results) {
        if (!topic.name) {
            results.errors.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Name is required`);
        }

        // Validate learning objectives
        if (!Array.isArray(topic.learning_objectives)) {
            results.errors.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Learning objectives must be an array`);
        } else if (topic.learning_objectives.length === 0) {
            results.warnings.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: No learning objectives defined`);
        }

        // Validate keywords
        if (!Array.isArray(topic.keywords)) {
            results.errors.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Keywords must be an array`);
        } else if (topic.keywords.length === 0) {
            results.warnings.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: No keywords defined`);
        }

        // Check for duplicate keywords
        if (Array.isArray(topic.keywords)) {
            const uniqueKeywords = new Set(topic.keywords);
            if (uniqueKeywords.size !== topic.keywords.length) {
                results.warnings.push(`Unit ${unitIndex + 1}, Topic ${topicIndex + 1}: Contains duplicate keywords`);
            }
        }
    }
}

const syllabusValidationService = new SyllabusValidationService();
export default syllabusValidationService;

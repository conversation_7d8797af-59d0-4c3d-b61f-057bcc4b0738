/**
 * LLM API Usage Monitoring Service
 * Tracks and monitors usage of LLM API calls for analytics and cost control
 */

import { getDb } from '../firebase.js';

/**
 * Track a single LLM API call usage
 * @param {Object} usageData - Object containing usage information
 * @param {string} usageData.operation - Type of operation (generate_questions, explain_theory, etc)
 * @param {number} usageData.tokensUsed - Estimated number of tokens used
 * @param {string} usageData.model - Model name (gemini, etc)
 * @param {Date} usageData.timestamp - When the call was made
 */
async function trackLLMUsage(usageData) {
  try {
    const db = getDb();
    const usageRef = db.collection('llm_usage').doc();
    
    await usageRef.set({
      ...usageData,
      cost: calculateEstimatedCost(usageData.tokensUsed, usageData.model),
      logged_at: new Date()
    });
    
    // Update daily usage aggregation
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const dailyUsageRef = db.collection('llm_usage_aggregated')
      .doc(`daily_${today.toISOString().split('T')[0]}`);
    
    const dailyDoc = await dailyUsageRef.get();
    
    if (dailyDoc.exists) {
      // Update existing daily record
      await dailyUsageRef.update({
        total_calls: dailyDoc.data().total_calls + 1,
        total_tokens: dailyDoc.data().total_tokens + usageData.tokensUsed,
        total_cost: dailyDoc.data().total_cost + calculateEstimatedCost(usageData.tokensUsed, usageData.model),
        operations: {
          ...dailyDoc.data().operations,
          [usageData.operation]: (dailyDoc.data().operations[usageData.operation] || 0) + 1
        },
        updated_at: new Date()
      });
    } else {
      // Create new daily record
      await dailyUsageRef.set({
        date: today,
        total_calls: 1,
        total_tokens: usageData.tokensUsed,
        total_cost: calculateEstimatedCost(usageData.tokensUsed, usageData.model),
        operations: {
          [usageData.operation]: 1
        },
        created_at: new Date(),
        updated_at: new Date()
      });
    }
    
    // If usage exceeds thresholds, trigger alerts
    await checkUsageAlerts(today);
    
    return { success: true };
  } catch (error) {
    console.error('Error tracking LLM usage:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get LLM usage statistics and reports
 * @param {string} period - daily, weekly, monthly
 * @param {Date} startDate - Start date for report period
 * @param {Date} endDate - End date for report period
 */
async function getLLMUsageStats(period, startDate, endDate) {
  try {
    const db = getDb();
    const stats = {
      total_calls: 0,
      total_tokens: 0,
      total_cost: 0,
      by_operation: {},
      by_model: {},
      time_series: []
    };
    
    // Format dates for collection queries
    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];
    
    // Query the appropriate collection based on period
    const collectionPrefix = period === 'daily' ? 'daily_' 
                          : period === 'weekly' ? 'weekly_' 
                          : 'monthly_';
    
    const snapshot = await db.collection('llm_usage_aggregated')
      .where('date', '>=', new Date(formattedStartDate))
      .where('date', '<=', new Date(formattedEndDate))
      .orderBy('date')
      .get();
    
    snapshot.forEach(doc => {
      const data = doc.data();
      stats.total_calls += data.total_calls;
      stats.total_tokens += data.total_tokens;
      stats.total_cost += data.total_cost;
      
      // Aggregate by operation
      Object.entries(data.operations || {}).forEach(([op, count]) => {
        stats.by_operation[op] = (stats.by_operation[op] || 0) + count;
      });
      
      // Add to time series for charts
      stats.time_series.push({
        date: data.date.toISOString().split('T')[0],
        calls: data.total_calls,
        tokens: data.total_tokens,
        cost: data.total_cost
      });
    });
    
    // Get model breakdown from raw usage data
    const modelSnapshot = await db.collection('llm_usage')
      .where('timestamp', '>=', new Date(formattedStartDate))
      .where('timestamp', '<=', new Date(formattedEndDate))
      .get();
      
    modelSnapshot.forEach(doc => {
      const data = doc.data();
      const model = data.model || 'unknown';
      
      if (!stats.by_model[model]) {
        stats.by_model[model] = {
          calls: 0,
          tokens: 0,
          cost: 0
        };
      }
      
      stats.by_model[model].calls += 1;
      stats.by_model[model].tokens += data.tokensUsed || 0;
      stats.by_model[model].cost += data.cost || 0;
    });
    
    return stats;
  } catch (error) {
    console.error('Error getting LLM usage stats:', error);
    throw error;
  }
}

/**
 * Set usage alerts thresholds
 * @param {Object} thresholds - Object containing threshold settings
 */
async function setUsageAlerts(thresholds) {
  try {
    const db = getDb();
    await db.collection('system_settings').doc('llm_usage_alerts').set({
      ...thresholds,
      updated_at: new Date()
    });
    
    return { success: true };
  } catch (error) {
    console.error('Error setting usage alerts:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Check if usage exceeds thresholds and trigger alerts if needed
 * @param {Date} date - The date to check for
 */
async function checkUsageAlerts(date) {
  try {
    const db = getDb();
    
    // Get current thresholds
    const alertsDoc = await db.collection('system_settings').doc('llm_usage_alerts').get();
    
    if (!alertsDoc.exists) {
      // No thresholds set
      return;
    }
    
    const thresholds = alertsDoc.data();
    
    // Get today's usage
    const dateStr = date.toISOString().split('T')[0];
    const dailyUsageDoc = await db.collection('llm_usage_aggregated')
      .doc(`daily_${dateStr}`)
      .get();
    
    if (!dailyUsageDoc.exists) {
      return;
    }
    
    const dailyUsage = dailyUsageDoc.data();
    
    // Check if thresholds exceeded
    let alertsTriggered = [];
    
    if (thresholds.daily_cost_limit && dailyUsage.total_cost >= thresholds.daily_cost_limit) {
      alertsTriggered.push({
        type: 'cost_limit',
        threshold: thresholds.daily_cost_limit,
        current: dailyUsage.total_cost
      });
    }
    
    if (thresholds.daily_token_limit && dailyUsage.total_tokens >= thresholds.daily_token_limit) {
      alertsTriggered.push({
        type: 'token_limit',
        threshold: thresholds.daily_token_limit,
        current: dailyUsage.total_tokens
      });
    }
    
    if (thresholds.daily_call_limit && dailyUsage.total_calls >= thresholds.daily_call_limit) {
      alertsTriggered.push({
        type: 'call_limit',
        threshold: thresholds.daily_call_limit,
        current: dailyUsage.total_calls
      });
    }
    
    // Record alerts if any triggered
    if (alertsTriggered.length > 0) {
      await db.collection('llm_usage_alerts').add({
        date: date,
        alerts: alertsTriggered,
        triggered_at: new Date()
      });
      
      // In production, send notifications via email, Slack, etc.
      console.warn('LLM usage alerts triggered:', alertsTriggered);
    }
  } catch (error) {
    console.error('Error checking usage alerts:', error);
  }
}

/**
 * Calculate estimated cost based on token count and model
 * @param {number} tokens - Number of tokens used
 * @param {string} model - Model name
 * @returns {number} Estimated cost in USD
 */
function calculateEstimatedCost(tokens, model) {
  // Prices per 1000 tokens (example rates, adjust to actual)
  const ratePerThousandTokens = {
    'gemini-pro': 0.005,
    'gemini': 0.005, // alias
    'gpt-4': 0.06,
    'gpt-3.5-turbo': 0.002,
    'default': 0.01
  };
  
  const rate = ratePerThousandTokens[model] || ratePerThousandTokens.default;
  return (tokens / 1000) * rate;
}

export {
  trackLLMUsage,
  getLLMUsageStats,
  setUsageAlerts
};

export default {
  trackLLMUsage,
  getLLMUsageStats,
  setUsageAlerts
};

import { getDb } from '../firebase.js';
import loggingService from './loggingService.js';
import syllabusValidationService from './syllabusValidationService.js';

class SyllabusBulkUploadService {
    constructor() {
        this.db = getDb();
        this.loggingService = loggingService;
        this.validationService = syllabusValidationService;
    }

    /**
     * Process bulk upload of syllabi
     * @param {Array} syllabi - Array of syllabus objects to upload
     * @returns {Object} - Upload results with status for each syllabus
     */
    async processBulkUpload(syllabi) {
        const results = {
            total: syllabi.length,
            successful: 0,
            failed: 0,
            errors: [],
            details: []
        };

        const batch = this.db.batch();
        
        try {
            // First validate all syllabi
            for (const syllabus of syllabi) {
                const validation = this.validationService.validateSyllabus(syllabus);
                if (validation.errors.length > 0) {
                    results.failed++;
                    results.details.push({
                        name: syllabus.name || 'Unknown',
                        status: 'failed',
                        errors: validation.errors
                    });
                    continue;
                }

                // Add to batch if valid
                const docRef = this.db.collection('syllabi').doc();
                batch.set(docRef, {
                    ...syllabus,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    status: 'active'
                });
                results.successful++;
                results.details.push({
                    name: syllabus.name,
                    status: 'success',
                    id: docRef.id
                });
            }

            // Commit the batch
            await batch.commit();

            return results;
        } catch (error) {
            this.loggingService.logError('Bulk upload failed', { error });
            throw error;
        }
    }

    /**
     * Validate bulk upload format
     * @param {Object} data - The uploaded file data
     * @returns {Object} - Validation results
     */
    validateBulkUploadFormat(data) {
        const results = {
            isValid: true,
            errors: []
        };

        if (!Array.isArray(data)) {
            results.isValid = false;
            results.errors.push('Upload must be an array of syllabi');
            return results;
        }

        data.forEach((syllabus, index) => {
            if (!syllabus.name) {
                results.errors.push(`Syllabus ${index + 1}: Missing name`);
            }
            if (!syllabus.subject) {
                results.errors.push(`Syllabus ${index + 1}: Missing subject`);
            }
            if (!syllabus.level || !['high_school', 'undergraduate', 'graduate'].includes(syllabus.level)) {
                results.errors.push(`Syllabus ${index + 1}: Invalid level`);
            }
            if (!Array.isArray(syllabus.units)) {
                results.errors.push(`Syllabus ${index + 1}: Units must be an array`);
            }
        });

        results.isValid = results.errors.length === 0;
        return results;
    }
}

const syllabusBulkUploadService = new SyllabusBulkUploadService();
export default syllabusBulkUploadService;

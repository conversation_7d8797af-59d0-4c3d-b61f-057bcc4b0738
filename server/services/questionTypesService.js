import { getDb } from '../firebase.js';
import { getValidator } from '../models/questionTypesSchema.js';
import loggingService from './loggingService.js';
import llmService from './llmService.js';

class QuestionTypesService {
    constructor() {
        this.db = null;
        this.questionTypes = {
            multiple_choice: this.generateMultipleChoice.bind(this),
            fill_in_blank: this.generateFillInBlank.bind(this),
            true_false: this.generateTrueFalse.bind(this),
            short_answer: this.generateShortAnswer.bind(this),
            theory: this.generateTheory.bind(this)
        };
    }

    async getDbInstance() {
        if (!this.db) {
            this.db = await getDb();
        }
        return this.db;
    }

    /**
     * Generate questions of a specific type
     */
    async generateQuestions(params) {
        const {
            questionType = 'multiple_choice',
            numQuestions = 1,
            topics = [],
            difficulty = 'medium',
            syllabus_id,
            unit_id,
            topic_id
        } = params;

        try {
            const generator = this.questionTypes[questionType];
            if (!generator) {
                throw new Error(`Unsupported question type: ${questionType}`);
            }

            const questions = await generator({
                ...params,
                numQuestions,
                topics,
                difficulty
            });

            // Validate questions
            const validator = getValidator(questionType);
            const validQuestions = questions.filter(q => {
                const isValid = validator(q);
                if (!isValid) {
                    loggingService.logError('Invalid question generated', {
                        questionType,
                        errors: validator.errors
                    });
                }
                return isValid;
            });

            // Save valid questions to Firestore
            await this.saveQuestions(validQuestions);

            return validQuestions;
        } catch (error) {
            loggingService.logError('Failed to generate questions', { error });
            throw error;
        }
    }

    /**
     * Generate multiple choice questions
     */
    async generateMultipleChoice(params) {
        const prompt = `Generate ${params.numQuestions} multiple choice questions about ${params.topics.join(', ')} at ${params.difficulty} difficulty level.
Each question should have:
- Clear question text
- 4 options (A, B, C, D)
- One correct answer
- Detailed explanation
- Difficulty level justification`;

        const response = await llmService.generateContent(prompt);
        return this.parseMultipleChoiceResponse(response, params);
    }

    /**
     * Generate fill in the blank questions
     */
    async generateFillInBlank(params) {
        const prompt = `Generate ${params.numQuestions} fill-in-the-blank questions about ${params.topics.join(', ')} at ${params.difficulty} difficulty level.
Each question should have:
- Question text with a blank (_____) 
- Correct answer
- List of acceptable alternative answers
- Explanation
- Whether case sensitivity matters`;

        const response = await llmService.generateContent(prompt);
        return this.parseFillInBlankResponse(response, params);
    }

    /**
     * Generate true/false questions
     */
    async generateTrueFalse(params) {
        const prompt = `Generate ${params.numQuestions} true/false questions about ${params.topics.join(', ')} at ${params.difficulty} difficulty level.
Each question should have:
- Clear statement to evaluate
- True/False answer
- Detailed explanation why it's true or false
- Common misconceptions addressed`;

        const response = await llmService.generateContent(prompt);
        return this.parseTrueFalseResponse(response, params);
    }

    /**
     * Generate short answer questions
     */
    async generateShortAnswer(params) {
        const prompt = `Generate ${params.numQuestions} short answer questions about ${params.topics.join(', ')} at ${params.difficulty} difficulty level.
Each question should have:
- Question requiring brief explanation
- Model answer
- Key concepts/keywords to look for
- Word limit suggestion
- Grading rubric`;

        const response = await llmService.generateContent(prompt);
        return this.parseShortAnswerResponse(response, params);
    }

    /**
     * Generate theory questions
     */
    async generateTheory(params) {
        const prompt = `Generate ${params.numQuestions} theory questions about ${params.topics.join(', ')} at ${params.difficulty} difficulty level.
Each question should have:
- Complex conceptual question
- Comprehensive answer
- Related subtopics
- Key references
- Connected concepts`;

        const response = await llmService.generateContent(prompt);
        return this.parseTheoryResponse(response, params);
    }

    /**
     * Parse LLM response for multiple choice questions
     */
    parseMultipleChoiceResponse(response, params) {
        // Implementation would parse the LLM response into the required schema format
        // This is a simplified example
        return response.questions.map(q => ({
            question_id: `mc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            question_type: 'multiple_choice',
            question_text: q.question,
            options: q.options,
            correct_answer: q.answer,
            explanation: q.explanation,
            difficulty: params.difficulty,
            topics: params.topics,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
    }

    /**
     * Parse LLM response for fill in the blank questions
     */
    parseFillInBlankResponse(response, params) {
        return response.questions.map(q => ({
            question_id: `fib_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            question_type: 'fill_in_blank',
            question_text: q.question,
            correct_answer: q.answer,
            acceptable_answers: q.alternatives,
            explanation: q.explanation,
            case_sensitive: q.caseSensitive,
            difficulty: params.difficulty,
            topics: params.topics,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
    }

    /**
     * Parse LLM response for true/false questions
     */
    parseTrueFalseResponse(response, params) {
        return response.questions.map(q => ({
            question_id: `tf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            question_type: 'true_false',
            question_text: q.statement,
            correct_answer: q.isTrue,
            explanation: q.explanation,
            difficulty: params.difficulty,
            topics: params.topics,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
    }

    /**
     * Parse LLM response for short answer questions
     */
    parseShortAnswerResponse(response, params) {
        return response.questions.map(q => ({
            question_id: `sa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            question_type: 'short_answer',
            question_text: q.question,
            correct_answer: q.modelAnswer,
            keywords: q.keywords,
            max_words: q.wordLimit,
            rubric: q.rubric,
            explanation: q.explanation,
            difficulty: params.difficulty,
            topics: params.topics,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
    }

    /**
     * Parse LLM response for theory questions
     */
    parseTheoryResponse(response, params) {
        return response.questions.map(q => ({
            question_id: `th_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            question_type: 'theory',
            question_text: q.question,
            correct_answer: q.answer,
            subtopics: q.subtopics,
            references: q.references,
            related_questions: q.relatedQuestions,
            explanation: q.explanation,
            difficulty: params.difficulty,
            topics: params.topics,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }));
    }

    /**
     * Save questions to Firestore
     */
    async saveQuestions(questions) {
        const db = await this.getDbInstance();
        const batch = db.batch();
        questions.forEach(question => {
            const ref = db.collection('questions').doc(question.question_id);
            batch.set(ref, question);
        });
        await batch.commit();
    }

    /**
     * Evaluate a short answer response
     */
    async evaluateShortAnswer(questionId, userAnswer) {
        try {
            const db = await this.getDbInstance();
            const questionDoc = await db.collection('questions').doc(questionId).get();
            if (!questionDoc.exists) {
                throw new Error('Question not found');
            }

            const question = questionDoc.data();
            if (question.question_type !== 'short_answer') {
                throw new Error('Not a short answer question');
            }

            const prompt = `Evaluate this short answer response:
Question: ${question.question_text}
Model Answer Keywords: ${question.keywords.join(', ')}
Word Limit: ${question.max_words}
User's Answer: ${userAnswer}

Evaluate based on:
1. Content accuracy (${question.rubric.content} points)
2. Keyword usage (${question.rubric.keywords} points)
3. Grammar and clarity (${question.rubric.grammar} points)

Provide:
1. Total score
2. Breakdown of points
3. Feedback for improvement`;

            const evaluation = await llmService.generateContent(prompt);
            return this.parseEvaluation(evaluation);
        } catch (error) {
            loggingService.logError('Failed to evaluate short answer', { error });
            throw error;
        }
    }

    /**
     * Parse evaluation response
     */
    parseEvaluation(evaluation) {
        // Implementation would parse the LLM evaluation response
        return {
            score: evaluation.totalScore,
            breakdown: {
                content: evaluation.contentScore,
                keywords: evaluation.keywordScore,
                grammar: evaluation.grammarScore
            },
            feedback: evaluation.feedback
        };
    }
}

const questionTypesService = new QuestionTypesService();
export default questionTypesService;

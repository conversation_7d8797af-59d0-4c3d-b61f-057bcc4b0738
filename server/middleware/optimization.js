import compression from 'compression';
import redisService from '../services/redisService.js';
import loggingService from '../services/loggingService.js';
import { getDb } from '../firebase.js';

// Cache middleware
const cacheMiddleware = (duration) => {
    return async (req, res, next) => {
        if (req.method !== 'GET') {
            return next();
        }

        const key = `cache:${req.originalUrl}`;
        try {
            const cachedResponse = await redisService.get(key);
            if (cachedResponse) {
                return res.json(JSON.parse(cachedResponse));
            }

            // Store original send
            const originalSend = res.send;
            res.send = function(body) {
                if (res.statusCode === 200) {
                    redisService.set(key, body, duration);
                }
                originalSend.call(this, body);
            };

            next();
        } catch (error) {
            next();
        }
    };
};

// Rate limiting middleware
const rateLimitMiddleware = async (req, res, next) => {
    const key = `ratelimit:${req.ip}`;
    const limit = 100; // requests
    const window = 3600; // 1 hour in seconds

    try {
        const current = await redisService.get(key) || 0;
        if (current >= limit) {
            return res.status(429).json({
                error: 'Too many requests'
            });
        }

        await redisService.set(key, parseInt(current) + 1, window);
        next();
    } catch (error) {
        next();
    }
};

// Query optimization middleware
const queryOptimizationMiddleware = (req, res, next) => {
    // Add query hints for indexed fields
    if (req.query.subject) {
        req.queryHints = {
            subject: 1
        };
    }
    if (req.query.difficulty) {
        req.queryHints = {
            ...req.queryHints,
            difficulty: 1
        };
    }
    next();
};

// Response compression
const compressionMiddleware = compression({
    filter: (req, res) => {
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    },
    level: 6 // Compression level (0-9)
});

// Server-side rendering middleware
const ssrMiddleware = async (req, res, next) => {
    if (!req.headers['user-agent']?.includes('bot')) {
        return next();
    }

    try {
        // Pre-render content for bots
        const content = await redisService.get(`ssr:${req.originalUrl}`);
        if (content) {
            return res.send(content);
        }
        next();
    } catch (error) {
        next();
    }
};

// Enhanced caching middleware with intelligent cache invalidation
const intelligentCacheMiddleware = (duration) => {
    return async (req, res, next) => {
        if (req.method !== 'GET') {
            return next();
        }

        const key = `cache:${req.originalUrl}`;
        try {
            // Check cache freshness
            const cachedData = await redisService.get(key);
            if (cachedData) {
                const { data, timestamp } = JSON.parse(cachedData);
                const age = Date.now() - timestamp;

                // If data is still fresh, return it
                if (age < duration) {
                    return res.json(data);
                }

                // If data is stale but not too old, return it and refresh in background
                if (age < duration * 2) {
                    res.json(data);
                    // Trigger background refresh
                    refreshCache(req.originalUrl, key, duration);
                    return;
                }
            }

            // Store original send
            const originalSend = res.send;
            res.send = function(body) {
                if (res.statusCode === 200) {
                    const cacheData = {
                        data: JSON.parse(body),
                        timestamp: Date.now()
                    };
                    redisService.set(key, JSON.stringify(cacheData), duration);
                }
                originalSend.call(this, body);
            };

            next();
        } catch (error) {
            loggingService.logError('Cache middleware error', { error });
            next();
        }
    };
};

// Background cache refresh
async function refreshCache(url, key, duration) {
    try {
        const response = await fetch(`http://localhost:${process.env.PORT}${url}`, {
            headers: { 'X-Internal-Request': 'true' }
        });
        const data = await response.json();
        const cacheData = {
            data,
            timestamp: Date.now()
        };
        await redisService.set(key, JSON.stringify(cacheData), duration);
    } catch (error) {
        loggingService.logError('Cache refresh error', { error });
    }
}

// Enhanced query optimization middleware
const enhancedQueryOptimizationMiddleware = async (req, res, next) => {
    const db = getDb();
    
    // Add query metadata
    req.queryMetadata = {
        startTime: Date.now(),
        collection: req.path.split('/')[1],
        operation: req.method
    };

    // Intercept Firestore queries
    const originalGet = db.collection;
    db.collection = function() {
        const collection = originalGet.apply(this, arguments);
        const originalQuery = collection.where;
        
        collection.where = function() {
            // Log query for optimization analysis
            loggingService.logInfo('Firestore query', {
                collection: arguments[0],
                field: arguments[1],
                operation: arguments[2],
                value: arguments[3]
            });
            return originalQuery.apply(this, arguments);
        };
        
        return collection;
    };

    next();
};

// Enhanced compression middleware
const enhancedCompressionMiddleware = compression({
    filter: (req, res) => {
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    },
    level: 6,
    threshold: '1kb' // Only compress responses larger than 1KB
});

// Server-side rendering middleware with caching
const enhancedSsrMiddleware = async (req, res, next) => {
    if (!req.accepts('html') || req.path.startsWith('/api')) {
        return next();
    }

    const key = `ssr:${req.originalUrl}`;
    try {
        const cached = await redisService.get(key);
        if (cached) {
            return res.send(cached);
        }

        // Continue with SSR
        next();
    } catch (error) {
        loggingService.logError('SSR middleware error', { error });
        next();
    }
};

export {
    intelligentCacheMiddleware,
    rateLimitMiddleware,
    enhancedQueryOptimizationMiddleware,
    enhancedCompressionMiddleware,
    enhancedSsrMiddleware
};

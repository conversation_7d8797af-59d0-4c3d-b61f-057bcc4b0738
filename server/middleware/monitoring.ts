import { Request, Response, NextFunction } from 'express';
import { monitoringUtils } from '../config/monitoring.js';

export const monitoringMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Record start time
  const start = Date.now();
  
  // Add response listener
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // Record API metrics
    monitoringUtils.recordApiCall(
      req.path,
      duration,
      res.statusCode
    );
  });
  
  // Error handling
  const originalSend = res.send;
res.send = function(this: Response, body: any): Response {
    if (res.statusCode >= 400) {
      monitoringUtils.recordError(
        new Error(`HTTP ${res.statusCode}`),
        {
          path: req.path,
          method: req.method,
          query: req.query,
          body: req.body
        }
      );
    }
    return originalSend.call(this, body);
  };
  
  next();
};

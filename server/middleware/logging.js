import loggingService from '../services/loggingService.js';

/**
 * API logging middleware
 */
export function apiLogger(req, res, next) {
  const start = Date.now();
  
  // Capture the original end function
  const originalEnd = res.end;
  
  // Override end function to log after response is sent
  res.end = function(...args) {
    const duration = Date.now() - start;
    const status = res.statusCode;
    
    // Log the API call
    loggingService.logApiCall(req, duration, status)
      .catch(err => console.error('Error logging API call:', err));
    
    // Call the original end function
    originalEnd.apply(res, args);
  };
  
  next();
}

/**
 * Error logging middleware
 */
export function errorLogger(err, req, res, next) {
  const duration = Date.now() - req._startTime;
  
  // Log the error
  loggingService.logApiCall(req, duration, res.statusCode, err)
    .catch(error => console.error('Error logging API error:', error));
  
  next(err);
}

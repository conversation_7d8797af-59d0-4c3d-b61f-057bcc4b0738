import express from 'express';
import analyticsService from '../services/analyticsService.js';
import { authenticateJWT } from '../middleware/auth.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

// All routes require authentication
router.use(authenticateJWT);

/**
 * Get performance trends
 */
router.get('/trends', async (req, res) => {
    try {
        const userId = req.user.id;
        const { timeRange } = req.query;
        const trends = await analyticsService.getPerformanceTrends(userId, timeRange);
        res.json({ trends });
    } catch (error) {
        loggingService.logError('Failed to get performance trends', { error });
        res.status(500).json({ error: 'Failed to get performance trends' });
    }
});

/**
 * Get weak areas analysis
 */
router.get('/weak-areas', async (req, res) => {
    try {
        const userId = req.user.id;
        const weakAreas = await analyticsService.getWeakAreas(userId);
        res.json({ weakAreas });
    } catch (error) {
        loggingService.logError('Failed to get weak areas', { error });
        res.status(500).json({ error: 'Failed to get weak areas analysis' });
    }
});

/**
 * Get personalized study plan
 */
router.get('/study-plan', async (req, res) => {
    try {
        const userId = req.user.id;
        const plan = await analyticsService.generateStudyPlan(userId);
        res.json({ plan });
    } catch (error) {
        loggingService.logError('Failed to generate study plan', { error });
        res.status(500).json({ error: 'Failed to generate study plan' });
    }
});

/**
 * Get detailed topic performance
 */
router.get('/topic-performance/:topic', async (req, res) => {
    try {
        const userId = req.user.id;
        const { topic } = req.params;
        const performance = await analyticsService.getTopicPerformance(userId, topic);
        res.json({ performance });
    } catch (error) {
        loggingService.logError('Failed to get topic performance', { error });
        res.status(500).json({ error: 'Failed to get topic performance' });
    }
});

export default router;

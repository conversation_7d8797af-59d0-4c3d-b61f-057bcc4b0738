import express from 'express';
import { getDb } from '../firebase.js';
import loggingService from '../services/loggingService.js';
import { authenticateJWT } from '../middleware/auth.js';

const router = express.Router();

/**
 * Get all available syllabi
 * @route GET /api/syllabi
 */
router.get('/', authenticateJWT, async (req, res) => {
  try {
    const db = getDb();
    const snapshot = await db.collection('syllabi').get();
    
    const syllabi = [];
    snapshot.forEach(doc => {
      syllabi.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    res.json({ syllabi });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'getAllSyllabi',
        route: req.originalUrl,
        userId: req.user?.id
    });
    res.status(500).json({ 
        success: false,
        error: 'Failed to fetch syllabi', 
        message: error.message
    });
  }
});

/**
 * Get a specific syllabus by ID
 * @route GET /api/syllabi/:id
 */
router.get('/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    const doc = await db.collection('syllabi').doc(id).get();
    
    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Syllabus not found' 
      });
    }
    
    res.json({
      id: doc.id,
      ...doc.data()
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'getSyllabusById',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to fetch syllabus', 
        message: error.message 
    });
  }
});

/**
 * Create a new syllabus (admin only)
 * @route POST /api/syllabi
 */
router.post('/', authenticateJWT, async (req, res) => {
  try {
    const syllabusData = req.body;
    const userId = req.user?.id;
    
    if (!syllabusData.name || !syllabusData.description || !syllabusData.exam_board || !syllabusData.units) {
      return res.status(400).json({ 
        success: false,
        error: 'Missing required fields', 
        message: 'Required fields: name, description, exam_board, units.',
        required: ['name', 'description', 'exam_board', 'units'] 
      });
    }
    
    if (!Array.isArray(syllabusData.units)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid syllabus structure',
            message: 'Syllabus units must be an array.'
        });
    }

    for (const unit of syllabusData.units) {
      if (!unit.unit_id || !unit.unit_name || !unit.topics) {
        return res.status(400).json({ 
          success: false,
          error: 'Invalid unit structure', 
          message: `Unit missing required fields (unit_id, unit_name, topics). Received: ${JSON.stringify(unit)}`,
          required: ['unit_id', 'unit_name', 'topics'] 
        });
      }
      
      if (!Array.isArray(unit.topics)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid unit structure',
            message: `Unit topics must be an array. Received for unit ${unit.unit_id}: ${JSON.stringify(unit.topics)}`
        });
      }

      for (const topic of unit.topics) {
        if (!topic.topic_id || !topic.topic_name) {
          return res.status(400).json({ 
            success: false,
            error: 'Invalid topic structure', 
            message: `Topic missing required fields (topic_id, topic_name). Received: ${JSON.stringify(topic)}`,
            required: ['topic_id', 'topic_name'] 
          });
        }
      }
    }
    
    const db = getDb();
    const syllabusRef = db.collection('syllabi').doc();
    
    await syllabusRef.set({
      ...syllabusData,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: userId
    });
    
    res.status(201).json({ 
      success: true, 
      id: syllabusRef.id,
      message: 'Syllabus created successfully' 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'createSyllabus',
        route: req.originalUrl,
        userId: req.user?.id,
        body: req.body
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to create syllabus', 
        message: error.message 
    });
  }
});

/**
 * Update a syllabus (admin only)
 * @route PUT /api/syllabi/:id
 */
router.put('/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    const syllabusData = req.body;
    const userId = req.user?.id;
    
    const db = getDb();
    const syllabusRef = db.collection('syllabi').doc(id);
    
    const doc = await syllabusRef.get();
    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Syllabus not found' 
      });
    }

    if (syllabusData.units && !Array.isArray(syllabusData.units)) {
        return res.status(400).json({
            success: false, error: 'Invalid syllabus structure for update',
            message: 'Syllabus units must be an array if provided.'
        });
    }
    
    if (syllabusData.units) { // Only validate units if they are provided
        for (const unit of syllabusData.units) {
          if (!unit.unit_id || !unit.unit_name || !unit.topics) {
            return res.status(400).json({ 
              success: false, error: 'Invalid unit structure for update', 
              message: `Unit missing required fields (unit_id, unit_name, topics). Received: ${JSON.stringify(unit)}`,
              required: ['unit_id', 'unit_name', 'topics'] 
            });
          }
          if (unit.topics && !Array.isArray(unit.topics)) {
            return res.status(400).json({
                success: false, error: 'Invalid unit structure for update',
                message: `Unit topics must be an array if provided. Received for unit ${unit.unit_id}: ${JSON.stringify(unit.topics)}`
            });
          }
          if (unit.topics) { // Only validate topics if they are provided
              for (const topic of unit.topics) {
                if (!topic.topic_id || !topic.topic_name) {
                  return res.status(400).json({ 
                    success: false, error: 'Invalid topic structure for update', 
                    message: `Topic missing required fields (topic_id, topic_name). Received: ${JSON.stringify(topic)}`,
                    required: ['topic_id', 'topic_name'] 
                  });
                }
              }
          }
        }
    }
    
    await syllabusRef.update({
      ...syllabusData,
      updated_at: new Date(),
      updated_by: userId
    });
    
    res.json({ 
      success: true, 
      message: 'Syllabus updated successfully' 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'updateSyllabus',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params,
        body: req.body
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to update syllabus', 
        message: error.message 
    });
  }
});

/**
 * Delete a syllabus (admin only)
 * @route DELETE /api/syllabi/:id
 */
router.delete('/:id', authenticateJWT, async (req, res) => {
  try {
    const { id } = req.params;
    
    const db = getDb();
    const syllabusRef = db.collection('syllabi').doc(id);
    
    const doc = await syllabusRef.get();
    if (!doc.exists) {
      return res.status(404).json({ 
        success: false, 
        error: 'Syllabus not found' 
      });
    }
    
    await syllabusRef.delete();
    
    res.json({ 
      success: true, 
      message: 'Syllabus deleted successfully' 
    });
  } catch (error) {
    loggingService.logError(error, {
        operation: 'deleteSyllabus',
        route: req.originalUrl,
        userId: req.user?.id,
        params: req.params
    });
    res.status(500).json({ 
        success: false, 
        error: 'Failed to delete syllabus', 
        message: error.message 
    });
  }
});

export default router;

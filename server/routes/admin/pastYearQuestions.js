import express from 'express';
import multer from 'multer';
import csv from 'csv-parse';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import pastYearQuestionService from '../../services/pastYearQuestionService.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();
const upload = multer({ storage: multer.memoryStorage() });

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Add a single past year question
 * POST /api/admin/past-year-questions
 */
router.post('/', async (req, res) => {
    try {
        const questionId = await pastYearQuestionService.addQuestion(req.body);
        res.status(201).json({ questionId });
    } catch (error) {
        loggingService.logError('Failed to add past year question', { error });
        res.status(400).json({ error: error.message });
    }
});

/**
 * Bulk upload questions via CSV
 * POST /api/admin/past-year-questions/bulk
 */
router.post('/bulk', upload.single('file'), async (req, res) => {
    if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
    }

    try {
        const questions = [];
        const parser = csv.parse({
            columns: true,
            skip_empty_lines: true
        });

        parser.on('readable', function() {
            let record;
            while ((record = parser.read()) !== null) {
                // Transform CSV record to question format
                const question = {
                    question_text: record.question_text,
                    options: record.options?.split('|') || [],
                    correct_answer: record.correct_answer,
                    explanation: record.explanation,
                    year: parseInt(record.year),
                    exam_name: record.exam_name,
                    source_url: record.source_url,
                    topics: record.topics?.split('|') || [],
                    difficulty: record.difficulty,
                    verified: record.verified === 'true'
                };
                questions.push(question);
            }
        });

        // Handle parsing end
        const parsePromise = new Promise((resolve, reject) => {
            parser.on('end', () => resolve(questions));
            parser.on('error', reject);
        });

        // Parse the file
        parser.write(req.file.buffer);
        parser.end();

        // Wait for parsing to complete and upload questions
        const parsedQuestions = await parsePromise;
        const results = await pastYearQuestionService.bulkUpload(parsedQuestions);

        res.json(results);
    } catch (error) {
        loggingService.logError('Failed to bulk upload questions', { error });
        res.status(400).json({ error: error.message });
    }
});

/**
 * Get questions by topic
 * GET /api/admin/past-year-questions/topic/:topicId
 */
router.get('/topic/:topicId', async (req, res) => {
    try {
        const questions = await pastYearQuestionService.getQuestionsByTopic(
            req.params.topicId,
            req.query
        );
        res.json(questions);
    } catch (error) {
        loggingService.logError('Failed to get questions by topic', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get question statistics
 * GET /api/admin/past-year-questions/statistics
 */
router.get('/statistics', async (req, res) => {
    try {
        const stats = await pastYearQuestionService.getStatistics();
        res.json(stats);
    } catch (error) {
        loggingService.logError('Failed to get question statistics', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Update question usage stats
 * POST /api/admin/past-year-questions/:questionId/usage
 */
router.post('/:questionId/usage', async (req, res) => {
    try {
        await pastYearQuestionService.updateUsageStats(req.params.questionId);
        res.json({ message: 'Usage stats updated' });
    } catch (error) {
        loggingService.logError('Failed to update usage stats', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

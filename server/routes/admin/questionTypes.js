import express from 'express';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import questionTypesService from '../../services/questionTypesService.js';
import loggingService from '../../services/loggingService.js';

const router = express.Router();

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Generate questions of a specific type
 * POST /api/admin/question-types/generate
 */
router.post('/generate', async (req, res) => {
    try {
        const questions = await questionTypesService.generateQuestions(req.body);
        res.json(questions);
    } catch (error) {
        loggingService.logError('Failed to generate questions', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Evaluate a short answer response
 * POST /api/admin/question-types/evaluate-short-answer
 */
router.post('/evaluate-short-answer', async (req, res) => {
    try {
        const { questionId, answer } = req.body;
        const evaluation = await questionTypesService.evaluateShortAnswer(questionId, answer);
        res.json(evaluation);
    } catch (error) {
        loggingService.logError('Failed to evaluate short answer', { error });
        res.status(500).json({ error: error.message });
    }
});

/**
 * Get question types configuration
 * GET /api/admin/question-types/config
 */
router.get('/config', async (req, res) => {
    try {
        const config = {
            types: [
                {
                    id: 'multiple_choice',
                    name: 'Multiple Choice',
                    description: '4 options with one correct answer',
                    hasOptions: true,
                    requiresEvaluation: false
                },
                {
                    id: 'fill_in_blank',
                    name: 'Fill in the Blank',
                    description: 'Text with blanks to fill',
                    hasOptions: false,
                    requiresEvaluation: false
                },
                {
                    id: 'true_false',
                    name: 'True/False',
                    description: 'Statement to evaluate as true or false',
                    hasOptions: false,
                    requiresEvaluation: false
                },
                {
                    id: 'short_answer',
                    name: 'Short Answer',
                    description: 'Brief written response with keywords',
                    hasOptions: false,
                    requiresEvaluation: true
                },
                {
                    id: 'theory',
                    name: 'Theory',
                    description: 'Comprehensive conceptual question',
                    hasOptions: false,
                    requiresEvaluation: true
                }
            ],
            difficulties: ['easy', 'medium', 'hard', 'expert'],
            defaultSettings: {
                type: 'multiple_choice',
                difficulty: 'medium',
                numQuestions: 10
            }
        };
        res.json(config);
    } catch (error) {
        loggingService.logError('Failed to get question types config', { error });
        res.status(500).json({ error: error.message });
    }
});

export default router;

import express from 'express';
import { adminAuthMiddleware } from '../../middleware/auth.js';
import loggingService from '../../services/loggingService.js';
import feedbackService from '../../services/feedbackService.js';

const router = express.Router();

// Apply admin auth middleware to all routes
router.use(adminAuthMiddleware);

/**
 * Get feedback for a question
 * GET /api/admin/feedback/question/:questionId
 */
router.get('/question/:questionId', async (req, res) => {
    try {
        const feedback = await feedbackService.getQuestionFeedback(req.params.questionId);
        res.json(feedback);
    } catch (error) {
        loggingService.logError('Failed to get question feedback', { error });
        res.status(500).json({ error: 'Failed to get feedback' });
    }
});

/**
 * Get feedback review queue
 * GET /api/admin/feedback/queue
 */
router.get('/queue', async (req, res) => {
    try {
        const queue = await feedbackService.getReviewQueue(req.query);
        res.json(queue);
    } catch (error) {
        loggingService.logError('Failed to get review queue', { error });
        res.status(500).json({ error: 'Failed to get review queue' });
    }
});

/**
 * Submit feedback for a question
 * POST /api/admin/feedback
 */
router.post('/', async (req, res) => {
    try {
        const feedbackId = await feedbackService.submitFeedback(req.body);
        res.status(201).json({ feedbackId });
    } catch (error) {
        loggingService.logError('Failed to submit feedback', { error });
        res.status(400).json({ error: error.message });
    }
});

/**
 * Process reviewer decision
 * POST /api/admin/feedback/:feedbackId/review
 */
router.post('/:feedbackId/review', async (req, res) => {
    try {
        await feedbackService.processReviewerDecision(req.params.feedbackId, {
            ...req.body,
            reviewer_id: req.user.id // from auth middleware
        });
        res.json({ message: 'Review processed successfully' });
    } catch (error) {
        loggingService.logError('Failed to process review', { error });
        res.status(400).json({ error: error.message });
    }
});

export default router;

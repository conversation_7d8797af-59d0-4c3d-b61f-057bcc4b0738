import express from 'express';
import gamificationService from '../services/gamificationService.js';
import { authenticateJWT } from '../middleware/auth.js';
import loggingService from '../services/loggingService.js';

const router = express.Router();

// Get user gamification data
router.get('/user/:userId', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const gamificationData = await gamificationService.getUserGamificationData(userId);
    
    res.json({
      success: true,
      data: gamificationData
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getUserGamificationData',
      route: req.originalUrl,
      userId: userId
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch gamification data',
      message: error.message
    });
  }
});

// Award XP to user
router.post('/award-xp', authenticateJWT, async (req, res) => {
  try {
    const { userId, amount, source, metadata } = req.body;
    
    if (!userId || !amount || !source) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, amount, source'
      });
    }

    const result = await gamificationService.awardXP(userId, amount, source, metadata);
    
    // Check for level up
    const levelUpResult = await gamificationService.checkLevelUp(userId, result.newXP);
    
    // Check for new achievements
    const achievements = await gamificationService.checkAchievements(userId, 'xp_earned', {
      amount,
      source,
      totalXP: result.newXP
    });

    res.json({
      success: true,
      xpAwarded: amount,
      newXP: result.newXP,
      levelUp: levelUpResult,
      newAchievements: achievements
    });
  } catch (error) {
    const { userId: targetUserId } = req.body; 
    loggingService.logError(error, {
      operation: 'awardXP',
      route: req.originalUrl,
      userId: req.user?.id, // Authenticated user performing the action
      targetUserId: targetUserId, // User being awarded XP
      body: req.body
    });
    res.status(500).json({
      success: false,
      error: 'Failed to award XP',
      message: error.message
    });
  }
});

// Update user streak
router.post('/streak/:userId', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const streakResult = await gamificationService.updateStreak(userId);
    
    // Award streak bonus XP
    if (streakResult.streakMaintained) {
      const bonusXP = Math.min(streakResult.currentStreak * 5, 100);
      await gamificationService.awardXP(userId, bonusXP, 'streak_bonus', { 
        streak: streakResult.currentStreak 
      });
    }
    
    res.json({
      success: true,
      ...streakResult
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'updateStreak',
      route: req.originalUrl,
      userId: userId, // from req.params
      authenticatedUserId: req.user?.id
    });
    res.status(500).json({
      success: false,
      error: 'Failed to update streak',
      message: error.message
    });
  }
});

// Get leaderboard
router.get('/leaderboard/:type/:subject?', authenticateJWT, async (req, res) => {
  try {
    const { type, subject } = req.params;
    const { limit = 50 } = req.query;
    
    const leaderboard = await gamificationService.getLeaderboard(type, subject, parseInt(limit));
    
    res.json({
      success: true,
      data: leaderboard
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getLeaderboard',
      route: req.originalUrl,
      userId: req.user?.id,
      params: req.params,
      query: req.query
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch leaderboard',
      message: error.message
    });
  }
});

// Get user badges
router.get('/badges/:userId', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const badges = await gamificationService.getUserBadges(userId);
    
    res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getUserBadges',
      route: req.originalUrl,
      userId: userId, // This is req.params.userId
      authenticatedUserId: req.user?.id
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch badges',
      message: error.message
    });
  }
});

// Get available badges
router.get('/badges', authenticateJWT, async (req, res) => {
  try {
    const badges = await gamificationService.getAllBadges();
    
    res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getAllBadges',
      route: req.originalUrl,
      authenticatedUserId: req.user?.id
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch badges',
      message: error.message
    });
  }
});

// Get daily mission
router.get('/mission/:userId', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const mission = await gamificationService.getDailyMission(userId);
    
    res.json({
      success: true,
      data: mission
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getDailyMission',
      route: req.originalUrl,
      userId: userId, // userId from req.params
      authenticatedUserId: req.user?.id
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch daily mission',
      message: error.message
    });
  }
});

// Update mission progress
router.post('/mission/progress', authenticateJWT, async (req, res) => {
  try {
    const { userId, missionId, progress } = req.body;
    
    const result = await gamificationService.updateMissionProgress(userId, missionId, progress);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    const { userId: targetUserId, missionId } = req.body;
    loggingService.logError(error, {
      operation: 'updateMissionProgress',
      route: req.originalUrl,
      authenticatedUserId: req.user?.id,
      targetUserId: targetUserId,
      missionId: missionId,
      body: req.body
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to update mission progress',
      message: error.message
    });
  }
});

// Get user achievements
router.get('/achievements/:userId', authenticateJWT, async (req, res) => {
  try {
    const { userId } = req.params;
    const achievements = await gamificationService.getUserAchievements(userId);
    
    res.json({
      success: true,
      data: achievements
    });
  } catch (error) {
    loggingService.logError(error, {
      operation: 'getUserAchievements',
      route: req.originalUrl,
      userId: userId, // This is req.params.userId
      authenticatedUserId: req.user?.id
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch achievements',
      message: error.message
    });
  }
});

// Trigger celebration
router.post('/celebrate', authenticateJWT, async (req, res) => {
  try {
    const { userId, type, data } = req.body;
    
    // This could trigger real-time celebrations via WebSocket
    const celebration = await gamificationService.triggerCelebration(userId, type, data);
    
    res.json({
      success: true,
      data: celebration
    });
  } catch (error) {
    const { userId: targetUserId } = req.body;
    loggingService.logError(error, {
      operation: 'triggerCelebration',
      route: req.originalUrl,
      authenticatedUserId: req.user?.id,
      targetUserId: targetUserId,
      body: req.body
    });
    res.status(500).json({ 
      success: false, 
      error: 'Failed to trigger celebration',
      message: error.message
    });
  }
});

export default router;
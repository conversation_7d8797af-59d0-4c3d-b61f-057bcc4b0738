import winston from 'winston';

export interface LoggingService {
  info(message: string, meta?: Record<string, any>): void;
  error(message: string, meta?: Record<string, any>): void;
  debug(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  getFileTransport(): winston.Transport;
  getConsoleTransport(): winston.Transport;
}

export interface MonitoringService {
  recordApiCall(endpoint: string, duration: number, status: number): void;
  recordLlmUsage(model: string, tokens: number, cost: number): void;
  recordError(error: Error, context: Record<string, unknown>): void;
}

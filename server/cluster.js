const cluster = require('cluster');
const os = require('os');
const loggingService = require('./services/loggingService');
const redisService = require('./services/redisService');

if (cluster.isMaster) {
    const numCPUs = os.cpus().length;
    
    // Fork workers based on CPU cores
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }

    // Log when a worker starts
    cluster.on('online', (worker) => {
        loggingService.logInfo(`Worker ${worker.process.pid} is online`);
    });

    // Handle worker exit and restart
    cluster.on('exit', (worker, code, signal) => {
        loggingService.logError(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
        // Restart the worker
        cluster.fork();
    });

    // Monitor server load
    setInterval(async () => {
        try {
            const load = await redisService.getServerLoad();
            if (load > 80) { // 80% CPU usage threshold
                loggingService.logWarning(`High server load detected: ${load}%`);
                // Could implement auto-scaling here
            }
        } catch (error) {
            loggingService.logError(error, { operation: 'monitorServerLoad' });
        }
    }, 60000); // Check every minute

} else {
    // Worker process
    require('./index');
}

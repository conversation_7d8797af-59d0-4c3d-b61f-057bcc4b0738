// Schema for syllabus data
export const syllabusSchema = {
    type: 'object',
    required: ['syllabus_id', 'name', 'exam_board', 'units'],
    properties: {
        syllabus_id: { type: 'string' },
        name: { type: 'string' },
        description: { type: 'string' },
        exam_board: { type: 'string' },
        units: {
            type: 'array',
            items: {
                type: 'object',
                required: ['unit_id', 'unit_name', 'weightage', 'topics'],
                properties: {
                    unit_id: { type: 'string' },
                    unit_name: { type: 'string' },
                    weightage: { type: 'number', minimum: 0, maximum: 100 },
                    topics: {
                        type: 'array',
                        items: {
                            type: 'object',
                            required: ['topic_id', 'topic_name'],
                            properties: {
                                topic_id: { type: 'string' },
                                topic_name: { type: 'string' },
                                subtopics: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        required: ['subtopic_id', 'subtopic_name'],
                                        properties: {
                                            subtopic_id: { type: 'string' },
                                            subtopic_name: { type: 'string' }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

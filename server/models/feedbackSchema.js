// Schema for question feedback data
export const feedbackSchema = {
    type: 'object',
    required: ['question_id', 'user_id', 'feedback_type', 'rating'],
    properties: {
        feedback_id: { type: 'string' },
        question_id: { type: 'string' },
        user_id: { type: 'string' },
        feedback_type: { 
            type: 'string',
            enum: ['quality_review', 'challenge_answer', 'content_correction']
        },
        rating: {
            type: 'string',
            enum: ['good', 'needs_review', 'bad']
        },
        notes: { type: 'string' },
        suggested_correction: {
            type: 'object',
            properties: {
                question_text: { type: 'string' },
                options: {
                    type: 'array',
                    items: { type: 'string' }
                },
                correct_answer: { type: 'string' },
                explanation: { type: 'string' }
            }
        },
        status: {
            type: 'string',
            enum: ['pending', 'reviewed', 'approved', 'rejected'],
            default: 'pending'
        },
        reviewer_id: { type: 'string' },
        reviewer_notes: { type: 'string' },
        created_at: { type: 'string', format: 'date-time' },
        updated_at: { type: 'string', format: 'date-time' }
    }
};

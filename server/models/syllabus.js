/**
 * Syllabus model and schema definition
 * This defines the structure for storing and managing syllabus data in Firestore
 */

const { getDb } = require('../firebase');

class SyllabusModel {
  constructor() {
    this.db = getDb();
    this.collection = 'syllabi';
  }

  /**
   * Schema for a syllabus document
   * @typedef {Object} Syllabus
   * @property {string} id - Unique identifier
   * @property {string} name - Name of the syllabus
   * @property {string} subject - Subject (e.g., Physics, Chemistry)
   * @property {string} level - Academic level (e.g., High School, Undergraduate)
   * @property {string} description - Brief description
   * @property {string} version - Version number
   * @property {Date} created_at - Creation timestamp
   * @property {Date} updated_at - Last update timestamp
   * @property {string} created_by - Admin ID who created
   * @property {string} status - Status (active, draft, archived)
   * @property {Array<Unit>} units - Array of units
   */

  /**
   * Schema for a unit within a syllabus
   * @typedef {Object} Unit
   * @property {string} id - Unique identifier
   * @property {string} name - Name of the unit
   * @property {string} description - Brief description
   * @property {number} order - Display order
   * @property {number} recommended_hours - Recommended study hours
   * @property {Array<Topic>} topics - Array of topics
   * @property {Object} learning_objectives - Key learning objectives
   * @property {Array<string>} prerequisites - Prerequisite units/concepts
   */

  /**
   * Schema for a topic within a unit
   * @typedef {Object} Topic
   * @property {string} id - Unique identifier
   * @property {string} name - Name of the topic
   * @property {string} description - Detailed description
   * @property {number} order - Display order
   * @property {number} difficulty - Difficulty level (1-5)
   * @property {Array<string>} keywords - Key concepts and terms
   * @property {Array<SubTopic>} subtopics - Array of subtopics
   * @property {Object} resources - Additional learning resources
   * @property {Object} question_distribution - Recommended question distribution
   */

  /**
   * Schema for a subtopic
   * @typedef {Object} SubTopic
   * @property {string} id - Unique identifier
   * @property {string} name - Name of the subtopic
   * @property {string} description - Detailed description
   * @property {number} order - Display order
   * @property {Array<string>} concepts - Key concepts to cover
   * @property {Object} question_weights - Weight for different question types
   */

  /**
   * Create a new syllabus
   * @param {Syllabus} syllabus - Syllabus data
   * @returns {Promise<string>} Created syllabus ID
   */
  async create(syllabus) {
    try {
      const doc = await this.db.collection(this.collection).add({
        ...syllabus,
        created_at: new Date(),
        updated_at: new Date(),
        version: '1.0',
        status: syllabus.status || 'draft'
      });
      return doc.id;
    } catch (error) {
      throw new Error(`Failed to create syllabus: ${error.message}`);
    }
  }

  /**
   * Get a syllabus by ID
   * @param {string} id - Syllabus ID
   * @returns {Promise<Syllabus>} Syllabus data
   */
  async get(id) {
    try {
      const doc = await this.db.collection(this.collection).doc(id).get();
      if (!doc.exists) {
        throw new Error('Syllabus not found');
      }
      return { id: doc.id, ...doc.data() };
    } catch (error) {
      throw new Error(`Failed to get syllabus: ${error.message}`);
    }
  }

  /**
   * Update a syllabus
   * @param {string} id - Syllabus ID
   * @param {Partial<Syllabus>} updates - Fields to update
   * @returns {Promise<void>}
   */
  async update(id, updates) {
    try {
      await this.db.collection(this.collection).doc(id).update({
        ...updates,
        updated_at: new Date()
      });
    } catch (error) {
      throw new Error(`Failed to update syllabus: ${error.message}`);
    }
  }

  /**
   * Delete a syllabus
   * @param {string} id - Syllabus ID
   * @returns {Promise<void>}
   */
  async delete(id) {
    try {
      await this.db.collection(this.collection).doc(id).delete();
    } catch (error) {
      throw new Error(`Failed to delete syllabus: ${error.message}`);
    }
  }

  /**
   * List all syllabi with optional filters
   * @param {Object} filters - Optional filters
   * @param {string} filters.subject - Filter by subject
   * @param {string} filters.level - Filter by level
   * @param {string} filters.status - Filter by status
   * @returns {Promise<Array<Syllabus>>} Array of syllabi
   */
  async list(filters = {}) {
    try {
      let query = this.db.collection(this.collection);

      if (filters.subject) {
        query = query.where('subject', '==', filters.subject);
      }
      if (filters.level) {
        query = query.where('level', '==', filters.level);
      }
      if (filters.status) {
        query = query.where('status', '==', filters.status);
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      throw new Error(`Failed to list syllabi: ${error.message}`);
    }
  }

  /**
   * Get topics for a specific unit
   * @param {string} syllabusId - Syllabus ID
   * @param {string} unitId - Unit ID
   * @returns {Promise<Array<Topic>>} Array of topics
   */
  async getTopics(syllabusId, unitId) {
    try {
      const syllabus = await this.get(syllabusId);
      const unit = syllabus.units.find(u => u.id === unitId);
      if (!unit) {
        throw new Error('Unit not found');
      }
      return unit.topics;
    } catch (error) {
      throw new Error(`Failed to get topics: ${error.message}`);
    }
  }

  /**
   * Update question distribution for a topic
   * @param {string} syllabusId - Syllabus ID
   * @param {string} unitId - Unit ID
   * @param {string} topicId - Topic ID
   * @param {Object} distribution - New question distribution
   * @returns {Promise<void>}
   */
  async updateQuestionDistribution(syllabusId, unitId, topicId, distribution) {
    try {
      const syllabus = await this.get(syllabusId);
      const unit = syllabus.units.find(u => u.id === unitId);
      if (!unit) {
        throw new Error('Unit not found');
      }

      const topic = unit.topics.find(t => t.id === topicId);
      if (!topic) {
        throw new Error('Topic not found');
      }

      topic.question_distribution = distribution;
      await this.update(syllabusId, { units: syllabus.units });
    } catch (error) {
      throw new Error(`Failed to update question distribution: ${error.message}`);
    }
  }
}

module.exports = new SyllabusModel();

{"name": "llm-test-series-frontend", "version": "1.0.0", "description": "Addictive LLM-powered test series platform", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "firebase": "^10.7.1", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "react-confetti": "^6.1.0", "react-spring": "^9.7.3", "lucide-react": "^0.303.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
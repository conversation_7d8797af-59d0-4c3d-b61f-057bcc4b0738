name: AI Mock Test System CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Use Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16.x'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests
        run: npm test
        
      - name: Run linting
        run: npm run lint
        
      - name: Run type checking
        run: npm run typecheck
        
  build:
    name: Build
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Use Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16.x'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v2
        with:
          name: build-output
          path: dist/
          
  deploy-staging:
    name: Deploy to Staging
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build-output
          path: dist/
          
      - name: Configure Firebase for Staging
        run: |
          npm install -g firebase-tools
          firebase use staging --token ${{ secrets.FIREBASE_TOKEN }}
          
      - name: Deploy to Firebase Staging
        run: firebase deploy --token ${{ secrets.FIREBASE_TOKEN }}
        
      - name: Run integration tests on staging
        run: npm run test:integration:staging
          
  deploy-production:
    name: Deploy to Production
    needs: build
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build-output
          path: dist/
          
      - name: Configure Firebase for Production
        run: |
          npm install -g firebase-tools
          firebase use production --token ${{ secrets.FIREBASE_TOKEN }}
          
      - name: Deploy to Firebase Production
        run: firebase deploy --token ${{ secrets.FIREBASE_TOKEN }}
        
  monitor:
    name: Setup Monitoring
    needs: [deploy-staging, deploy-production]
    runs-on: ubuntu-latest
    if: ${{ always() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop') }}
    
    steps:
      - name: Configure Monitoring Alerts
        run: |
          curl -X POST ${{ secrets.MONITORING_ENDPOINT }} \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer ${{ secrets.MONITORING_API_KEY }}" \
          -d '{"service": "ai-mock-test", "environment": "${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}", "deployment_id": "${{ github.sha }}"}'
          
      - name: Notify Deployment
        uses: slackapi/slack-github-action@v1
        with:
          payload: |
            {
              "text": "🚀 Deployed AI Mock Test System to ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "🚀 *AI Mock Test System Deployed*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Staging' }} deployment completed successfully."
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Staging' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Deployment ID:*\n${{ github.sha }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Deployment"
                      },
                      "url": "${{ github.ref == 'refs/heads/main' && secrets.PROD_URL || secrets.STAGING_URL }}"
                    },
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Commit"
                      },
                      "url": "https://github.com/${{ github.repository }}/commit/${{ github.sha }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

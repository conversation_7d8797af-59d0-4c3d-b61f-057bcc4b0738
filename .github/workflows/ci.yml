name: CI/CD Pipeline

env: # Standardize Node.js version
  NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
  NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
  NODE_VERSION: '18.19.0'

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * 0' # Weekly security scan

jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4 # Use latest version
    - uses: actions/setup-node@v4 # Use latest version
      with:
        node-version: '18.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run ESLint
      run: npm run lint
    
    - name: Run Prettier
      run: npm run format:check
    
    - name: Run TypeScript check
      run: npm run type:check
    
    - name: Run SonarCloud scan
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4 # Use latest version
    - uses: actions/setup-node@v4 # Use latest version
      with:
        node-version: ${{ env.NODE_VERSION }} # Consistent Node version
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: npm audit --production
    
    - name: Run OWASP dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        path: '.'
        format: 'HTML'
        args: >-
          --failOnCVSS 7
          --enableRetired

  build: # New build job to create artifacts once
    name: Build Application
    needs: [code-quality, security] # Build only if quality and security pass
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build (Staging)
        if: github.ref == 'refs/heads/develop'
        run: npm run build:staging
      - name: Build (Production)
        if: github.ref == 'refs/heads/main'
        run: npm run build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4 # Use latest version
        with:
          name: build-output
          path: dist/

  deploy-staging:
    needs: [build, test] # Depends on successful build and tests
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }} # Consistent Node version
        cache: 'npm'
    
    - name: Download build artifacts
      uses: actions/download-artifact@v4 # Use latest version
      with:
        name: build-output
        path: dist/

    - name: Deploy to Netlify (Staging)
      env: # Pass Netlify tokens as environment variables
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
      run: npx netlify deploy --dir=dist --message "Staging deploy for ${{ github.sha }}"
    # Remove conflicting Firebase/AWS deployment steps if Netlify is primary
    # - name: Configure AWS credentials
    #   uses: aws-actions/configure-aws-credentials@v4
    #   with:
    #     aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #     aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #     aws-region: us-east-1
    # - name: Deploy to staging
    #   run: npm run deploy:staging

  deploy-production:
    needs: [build, test, deploy-staging] # Depends on successful build, tests, and staging deploy
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }} # Consistent Node version
        cache: 'npm'
    
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-output
        path: dist/

    - name: Deploy to Netlify (Production)
      env: # Pass Netlify tokens as environment variables
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }} # This SNYK_TOKEN env var is misplaced here, should be in security job
      run: npx netlify deploy --dir=dist --prod --message "Production deploy for ${{ github.sha }}"
    # Remove conflicting Firebase/AWS deployment steps

  test:
    needs: [code-quality, security] # Test only if quality and security pass
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v4 # Use latest version
    - name: Use Node.js ${{ matrix.node-version }} # Consistent Node version
      uses: actions/setup-node@v4 # Use latest version
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v4 # Use latest version
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
    
    - name: Upload coverage to SonarCloud
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # This is fine
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }} # This is fine
      with:
        args: >-
          -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
          -Dsonar.coverage.exclusions=tests/**/*

  monitor: # Consolidate monitoring from main.yml
    name: Setup Monitoring & Notify
    needs: [deploy-staging, deploy-production]
    if: ${{ always() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop') }}
    runs-on: ubuntu-latest
    steps:
      - name: Configure Monitoring Alerts
        run: |
          curl -X POST ${{ secrets.MONITORING_ENDPOINT }} \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer ${{ secrets.MONITORING_API_KEY }}" \
          -d '{"service": "ai-mock-test", "environment": "${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}", "deployment_id": "${{ github.sha }}"}'
          
      - name: Notify Deployment
        uses: slackapi/slack-github-action@v1.23.0 # Use a specific version
        with:
          payload: |
            {
              "text": "🚀 Deployed AI Mock Test System to ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "🚀 *AI Mock Test System Deployed*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Staging' }} deployment completed successfully."
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Staging' }}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Deployment ID:*\n${{ github.sha }}"
                    }
                  ]
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Deployment"
                      },
                      "url": "${{ github.ref == 'refs/heads/main' && secrets.PROD_URL || secrets.STAGING_URL }}"
                    },
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Commit"
                      },
                      "url": "https://github.com/${{ github.repository }}/commit/${{ github.sha }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

sonar.projectKey=web_test_series
sonar.organization=indranil2020

# Source code paths
sonar.sources=server,public
sonar.tests=tests

# Test coverage paths
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=tests/**/*,public/js/vendor/**/*,server/config/**/*

# Quality gates
sonar.qualitygate.wait=true

# Code duplication settings
sonar.cpd.javascript.minimumLines=50

# Analysis settings
sonar.sourceEncoding=UTF-8
sonar.verbose=true

# Security settings
sonar.security.enabled=true
sonar.security.hotspots.enabled=true

# Exclude patterns
sonar.exclusions=node_modules/**/*,coverage/**/*,dist/**/*,*.config.js

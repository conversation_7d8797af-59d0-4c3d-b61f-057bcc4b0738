<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indian Teacher Exam Paper Explorer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, addDoc, getDocs, query, where } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        window.firebase = {
            initializeApp,
            getAuth,
            signInAnonymously,
            signInWithCustomToken,
            onAuthStateChanged,
            getFirestore,
            collection,
            addDoc,
            getDocs,
            query,
            where
        };
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 400px;
            max-height: 50vh;
        }
        .modal-enter {
            animation: fadeIn 0.3s ease-out;
        }
        .modal-leave {
            animation: fadeOut 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: scale(1); }
            to { opacity: 0; transform: scale(0.95); }
        }
        .filter-btn.active {
            background-color: #0d9488;
            color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #0d9488;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .option-label input[type="radio"]:checked + span {
            background-color: #0d9488;
            color: white;
            border-color: #0d9488;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div id="app" class="min-h-screen">
        <header class="bg-white shadow-sm sticky top-0 z-40">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <h1 class="text-2xl sm:text-3xl font-bold text-teal-700">Indian Teacher Exam Paper Explorer</h1>
                <p class="mt-1 text-slate-600">An interactive guide to PGT & TGT previous year question papers across subjects.</p>
            </div>
        </header>

        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

            <section id="filters" class="mb-8 p-4 bg-white rounded-lg shadow">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                        <h3 class="font-semibold mb-2 text-slate-700">Filter by Governance</h3>
                        <div id="governance-filter" class="flex flex-wrap gap-2">
                            <button data-filter-group="governance" data-filter="all" class="filter-btn active px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">All</button>
                            <button data-filter-group="governance" data-filter="Central" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Central</button>
                            <button data-filter-group="governance" data-filter="State" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">State</button>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-2 text-slate-700">Filter by Level</h3>
                        <div id="level-filter" class="flex flex-wrap gap-2">
                            <button data-filter-group="level" data-filter="all" class="filter-btn active px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">All</button>
                            <button data-filter-group="level" data-filter="PGT" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">PGT</button>
                            <button data-filter-group="level" data-filter="TGT" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">TGT</button>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-2 text-slate-700">Filter by Subject</h3>
                        <div id="subject-filter" class="flex flex-wrap gap-2">
                            <button data-filter-group="subject" data-filter="all" class="filter-btn active px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">All</button>
                            <button data-filter-group="subject" data-filter="Physics" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Physics</button>
                            <button data-filter-group="subject" data-filter="Chemistry" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Chemistry</button>
                            <button data-filter-group="subject" data-filter="Biology" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Biology</button>
                            <button data-filter-group="subject" data-filter="Mathematics" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Mathematics</button>
                            <button data-filter-group="subject" data-filter="English" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">English</button>
                            <button data-filter-group="subject" data-filter="Hindi" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Hindi</button>
                            <button data-filter-group="subject" data-filter="Social Science" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Social Science</button>
                            <button data-filter-group="subject" data-filter="General Science" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">General Science</button>
                            <button data-filter-group="subject" data-filter="Natural Science" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Natural Science</button>
                            <button data-filter-group="subject" data-filter="History" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">History</button>
                            <button data-filter-group="subject" data-filter="Political Science" class="filter-btn px-4 py-2 text-sm font-medium bg-slate-200 rounded-md hover:bg-slate-300 transition-all">Political Science</button>
                        </div>
                    </div>
                    <div id="state-filter-container" class="hidden">
                        <h3 class="font-semibold mb-2 text-slate-700">Select State</h3>
                        <select id="state-selector" class="w-full p-2 border border-slate-300 rounded-md bg-white shadow-sm focus:ring-teal-500 focus:border-teal-500">
                            <option value="all">All States</option>
                        </select>
                    </div>
                </div>
            </section>
            
            <section id="results-count" class="mb-4 text-slate-600 font-medium">
            </section>

            <section id="exam-cards-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            </section>
            
            <div id="no-results" class="text-center py-12 text-slate-500 hidden">
                <p class="text-xl font-semibold">No Matching Exams Found</p>
                <p>Try adjusting your filters.</p>
            </div>

            <section id="mock-test-system" class="mt-16 pt-8 border-t border-slate-200">
                <h2 class="text-2xl sm:text-3xl font-bold text-center text-slate-800 mb-2">Mock Test System ✨</h2>
                <p class="text-center max-w-3xl mx-auto text-slate-600 mb-8">Simulate an exam scenario by generating multiple-choice questions for your chosen subject and level. Track your time and score!</p>

                <div id="test-config" class="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
                    <h3 class="text-xl font-semibold mb-4 text-slate-700">Configure Your Test</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="test-subject-selector" class="block text-sm font-medium text-slate-700 mb-1">Subject</label>
                            <select id="test-subject-selector" class="w-full p-2 border border-slate-300 rounded-md bg-white shadow-sm focus:ring-teal-500 focus:border-teal-500">
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                                <option value="Biology">Biology</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="English">English</option>
                                <option value="Hindi">Hindi</option>
                                <option value="Social Science">Social Science</option>
                                <option value="General Science">General Science</option>
                                <option value="Natural Science">Natural Science</option>
                                <option value="History">History</option>
                                <option value="Political Science">Political Science</option>
                            </select>
                        </div>
                        <div>
                            <label for="test-level-selector" class="block text-sm font-medium text-slate-700 mb-1">Level</label>
                            <select id="test-level-selector" class="w-full p-2 border border-slate-300 rounded-md bg-white shadow-sm focus:ring-teal-500 focus:border-teal-500">
                                <option value="PGT">PGT</option>
                                <option value="TGT">TGT</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-6">
                        <label for="num-questions-input" class="block text-sm font-medium text-slate-700 mb-1">Number of Questions (Max 10)</label>
                        <input type="number" id="num-questions-input" value="5" min="1" max="10" class="w-full p-2 border border-slate-300 rounded-md bg-white shadow-sm focus:ring-teal-500 focus:border-teal-500">
                    </div>
                    <button id="start-test-btn" class="w-full bg-teal-600 text-white font-semibold py-3 px-4 rounded-md hover:bg-teal-700 transition-colors inline-flex items-center justify-center">
                        Start Test
                    </button>
                    <div id="test-loading-indicator" class="mt-4 text-center hidden">
                        <div class="spinner mx-auto"></div>
                        <p class="text-slate-600 mt-2">Generating questions, please wait...</p>
                    </div>
                    <div id="test-error-message" class="mt-4 p-3 bg-red-100 text-red-700 rounded-md hidden"></div>
                </div>

                <div id="test-active-area" class="hidden mt-8 bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-slate-800">Question <span id="current-question-num">1</span> of <span id="total-questions-num"></span></h3>
                        <div class="text-lg font-semibold text-teal-700">Time Left: <span id="timer">00:00</span></div>
                    </div>
                    <div id="question-display" class="mb-6 text-lg text-slate-700"></div>
                    <div id="options-display" class="space-y-3 mb-6"></div>
                    <div class="flex justify-between">
                        <button id="prev-question-btn" class="bg-slate-200 text-slate-700 font-semibold py-2 px-4 rounded-md hover:bg-slate-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">Previous</button>
                        <button id="next-question-btn" class="bg-teal-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">Next</button>
                    </div>
                    <button id="submit-test-btn" class="w-full mt-6 bg-blue-600 text-white font-semibold py-3 px-4 rounded-md hover:bg-blue-700 transition-colors">Submit Test</button>
                </div>

                <div id="test-results-area" class="hidden mt-8 bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
                    <h3 class="text-xl font-bold text-center text-slate-800 mb-4">Test Results</h3>
                    <p class="text-center text-lg text-slate-700 mb-4">You scored <span id="score-display" class="font-bold text-teal-700"></span> out of <span id="total-score-display" class="font-bold text-teal-700"></span>.</p>
                    <div id="detailed-results" class="space-y-4"></div>
                    <button id="retake-test-btn" class="w-full mt-6 bg-teal-600 text-white font-semibold py-3 px-4 rounded-md hover:bg-teal-700 transition-colors">Retake Test</button>
                </div>
            </section>


            <section id="analysis" class="mt-16 pt-8 border-t border-slate-200">
                <h2 class="text-2xl sm:text-3xl font-bold text-center text-slate-800 mb-2">Key Insights at a Glance</h2>
                <p class="text-center max-w-3xl mx-auto text-slate-600 mb-12">The following charts visualize key findings from the report, highlighting trends in solution availability and the primary sources for exam materials.</p>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                         <h3 class="text-xl font-semibold text-center mb-4">Solution Availability by Exam Body</h3>
                        <div class="chart-container">
                            <canvas id="solutionChart"></canvas>
                        </div>
                    </div>
                     <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-semibold text-center mb-4">Primary Resource Providers</h3>
                        <div class="chart-container" style="max-width: 400px; height: 400px;">
                            <canvas id="providerChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <section id="recommendations" class="mt-16 pt-8 border-t border-slate-200">
                <h2 class="text-2xl sm:text-3xl font-bold text-center text-slate-800 mb-12">Recommendations for Aspirants</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                </div>
                <div class="text-center mt-8">
                    <button id="generate-study-tip-btn" class="bg-teal-600 text-white font-semibold py-3 px-6 rounded-md hover:bg-teal-700 transition-colors inline-flex items-center justify-center">
                        ✨ Get a Study Tip
                    </button>
                    <div id="study-tip-output" class="mt-4 p-4 bg-teal-50 text-teal-800 rounded-md shadow-inner max-w-xl mx-auto hidden"></div>
                </div>
            </section>

        </main>
        
        <footer class="bg-white mt-16 border-t border-slate-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 text-center text-sm text-slate-500">
                <p>Data synthesized from the "Comprehensive Compendium of Physics PGT and TGT Previous Year Question Papers" report.</p>
                <p>This is an interactive visualization for educational purposes.</p>
            </div>
        </footer>
    </div>
    
    <div id="modal-container" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4 hidden">
        <div id="modal-content" class="bg-white rounded-lg shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        </div>
    </div>

    <script type="module">
        const { initializeApp, getAuth, signInAnonymously, signInWithCustomToken, onAuthStateChanged, getFirestore, collection, addDoc, getDocs, query, where } = window.firebase;

        // Firebase variables
        let app;
        let db;
        let auth;
        let userId;

        // Initialize Firebase and authenticate
        async function initializeFirebase() {
            try {
                const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
                const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : {};

                if (Object.keys(firebaseConfig).length === 0) {
                    console.error("Firebase config is not defined. Cannot initialize Firestore.");
                    return;
                }

                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                auth = getAuth(app);

                // Sign in anonymously or with custom token
                if (typeof __initial_auth_token !== 'undefined') {
                    await signInWithCustomToken(auth, __initial_auth_token);
                } else {
                    await signInAnonymously(auth);
                }

                // Get current user ID
                onAuthStateChanged(auth, (user) => {
                    if (user) {
                        userId = user.uid;
                        console.log("Firebase initialized. User ID:", userId);
                    } else {
                        console.log("No user signed in.");
                        userId = crypto.randomUUID(); // Use a random ID if not authenticated
                    }
                });

            } catch (error) {
                console.error("Error initializing Firebase:", error);
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            await initializeFirebase(); // Initialize Firebase when DOM is ready

            const examData = [
                // Central Government Exams
                {
                    id: 'emrs-physics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Physics',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'General EMRS PGT papers (Physics content not consistently identified)', solutionStatus: 'Unavailable', solutionText: 'Specific PGT Physics papers with solutions are not readily available. Ambiguity surrounds the nature of solutions; often not detailed step-by-step.',
                    links: [{ text: 'EMRS Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-chemistry-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Chemistry',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Readily available', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions are explicitly provided, including clear explanations and step-by-step application.',
                    links: [{ text: 'EMRS PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/emrs-pgt/previous-year-papers' }]
                },
                {
                    id: 'emrs-biology-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Biology',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available through various platforms', solutionStatus: 'Mixed', solutionText: 'Solutions are generally provided. While not always explicitly "detailed," emphasis on "enhancing problem-solving skills" implies more than basic answer keys.',
                    links: [{ text: 'EMRS PGT Biology Papers (Testbook)', url: 'https://testbook.com/emrs-pgt/previous-year-papers' }]
                },
                {
                    id: 'emrs-mathematics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Mathematics',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Accessible', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "detailed solutions" for PGT Mathematics questions.',
                    links: [{ text: 'EMRS PGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-mathematics-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Mathematics',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available for download', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "detailed solutions" for TGT Mathematics questions.',
                    links: [{ text: 'EMRS TGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-english-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'English',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are available, with some sources confirming "detailed solutions" for English questions.',
                    links: [{ text: 'EMRS PGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-english-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'English',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are available, with specific sources confirming "detailed solutions" for TGT English questions.',
                    links: [{ text: 'EMRS TGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-hindi-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Hindi',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are available, with some sources confirming "detailed solutions" for Hindi questions.',
                    links: [{ text: 'EMRS PGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-hindi-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Hindi',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are available, with some sources explicitly confirming "detailed solutions" for TGT Hindi questions.',
                    links: [{ text: 'EMRS TGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-social-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'Social Science',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "detailed solutions" for TGT Social Studies questions.',
                    links: [{ text: 'EMRS TGT Social Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/emrs-previous-year-question-papers' }]
                },
                {
                    id: 'emrs-general-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Eklavya Model Residential Schools (EMRS)', shortName: 'EMRS', subject: 'General Science',
                    description: 'EMRS is an initiative by the Ministry of Tribal Affairs, providing quality education to tribal students and recruiting its own teaching staff.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general statements indicating their utility. Explicit confirmation of "detailed step-by-step" solutions is not as consistently highlighted as for specific PGT subjects.',
                    links: [{ text: 'EMRS TGT Science Previous Year Papers (IFAS Online)', url: 'https://ifasonline.com/emrs-tgt/science-previous-year-question-paper/678e4512160063a6861393d6/650f618714ccaa507d741cff/678e45d2160063a68613996d' }]
                },
                {
                    id: 'kvs-physics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Physics',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: '2018, 2017, 2013', solutionStatus: 'Mixed', solutionText: 'Varies: Some platforms claim "detailed solutions," while others provide answer keys or sample questions. Aspirants should verify the content.',
                    links: [
                        { text: 'KVS Papers (JagranJosh)', url: 'https://www.jagranjosh.com/exams/kvs/question-papers' },
                        { text: 'KVS Papers (Adda247)', url: 'https://www.adda247.com/teaching-jobs-exam/kvs-previous-year-papers/' },
                        { text: 'KVS Papers (PW.Live - claims detailed solutions)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }
                    ]
                },
                {
                    id: 'kvs-chemistry-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Chemistry',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided with these papers. Some sources generally claim "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/kvs-pgt/previous-year-papers' }]
                },
                {
                    id: 'kvs-biology-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Biology',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, and some sources explicitly confirm "detailed solutions" for KVS PGT Biology.',
                    links: [{ text: 'KVS PGT Biology Papers (Testbook)', url: 'https://testbook.com/kvs-pgt/previous-year-papers' }]
                },
                {
                    id: 'kvs-mathematics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Mathematics',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS PGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-mathematics-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Mathematics',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS TGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-english-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'English',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS PGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-english-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'English',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS TGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-hindi-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Hindi',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS PGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-hindi-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Hindi',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS TGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-social-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'Social Science',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with general claims of "detailed solutions" for KVS papers.',
                    links: [{ text: 'KVS TGT Social Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/kvs-previous-year-question-papers' }]
                },
                {
                    id: 'kvs-general-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Kendriya Vidyalaya Sangathan (KVS)', shortName: 'KVS', subject: 'General Science',
                    description: 'KVS is an autonomous body under the Ministry of Education, managing Kendriya Vidyalaya schools nationwide. Its recruitment drives are highly anticipated for PGT and TGT positions.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for KVS TGT Science.',
                    links: [{ text: 'KVS TGT Science Papers (Testbook)', url: 'https://testbook.com/kvs-tgt/previous-year-papers' }]
                },
                {
                    id: 'nvs-physics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Physics',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Dec 2022, Mar 2019', solutionStatus: 'Mixed', solutionText: 'Varies: Some platforms claim "detailed solutions," but others may only offer papers with answer keys. In-depth explanations are often part of paid courses.',
                    links: [
                        { text: 'NVS PGT Papers (Testbook)', url: 'https://testbook.com/nvs-pgt/previous-year-papers' },
                        { text: 'NVS PGT March 2019 (Adda247)', url: 'https://www.adda247.com/teaching-jobs-exam/nvs-previous-year-paper/' },
                        { text: 'NVS Papers (PW.Live - claims detailed solutions)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }
                    ]
                },
                {
                    id: 'nvs-chemistry-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Chemistry',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Some sources generally claim "detailed solutions" for NVS papers, but explicit confirmation for detailed step-by-step solutions for Chemistry is not consistently clear.',
                    links: [{ text: 'NVS PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/nvs-pgt/previous-year-papers' }]
                },
                {
                    id: 'nvs-biology-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Biology',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some sources generally stating "detailed solutions" for NVS PGT papers.',
                    links: [{ text: 'NVS PGT Biology Papers (Testbook)', url: 'https://testbook.com/nvs-pgt/previous-year-papers' }]
                },
                {
                    id: 'nvs-mathematics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Mathematics',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some sources generally stating "detailed solutions" for NVS papers.',
                    links: [{ text: 'NVS PGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-mathematics-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Mathematics',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Answer Key Only', solutionText: 'Solutions are provided, but "answer keys" are often specified for NVS TGT Mathematics.',
                    links: [{ text: 'NVS TGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-english-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'English',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for NVS papers.',
                    links: [{ text: 'NVS PGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-english-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'English',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for NVS papers.',
                    links: [{ text: 'NVS TGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-hindi-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Hindi',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for NVS papers.',
                    links: [{ text: 'NVS PGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-hindi-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Hindi',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some general claims of "detailed solutions" for NVS papers.',
                    links: [{ text: 'NVS TGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-social-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'Social Science',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Answer Key Only', solutionText: 'Solutions are provided, with some sources specifically mentioning "answer keys".',
                    links: [{ text: 'NVS TGT Social Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/nvs-previous-year-question-papers' }]
                },
                {
                    id: 'nvs-general-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Navodaya Vidyalaya Samiti (NVS)', shortName: 'NVS', subject: 'General Science',
                    description: 'NVS manages residential schools for talented rural children. It conducts national-level recruitment for teaching positions.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Testbook explicitly provides "Solved Problems with Detailed Solutions" for NVS TGT Science questions.',
                    links: [{ text: 'NVS TGT Science Papers (Testbook)', url: 'https://testbook.com/nvs-tgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-physics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Physics',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: '2021, 2018, 2015', solutionStatus: 'Answer Key Only', solutionText: 'Available papers typically come with answer keys. Detailed, step-by-step solutions are generally not provided in the free PDFs.',
                    links: [
                        { text: 'DSSSB PGT Physics Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' },
                        { text: 'DSSSB Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }
                    ]
                },
                {
                    id: 'dsssb-chemistry-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Chemistry',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: '2015, 2021', solutionStatus: 'Unavailable', solutionText: 'Information regarding the detail of solutions is often unspecified or stated as "unavailable".',
                    links: [{ text: 'DSSSB PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-biology-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Biology',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: '2018, 2021', solutionStatus: 'Unavailable', solutionText: 'Information on detailed solutions is often "unavailable" or not specified.',
                    links: [{ text: 'DSSSB PGT Biology Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-mathematics-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Mathematics',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Mixed', solutionText: 'Some books offer "solved papers". Explicit confirmation of "detailed step-by-step" solutions from online platforms is not consistently clear.',
                    links: [{ text: 'DSSSB PGT Mathematics Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-mathematics-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Mathematics',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Some books explicitly stating "detailed solutions" for DSSSB TGT Mathematics.',
                    links: [{ text: 'DSSSB TGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }]
                },
                {
                    id: 'dsssb-english-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'English',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "complete solutions" or "detailed explanations" for English books.',
                    links: [{ text: 'DSSSB PGT English Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-english-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'English',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "complete solutions" or "detailed explanations" for English books.',
                    links: [{ text: 'DSSSB TGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }]
                },
                {
                    id: 'dsssb-hindi-pgt', governance: 'Central', level: 'PGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Hindi',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Unavailable', solutionText: 'Information is often "unavailable" or not specified if solutions are detailed.',
                    links: [{ text: 'DSSSB PGT Hindi Papers (Testbook)', url: 'https://testbook.com/dsssb-pgt/previous-year-papers' }]
                },
                {
                    id: 'dsssb-hindi-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Hindi',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for DSSSB TGT Hindi.',
                    links: [{ text: 'DSSSB TGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }]
                },
                {
                    id: 'dsssb-natural-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Natural Science',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Commercial study guides explicitly claim "detailed answer explanations" for DSSSB TGT Natural Science, covering Physics, Chemistry, Biology, and Pedagogy.',
                    links: [{ text: 'DSSSB TGT Natural Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }]
                },
                {
                    id: 'dsssb-social-science-tgt', governance: 'Central', level: 'TGT', state: '', name: 'Delhi Subordinate Services Selection Board (DSSSB)', shortName: 'DSSSB', subject: 'Social Science',
                    description: 'DSSSB is the primary recruiting body for various government positions, including teachers, within the National Capital Territory of Delhi.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for TGT Social Studies questions (referencing EMRS).',
                    links: [{ text: 'DSSSB TGT Social Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/dsssb-previous-year-question-papers' }]
                },
                // State Government Exams
                {
                    id: 'upsessb-physics-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Physics',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: '2000-2021 (multiple years)', solutionStatus: 'Detailed Solutions', solutionText: 'A comprehensive collection of PGT Physics papers with detailed solutions is available, particularly from specialized platforms like PhysicsScholar.',
                    links: [
                        { text: 'UP PGT Physics Solutions (PhysicsScholar)', url: 'https://physicsscholar.com/up-pgt-physics-free-self-study-guide-notes-pyqs/' },
                        { text: 'UP TGT Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-tgt-previous-year-question-papers' }
                    ]
                },
                {
                    id: 'upsessb-chemistry-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Chemistry',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: '2015, 2016', solutionStatus: 'Detailed Solutions', solutionText: 'Several sources explicitly confirm "detailed solutions" for UP PGT Chemistry.',
                    links: [{ text: 'UP PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/up-pgt/previous-year-papers' }]
                },
                {
                    id: 'upsessb-biology-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Biology',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Testbook provides "Solved Problems with Detailed Solutions" for UP PGT Biology.',
                    links: [{ text: 'UP PGT Biology Papers (Testbook)', url: 'https://testbook.com/up-pgt/previous-year-papers' }]
                },
                {
                    id: 'upsessb-mathematics-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Mathematics',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided, with some books explicitly confirming "detailed solutions" for UP PGT Mathematics.',
                    links: [{ text: 'UP PGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-pgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-mathematics-tgt', governance: 'State', level: 'TGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Mathematics',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some books explicitly confirming "detailed solutions" for UP TGT Mathematics.',
                    links: [{ text: 'UP TGT Mathematics Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-tgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-english-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'English',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "detailed solutions" for UP PGT English.',
                    links: [{ text: 'UP PGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-pgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-english-tgt', governance: 'State', level: 'TGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'English',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "complete solutions" for UP TGT English.',
                    links: [{ text: 'UP TGT English Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-tgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-hindi-pgt', governance: 'State', level: 'PGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Hindi',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly confirming "detailed solutions" for UP PGT Hindi.',
                    links: [{ text: 'UP PGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-pgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-hindi-tgt', governance: 'State', level: 'TGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Hindi',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'UP TGT Hindi Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-tgt-previous-year-question-papers' }]
                },
                {
                    id: 'upsessb-social-science-tgt', governance: 'State', level: 'TGT', state: 'Uttar Pradesh', name: 'Uttar Pradesh (UPSESSB)', shortName: 'UP', subject: 'Social Science',
                    description: 'The UPSESSB recruits teachers for government-aided schools across the state.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'UP TGT Social Science Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/up-tgt-previous-year-question-papers' }]
                },
                {
                    id: 'rpsc-physics-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Physics',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: '2011, 2015, 2018, 2022', solutionStatus: 'Mixed', solutionText: 'Varies: Resources range from answer keys to detailed solved problems and even video solutions. Platforms like Testbook and some YouTube channels offer detailed explanations.',
                    links: [
                        { text: 'RPSC School Lecturer Solutions (Testbook)', url: 'https://testbook.com/questions/rpsc-school-lecturer-physics-questions--6729deaa9b25a41cdf543c26' },
                        { text: 'RPSC 1st Grade Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/rpsc-1st-grade-teachers-previous-year-question-papers' }
                    ]
                },
                {
                    id: 'rpsc-chemistry-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Chemistry',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'RPSC 1st Grade Chemistry Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-biology-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Biology',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Detailed Solutions', solutionText: 'Testbook explicitly provides "Solved Problems with Detailed Solutions" for RPSC School Lecturer Biology.',
                    links: [{ text: 'RPSC School Lecturer Biology Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-mathematics-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Mathematics',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'RPSC 1st Grade Mathematics Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-english-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'English',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'RPSC 1st Grade English Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-hindi-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Hindi',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'RPSC 1st Grade Hindi Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-history-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'History',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available for various years', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'RPSC 1st Grade History Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'rpsc-political-science-pgt', governance: 'State', level: 'PGT', state: 'Rajasthan', name: 'Rajasthan Public Service Commission (RPSC)', shortName: 'RPSC', subject: 'Political Science',
                    description: 'RPSC conducts recruitment for various state government posts, including 1st Grade Teachers (PGT/School Lecturer).',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Testbook explicitly provides "Solved Problems with Detailed Solutions" for RPSC School Lecturer Political Science.',
                    links: [{ text: 'RPSC School Lecturer Political Science Papers (Testbook)', url: 'https://testbook.com/rpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'bseb-physics-pgt', governance: 'State', level: 'PGT', state: 'Bihar', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Physics',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 2 (Physics) available', solutionStatus: 'Mixed', solutionText: 'Varies: STET papers are available with "solutions" or "answers," but the depth is not always specified. Commercial platforms list books with "Solved Papers & Practice Book" specifically for PGT Physics.',
                    links: [
                        { text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' },
                        { text: 'Bihar STET Papers (Adda247)', url: 'https://www.adda247.com/teaching-jobs-exam/bihar-tet-previous-year-paper-2/' }
                    ]
                },
                {
                    id: 'bseb-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Chemistry',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 2 includes Chemistry for Class 11-12', solutionStatus: 'Mixed', solutionText: 'Solutions are provided. However, explicit confirmation of detailed step-by-step solutions is not consistently clear.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Biology',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 2 includes Biology for Class 11-12', solutionStatus: 'Mixed', solutionText: 'Solutions are provided. However, explicit confirmation of detailed step-by-step solutions is not consistently clear.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Mathematics',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Mathematics content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Mathematics',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Mathematics content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-english-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'English',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include English content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'English',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include English content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-hindi-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Hindi',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Hindi content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Hindi',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Hindi content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-history-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'History',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 2 includes History content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-social-science-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Social Science',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Social Science content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Social Science',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 1 and 2 include Social Science content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'bseb-political-science-pgt', governance: 'State', level: 'PGT', state: '', name: 'Bihar School Examination Board (BSEB)', shortName: 'Bihar', subject: 'Political Science',
                    description: 'The BSEB conducts the State Teacher Eligibility Test (STET), which is a qualifying exam. Final recruitment may be through a separate exam (e.g., by BPSC).',
                    papers: 'STET Paper 2 includes Political Science content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Bihar STET Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/bihar-stet-previous-year-question-papers' }]
                },
                {
                    id: 'wbssc-physics-pgt', governance: 'State', level: 'PGT', state: 'West Bengal', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Physics',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Limited', solutionText: 'Solutions are mentioned, but explicit detail about "step-by-step explanations" is not consistently specified. Direct inspection of downloaded files is recommended.',
                    links: [
                        { text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' },
                        { text: 'WBSSC SLST Papers (Adda247)', url: 'https://www.adda247.com/teaching-jobs-exam/wbssc-slst-assistant-teacher-previous-year-question-paper/' }
                    ]
                },
                {
                    id: 'wbssc-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Chemistry',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Biology',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Mathematics',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Mathematics',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'English',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Hindi',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'wbssc-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'West Bengal School Service Commission (WBSSC)', shortName: 'WBSSC', subject: 'Social Science',
                    description: 'The WBSSC conducts the State Level Selection Test (SLST) for recruiting Assistant Teachers for classes IX-X (TGT) and XI-XII (PGT).',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'WBSSC SLST Papers (RRBApply.com)', url: 'https://rrbapply.com/wbssc-slst-previous-question-papers/' }]
                },
                {
                    id: 'tn-trb-physics-pgt', governance: 'State', level: 'PGT', state: 'Tamil Nadu', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Physics',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Feb 2024, 2017, 2012', solutionStatus: 'Detailed Solutions', solutionText: 'Key platforms like Testbook explicitly provide detailed solutions that explain underlying concepts and problem-solving techniques, making them highly valuable.',
                    links: [
                        { text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' },
                         { text: 'TN TRB Papers with Solutions (Testbook)', url: 'https://testbook.com/tn-trb/previous-year-papers' }
                    ]
                },
                {
                    id: 'tn-trb-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Chemistry',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Feb 2024', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with the explicit statement that they help in understanding underlying concepts and problem-solving techniques.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Biology',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Botany and Zoology official papers available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with the explicit statement that they help in understanding underlying concepts and problem-solving techniques.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Mathematics',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Available', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with the explicit statement that they help in understanding underlying concepts and problem-solving techniques.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-english-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'English',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-hindi-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Hindi',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'TN SET Hindi previous year papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'TN SET Hindi Papers (Testbook)', url: 'https://testbook.com/tn-set/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-history-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'History',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-social-science-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Social Science',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'TN TRB Graduate Teacher Papers (Testbook)', url: 'https://testbook.com/tn-trb-graduate-teacher/previous-year-papers' }]
                },
                {
                    id: 'tn-trb-political-science-pgt', governance: 'State', level: 'PGT', state: '', name: 'Tamil Nadu Teachers Recruitment Board (TN TRB)', shortName: 'TN TRB', subject: 'Political Science',
                    description: 'The TN TRB recruits teachers for government schools in Tamil Nadu, including Graduate Teacher positions.',
                    papers: 'TN SET Political Science papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'TN SET Political Science Papers (Testbook)', url: 'https://testbook.com/tn-set/previous-year-papers' }]
                },
                {
                    id: 'kea-physics-pgt', governance: 'State', level: 'PGT', state: 'Karnataka', name: 'Karnataka Examinations Authority (KEA)', shortName: 'KEA', subject: 'Physics',
                    description: 'The KEA conducts various examinations, including KCET, which can be relevant for PGT/TGT level Physics.',
                    papers: 'KCET Physics 2024, 2025', solutionStatus: 'Mixed', solutionText: 'KCET Physics answer keys and question paper solutions are released by coaching institutes and KEA. Some sources explicitly mention "answer key solutions" by various coaching institutes, implying focus on detailed explanations.',
                    links: [{ text: 'KCET Physics Question Papers (Testbook)', url: 'https://testbook.com/kcet/previous-year-papers' }]
                },
                {
                    id: 'kea-mathematics-general', governance: 'State', level: 'PGT', state: '', name: 'Karnataka Examinations Authority (KEA)', shortName: 'KEA', subject: 'Mathematics',
                    description: 'The KEA conducts various examinations, including KCET, which can be relevant for PGT/TGT level Mathematics.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'KEA provides previous year question papers for Mathematics.',
                    links: [{ text: 'KCET Mathematics Question Papers (Testbook)', url: 'https://testbook.com/kcet/previous-year-papers' }]
                },
                {
                    id: 'kea-biology-general', governance: 'State', level: 'PGT', state: '', name: 'Karnataka Examinations Authority (KEA)', shortName: 'KEA', subject: 'Biology',
                    description: 'The KEA conducts various examinations, including KCET, which can be relevant for PGT/TGT level Biology.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'KEA provides previous year question papers for Biology.',
                    links: [{ text: 'KCET Biology Question Papers (Testbook)', url: 'https://testbook.com/kcet/previous-year-papers' }]
                },
                {
                    id: 'kea-chemistry-general', governance: 'State', level: 'PGT', state: '', name: 'Karnataka Examinations Authority (KEA)', shortName: 'KEA', subject: 'Chemistry',
                    description: 'The KEA conducts various examinations, including KCET, which can be relevant for PGT/TGT level Chemistry.',
                    papers: 'Available', solutionStatus: 'Solutions provided', solutionText: 'KEA provides previous year question papers for Chemistry.',
                    links: [{ text: 'KCET Chemistry Question Papers (Testbook)', url: 'https://testbook.com/kcet/previous-year-papers' }]
                },
                {
                    id: 'mppeb-physics-pgt', governance: 'State', level: 'PGT', state: 'Madhya Pradesh', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Physics',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School Physics 2019', solutionStatus: 'Mixed', solutionText: 'While overall statement mentions "Solution PDF", it is not explicitly stated whether these are detailed step-by-step explanations for High School Physics.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Chemistry',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School Chemistry available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for MP TET papers. However, the nature of solutions for High School Chemistry is not consistently detailed.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Biology',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School Biology available', solutionStatus: 'Mixed', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for MP TET papers. However, the nature of solutions for Biology content is not consistently detailed across all sources.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Mathematics',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School Mathematics available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Mathematics',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET Middle School Mathematics available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-english-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'English',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School English available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'English',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET Middle School English available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-hindi-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Hindi',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School Hindi available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Hindi',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET Middle School Hindi available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-history-pgt', governance: 'State', level: 'PGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'History',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET High School History available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'mppeb-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Madhya Pradesh Professional Examination Board (MPPEB)', shortName: 'MPPEB', subject: 'Social Science',
                    description: 'The MPPEB conducts the Madhya Pradesh Teacher Eligibility Test (MP TET) for various teaching levels.',
                    papers: 'MP TET Middle School Social Science available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'MPTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/mptet-previous-year-question-papers' }]
                },
                {
                    id: 'pstet-physics-tgt', governance: 'State', level: 'TGT', state: 'Punjab', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Physics',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 includes Mathematics and Science', solutionStatus: 'Answer Key Only', solutionText: 'While general statements indicate "answer key PDF" and "solutions" are available, it is not explicitly specified whether detailed step-by-step solutions are provided for Physics content.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-chemistry-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Chemistry',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 Mathematics and Science would include Chemistry content', solutionStatus: 'Mixed', solutionText: 'While solutions are generally mentioned, explicit confirmation of detailed solutions for Chemistry content is not consistently provided.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-biology-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Biology',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 Mathematics and Science would include Biology content', solutionStatus: 'Mixed', solutionText: 'Solutions are generally mentioned.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Mathematics',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 Mathematics and Science includes Mathematics content', solutionStatus: 'Mixed', solutionText: 'Solutions are provided. However, explicit confirmation of detailed solutions is not consistently provided.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'English',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 includes English content', solutionStatus: 'Mixed', solutionText: 'Solutions are provided. However, explicit confirmation of detailed solutions is not consistently provided.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Hindi',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 includes Hindi content', solutionStatus: 'Mixed', solutionText: 'Solutions are provided. However, explicit confirmation of detailed solutions is not consistently provided.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'pstet-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Punjab Teacher Eligibility Test (PSTET)', shortName: 'PSTET', subject: 'Social Science',
                    description: 'The Punjab School Education Board conducts the PSTET annually.',
                    papers: 'PSTET Paper 2 includes Social Studies/Social Science content', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'PSTET Previous Year Question Paper (DailyJobAlert.in)', url: 'https://dailyjobalert.in/pstet-previous-year-question-paper/' }]
                },
                {
                    id: 'hppsc-physics-pgt', governance: 'State', level: 'PGT', state: 'Himachal Pradesh', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Physics',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: '2024', solutionStatus: 'Answer Key Only', solutionText: 'For HPPSC PGT Physics, "Answer Key" is explicitly mentioned. Explicit confirmation of detailed step-by-step solutions is not consistently provided.',
                    links: [{ text: 'HPPSC PGT(Physics) Question Paper (Himexam.com)', url: 'https://himexam.com/hppsc-pgtphysics-question-paper-held-on-11-april-2024/' }]
                },
                {
                    id: 'hppsc-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Chemistry',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for HP PGT Chemistry.',
                    links: [{ text: 'HP PGT Chemistry Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Biology',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with some sources explicitly stating "detailed solutions" for HP PGT Biology.',
                    links: [{ text: 'HP PGT Biology Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Mathematics',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'HP PGT Mathematics Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-english-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'English',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'HP PGT English Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-hindi-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Hindi',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'HP PGT Hindi Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-history-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'History',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'HP PGT History Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'hppsc-political-science-pgt', governance: 'State', level: 'PGT', state: '', name: 'Himachal Pradesh Public Service Commission (HPPSC)', shortName: 'HPPSC', subject: 'Political Science',
                    description: 'HPPSC conducts PGT recruitment.',
                    papers: 'Available for download', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'HP PGT Political Science Papers (Testbook)', url: 'https://testbook.com/hp-pgt/previous-year-papers' }]
                },
                {
                    id: 'jkssb-chemistry-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'Chemistry',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKSET Chemical Sciences papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'JKSET Chemical Sciences Papers (Testbook)', url: 'https://testbook.com/jkset/previous-year-papers' }]
                },
                {
                    id: 'jkssb-biology-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'Biology',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKPSC School Lecturer Zoology available', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions are provided for JKPSC School Lecturer Zoology, including "Expert Solutions: Detailed explanations by experienced Zoology professionals".',
                    links: [{ text: 'JKPSC School Lecturer Zoology Papers (Testbook)', url: 'https://testbook.com/jkpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'jkssb-mathematics-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'Mathematics',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKSSB Topic-wise Solved Question Papers include Mathematics', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with "answer key and detailed explanations".',
                    links: [{ text: 'JKSSB Solved Papers (Kashmir Book Store)', url: 'https://kashmirbookstore.com/product/jkssb-previous-years-solved-papers-2019-2025/' }]
                },
                {
                    id: 'jkssb-english-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'English',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKSSB Topic-wise Solved Question Papers include General English', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with "answer key and detailed explanations".',
                    links: [{ text: 'JKSSB Solved Papers (Kashmir Book Store)', url: 'https://kashmirbookstore.com/product/jkssb-previous-years-solved-papers-2019-2025/' }]
                },
                {
                    id: 'jkssb-hindi-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'Hindi',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKPSC School Lecturer Hindi previous year papers available', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions are provided for JKPSC School Lecturer Hindi, including "step-by-step solutions to each question".',
                    links: [{ text: 'JKPSC School Lecturer Hindi Papers (Testbook)', url: 'https://testbook.com/jkpsc-school-lecturer/previous-year-papers' }]
                },
                {
                    id: 'jkssb-history-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'History',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKSSB Topic-wise Solved Question Papers include History', solutionStatus: 'Detailed Solutions', solutionText: 'Solutions are provided, with "answer key and detailed explanations".',
                    links: [{ text: 'JKSSB Solved Papers (Kashmir Book Store)', url: 'https://kashmirbookstore.com/product/jkssb-previous-years-solved-papers-2019-2025/' }]
                },
                {
                    id: 'jkssb-political-science-pgt', governance: 'State', level: 'PGT', state: 'Jammu & Kashmir', name: 'Jammu and Kashmir Services Selection Board (JKSSB)', shortName: 'JKSSB', subject: 'Political Science',
                    description: 'The JKSSB conducts various recruitment examinations, and previous year papers are considered important for preparation.',
                    papers: 'JKSET Political Science papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'JKSET Political Science Papers (Testbook)', url: 'https://testbook.com/jkset/previous-year-papers' }]
                },
                {
                    id: 'cgpeb-physics-tgt', governance: 'State', level: 'TGT', state: 'Chhattisgarh', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Physics',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 includes Math and Science', solutionStatus: 'Detailed Solutions', solutionText: 'Some sources explicitly state that detailed solutions in both PDF and video format are provided for CGTET previous year question papers.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-chemistry-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Chemistry',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 Math and Science would include Chemistry content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers. This implies detailed solutions for Chemistry content.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-biology-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Biology',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 Math and Science would include Biology content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers. This implies detailed solutions for Biology content.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Mathematics',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 Math and Science includes Mathematics content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'English',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 includes English content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Hindi',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 includes Hindi content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'cgpeb-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Chhattisgarh Professional Examination Board (CGPEB)', shortName: 'CGPEB', subject: 'Social Science',
                    description: 'The CGPEB conducts the Chhattisgarh Teacher Eligibility Test (CGTET).',
                    papers: 'CGTET Paper 2 includes Social Science content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for CGTET previous year question papers.',
                    links: [{ text: 'CGTET Previous Year Question Papers (PW.Live)', url: 'https://www.pw.live/teaching/exams/cg-tet-previous-year-question-papers' }]
                },
                {
                    id: 'utet-physics-tgt', governance: 'State', level: 'TGT', state: 'Uttarakhand', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Physics',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 2 includes Math and Science', solutionStatus: 'Detailed Solutions', solutionText: 'For UTET, detailed solutions in both PDF and video format are provided for previous year question papers. This indicates that detailed explanations are available for Physics content within the Math and Science papers.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-chemistry-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Chemistry',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 2 Math and Science would include Chemistry content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers. This implies detailed solutions for Chemistry content.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-biology-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Biology',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 2 Math and Science would include Biology content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers. This implies detailed solutions for Biology content.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Mathematics',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 2 Math and Science includes Mathematics content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'English',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 1 and 2 include English content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Hindi',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 1 and 2 include Hindi content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'utet-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Uttarakhand Board of School Education (UBSE)', shortName: 'UBSE', subject: 'Social Science',
                    description: 'The UBSE conducts the Uttarakhand Teacher Eligibility Test (UTET).',
                    papers: 'UTET Paper 2 Social Science papers available', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions in both PDF and video format are provided for UTET previous year question papers.',
                    links: [{ text: 'UTET Previous Year Question Papers (JagranJosh)', url: 'https://www.jagranjosh.com/articles/utet-previous-year-question-papers-download-pdf-uttarakhand-tet-paper-1-and-2-1728386284-1' }]
                },
                {
                    id: 'ktet-physics-tgt', governance: 'State', level: 'TGT', state: 'Kerala', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Physics',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category III Physical Science papers (2019, 2020)', solutionStatus: 'Detailed Solutions', solutionText: 'Some sources explicitly state that "Problems with detailed solutions" are provided for K-TET Physical Science Category III. This indicates that detailed solutions are generally available for KTET Physical Science, which covers both Physics and Chemistry.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-chemistry-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Chemistry',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category III Physical Science includes Chemistry content', solutionStatus: 'Detailed Solutions', solutionText: 'Detailed solutions are provided for KTET Physical Science.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-biology-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Biology',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category III Natural Science papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided for KTET Natural Science and Biological Science papers.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Mathematics',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category I, II, and III Mathematics papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'English',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category I, II, and III English papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Hindi',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category III Hindi papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'ktet-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Kerala Teacher Eligibility Test (KTET)', shortName: 'KTET', subject: 'Social Science',
                    description: 'The Kerala Pareeksha Bhavan conducts the KTET.',
                    papers: 'KTET Category III Social Science papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'KTET Previous Year Question Papers (Testbook)', url: 'https://testbook.com/ktet-exam/previous-year-papers' }]
                },
                {
                    id: 'gujarat-tet-physics-tgt', governance: 'State', level: 'TGT', state: 'Gujarat', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Physics',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET Paper 2 Maths & Science papers available', solutionStatus: 'Mixed', solutionText: 'For Gujarat TET Paper 2 Maths & Science, an answer key is explicitly mentioned for the 2021 paper. However, explicit confirmation of detailed step-by-step solutions for Physics content is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-physics-pgt', governance: 'State', level: 'PGT', state: 'Gujarat', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'Physics',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET Physical Sciences papers (Paper II and Paper III) available from 2002 to 2024', solutionStatus: 'Answer Key Only', solutionText: 'For Gujarat SET Physical Sciences, answer keys are provided. However, the official Gujarat SET website does not explicitly state solutions for its Physical Sciences papers.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-set-chemistry-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'Chemistry',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET Chemical Sciences papers (Paper II and Paper III) available from 2002 to 2024', solutionStatus: 'Mixed', solutionText: 'For Gujarat SET Chemical Sciences, answer keys are provided. However, explicit confirmation of detailed step-by-step solutions is not consistently provided.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-chemistry-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Chemistry',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET Paper 2 Maths & Science would include Chemistry content', solutionStatus: 'Mixed', solutionText: 'For Gujarat TET Paper 2 Maths & Science, an answer key is explicitly mentioned for the 2021 paper. However, explicit confirmation of detailed step-by-step solutions for Chemistry content is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-biology-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'Biology',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET Life Sciences papers (Paper II and Paper III) available from 2002 to 2024', solutionStatus: 'Mixed', solutionText: 'For Gujarat SET Life Sciences, solutions are provided. However, explicit confirmation of detailed step-by-step solutions is not consistently provided.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-biology-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Biology',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET Paper 2 Maths & Science would include Biology content', solutionStatus: 'Mixed', solutionText: 'For Gujarat TET Paper 2 Maths & Science, an answer key is explicitly mentioned for the 2021 paper. However, explicit confirmation of detailed step-by-step solutions for Biology content is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-mathematics-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'Mathematics',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET Mathematical Sciences papers (Paper II and Paper III) available from 2002 to 2024', solutionStatus: 'Answer Key Only', solutionText: 'For Gujarat SET Mathematical Sciences, answer keys are provided.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-mathematics-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Mathematics',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET Paper 2 Maths & Science includes Mathematics content', solutionStatus: 'Mixed', solutionText: 'For Gujarat TET Paper 2 Maths & Science, an answer key is explicitly mentioned for the 2021 paper. However, explicit confirmation of detailed step-by-step solutions is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-english-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'English',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET English papers available', solutionStatus: 'Answer Key Only', solutionText: 'Answer keys are provided for Gujarat SET English.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-english-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'English',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET includes English as Language II', solutionStatus: 'Mixed', solutionText: 'Answer keys are provided for Gujarat TET English. Explicit confirmation of detailed solutions is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-hindi-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'Hindi',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET Hindi papers available', solutionStatus: 'Answer Key Only', solutionText: 'Answer keys are provided for Gujarat SET Hindi.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-hindi-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Hindi',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET includes Hindi as Language II', solutionStatus: 'Mixed', solutionText: 'Answer keys are provided for Gujarat TET Hindi. Explicit confirmation of detailed solutions is not consistently provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                },
                {
                    id: 'gujarat-set-history-pgt', governance: 'State', level: 'PGT', state: '', name: 'Gujarat State Eligibility Test (SET)', shortName: 'Gujarat SET', subject: 'History',
                    description: 'Gujarat SET is for Assistant Professor/Lecturer.',
                    papers: 'Gujarat SET History papers available', solutionStatus: 'Answer Key Only', solutionText: 'Answer keys are provided for Gujarat SET History.',
                    links: [{ text: 'Old Question Papers (Gujarat SET Official)', url: 'https://www.gujaratset.ac.in/en/oldpap' }]
                },
                {
                    id: 'gujarat-tet-social-science-tgt', governance: 'State', level: 'TGT', state: '', name: 'Gujarat Teacher Eligibility Test (TET)', shortName: 'Gujarat TET', subject: 'Social Science',
                    description: 'Gujarat TET is for school teachers.',
                    papers: 'Gujarat TET Paper 2 Social Science papers available', solutionStatus: 'Solutions provided', solutionText: 'Solutions are provided.',
                    links: [{ text: 'Gujarat TET Recruitment Previous Year Question Paper (Prepp.in)', url: 'https://prepp.in/gujarat-tet-exam/previous-year-question-paper' }]
                }
            ];

            const recommendationsData = [
                {
                    title: "Prioritize Recent Papers",
                    icon: "📅",
                    text: "Focus on the last 5-10 years of papers as they best reflect current exam patterns and syllabi."
                },
                {
                    title: "Verify Solution Depth",
                    icon: "🔎",
                    text: "Always check if 'solutions' are detailed explanations or just answer keys. For Physics, detailed solutions are crucial."
                },
                {
                    title: "Leverage Multiple Platforms",
                    icon: "📚",
                    text: "Don't rely on one source. Cross-reference papers and solutions across different platforms to find the best materials."
                },
                {
                    title: "Utilize Video Resources",
                    icon: "▶️",
                    text: "Supplement PDFs with video solutions from platforms like YouTube for complex problems."
                },
                {
                    title: "Understand Exam Type",
                    icon: "📄",
                    text: "Distinguish between eligibility tests (TETs) and direct recruitment exams, as their patterns may differ."
                },
                {
                    title: "Adapt to Subject Grouping",
                    icon: "🧪",
                    text: "For TGT roles, be prepared to find Physics questions within broader 'Science' or 'Physical Science' papers."
                },
                {
                    title: "Consider Paid Resources",
                    icon: "💳",
                    text: "If free resources are insufficient, reputable paid courses can offer more comprehensive, in-depth solutions."
                },
                {
                    title: "Practice Structurally",
                    icon: "⏱️",
                    text: "Use past papers for timed mock tests to improve speed, accuracy, and exam temperament."
                }
            ];

            const cardsContainer = document.getElementById('exam-cards-container');
            const stateSelector = document.getElementById('state-selector');
            const governanceFilter = document.getElementById('governance-filter');
            const levelFilter = document.getElementById('level-filter');
            const subjectFilter = document.getElementById('subject-filter'); // New subject filter
            const stateFilterContainer = document.getElementById('state-filter-container');
            const resultsCount = document.getElementById('results-count');
            const noResultsDiv = document.getElementById('no-results');
            const modalContainer = document.getElementById('modal-container');
            const modalContent = document.getElementById('modal-content');
            const generateStudyTipBtn = document.getElementById('generate-study-tip-btn');
            const studyTipOutput = document.getElementById('study-tip-output');

            // Mock Test System Elements
            const testConfigSection = document.getElementById('test-config');
            const testActiveArea = document.getElementById('test-active-area');
            const testResultsArea = document.getElementById('test-results-area');
            const startTestBtn = document.getElementById('start-test-btn');
            const testLoadingIndicator = document.getElementById('test-loading-indicator');
            const testErrorMessage = document.getElementById('test-error-message');
            const testSubjectSelector = document.getElementById('test-subject-selector');
            const testLevelSelector = document.getElementById('test-level-selector');
            const numQuestionsInput = document.getElementById('num-questions-input');
            const currentQuestionNumSpan = document.getElementById('current-question-num');
            const totalQuestionsNumSpan = document.getElementById('total-questions-num');
            const timerSpan = document.getElementById('timer');
            const questionDisplay = document.getElementById('question-display');
            const optionsDisplay = document.getElementById('options-display');
            const prevQuestionBtn = document.getElementById('prev-question-btn');
            const nextQuestionBtn = document.getElementById('next-question-btn');
            const submitTestBtn = document.getElementById('submit-test-btn');
            const scoreDisplay = document.getElementById('score-display');
            const totalScoreDisplay = document.getElementById('total-score-display');
            const detailedResultsDiv = document.getElementById('detailed-results');
            const retakeTestBtn = document.getElementById('retake-test-btn');

            let filters = {
                governance: 'all',
                level: 'all',
                subject: 'all', // Initialize new subject filter
                state: 'all'
            };

            let currentTestQuestions = [];
            let currentQuestionIndex = 0;
            let userAnswers = [];
            let timerInterval;
            let timeLeft = 0; // In seconds
            const TIME_PER_QUESTION_SECONDS = 60; // 1 minute per question

            function getSolutionBadge(status) {
                switch (status) {
                    case 'Detailed Solutions':
                        return 'bg-green-100 text-green-800';
                    case 'Mixed':
                        return 'bg-yellow-100 text-yellow-800';
                    case 'Limited':
                        return 'bg-orange-100 text-orange-800';
                     case 'Answer Key Only':
                        return 'bg-blue-100 text-blue-800';
                    case 'Unavailable':
                        return 'bg-red-100 text-red-800';
                    case 'Solutions provided':
                        return 'bg-purple-100 text-purple-800';
                    default:
                        return 'bg-slate-100 text-slate-800';
                }
            }

            function renderCards(data) {
                cardsContainer.innerHTML = '';
                 if (data.length === 0) {
                    resultsCount.textContent = 'Showing 0 results.';
                    noResultsDiv.classList.remove('hidden');
                    return;
                }
                noResultsDiv.classList.add('hidden');
                
                resultsCount.textContent = `Showing ${data.length} result(s).`;
                data.forEach(exam => {
                    const card = document.createElement('div');
                    card.className = 'bg-white rounded-lg shadow-md p-6 flex flex-col hover:shadow-xl hover:-translate-y-1 transition-all duration-300';
                    card.innerHTML = `
                        <div class="flex-grow">
                            <div class="flex justify-between items-start">
                                <h3 class="text-xl font-bold text-slate-800">${exam.name}</h3>
                                <span class="text-xs font-semibold px-2 py-1 ${getSolutionBadge(exam.solutionStatus)} rounded-full">${exam.solutionStatus}</span>
                            </div>
                            <p class="text-sm text-slate-500 mb-2">${exam.governance} | ${exam.state || 'N/A'}</p>
                            <p class="text-base font-semibold text-teal-700 mb-4">${exam.subject} (${exam.level})</p>
                            <p class="text-sm text-slate-600 mb-4">${exam.description}</p>
                            <div class="text-sm mb-4">
                               <p class="font-semibold mb-1">Available Papers:</p>
                               <p class="text-slate-600">${exam.papers}</p>
                            </div>
                        </div>
                        <button data-id="${exam.id}" class="view-details-btn w-full mt-4 bg-teal-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-teal-700 transition-colors">View Details</button>
                    `;
                    cardsContainer.appendChild(card);
                });
            }

            function populateStates() {
                const states = [...new Set(examData.filter(e => e.state).map(e => e.state))].sort();
                states.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state;
                    option.textContent = state;
                    stateSelector.appendChild(option);
                });
            }

            function applyFilters() {
                let filteredData = examData;

                if (filters.governance !== 'all') {
                    filteredData = filteredData.filter(e => e.governance === filters.governance);
                }

                if (filters.level !== 'all') {
                    filteredData = filteredData.filter(e => e.level === filters.level);
                }
                
                if (filters.subject !== 'all') {
                    filteredData = filteredData.filter(e => e.subject === filters.subject);
                }
                
                if (filters.governance === 'State' && filters.state !== 'all') {
                    filteredData = filteredData.filter(e => e.state === filters.state);
                }

                renderCards(filteredData);
            }

            function handleFilterClick(e) {
                if (!e.target.matches('button[data-filter]')) return;
                
                const button = e.target;
                const group = button.dataset.filterGroup;
                const value = button.dataset.filter;
                
                filters[group] = value;
                
                document.querySelectorAll(`button[data-filter-group="${group}"]`).forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                if (group === 'governance') {
                    if (value === 'State') {
                        stateFilterContainer.classList.remove('hidden');
                    } else {
                        stateFilterContainer.classList.add('hidden');
                        filters.state = 'all'; 
                        stateSelector.value = 'all';
                    }
                }
                
                applyFilters();
            }
            
            async function generateQuestion(exam) {
                const questionOutputDiv = document.getElementById('question-output');
                const generateQuestionBtn = document.getElementById('generate-question-btn');
                
                questionOutputDiv.innerHTML = '<div class="flex items-center justify-center py-4"><div class="spinner"></div><span class="ml-2 text-slate-600">Generating question...</span></div>';
                generateQuestionBtn.disabled = true;

                try {
                    let chatHistory = [];
                    // Refined prompt to encourage diverse and high-quality questions with explanation
                    const prompt = `Generate a single, challenging, and original multiple-choice ${exam.subject} question suitable for a ${exam.level} level teacher recruitment exam in India. The question should test conceptual understanding or problem-solving skills. Provide 4 distinct and plausible options (A, B, C, D). Clearly indicate the correct answer and provide a detailed, step-by-step explanation for why that answer is correct, including any relevant formulas or principles. The explanation should be comprehensive enough for a student to learn from it. Do not include any introductory or concluding remarks, just the question, options, correct answer, and explanation.`;
                    
                    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
                    const payload = { contents: chatHistory };
                    const apiKey = "";
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                    
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    const result = await response.json();

                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        const text = result.candidates[0].content.parts[0].text;
                        questionOutputDiv.innerHTML = `<div class="p-4 bg-slate-100 rounded-md text-slate-700 whitespace-pre-wrap">${text}</div>`;
                    } else {
                        questionOutputDiv.innerHTML = '<p class="text-red-600">Failed to generate question. Please try again.</p>';
                    }
                } catch (error) {
                    console.error("Error generating question:", error);
                    questionOutputDiv.innerHTML = `<p class="text-red-600">Error: ${error.message}. Please check console for details.</p>`;
                } finally {
                    generateQuestionBtn.disabled = false;
                }
            }

            async function generateStudyTip() {
                const generateBtn = document.getElementById('generate-study-tip-btn');
                
                studyTipOutput.classList.remove('hidden');
                studyTipOutput.innerHTML = '<div class="flex items-center justify-center py-2"><div class="spinner"></div><span class="ml-2 text-teal-700">Generating tip...</span></div>';
                generateBtn.disabled = true;

                try {
                    let chatHistory = [];
                    const prompt = "Provide a single, concise, and actionable study tip for preparing for competitive physics teacher exams in India. The tip should be general and applicable to various exams (PGT/TGT). Do not include any introductory or concluding remarks, just the tip.";
                    
                    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
                    const payload = { contents: chatHistory };
                    const apiKey = "";
                    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                    
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    const result = await response.json();

                    if (result.candidates && result.candidates.length > 0 &&
                        result.candidates[0].content && result.candidates[0].content.parts &&
                        result.candidates[0].content.parts.length > 0) {
                        const text = result.candidates[0].content.parts[0].text;
                        studyTipOutput.innerHTML = `<p class="text-teal-800">${text}</p>`;
                    } else {
                        studyTipOutput.innerHTML = '<p class="text-red-600">Failed to generate tip. Please try again.</p>';
                    }
                } catch (error) {
                    console.error("Error generating study tip:", error);
                    studyTipOutput.innerHTML = `<p class="text-red-600">Error: ${error.message}.</p>`;
                } finally {
                    generateBtn.disabled = false;
                }
            }

            function openModal(examId) {
                const exam = examData.find(e => e.id === examId);
                if (!exam) return;

                modalContent.innerHTML = `
                    <div class="p-6 sm:p-8">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h2 class="text-2xl font-bold text-slate-800">${exam.name}</h2>
                                <p class="text-sm text-slate-500">${exam.governance} | ${exam.state || 'N/A'}</p>
                                <p class="text-lg font-semibold text-teal-700 mt-2">${exam.subject} (${exam.level})</p>
                            </div>
                            <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800 text-2xl">&times;</button>
                        </div>
                        <div class="space-y-6">
                            <div>
                                <h4 class="font-semibold text-slate-700 mb-2">About the Exam Body</h4>
                                <p class="text-slate-600 text-base">${exam.description}</p>
                            </div>
                             <div>
                                <h4 class="font-semibold text-slate-700 mb-2">Available Papers & Years</h4>
                                <p class="text-slate-600">${exam.papers}</p>
                            </div>
                            <div>
                                <h4 class="font-semibold text-slate-700 mb-2">Solution Status</h4>
                                 <div class="flex items-center gap-x-3">
                                    <span class="text-xs font-semibold px-2 py-1 ${getSolutionBadge(exam.solutionStatus)} rounded-full">${exam.solutionStatus}</span>
                                    <p class="text-slate-600 text-base">${exam.solutionText}</p>
                                </div>
                            </div>
                             <div>
                                <h4 class="font-semibold text-slate-700 mb-2">Key Access Links</h4>
                                <div class="space-y-2 mb-4">
                                    ${exam.links.map(link => `<a href="${link.url}" target="_blank" rel="noopener noreferrer" class="block bg-slate-100 p-3 rounded-md hover:bg-teal-50 text-teal-700 font-medium transition-colors">${link.text} &rarr;</a>`).join('')}
                                </div>
                            </div>
                            <div class="border-t border-slate-200 pt-6">
                                <h4 class="font-semibold text-slate-700 mb-4">Practice with AI ✨</h4>
                                <button id="generate-question-btn" class="bg-blue-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                                    Generate Practice Question ✨
                                </button>
                                <div id="question-output" class="mt-4 text-slate-700"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                modalContainer.classList.remove('hidden');
                modalContainer.classList.add('modal-enter');
                document.body.style.overflow = 'hidden';
                
                document.getElementById('close-modal-btn').addEventListener('click', closeModal);
                document.getElementById('generate-question-btn').addEventListener('click', () => generateQuestion(exam));
            }
            
            function closeModal() {
                 modalContainer.classList.remove('modal-enter');
                 modalContainer.classList.add('modal-leave');
                 setTimeout(() => {
                    modalContainer.classList.add('hidden');
                    modalContainer.classList.remove('modal-leave');
                    modalContent.innerHTML = '';
                    document.body.style.overflow = 'auto';
                }, 300);
            }
            
            function renderRecommendations() {
                const container = document.querySelector('#recommendations .grid');
                recommendationsData.forEach(rec => {
                    const item = document.createElement('div');
                    item.className = 'bg-white p-6 rounded-lg shadow-md flex items-start gap-4';
                    item.innerHTML = `
                        <div class="text-2xl">${rec.icon}</div>
                        <div>
                            <h4 class="font-semibold text-slate-800">${rec.title}</h4>
                            <p class="text-sm text-slate-600">${rec.text}</p>
                        </div>
                    `;
                    container.appendChild(item);
                });
            }
            
            function renderCharts() {
                const solutionCtx = document.getElementById('solutionChart').getContext('2d');
                const providerCtx = document.getElementById('providerChart').getContext('2d');
                
                // Aggregate solution status counts across all subjects
                const solutionStatusCounts = {
                    'Detailed Solutions': 0, 'Mixed': 0, 'Limited': 0, 'Answer Key Only': 0, 'Unavailable': 0, 'Solutions provided': 0
                };
                examData.forEach(exam => {
                    if (solutionStatusCounts.hasOwnProperty(exam.solutionStatus)) {
                         solutionStatusCounts[exam.solutionStatus]++;
                    }
                });

                const chartLabels = Object.keys(solutionStatusCounts);
                const chartData = Object.values(solutionStatusCounts);
                const chartColors = ['#10B981', '#F59E0B', '#F97316', '#3B82F6', '#EF4444', '#8B5CF6']; // Added purple for 'Solutions provided'

                new Chart(solutionCtx, {
                    type: 'bar',
                    data: {
                        labels: chartLabels,
                        datasets: [{
                            label: 'Number of Exam Entries',
                            data: chartData,
                            backgroundColor: chartColors,
                            borderColor: chartColors.map(color => color.replace('100', '400').replace('50', '200')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        responsive: true,
                        plugins: {
                            title: { display: false },
                            legend: { display: false },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.label}: ${context.raw}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                ticks: {
                                    autoSkip: false,
                                    maxRotation: 45,
                                    minRotation: 45,
                                    callback: function(value, index, values) {
                                        const label = this.getLabelForValue(value);
                                        if (label.length > 16) {
                                            return label.split(' ').join('\n'); // Simple word wrap
                                        }
                                        return label;
                                    }
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Number of Entries'
                                }
                            }
                        }
                    }
                });
                
                const providerData = {
                    labels: ['Online Aggregators (Testbook, Adda247, etc.)', 'Specialized Platforms (PhysicsScholar)', 'Official Websites', 'Video Platforms (YouTube)', 'Commercial Books'],
                    datasets: [{
                        label: 'Resource Providers',
                        data: [60, 10, 5, 15, 10], // Adjusted percentages to reflect all sources
                        backgroundColor: ['#0d9488', '#0f766e', '#9ca3af', '#f87171', '#6d28d9'],
                        hoverOffset: 4
                    }]
                };

                new Chart(providerCtx, {
                    type: 'doughnut',
                    data: providerData,
                    options: {
                        maintainAspectRatio: false,
                        responsive: true,
                        plugins: {
                             legend: { display: true, position: 'bottom' },
                             tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed !== null) {
                                            label += context.parsed + '%';
                                        }
                                        return label;
                                    }
                                }
                             }
                        }
                    }
                });
            }

            // Mock Test System Functions
            async function startTest() {
                if (!db || !userId) {
                    testErrorMessage.textContent = 'Database not initialized. Please try again later.';
                    testErrorMessage.classList.remove('hidden');
                    return;
                }

                const subject = testSubjectSelector.value;
                const level = testLevelSelector.value;
                const numQuestions = parseInt(numQuestionsInput.value);

                if (isNaN(numQuestions) || numQuestions < 1 || numQuestions > 10) {
                    testErrorMessage.textContent = 'Please enter a valid number of questions (1-10).';
                    testErrorMessage.classList.remove('hidden');
                    return;
                }
                testErrorMessage.classList.add('hidden');

                testConfigSection.classList.add('hidden');
                testLoadingIndicator.classList.remove('hidden');
                startTestBtn.disabled = true;

                try {
                    // Check Firestore for existing questions
                    const q = query(collection(db, `artifacts/${__app_id}/users/${userId}/generated_questions`),
                                    where("subject", "==", subject),
                                    where("level", "==", level),
                                    where("numQuestions", "==", numQuestions));
                    const querySnapshot = await getDocs(q);

                    if (!querySnapshot.empty) {
                        // Found existing questions, use the first one
                        currentTestQuestions = querySnapshot.docs[0].data().questions;
                        console.log("Loaded questions from Firestore.");
                    } else {
                        // No existing questions, generate new ones
                        let chatHistory = [];
                        // Refined prompt to encourage diverse and high-quality questions with explanation and source tag
                        const prompt = `Generate ${numQuestions} multiple-choice ${subject} questions suitable for a ${level} level teacher recruitment exam in India. Ensure diversity in topics and question types (conceptual, numerical, factual). Each question should have 4 distinct and plausible options (A, B, C, D). Specify the correct answer and provide a detailed, comprehensive explanation for why that answer is correct, including any relevant formulas, principles, or historical context. The explanation should be suitable for a student to learn from it. Also, include a 'source_tag' for each question, indicating it is 'AI Generated - ${subject} ${level}'. Provide the output as a JSON array of objects, where each object has 'question' (string), 'options' (an array of 4 strings), 'correctAnswer' (string, e.g., 'A', 'B', 'C', or 'D'), 'explanation' (string), and 'source_tag' (string).`;
                        
                        chatHistory.push({ role: "user", parts: [{ text: prompt }] });
                        const payload = {
                            contents: chatHistory,
                            generationConfig: {
                                responseMimeType: "application/json",
                                responseSchema: {
                                    type: "ARRAY",
                                    items: {
                                        type: "OBJECT",
                                        properties: {
                                            "question": { "type": "STRING" },
                                            "options": {
                                                "type": "ARRAY",
                                                "items": { "type": "STRING" },
                                                "minItems": 4,
                                                "maxItems": 4
                                            },
                                            "correctAnswer": { "type": "STRING" },
                                            "explanation": { "type": "STRING" },
                                            "source_tag": { "type": "STRING" } // New field for internal tracking
                                        },
                                        "propertyOrdering": ["question", "options", "correctAnswer", "explanation", "source_tag"]
                                    }
                                }
                            }
                        };
                        const apiKey = "";
                        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                        
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });
                        const result = await response.json();

                        if (result.candidates && result.candidates.length > 0 &&
                            result.candidates[0].content && result.candidates[0].content.parts &&
                            result.candidates[0].content.parts.length > 0) {
                            const jsonText = result.candidates[0].content.parts[0].text;
                            currentTestQuestions = JSON.parse(jsonText);
                            
                            if (currentTestQuestions.length === 0) {
                                testErrorMessage.textContent = 'No questions generated. Please try again.';
                                testErrorMessage.classList.remove('hidden');
                                testConfigSection.classList.remove('hidden');
                                return;
                            }

                            // Save generated questions to Firestore
                            await addDoc(collection(db, `artifacts/${__app_id}/users/${userId}/generated_questions`), {
                                subject: subject,
                                level: level,
                                numQuestions: numQuestions,
                                timestamp: new Date(),
                                questions: currentTestQuestions
                            });
                            console.log("Saved new questions to Firestore.");

                        } else {
                            testErrorMessage.textContent = 'Failed to parse generated questions. Please try again.';
                            testErrorMessage.classList.remove('hidden');
                            testConfigSection.classList.remove('hidden');
                            return;
                        }
                    }

                    currentQuestionIndex = 0;
                    userAnswers = Array(currentTestQuestions.length).fill(null);
                    timeLeft = currentTestQuestions.length * TIME_PER_QUESTION_SECONDS;

                    testLoadingIndicator.classList.add('hidden');
                    testActiveArea.classList.remove('hidden');
                    totalQuestionsNumSpan.textContent = currentTestQuestions.length;
                    startTimer();
                    displayQuestion();

                } catch (error) {
                    console.error("Error starting test or generating questions:", error);
                    testErrorMessage.textContent = `Error: ${error.message}. Could not generate or load questions.`;
                    testErrorMessage.classList.remove('hidden');
                    testConfigSection.classList.remove('hidden');
                } finally {
                    startTestBtn.disabled = false;
                    testLoadingIndicator.classList.add('hidden');
                }
            }

            function startTimer() {
                clearInterval(timerInterval);
                timerInterval = setInterval(() => {
                    timeLeft--;
                    updateTimerDisplay();
                    if (timeLeft <= 0) {
                        clearInterval(timerInterval);
                        submitTest();
                    }
                }, 1000);
            }

            function updateTimerDisplay() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerSpan.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            function displayQuestion() {
                const question = currentTestQuestions[currentQuestionIndex];
                currentQuestionNumSpan.textContent = currentQuestionIndex + 1;
                questionDisplay.innerHTML = `<p>${currentQuestionIndex + 1}. ${question.question}</p>`;
                optionsDisplay.innerHTML = '';

                question.options.forEach((option, idx) => {
                    const optionChar = String.fromCharCode(65 + idx); // A, B, C, D
                    const optionHtml = `
                        <label class="option-label flex items-center p-3 border border-slate-300 rounded-md cursor-pointer hover:bg-slate-100 transition-colors">
                            <input type="radio" name="question-${currentQuestionIndex}" value="${optionChar}" class="hidden" ${userAnswers[currentQuestionIndex] === optionChar ? 'checked' : ''}>
                            <span class="w-5 h-5 border border-slate-400 rounded-full flex items-center justify-center mr-3 text-sm flex-shrink-0">
                                ${optionChar}
                            </span>
                            <span class="text-slate-700 flex-grow">${option}</span>
                        </label>
                    `;
                    optionsDisplay.innerHTML += optionHtml;
                });

                document.querySelectorAll(`input[name="question-${currentQuestionIndex}"]`).forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        userAnswers[currentQuestionIndex] = e.target.value;
                    });
                });

                updateNavigationButtons();
            }

            function updateNavigationButtons() {
                prevQuestionBtn.disabled = currentQuestionIndex === 0;
                nextQuestionBtn.disabled = currentQuestionIndex === currentTestQuestions.length - 1;
            }

            function navigateQuestion(direction) {
                if (direction === 'next' && currentQuestionIndex < currentTestQuestions.length - 1) {
                    currentQuestionIndex++;
                } else if (direction === 'prev' && currentQuestionIndex > 0) {
                    currentQuestionIndex--;
                }
                displayQuestion();
            }

            function submitTest() {
                clearInterval(timerInterval);
                let correctCount = 0;
                detailedResultsDiv.innerHTML = '';

                currentTestQuestions.forEach((question, index) => {
                    const userAnswer = userAnswers[index];
                    const isCorrect = userAnswer === question.correctAnswer;
                    if (isCorrect) {
                        correctCount++;
                    }

                    const resultItem = document.createElement('div');
                    resultItem.className = `p-4 rounded-md ${isCorrect ? 'bg-green-50' : 'bg-red-50'} border ${isCorrect ? 'border-green-200' : 'border-red-200'}`;
                    resultItem.innerHTML = `
                        <p class="font-semibold text-slate-800 mb-2">${index + 1}. ${question.question}</p>
                        <ul class="list-disc list-inside text-sm text-slate-700 mb-2">
                            ${question.options.map((opt, i) => `<li>${String.fromCharCode(65 + i)}. ${opt}</li>`).join('')}
                        </ul>
                        <p class="text-sm">Your Answer: <span class="font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}">${userAnswer || 'Not Answered'}</span></p>
                        <p class="text-sm">Correct Answer: <span class="font-medium text-green-700">${question.correctAnswer}</span></p>
                        ${question.explanation ? `<div class="mt-2 p-2 bg-slate-100 rounded-md text-sm text-slate-700"><strong>Explanation:</strong> ${question.explanation}</div>` : ''}
                    `;
                    detailedResultsDiv.appendChild(resultItem);
                });

                scoreDisplay.textContent = correctCount;
                totalScoreDisplay.textContent = currentTestQuestions.length;

                testActiveArea.classList.add('hidden');
                testResultsArea.classList.remove('hidden');
            }

            function retakeTest() {
                testResultsArea.classList.add('hidden');
                testConfigSection.classList.remove('hidden');
                currentTestQuestions = [];
                userAnswers = [];
                currentQuestionIndex = 0;
                timeLeft = 0;
                clearInterval(timerInterval);
                timerSpan.textContent = '00:00';
            }

            // Event Listeners
            governanceFilter.addEventListener('click', handleFilterClick);
            levelFilter.addEventListener('click', handleFilterClick);
            subjectFilter.addEventListener('click', handleFilterClick); // New event listener for subject filter
            stateSelector.addEventListener('change', (e) => {
                filters.state = e.target.value;
                applyFilters();
            });

            cardsContainer.addEventListener('click', (e) => {
                const button = e.target.closest('.view-details-btn');
                if (button) {
                    openModal(button.dataset.id);
                }
            });
            
            modalContainer.addEventListener('click', (e) => {
                if (e.target === modalContainer) {
                    closeModal();
                }
            });

            generateStudyTipBtn.addEventListener('click', generateStudyTip);

            // Mock Test System Event Listeners
            startTestBtn.addEventListener('click', startTest);
            prevQuestionBtn.addEventListener('click', () => navigateQuestion('prev'));
            nextQuestionBtn.addEventListener('click', () => navigateQuestion('next'));
            submitTestBtn.addEventListener('click', submitTest);
            retakeTestBtn.addEventListener('click', retakeTest);

            // Initial render
            populateStates();
            applyFilters(); // Initial render with all filters set to 'all'
            renderRecommendations();
            renderCharts();
        });
    </script>
</body>
</html>

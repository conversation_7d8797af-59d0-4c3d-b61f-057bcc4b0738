# AI-Powered Mock Test System

A comprehensive modular platform for government and teacher recruitment exam preparation featuring AI-generated questions, adaptive learning, and performance analytics.

## Features

- **Smart Mock Tests**: AI-powered exam simulations with syllabus-based question generation
- **Comprehensive Analytics**: Detailed performance tracking with subject-wise and progress charts
- **Syllabus Management**: Browse, create, and manage syllabi for various exams
- **Immediate Feedback**: Detailed explanations and answer verification
- **Challenge System**: Question validity and answer verification system
- **Responsive Design**: Works seamlessly across desktop and mobile devices

## Tech Stack

- **Frontend**: HTML, JavaScript, Tailwind CSS, Chart.js
- **Backend**: Node.js, Express.js
- **Database**: Firebase Firestore
- **Authentication**: Firebase Authentication
- **AI**: Google Gemini LLM API

## Directory Structure

```
├── public/                  # Frontend assets
│   ├── css/                 # Stylesheets
│   ├── images/              # Image assets
│   ├── js/                  # JavaScript modules
│   │   ├── analytics.js     # Analytics dashboard module
│   │   ├── app.js           # Main application controller
│   │   ├── auth.js          # Authentication module
│   │   ├── config.js        # Firebase configuration
│   │   ├── examExplorer.js  # Exam exploration module
│   │   ├── mockTest.js      # Mock test module
│   │   └── syllabus.js      # Syllabus management module
│   └── index.html           # Main HTML entry point
├── server/                  # Backend code
│   ├── routes/              # API route definitions
│   │   ├── questions.js     # Question API endpoints
│   │   ├── syllabi.js       # Syllabus API endpoints
│   │   └── testSessions.js  # Test session & analytics endpoints
│   └── services/            # Business logic services
├── src/                     # Core application code
└── .env                     # Environment variables (not in repo)
```

## 🚀 Quick Start (No Firebase Setup Required)

Want to try the app immediately? Follow these steps:

```bash
# 1. Clone and install
git clone https://github.com/yourusername/ai-powered-mock-test.git
cd ai-powered-mock-test
npm install

# 2. Start in development mode (uses mock database)
npm run dev

# 3. Open browser
open http://localhost:3000
```

The app will run with a **mock database** - perfect for testing and development!

## Setup Instructions

### Prerequisites

- Node.js v14+ and npm
- Firebase account (free tier available) - **Optional for development**
- Google Cloud account with Gemini API access - **Optional for AI features**

## 🔥 Firebase Setup (Required)

Firebase provides the database (Firestore) and authentication for this application. Here's how to set it up:

### Step 1: Create Firebase Project

1. **Go to Firebase Console**: Visit [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. **Create New Project**: Click "Create a project"
3. **Project Name**: Enter a name like `ai-mock-test-system`
4. **Google Analytics**: Choose "Enable" (recommended) or "Not now"
5. **Create Project**: Click "Create project" and wait for setup

### Step 2: Enable Required Services

#### Enable Firestore Database
1. In Firebase Console, go to **"Firestore Database"**
2. Click **"Create database"**
3. **Security Rules**: Choose **"Start in test mode"** (for development)
4. **Location**: Choose your preferred region (e.g., `us-central1`)
5. Click **"Done"**

#### Enable Authentication (Optional but Recommended)
1. Go to **"Authentication"** → **"Get started"**
2. Go to **"Sign-in method"** tab
3. Enable **"Email/Password"** provider
4. Click **"Save"**

### Step 3: Get Firebase Configuration

#### For Frontend (Client SDK)
1. Go to **Project Settings** (gear icon)
2. Scroll to **"Your apps"** section
3. Click **"Web app"** icon (`</>`)
4. **App nickname**: Enter `ai-mock-test-frontend`
5. **Firebase Hosting**: Check this box
6. Click **"Register app"**
7. **Copy the config object** - you'll need this for `public/js/config.js`

#### For Backend (Admin SDK)
1. In **Project Settings**, go to **"Service accounts"** tab
2. Click **"Generate new private key"**
3. Click **"Generate key"** - this downloads a JSON file
4. **Keep this file secure** - it contains admin credentials

### Step 4: Install Firebase CLI

```bash
npm install -g firebase-tools
```

### Step 5: Initialize Firebase in Your Project

```bash
# Login to Firebase
firebase login

# Initialize Firebase in your project directory
firebase init
```

**During `firebase init`, choose these options:**

1. **Which Firebase features?**
   - ✅ **Firestore: Configure security rules and indexes**
   - ✅ **Hosting: Configure files for Firebase Hosting**
   - ✅ **Storage: Configure a security rules file for Cloud Storage**

2. **Use an existing project?**
   - ✅ **Use an existing project**
   - Select your project from the list

3. **Firestore Setup:**
   - **Rules file**: Press Enter (default: `firestore.rules`)
   - **Indexes file**: Press Enter (default: `firestore.indexes.json`)

4. **Hosting Setup:**
   - **Public directory**: Enter `public`
   - **Single-page app**: Enter `y` (Yes)
   - **Automatic builds**: Enter `N` (No)
   - **Overwrite index.html**: Enter `N` (No)

5. **Storage Setup:**
   - **Rules file**: Press Enter (default: `storage.rules`)

### Environment Variables Setup

Create a `.env` file in the root directory:

```bash
# Server Configuration
PORT=3000

# Firebase Admin SDK Configuration (from the downloaded JSON file)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Google Gemini API (get from Google AI Studio)
GEMINI_API_KEY=your-gemini-api-key

# Optional: Redis (for production caching)
ENABLE_REDIS=false
REDIS_HOST=localhost
REDIS_PORT=6379
```

### Step 6: Configure Frontend Firebase

Update `public/js/config.js` with your Firebase config:

```javascript
// Firebase configuration (from Step 3)
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};

// Initialize Firebase
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
```

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-powered-mock-test.git
   cd ai-powered-mock-test
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (see above)

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open `http://localhost:3000` in your browser

## 🔧 How Firebase Integration Works

### Database Structure
The application uses Firestore with these main collections:

```
📁 users/                    # User profiles and progress
  └── {userId}/
      ├── profile/           # User information
      ├── gamification/      # XP, levels, badges
      └── analytics/         # Performance data

📁 questions/                # Generated questions
  └── {questionId}/
      ├── question           # Question text
      ├── options           # Multiple choice options
      ├── correctAnswer     # Correct answer
      └── metadata          # Subject, difficulty, etc.

📁 testSessions/            # Test attempts and results
  └── {sessionId}/
      ├── userId            # Who took the test
      ├── questions         # Questions asked
      ├── answers           # User responses
      └── results           # Scores and analytics

📁 syllabi/                 # Exam syllabi
  └── {syllabusId}/
      ├── title             # Syllabus name
      ├── subjects          # Subject breakdown
      └── content           # Detailed content
```

### Development vs Production

#### Development Mode (Default)
- Uses **Mock Firebase** (no internet required)
- Data stored in memory (resets on restart)
- Perfect for development and testing
- No Firebase credentials needed

#### Production Mode
- Uses **Real Firebase** (requires setup)
- Data persisted in cloud
- Requires Firebase credentials in `.env`
- Enable by setting up Firebase properly

### Switching Between Modes

The application automatically detects:
- If Firebase credentials are valid → Uses real Firebase
- If Firebase credentials missing/invalid → Uses mock Firebase
- You'll see: `"Failed to initialize Firebase with service account, falling back to mock DB"`

## 🚨 Troubleshooting Firebase Setup

### Common Issues & Solutions

#### 1. "Failed to initialize Firebase" Error
**Cause**: Missing or incorrect Firebase credentials
**Solution**:
- Check your `.env` file has correct values
- Ensure `FIREBASE_PRIVATE_KEY` includes `\n` characters
- Verify project ID matches your Firebase project

#### 2. "Permission denied" Errors
**Cause**: Firestore security rules too restrictive
**Solution**:
```javascript
// In Firebase Console → Firestore → Rules, use:
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true; // For development only!
    }
  }
}
```

#### 3. "Module not found" Errors
**Cause**: Missing Firebase dependencies
**Solution**:
```bash
npm install firebase firebase-admin
```

#### 4. Frontend Firebase Connection Issues
**Cause**: Incorrect frontend configuration
**Solution**: Update `public/js/config.js` with correct Firebase config

### Getting Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click **"Create API Key"**
3. Copy the key and add to `.env` as `GEMINI_API_KEY`

### Testing Your Setup

1. **Start the server**: `npm run dev`
2. **Check logs**: Should see "Worker running on port 3000"
3. **Test database**: Create a test question through the UI
4. **Check Firebase Console**: Verify data appears in Firestore

## 🎯 When Do You Need Firebase?

### Development Phase
- **Not Required**: App works with mock database
- **Optional**: Set up for testing real Firebase features
- **Recommended**: Set up before production deployment

### Production Deployment
- **Required**: Real database needed for persistent data
- **Required**: User authentication for multi-user support
- **Required**: Data backup and security

### Feature Requirements

| Feature | Mock DB | Firebase Required |
|---------|---------|-------------------|
| Basic Testing | ✅ | ❌ |
| Question Generation | ✅ | ❌ |
| User Accounts | ❌ | ✅ |
| Data Persistence | ❌ | ✅ |
| Multi-user Support | ❌ | ✅ |
| Production Deployment | ❌ | ✅ |

## Deployment (Phase 6)

### Google Cloud Platform Deployment

1. Create a GCP project and enable Cloud Run
2. Build and deploy the container:
   ```
   gcloud builds submit --tag gcr.io/PROJECT_ID/mock-test-app
   gcloud run deploy mock-test-app --image gcr.io/PROJECT_ID/mock-test-app --platform managed
   ```

3. Configure environment variables in the Cloud Run service

### Firebase Deployment Alternative

1. Install Firebase CLI:
   ```
   npm install -g firebase-tools
   ```

2. Initialize Firebase:
   ```
   firebase login
   firebase init
   ```

3. Deploy the application:
   ```
   firebase deploy
   ```

## CI/CD Pipeline Setup

### GitHub Actions Workflow

Create `.github/workflows/main.yml`:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm ci
      - run: npm test
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Cloud Run
        uses: google-github-actions/deploy-cloudrun@main
        with:
          service: mock-test-app
          image: gcr.io/${{ secrets.GCP_PROJECT_ID }}/mock-test-app
          credentials: ${{ secrets.GCP_SA_KEY }}
```

## Monitoring and Logging

### Application Monitoring

1. Set up Google Cloud Monitoring for application performance
2. Configure alerts for error rates, response times, and service availability
3. Add dashboards for user activity and system health

### API Usage Tracking

1. Implement middleware for tracking API usage:
   ```javascript
   app.use((req, res, next) => {
     const start = Date.now();
     res.on('finish', () => {
       const duration = Date.now() - start;
       console.log(`${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
     });
     next();
   });
   ```

2. Set up Google Cloud Logging for centralized log management

### LLM API Cost Monitoring

1. Create a service for tracking LLM API costs:
   ```javascript
   async function trackLLMUsage(tokens, model) {
     await db.collection('api_usage').add({
       timestamp: new Date(),
       tokens,
       model,
       estimatedCost: calculateCost(tokens, model)
     });
   }
   ```

2. Set up budget alerts in Google Cloud Console

## Scaling Strategy

1. Implement horizontal scaling with Cloud Run's auto-scaling capabilities
2. Use Firebase's built-in scaling for database operations
3. Implement caching for frequently accessed data
4. Optimize LLM API calls with proper batching and throttling

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

{"body":{},"duration":5,"error":null,"level":"info","message":"API Call","method":"GET","path":"/difficulty/mathematics/algebra","query":{},"status":200,"timestamp":"2025-06-07T17:10:01.111Z","userId":"test123"}
{"level":"error","message":"Failed to store API log in Firestore: this.db.collection(...).add is not a function","stack":"TypeError: this.db.collection(...).add is not a function\n    at LoggingService.logApiCall (/home/<USER>/git/web_test_series/server/services/loggingService.js:50:44)\n    at res.end (/home/<USER>/git/web_test_series/server/middleware/logging.js:18:20)\n    at ServerResponse.send (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:278:15)\n    at /home/<USER>/git/web_test_series/server/routes/questions.js:108:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-07T17:10:01.114Z"}
{"body":{"level":"medium","numQuestions":2,"subject":"mathematics","test_type":"practice","topic":"algebra"},"duration":9,"error":null,"level":"info","message":"API Call","method":"POST","path":"/generate","query":{},"status":500,"timestamp":"2025-06-07T17:10:05.518Z","userId":"test123"}
{"level":"error","message":"Failed to store API log in Firestore: this.db.collection(...).add is not a function","stack":"TypeError: this.db.collection(...).add is not a function\n    at LoggingService.logApiCall (/home/<USER>/git/web_test_series/server/services/loggingService.js:50:44)\n    at res.end (/home/<USER>/git/web_test_series/server/middleware/logging.js:18:20)\n    at ServerResponse.send (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:278:15)\n    at /home/<USER>/git/web_test_series/server/routes/questions.js:41:21\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-07T17:10:05.520Z"}
{"body":{},"duration":2,"error":null,"level":"info","message":"API Call","method":"GET","path":"/difficulty/mathematics/algebra","query":{},"status":200,"timestamp":"2025-06-07T17:18:44.146Z","userId":"test123"}
{"body":{"level":"medium","numQuestions":2,"subject":"mathematics","test_type":"practice","topic":"algebra"},"duration":4,"error":null,"level":"info","message":"API Call","method":"POST","path":"/generate","query":{},"status":500,"timestamp":"2025-06-07T17:18:55.099Z","userId":"test123"}
{"cost":0.000224,"duration":1,"error":null,"level":"info","message":"LLM Call","prompt":"Generate 1 medium level questions for mathematics topic: ","response":"[{\"id\":\"q1749317160033_0\",\"question\":\"Sample medium mathematics question 1 about \",\"options\":[\"Option A\",\"Option B\",\"Option C\",\"Option D\"],\"correctAnswer\":\"A\",\"explanation\":\"This is a sample explanation for question 1\",\"difficulty\":\"medium\",\"topic\":\"\",\"test_type\":\"practice\",\"syllabus_id\":null,\"unit_id\":null,\"topic_id\":null,\"multimedia\":null,\"generated_at\":\"2025-06-07T17:26:00.034Z\"}]","timestamp":"2025-06-07T17:26:00.034Z","type":"question_generation"}
{"body":{"level":"medium","numQuestions":2,"subject":"mathematics","test_type":"practice","topic":"algebra"},"duration":10,"error":null,"level":"info","message":"API Call","method":"POST","path":"/generate","query":{},"status":200,"timestamp":"2025-06-07T17:26:00.039Z","userId":"test123"}
{"body":{},"duration":2,"error":null,"level":"info","message":"API Call","method":"GET","path":"/difficulty/mathematics/algebra","query":{},"status":200,"timestamp":"2025-06-07T17:29:16.286Z","userId":"test123"}
{"cost":0.001958,"duration":0,"error":null,"level":"info","message":"LLM Call","prompt":"Generate 10 medium level questions for mathematics topic: ","response":"[{\"id\":\"q1749317365633_0\",\"question\":\"Sample medium mathematics question 1 about \",\"options\":[\"Option A\",\"Option B\",\"Option C\",\"Option D\"],\"correctAnswer\":\"A\",\"explanation\":\"This is a sample explanation for question 1\",\"difficulty\":\"medium\",\"topic\":\"\",\"test_type\":\"practice\",\"syllabus_id\":null,\"unit_id\":null,\"topic_id\":null,\"multimedia\":null,\"generated_at\":\"2025-06-07T17:29:25.633Z\"},{\"id\":\"q1749317365633_1\",\"question\":\"Sample medium mathematics question 2 about \",\"options\":[\"Option A\",\"Option B\",\"Option C\",\"Option D\"],\"correctAnswer\":\"A\",\"explanation\":\"This is a sample explanation for question 2\",\"difficulty\":\"medium\",\"topic\":\"\",\"test_type\":\"practice\",\"syllabus_id\":null,\"unit_id\":null,\"topic_id\":null,\"multimedia\":null,\"generated_at\":\"2025-06-07T17:29:25.633Z\"},{\"id\":\"q1749317365633_2\",\"question\":\"Sample medium mathematics question 3 about \",\"options\":[\"Option A\",\"Option B\",\"Option C\",\"Option D\"],\"correctAnswer\":\"A\",\"explanation\":\"This is a sample explanation for question 3\",\"difficulty...","timestamp":"2025-06-07T17:29:25.633Z","type":"question_generation"}
{"body":{"level":"medium","numQuestions":2,"subject":"mathematics","test_type":"practice","topic":"algebra"},"duration":7,"error":null,"level":"info","message":"API Call","method":"POST","path":"/generate","query":{},"status":200,"timestamp":"2025-06-07T17:29:25.639Z","userId":"test123"}
{"body":{"clarity":0.9,"difficultyMatch":0.8,"helpful":true},"duration":2,"error":null,"level":"info","message":"API Call","method":"POST","path":"/api/questions/feedback/q1749317365633_0","query":{},"status":404,"timestamp":"2025-06-07T17:29:36.960Z","userId":"test123"}
{"body":{"clarity":0.9,"difficultyMatch":0.8,"helpful":true},"duration":0,"error":null,"level":"info","message":"API Call","method":"POST","path":"/q1749317365633_0/feedback","query":{},"status":200,"timestamp":"2025-06-07T17:29:43.257Z","userId":"test123"}

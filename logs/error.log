{"level":"error","message":"Failed to store API log in Firestore: this.db.collection(...).add is not a function","stack":"TypeError: this.db.collection(...).add is not a function\n    at LoggingService.logApiCall (/home/<USER>/git/web_test_series/server/services/loggingService.js:50:44)\n    at res.end (/home/<USER>/git/web_test_series/server/middleware/logging.js:18:20)\n    at ServerResponse.send (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:278:15)\n    at /home/<USER>/git/web_test_series/server/routes/questions.js:108:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-07T17:10:01.114Z"}
{"level":"error","message":"Failed to store API log in Firestore: this.db.collection(...).add is not a function","stack":"TypeError: this.db.collection(...).add is not a function\n    at LoggingService.logApiCall (/home/<USER>/git/web_test_series/server/services/loggingService.js:50:44)\n    at res.end (/home/<USER>/git/web_test_series/server/middleware/logging.js:18:20)\n    at ServerResponse.send (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:232:10)\n    at ServerResponse.json (/home/<USER>/git/web_test_series/node_modules/express/lib/response.js:278:15)\n    at /home/<USER>/git/web_test_series/server/routes/questions.js:41:21\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-07T17:10:05.520Z"}
